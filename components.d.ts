/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    BottomTab: typeof import('./src/components/common/bottomTab.vue')['default']
    CenterTitle: typeof import('./src/components/common/CenterTitle.vue')['default']
    DataBox: typeof import('./src/components/common/DataBox.vue')['default']
    HistoryDialog: typeof import('./src/components/common/HistoryDialog.vue')['default']
    NavBar: typeof import('./src/components/common/NavBar.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
