{"name": "smart-ventilate", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@scena/ruler": "^0.20.0", "@tweenjs/tween.js": "^21.0.0", "@vueuse/core": "^10.5.0", "animate.css": "^4.1.1", "autoprefixer": "^10.4.16", "axios": "^1.3.5", "camera-controls": "^2.7.2", "dat.gui": "^0.7.9", "dompurify": "^3.2.5", "echarts": "^5.4.3", "element-plus": "^2.7.7", "gsap": "^3.9.1", "highlight.js": "^11.11.1", "hls.js": "^1.6.7", "leaflet": "^1.9.4", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "markdown-it-katex": "^2.0.3", "markdown-it-latex": "^0.2.0", "markdown-it-mathjax": "^2.0.0", "marked": "^15.0.11", "mitt": "^3.0.1", "pinia": "^2.1.7", "pinia-persistedstate-plugin": "^0.1.0", "pinia-plugin-persistedstate": "^3.2.0", "pinyin-pro": "^3.26.0", "postcss-pxtorem": "^5.1.1", "text-decoding": "^1.0.0", "three": "^0.170.0", "vue": "^3.4.27", "vue-router": "^4.4.0", "vue-sketch-ruler": "^1.0.3", "vue-slider-component": "^3.2.24", "xlsx": "^0.18.5"}, "devDependencies": {"@element-plus/icons-vue": "^2.1.0", "@types/node": "^20.10.5", "@vitejs/plugin-vue": "^4.2.3", "dayjs": "^1.11.13", "sass": "^1.64.2", "unocss": "^0.56.1", "unplugin-auto-import": "^0.16.6", "unplugin-element-plus": "^0.8.0", "unplugin-vue-components": "^0.25.1", "vite": "^4.5.0", "vite-plugin-glsl": "^1.1.2"}}