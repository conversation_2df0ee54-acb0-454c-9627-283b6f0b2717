{"version": 3, "file": "signalr.min.js", "mappings": "AAAA,IAA2CA,EAAMC,EAAND,EASxCE,KAT8CD,EASxC,I,MCRT,IAAIE,EAAsB,CCA1BA,EAAwB,CAACC,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXF,EAAoBI,EAAEF,EAAYC,KAASH,EAAoBI,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAE1E,GCNDH,EAAoBS,EAAI,WACvB,GAA0B,iBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAOC,MAAQ,IAAIC,SAAS,cAAb,EAGhB,CAFE,MAAOC,GACR,GAAsB,iBAAXC,OAAqB,OAAOA,MACxC,CACA,CAPuB,GCAxBd,EAAoBI,EAAI,CAACW,EAAKC,IAAUX,OAAOY,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFhB,EAAoBoB,EAAKnB,IACH,oBAAXoB,QAA0BA,OAAOC,aAC1CjB,OAAOC,eAAeL,EAASoB,OAAOC,YAAa,CAAEC,MAAO,WAE7DlB,OAAOC,eAAeL,EAAS,IAAc,CAAEsB,OAAO,GAAO,E,ICGlDC,E,8VCFL,MAAMC,UAAkBC,MAa3BC,YAAYC,EAAsBC,GAC9B,MAAMC,aAAuBb,UAC7Bc,MAAM,GAAGH,mBAA8BC,MACvClB,KAAKkB,WAAaA,EAIlBlB,KAAKqB,UAAYF,CACrB,EAIG,MAAMG,UAAqBP,MAS9BC,YAAYC,EAAuB,uBAC/B,MAAME,aAAuBb,UAC7Bc,MAAMH,GAINjB,KAAKqB,UAAYF,CACrB,EAIG,MAAMI,UAAmBR,MAS5BC,YAAYC,EAAuB,sBAC/B,MAAME,aAAuBb,UAC7Bc,MAAMH,GAINjB,KAAKqB,UAAYF,CACrB,EAKG,MAAMK,UAAkCT,MAgB3CC,YAAYS,EAAiBC,GACzB,MAAMP,aAAuBb,UAC7Bc,MAAMK,GACNzB,KAAK0B,UAAYA,EACjB1B,KAAK2B,UAAY,4BAIjB3B,KAAKqB,UAAYF,CACrB,EAKG,MAAMS,UAA+Bb,MAgBxCC,YAAYS,EAAiBC,GACzB,MAAMP,aAAuBb,UAC7Bc,MAAMK,GACNzB,KAAK0B,UAAYA,EACjB1B,KAAK2B,UAAY,yBAIjB3B,KAAKqB,UAAYF,CACrB,EAKG,MAAMU,UAAoCd,MAgB7CC,YAAYS,EAAiBC,GACzB,MAAMP,aAAuBb,UAC7Bc,MAAMK,GACNzB,KAAK0B,UAAYA,EACjB1B,KAAK2B,UAAY,8BAIjB3B,KAAKqB,UAAYF,CACrB,EAKG,MAAMW,UAAyCf,MAYlDC,YAAYS,GACR,MAAMN,aAAuBb,UAC7Bc,MAAMK,GACNzB,KAAK2B,UAAY,mCAIjB3B,KAAKqB,UAAYF,CACrB,EAKG,MAAMY,UAAwBhB,MAajCC,YAAYS,EAAiBO,GACzB,MAAMb,aAAuBb,UAC7Bc,MAAMK,GAENzB,KAAKgC,YAAcA,EAInBhC,KAAKqB,UAAYF,CACrB,EC/KG,MAAMc,EAqCTjB,YACoBE,EACAgB,EACAC,GAFA,KAAAjB,WAAAA,EACA,KAAAgB,WAAAA,EACA,KAAAC,QAAAA,CACpB,EAOG,MAAeC,EAeXvC,IAAIwC,EAAaC,GACpB,OAAOtC,KAAKuC,KAAK,IACVD,EACHE,OAAQ,MACRH,OAER,CAgBOI,KAAKJ,EAAaC,GACrB,OAAOtC,KAAKuC,KAAK,IACVD,EACHE,OAAQ,OACRH,OAER,CAgBOK,OAAOL,EAAaC,GACvB,OAAOtC,KAAKuC,KAAK,IACVD,EACHE,OAAQ,SACRH,OAER,CAeOM,gBAAgBN,GACnB,MAAO,EACX,GF5JJ,SAAYxB,GAER,qBAEA,qBAEA,iCAEA,yBAEA,qBAEA,2BAEA,kBACH,CAfD,CAAYA,IAAAA,EAAQ,KGFb,MAAM+B,EAIT,cAAuB,CAIhBC,IAAIC,EAAqBC,GAChC,EAPc,EAAAC,SAAoB,IAAIJ,ECKnC,MAAMK,EAAkB,kBAExB,MAAMC,EACFC,kBAAkBC,EAAUC,GAC/B,GAAID,QACA,MAAM,IAAIrC,MAAM,QAAQsC,2BAEhC,CACOF,kBAAkBC,EAAaC,GAClC,IAAKD,GAAOA,EAAIE,MAAM,SAClB,MAAM,IAAIvC,MAAM,QAAQsC,mCAEhC,CAEOF,YAAYC,EAAUG,EAAaF,GAEtC,KAAMD,KAAOG,GACT,MAAM,IAAIxC,MAAM,WAAWsC,YAAeD,KAElD,EAIG,MAAMI,EAESC,uBACd,MAAyB,iBAAXtD,QAAkD,iBAApBA,OAAOuD,QACvD,CAGkBC,yBACd,MAAuB,iBAATvE,MAAqB,kBAAmBA,IAC1D,CAGWwE,2BACP,MAAyB,iBAAXzD,aAAkD,IAApBA,OAAOuD,QACvD,CAIkBG,oBACd,OAAQ7D,KAAKyD,YAAczD,KAAK2D,cAAgB3D,KAAK4D,aACzD,EAIG,SAASE,EAAcC,EAAWC,GACrC,IAAIC,EAAS,GAYb,OAXIC,EAAcH,IACdE,EAAS,yBAAyBF,EAAKI,aACnCH,IACAC,GAAU,eAYf,SAA2BF,GAC9B,MAAMK,EAAO,IAAIC,WAAWN,GAG5B,IAAIO,EAAM,GAOV,OANAF,EAAKG,SAASC,IAEVF,GAAO,KADKE,EAAM,GAAK,IAAM,KACXA,EAAIC,SAAS,MAAM,IAIlCH,EAAII,OAAO,EAAGJ,EAAIK,OAAS,EACtC,CAxBqCC,CAAkBb,QAExB,iBAATA,IACdE,EAAS,yBAAyBF,EAAKY,SACnCX,IACAC,GAAU,eAAeF,OAG1BE,CACX,CAmBO,SAASC,EAAcd,GAC1B,OAAOA,GAA8B,oBAAhByB,cAChBzB,aAAeyB,aAEXzB,EAAIpC,aAAwC,gBAAzBoC,EAAIpC,YAAYqC,KAChD,CAGOyB,eAAeC,EAAYC,EAAiBC,EAAuBC,EAAwB7C,EAChEF,EAA+BG,GAC7D,MAAM6C,EAAiC,CAAC,GAEjC9B,EAAMzC,GAASwE,IACtBD,EAAQ9B,GAAQzC,EAEhBoE,EAAOnC,IAAIhC,EAASwE,MAAO,IAAIJ,8BAA0CnB,EAAc3B,EAASG,EAAQgD,uBAExG,MAAMC,EAAerB,EAAc/B,GAAW,cAAgB,OACxDqD,QAAiBN,EAAWzC,KAAKJ,EAAK,CACxCF,UACAgD,QAAS,IAAKA,KAAY7C,EAAQ6C,SAClCI,eACAE,QAASnD,EAAQmD,QACjBC,gBAAiBpD,EAAQoD,kBAG7BV,EAAOnC,IAAIhC,EAASwE,MAAO,IAAIJ,mDAA+DO,EAAStE,cAC3G,CAoBO,MAAMyE,EAIT3E,YAAY4E,EAAqBC,GAC7B7F,KAAK8F,EAAWF,EAChB5F,KAAK+F,EAAYF,CACrB,CAEOG,UACH,MAAMC,EAAgBjG,KAAK8F,EAASI,UAAUC,QAAQnG,KAAK+F,GACvDE,GAAS,GACTjG,KAAK8F,EAASI,UAAUE,OAAOH,EAAO,GAGH,IAAnCjG,KAAK8F,EAASI,UAAUvB,QAAgB3E,KAAK8F,EAASO,gBACtDrG,KAAK8F,EAASO,iBAAiBC,OAAOC,IAAD,GAE7C,EAIG,MAAMC,EAWTxF,YAAYyF,GACRzG,KAAK0G,EAAYD,EACjBzG,KAAK2G,IAAMC,OACf,CAEO/D,IAAIgE,EAAoBpF,GAC3B,GAAIoF,GAAY7G,KAAK0G,EAAW,CAC5B,MAAMI,EAAM,KAAI,IAAIC,MAAOC,kBAAkBnG,EAASgG,OAAcpF,IACpE,OAAQoF,GACJ,KAAKhG,EAASoG,SACd,KAAKpG,EAASE,MACVf,KAAK2G,IAAIO,MAAMJ,GACf,MACJ,KAAKjG,EAASsG,QACVnH,KAAK2G,IAAIS,KAAKN,GACd,MACJ,KAAKjG,EAASwG,YACVrH,KAAK2G,IAAIW,KAAKR,GACd,MACJ,QAEI9G,KAAK2G,IAAI9D,IAAIiE,G,CAI7B,EAIG,SAAS1B,IACZ,IAAImC,EAAsB,uBAI1B,OAHI/D,EAASK,SACT0D,EAAsB,cAEnB,CAAEA,EAAqBC,EAAmBvE,EAASwE,IAyDtDjE,EAASK,OACF,SAEA,UA5D0E6D,KACzF,CAGO,SAASF,EAAmBG,EAAiBC,EAAYC,EAAiBC,GAE7E,IAAIC,EAAoB,qBAExB,MAAMC,EAAgBL,EAAQM,MAAM,KAmBpC,OAlBAF,GAAa,GAAGC,EAAc,MAAMA,EAAc,KAClDD,GAAa,KAAKJ,MAGdI,GADAH,GAAa,KAAPA,EACO,GAAGA,MAEH,eAGjBG,GAAa,GAAGF,IAGZE,GADAD,EACa,KAAKA,IAEL,4BAGjBC,GAAa,IACNA,CACX,CAGc,SAASN,IACnB,IAAIjE,EAASK,OAYT,MAAO,GAXP,OAAQqE,QAAQC,UACZ,IAAK,QACD,MAAO,aACX,IAAK,SACD,MAAO,QACX,IAAK,QACD,MAAO,QACX,QACI,OAAOD,QAAQC,SAK/B,CAGc,SAAST,IACnB,GAAIlE,EAASK,OACT,OAAOqE,QAAQE,SAASC,IAGhC,CAWO,SAASC,EAAepI,GAC3B,OAAIA,EAAEqI,MACKrI,EAAEqI,MACFrI,EAAEuB,QACFvB,EAAEuB,QAEN,GAAGvB,GACd,CC5QO,MAAMsI,UAAwBpG,EAOjC,YAAmB4C,GAIf,GAHA5D,QACApB,KAAKyI,EAAUzD,EAEM,oBAAV0D,MAAuB,CAG9B,MAAMC,EAA0D,QAGhE3I,KAAK4I,EAAO,IAAKD,EAAY,gBAAiBE,WAC9C7I,KAAK8I,EAAaH,EAAY,cAI9B3I,KAAK8I,EAAaH,EAAY,eAAZA,CAA4B3I,KAAK8I,EAAY9I,KAAK4I,E,MAEpE5I,KAAK8I,EAAaJ,MAAMK,KDuP7B,WAEH,GAA0B,oBAAfhJ,WACP,OAAOA,WAEX,GAAoB,oBAATX,KACP,OAAOA,KAEX,GAAsB,oBAAXe,OACP,OAAOA,OAEX,QAAsB,IAAX,EAAAL,EACP,OAAO,EAAAA,EAEX,MAAM,IAAIiB,MAAM,wBACpB,CCtQyCiI,IAEjC,GAA+B,oBAApBC,gBAAiC,CAGxC,MAAMN,EAA0D,QAGhE3I,KAAKkJ,EAAuBP,EAAY,mB,MAExC3I,KAAKkJ,EAAuBD,eAEpC,CAGOnE,WAAWqE,GAEd,GAAIA,EAAQC,aAAeD,EAAQC,YAAYC,QAC3C,MAAM,IAAI9H,EAGd,IAAK4H,EAAQ3G,OACT,MAAM,IAAIzB,MAAM,sBAEpB,IAAKoI,EAAQ9G,IACT,MAAM,IAAItB,MAAM,mBAGpB,MAAMuI,EAAkB,IAAItJ,KAAKkJ,EAEjC,IAAIhC,EAEAiC,EAAQC,cACRD,EAAQC,YAAYG,QAAU,KAC1BD,EAAgBE,QAChBtC,EAAQ,IAAI3F,CAAY,GAMhC,IAuBIiE,EAvBAiE,EAAiB,KACrB,GAAIN,EAAQ1D,QAAS,CACjB,MAAMiE,EAAYP,EAAQ1D,QAC1BgE,EAAYE,YAAW,KACnBL,EAAgBE,QAChBxJ,KAAKyI,EAAQ5F,IAAIhC,EAASsG,QAAS,8BACnCD,EAAQ,IAAI5F,CAAc,GAC3BoI,E,CAGiB,KAApBP,EAAQhH,UACRgH,EAAQhH,aAAUyH,GAElBT,EAAQhH,UAERgH,EAAQhE,QAAUgE,EAAQhE,SAAW,CAAC,EAClCjB,EAAciF,EAAQhH,SACtBgH,EAAQhE,QAAQ,gBAAkB,2BAElCgE,EAAQhE,QAAQ,gBAAkB,4BAK1C,IACIK,QAAiBxF,KAAK8I,EAAWK,EAAQ9G,IAAM,CAC3CwH,KAAMV,EAAQhH,QACd2H,MAAO,WACPC,aAAyC,IAA5BZ,EAAQzD,gBAA2B,UAAY,cAC5DP,QAAS,CACL,mBAAoB,oBACjBgE,EAAQhE,SAEf3C,OAAQ2G,EAAQ3G,OAChBwH,KAAM,OACNC,SAAU,SACVC,OAAQZ,EAAgBY,Q,CAE9B,MAAOhK,GACL,GAAIgH,EACA,MAAMA,EAMV,MAJAlH,KAAKyI,EAAQ5F,IACThC,EAASsG,QACT,4BAA4BjH,MAE1BA,C,SAEFuJ,GACAU,aAAaV,GAEbN,EAAQC,cACRD,EAAQC,YAAYG,QAAU,K,CAItC,IAAK/D,EAAS4E,GAAI,CACd,MAAMnJ,QAAqBoJ,EAAmB7E,EAAU,QACxD,MAAM,IAAI1E,EAAUG,GAAgBuE,EAAStD,WAAYsD,EAAS8E,O,CAGtE,MAAMnI,EAAUkI,EAAmB7E,EAAU2D,EAAQ5D,cAC/CgF,QAAgBpI,EAEtB,OAAO,IAAIF,EACPuD,EAAS8E,OACT9E,EAAStD,WACTqI,EAER,CAEO5H,gBAAgBN,GACnB,IAAImI,EAAkB,GAKtB,OAJIhH,EAASK,QAAU7D,KAAK4I,GAExB5I,KAAK4I,EAAK6B,WAAWpI,GAAK,CAACnC,EAAGwK,IAAMF,EAAUE,EAAEC,KAAK,QAElDH,CACX,EAGJ,SAASH,EAAmB7E,EAAoBD,GAC5C,IAAIpD,EACJ,OAAQoD,GACJ,IAAK,cACDpD,EAAUqD,EAASoF,cACnB,MACJ,IAAK,OAOL,QACIzI,EAAUqD,EAASqF,OACnB,MANJ,IAAK,OACL,IAAK,WACL,IAAK,OACD,MAAM,IAAI9J,MAAM,GAAGwE,uBAM3B,OAAOpD,CACX,CCxKO,MAAM2I,UAAsB1I,EAG/B,YAAmB4C,GACf5D,QACApB,KAAKyI,EAAUzD,CACnB,CAGOzC,KAAK4G,GAER,OAAIA,EAAQC,aAAeD,EAAQC,YAAYC,QACpC0B,QAAQC,OAAO,IAAIzJ,GAGzB4H,EAAQ3G,OAGR2G,EAAQ9G,IAIN,IAAI0I,SAAsB,CAACE,EAASD,KACvC,MAAME,EAAM,IAAIC,eAEhBD,EAAIE,KAAKjC,EAAQ3G,OAAS2G,EAAQ9G,KAAM,GACxC6I,EAAIxF,qBAA8CkE,IAA5BT,EAAQzD,iBAAuCyD,EAAQzD,gBAC7EwF,EAAIG,iBAAiB,mBAAoB,kBACjB,KAApBlC,EAAQhH,UACRgH,EAAQhH,aAAUyH,GAElBT,EAAQhH,UAEJ+B,EAAciF,EAAQhH,SACtB+I,EAAIG,iBAAiB,eAAgB,4BAErCH,EAAIG,iBAAiB,eAAgB,6BAI7C,MAAMlG,EAAUgE,EAAQhE,QACpBA,GACAzF,OAAO4L,KAAKnG,GACPZ,SAASgH,IACNL,EAAIG,iBAAiBE,EAAQpG,EAAQoG,GAAQ,IAIrDpC,EAAQ5D,eACR2F,EAAI3F,aAAe4D,EAAQ5D,cAG3B4D,EAAQC,cACRD,EAAQC,YAAYG,QAAU,KAC1B2B,EAAI1B,QACJwB,EAAO,IAAIzJ,EAAa,GAI5B4H,EAAQ1D,UACRyF,EAAIzF,QAAU0D,EAAQ1D,SAG1ByF,EAAIM,OAAS,KACLrC,EAAQC,cACRD,EAAQC,YAAYG,QAAU,MAG9B2B,EAAIZ,QAAU,KAAOY,EAAIZ,OAAS,IAClCW,EAAQ,IAAIhJ,EAAaiJ,EAAIZ,OAAQY,EAAIhJ,WAAYgJ,EAAI1F,UAAY0F,EAAIO,eAEzET,EAAO,IAAIlK,EAAUoK,EAAI1F,UAAY0F,EAAIO,cAAgBP,EAAIhJ,WAAYgJ,EAAIZ,Q,EAIrFY,EAAIQ,QAAU,KACV1L,KAAKyI,EAAQ5F,IAAIhC,EAASsG,QAAS,4BAA4B+D,EAAIZ,WAAWY,EAAIhJ,eAClF8I,EAAO,IAAIlK,EAAUoK,EAAIhJ,WAAYgJ,EAAIZ,QAAQ,EAGrDY,EAAIS,UAAY,KACZ3L,KAAKyI,EAAQ5F,IAAIhC,EAASsG,QAAS,8BACnC6D,EAAO,IAAI1J,EAAe,EAG9B4J,EAAI3I,KAAK4G,EAAQhH,QAAQ,IAlElB4I,QAAQC,OAAO,IAAIjK,MAAM,oBAHzBgK,QAAQC,OAAO,IAAIjK,MAAM,sBAuExC,ECpFG,MAAM6K,UAA0BxJ,EAInC,YAAmB4C,GAGf,GAFA5D,QAEqB,oBAAVsH,OAAyBlF,EAASK,OACzC7D,KAAK6L,EAAc,IAAIrD,EAAgBxD,OACpC,IAA8B,oBAAnBmG,eAGd,MAAM,IAAIpK,MAAM,+BAFhBf,KAAK6L,EAAc,IAAIf,EAAc9F,E,CAI7C,CAGOzC,KAAK4G,GAER,OAAIA,EAAQC,aAAeD,EAAQC,YAAYC,QACpC0B,QAAQC,OAAO,IAAIzJ,GAGzB4H,EAAQ3G,OAGR2G,EAAQ9G,IAINrC,KAAK6L,EAAYtJ,KAAK4G,GAHlB4B,QAAQC,OAAO,IAAIjK,MAAM,oBAHzBgK,QAAQC,OAAO,IAAIjK,MAAM,sBAOxC,CAEO4B,gBAAgBN,GACnB,OAAOrC,KAAK6L,EAAYlJ,gBAAgBN,EAC5C,ECzCG,MAAMyJ,EAIF3I,aAAa4I,GAChB,MAAO,GAAGA,IAASD,EAAkBE,iBACzC,CAEO7I,aAAa8I,GAChB,GAAIA,EAAMA,EAAMtH,OAAS,KAAOmH,EAAkBE,gBAC9C,MAAM,IAAIjL,MAAM,0BAGpB,MAAMmL,EAAWD,EAAMhE,MAAM6D,EAAkBE,iBAE/C,OADAE,EAASC,MACFD,CACX,EAfc,EAAAE,oBAAsB,GACtB,EAAAJ,gBAAkBK,OAAOC,aAAaR,EAAkBM,qBCYnE,MAAMG,EAEFC,sBAAsBC,GACzB,OAAOX,EAAkBY,MAAMC,KAAKC,UAAUH,GAClD,CAEOI,uBAAuB9I,GAC1B,IAAI+I,EACAC,EAEJ,GAAI7I,EAAcH,GAAO,CAErB,MAAMiJ,EAAa,IAAI3I,WAAWN,GAC5BkJ,EAAiBD,EAAW7G,QAAQ2F,EAAkBM,qBAC5D,IAAwB,IAApBa,EACA,MAAM,IAAIlM,MAAM,0BAKpB,MAAMmM,EAAiBD,EAAiB,EACxCH,EAAcT,OAAOC,aAAaa,MAAM,KAAMC,MAAM9M,UAAU+M,MAAM7M,KAAKwM,EAAWK,MAAM,EAAGH,KAC7FH,EAAiBC,EAAW7I,WAAa+I,EAAkBF,EAAWK,MAAMH,GAAgBI,OAAS,I,KAClG,CACH,MAAMC,EAAmBxJ,EACnBkJ,EAAiBM,EAASpH,QAAQ2F,EAAkBE,iBAC1D,IAAwB,IAApBiB,EACA,MAAM,IAAIlM,MAAM,0BAKpB,MAAMmM,EAAiBD,EAAiB,EACxCH,EAAcS,EAASC,UAAU,EAAGN,GACpCH,EAAiBQ,EAAS5I,OAASuI,EAAkBK,EAASC,UAAUN,GAAkB,I,CAI9F,MAAMhB,EAAWJ,EAAkB2B,MAAMX,GACnCtH,EAAWmH,KAAKc,MAAMvB,EAAS,IACrC,GAAI1G,EAASkI,KACT,MAAM,IAAI3M,MAAM,kDAMpB,MAAO,CAACgM,EAJ0CvH,EAKtD,EC5DJ,IAAYmI,ECUAC,GDVZ,SAAYD,GAER,+BAEA,+BAEA,+BAEA,2CAEA,2CAEA,mBAEA,oBACH,CAfD,CAAYA,IAAAA,EAAW,KEAhB,MAAME,EAOT7M,cACIhB,KAAKkG,UAAY,EACrB,CAEO4H,KAAKC,GACR,IAAK,MAAMlI,KAAY7F,KAAKkG,UACxBL,EAASiI,KAAKC,EAEtB,CAEO7G,MAAM8G,GACT,IAAK,MAAMnI,KAAY7F,KAAKkG,UACpBL,EAASqB,OACTrB,EAASqB,MAAM8G,EAG3B,CAEOC,WACH,IAAK,MAAMpI,KAAY7F,KAAKkG,UACpBL,EAASoI,UACTpI,EAASoI,UAGrB,CAEOC,UAAUrI,GAEb,OADA7F,KAAKkG,UAAUiI,KAAKtI,GACb,IAAIF,EAAoB3F,KAAM6F,EACzC,GD1BJ,SAAY+H,GAER,8BAEA,0BAEA,wBAEA,gCAEA,6BACH,CAXD,CAAYA,IAAAA,EAAkB,KAcvB,MAAMQ,EAmET,YAAoBC,EAAyBrJ,EAAiBsJ,EAAwBC,GAvC9E,KAAAC,EAAyB,EASzB,KAAAC,EAAuB,KAE3BzO,KAAKyI,EAAQ5F,IAAIhC,EAASsG,QAAS,uNAAuN,EA6B1PjE,EAAIwL,WAAWL,EAAY,cAC3BnL,EAAIwL,WAAW1J,EAAQ,UACvB9B,EAAIwL,WAAWJ,EAAU,YAEzBtO,KAAK2O,4BA1FyB,IA2F9B3O,KAAK4O,gCA1F+B,KA4FpC5O,KAAKyI,EAAUzD,EACfhF,KAAK6O,EAAYP,EACjBtO,KAAKqO,WAAaA,EAClBrO,KAAK8O,EAAmBP,EACxBvO,KAAK+O,EAAqB,IAAIxC,EAE9BvM,KAAKqO,WAAWW,UAAajL,GAAc/D,KAAKiP,EAAqBlL,GACrE/D,KAAKqO,WAAWa,QAAWhI,GAAkBlH,KAAKmP,EAAkBjI,GAEpElH,KAAKoP,EAAa,CAAC,EACnBpP,KAAKqP,EAAW,CAAC,EACjBrP,KAAKsP,EAAmB,GACxBtP,KAAKuP,EAAyB,GAC9BvP,KAAKwP,EAAwB,GAC7BxP,KAAKyP,EAAgB,EACrBzP,KAAK0P,GAA6B,EAClC1P,KAAK2P,EAAmB/B,EAAmBgC,aAC3C5P,KAAK6P,GAAqB,EAE1B7P,KAAK8P,EAAqB9P,KAAK6O,EAAUkB,aAAa,CAAErC,KAAMC,EAAYqC,MAC9E,CAhCO7M,cAAckL,EAAyBrJ,EAAiBsJ,EAAwBC,GACnF,OAAO,IAAIH,EAAcC,EAAYrJ,EAAQsJ,EAAUC,EAC3D,CAiCI0B,YACA,OAAOjQ,KAAK2P,CAChB,CAKIO,mBACA,OAAOlQ,KAAKqO,YAAcrO,KAAKqO,WAAW6B,cAAwB,IACtE,CAGIC,cACA,OAAOnQ,KAAKqO,WAAW8B,SAAW,EACtC,CAOIA,YAAQ9N,GACR,GAAIrC,KAAK2P,IAAqB/B,EAAmBgC,cAAgB5P,KAAK2P,IAAqB/B,EAAmBwC,aAC1G,MAAM,IAAIrP,MAAM,0FAGpB,IAAKsB,EACD,MAAM,IAAItB,MAAM,8CAGpBf,KAAKqO,WAAW8B,QAAU9N,CAC9B,CAMOgO,QAEH,OADArQ,KAAKsQ,EAAgBtQ,KAAKuQ,IACnBvQ,KAAKsQ,CAChB,CAEQxL,UACJ,GAAI9E,KAAK2P,IAAqB/B,EAAmBgC,aAC7C,OAAO7E,QAAQC,OAAO,IAAIjK,MAAM,0EAGpCf,KAAK2P,EAAmB/B,EAAmB4C,WAC3CxQ,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,2BAEjC,UACUzQ,KAAK0Q,IAEPlN,EAASC,WAETtD,OAAOuD,SAASiN,iBAAiB,SAAU3Q,KAAKyO,GAGpDzO,KAAK2P,EAAmB/B,EAAmBgD,UAC3C5Q,KAAK6P,GAAqB,EAC1B7P,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,wC,CACnC,MAAOvQ,GAGL,OAFAF,KAAK2P,EAAmB/B,EAAmBgC,aAC3C5P,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,gEAAgEvQ,OAC1F6K,QAAQC,OAAO9K,E,CAE9B,CAEQ4E,UACJ9E,KAAK6Q,OAAwBjH,EAC7B5J,KAAK0P,GAA6B,EAElC,MAAMoB,EAAmB,IAAI/F,SAAQ,CAACE,EAASD,KAC3ChL,KAAK+Q,EAAqB9F,EAC1BjL,KAAKgR,EAAqBhG,CAAM,UAG9BhL,KAAKqO,WAAWgC,MAAMrQ,KAAK6O,EAAUoC,gBAE3C,IACI,MAAMxE,EAA4C,CAC9C6B,SAAUtO,KAAK6O,EAAUxL,KACzBsE,QAAS3H,KAAK6O,EAAUlH,SAmB5B,GAhBA3H,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,oCAE3BzQ,KAAKkR,EAAalR,KAAK+O,EAAmBvC,sBAAsBC,IAEtEzM,KAAKyI,EAAQ5F,IAAIhC,EAASwG,YAAa,sBAAsBrH,KAAK6O,EAAUxL,UAG5ErD,KAAKmR,IACLnR,KAAKoR,IACLpR,KAAKqR,UAECP,EAKF9Q,KAAK6Q,EAKL,MAAM7Q,KAAK6Q,EAGV7Q,KAAKqO,WAAWiD,SAASC,yBACpBvR,KAAKkR,EAAalR,KAAK8P,E,CAEnC,MAAO5P,GASL,MARAF,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,oCAAoCvQ,8CAErEF,KAAKmR,IACLnR,KAAKwR,UAICxR,KAAKqO,WAAWoD,KAAKvR,GACrBA,C,CAEd,CAMO4E,aAEH,MAAM4M,EAAe1R,KAAKsQ,EAE1BtQ,KAAK2R,GAAe3R,KAAK4R,WACnB5R,KAAK2R,GAEX,UAEUD,C,CACR,MAAOxR,G,CAGb,CAEQ0R,GAAc1K,GAClB,OAAIlH,KAAK2P,IAAqB/B,EAAmBgC,cAC7C5P,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,8BAA8BvJ,+DACxD6D,QAAQE,WAGfjL,KAAK2P,IAAqB/B,EAAmBiE,eAC7C7R,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,+BAA+BvJ,4EACzDlH,KAAK2R,KAGhB3R,KAAK2P,EAAmB/B,EAAmBiE,cAE3C7R,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,2BAE7BzQ,KAAK8R,IAIL9R,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,iEAEjCtG,aAAanK,KAAK8R,IAClB9R,KAAK8R,QAAwBlI,EAE7B5J,KAAK+R,KACEhH,QAAQE,YAGnBjL,KAAKmR,IACLnR,KAAKwR,IACLxR,KAAK6Q,EAAwB3J,GAAS,IAAI3F,EAAW,uEAK9CvB,KAAKqO,WAAWoD,KAAKvK,IAChC,CASO8K,OAAgBC,KAAuBC,GAC1C,MAAOC,EAASC,GAAapS,KAAKqS,GAAwBH,GACpDI,EAAuBtS,KAAKuS,GAAwBN,EAAYC,EAAME,GAG5E,IAAII,EAEJ,MAAM5M,EAAU,IAAIiI,EAqCpB,OApCAjI,EAAQS,eAAiB,KACrB,MAAMoM,EAA4CzS,KAAK0S,GAAwBJ,EAAqBK,cAIpG,cAFO3S,KAAKoP,EAAWkD,EAAqBK,cAErCH,EAAaI,MAAK,IACd5S,KAAK6S,GAAkBJ,IAChC,EAGNzS,KAAKoP,EAAWkD,EAAqBK,cAAgB,CAACG,EAA+D5L,KAC7GA,EACAtB,EAAQsB,MAAMA,GAEP4L,IAEHA,EAAgBpF,OAASC,EAAYoF,WACjCD,EAAgB5L,MAChBtB,EAAQsB,MAAM,IAAInG,MAAM+R,EAAgB5L,QAExCtB,EAAQqI,WAGZrI,EAAQkI,KAAMgF,EAAoB,M,EAK9CN,EAAexS,KAAK6S,GAAkBP,GACjChM,OAAOpG,IACJ0F,EAAQsB,MAAMhH,UACPF,KAAKoP,EAAWkD,EAAqBK,aAAa,IAGjE3S,KAAKgT,GAAeb,EAASK,GAEtB5M,CACX,CAEQsL,EAAazP,GAEjB,OADAzB,KAAKqR,IACErR,KAAKqO,WAAW9L,KAAKd,EAChC,CAMQoR,GAAkBpR,GACtB,OAAOzB,KAAKkR,EAAalR,KAAK6O,EAAUkB,aAAatO,GACzD,CAWOc,KAAK0P,KAAuBC,GAC/B,MAAOC,EAASC,GAAapS,KAAKqS,GAAwBH,GACpDe,EAAcjT,KAAK6S,GAAkB7S,KAAKkT,GAAkBjB,EAAYC,GAAM,EAAME,IAI1F,OAFApS,KAAKgT,GAAeb,EAASc,GAEtBA,CACX,CAaOE,OAAgBlB,KAAuBC,GAC1C,MAAOC,EAASC,GAAapS,KAAKqS,GAAwBH,GACpDI,EAAuBtS,KAAKkT,GAAkBjB,EAAYC,GAAM,EAAOE,GAgC7E,OA9BU,IAAIrH,SAAa,CAACE,EAASD,KAEjChL,KAAKoP,EAAWkD,EAAqBK,cAAiB,CAACG,EAA+D5L,KAC9GA,EACA8D,EAAO9D,GAEA4L,IAEHA,EAAgBpF,OAASC,EAAYoF,WACjCD,EAAgB5L,MAChB8D,EAAO,IAAIjK,MAAM+R,EAAgB5L,QAEjC+D,EAAQ6H,EAAgBM,QAG5BpI,EAAO,IAAIjK,MAAM,4BAA4B+R,EAAgBpF,S,EAKzE,MAAM8E,EAAexS,KAAK6S,GAAkBP,GACvChM,OAAOpG,IACJ8K,EAAO9K,UAEAF,KAAKoP,EAAWkD,EAAqBK,aAAc,IAGlE3S,KAAKgT,GAAeb,EAASK,EAAa,GAIlD,CAQOa,GAAGpB,EAAoBqB,GACrBrB,GAAeqB,IAIpBrB,EAAaA,EAAWsB,cACnBvT,KAAKqP,EAAS4C,KACfjS,KAAKqP,EAAS4C,GAAc,KAIsB,IAAlDjS,KAAKqP,EAAS4C,GAAY9L,QAAQmN,IAItCtT,KAAKqP,EAAS4C,GAAY9D,KAAKmF,GACnC,CAiBOE,IAAIvB,EAAoBzP,GAC3B,IAAKyP,EACD,OAGJA,EAAaA,EAAWsB,cACxB,MAAME,EAAWzT,KAAKqP,EAAS4C,GAC/B,GAAKwB,EAGL,GAAIjR,EAAQ,CACR,MAAMkR,EAAYD,EAAStN,QAAQ3D,IAChB,IAAfkR,IACAD,EAASrN,OAAOsN,EAAW,GACH,IAApBD,EAAS9O,eACF3E,KAAKqP,EAAS4C,G,aAItBjS,KAAKqP,EAAS4C,EAG7B,CAMO/C,QAAQyE,GACPA,GACA3T,KAAKsP,EAAiBnB,KAAKwF,EAEnC,CAMOC,eAAeD,GACdA,GACA3T,KAAKuP,EAAuBpB,KAAKwF,EAEzC,CAMOE,cAAcF,GACbA,GACA3T,KAAKwP,EAAsBrB,KAAKwF,EAExC,CAEQ1E,EAAqBlL,GASzB,GARA/D,KAAKmR,IAEAnR,KAAK0P,IACN3L,EAAO/D,KAAK8T,GAA0B/P,GACtC/D,KAAK0P,GAA6B,GAIlC3L,EAAM,CAEN,MAAMmI,EAAWlM,KAAK6O,EAAUkF,cAAchQ,EAAM/D,KAAKyI,GAEzD,IAAK,MAAMhH,KAAWyK,EAClB,OAAQzK,EAAQiM,MACZ,KAAKC,EAAYqG,WAEbhU,KAAKiU,GAAoBxS,GACzB,MACJ,KAAKkM,EAAYuG,WACjB,KAAKvG,EAAYoF,WAAY,CACzB,MAAMY,EAAW3T,KAAKoP,EAAW3N,EAAQkR,cACzC,GAAIgB,EAAU,CACNlS,EAAQiM,OAASC,EAAYoF,mBACtB/S,KAAKoP,EAAW3N,EAAQkR,cAEnC,IACIgB,EAASlS,E,CACX,MAAOvB,GACLF,KAAKyI,EAAQ5F,IAAIhC,EAASE,MAAO,gCAAgCuH,EAAepI,K,EAGxF,K,CAEJ,KAAKyN,EAAYqC,KAEb,MACJ,KAAKrC,EAAYwG,MAAO,CACpBnU,KAAKyI,EAAQ5F,IAAIhC,EAASwG,YAAa,uCAEvC,MAAMH,EAAQzF,EAAQyF,MAAQ,IAAInG,MAAM,sCAAwCU,EAAQyF,YAAS0C,GAElE,IAA3BnI,EAAQ2S,eAKRpU,KAAKqO,WAAWoD,KAAKvK,GAGrBlH,KAAK2R,GAAe3R,KAAK4R,GAAc1K,GAG3C,K,CAEJ,QACIlH,KAAKyI,EAAQ5F,IAAIhC,EAASsG,QAAS,yBAAyB1F,EAAQiM,S,CAMpF1N,KAAKoR,GACT,CAEQ0C,GAA0B/P,GAC9B,IAAIsQ,EACAtH,EAEJ,KACKA,EAAesH,GAAmBrU,KAAK+O,EAAmBlC,uBAAuB9I,E,CACpF,MAAO7D,GACL,MAAMuB,EAAU,qCAAuCvB,EACvDF,KAAKyI,EAAQ5F,IAAIhC,EAASE,MAAOU,GAEjC,MAAMyF,EAAQ,IAAInG,MAAMU,GAExB,MADAzB,KAAKgR,EAAmB9J,GAClBA,C,CAEV,GAAImN,EAAgBnN,MAAO,CACvB,MAAMzF,EAAU,oCAAsC4S,EAAgBnN,MACtElH,KAAKyI,EAAQ5F,IAAIhC,EAASE,MAAOU,GAEjC,MAAMyF,EAAQ,IAAInG,MAAMU,GAExB,MADAzB,KAAKgR,EAAmB9J,GAClBA,C,CAMV,OAJIlH,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,8BAGrCzQ,KAAK+Q,IACEhE,CACX,CAEQsE,IACArR,KAAKqO,WAAWiD,SAASC,oBAM7BvR,KAAKwO,GAAiB,IAAIzH,MAAOuN,UAAYtU,KAAK4O,gCAElD5O,KAAKwR,IACT,CAEQJ,IACJ,KAAKpR,KAAKqO,WAAWiD,UAAatR,KAAKqO,WAAWiD,SAASC,oBAEvDvR,KAAKuU,GAAiB5K,YAAW,IAAM3J,KAAKwU,iBAAiBxU,KAAK2O,kCAGnC/E,IAA3B5J,KAAKyU,KACT,CACI,IAAIC,EAAW1U,KAAKwO,GAAiB,IAAIzH,MAAOuN,UAC5CI,EAAW,IACXA,EAAW,GAIf1U,KAAKyU,GAAoB9K,YAAW7E,UAChC,GAAI9E,KAAK2P,IAAqB/B,EAAmBgD,UAC7C,UACU5Q,KAAKkR,EAAalR,KAAK8P,E,CAC/B,MAGE9P,KAAKwR,G,IAGdkD,E,CAGf,CAGQF,gBAIJxU,KAAKqO,WAAWoD,KAAK,IAAI1Q,MAAM,uEACnC,CAEQ+D,SAA0B6P,GAC9B,MAAM1C,EAAa0C,EAAkBC,OAAOrB,cACtCsB,EAAU7U,KAAKqP,EAAS4C,GAC9B,IAAK4C,EAQD,OAPA7U,KAAKyI,EAAQ5F,IAAIhC,EAASsG,QAAS,mCAAmC8K,kBAGlE0C,EAAkBhC,eAClB3S,KAAKyI,EAAQ5F,IAAIhC,EAASsG,QAAS,wBAAwB8K,gCAAyC0C,EAAkBhC,wBAChH3S,KAAK6S,GAAkB7S,KAAK8U,GAAyBH,EAAkBhC,aAAc,kCAAmC,SAMtI,MAAMoC,EAAcF,EAAQxH,QAGtB2H,IAAkBL,EAAkBhC,aAE1C,IAAIsC,EACAC,EACAC,EACJ,IAAK,MAAMC,KAAKL,EACZ,IACI,MAAMM,EAAUJ,EAChBA,QAAYG,EAAEjI,MAAMnN,KAAM2U,EAAkBW,WACxCN,GAAmBC,GAAOI,IAC1BrV,KAAKyI,EAAQ5F,IAAIhC,EAASE,MAAO,kCAAkCkR,gCACnEkD,EAAoBnV,KAAK8U,GAAyBH,EAAkBhC,aAAe,oCAAqC,OAG5HuC,OAAYtL,C,CACd,MAAO1J,GACLgV,EAAYhV,EACZF,KAAKyI,EAAQ5F,IAAIhC,EAASE,MAAO,8BAA8BkR,mBAA4B/R,M,CAG/FiV,QACMnV,KAAK6S,GAAkBsC,GACtBH,GAEHE,EACAC,EAAoBnV,KAAK8U,GAAyBH,EAAkBhC,aAAe,GAAGuC,IAAa,WACpFtL,IAARqL,EACPE,EAAoBnV,KAAK8U,GAAyBH,EAAkBhC,aAAe,KAAMsC,IAEzFjV,KAAKyI,EAAQ5F,IAAIhC,EAASsG,QAAS,wBAAwB8K,gCAAyC0C,EAAkBhC,kBAEtHwC,EAAoBnV,KAAK8U,GAAyBH,EAAkBhC,aAAe,kCAAmC,aAEpH3S,KAAK6S,GAAkBsC,IAEzBF,GACAjV,KAAKyI,EAAQ5F,IAAIhC,EAASE,MAAO,qBAAqBkR,kDAGlE,CAEQ9C,EAAkBjI,GACtBlH,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,kCAAkCvJ,4BAAgClH,KAAK2P,MAGxG3P,KAAK6Q,EAAwB7Q,KAAK6Q,GAAyB3J,GAAS,IAAI3F,EAAW,iFAI/EvB,KAAK+Q,GACL/Q,KAAK+Q,IAGT/Q,KAAKuV,GAA0BrO,GAAS,IAAInG,MAAM,uEAElDf,KAAKmR,IACLnR,KAAKwR,IAEDxR,KAAK2P,IAAqB/B,EAAmBiE,cAC7C7R,KAAK+R,GAAe7K,GACblH,KAAK2P,IAAqB/B,EAAmBgD,WAAa5Q,KAAK8O,EAEtE9O,KAAKwV,GAAWtO,GACTlH,KAAK2P,IAAqB/B,EAAmBgD,WACpD5Q,KAAK+R,GAAe7K,EAQ5B,CAEQ6K,GAAe7K,GACnB,GAAIlH,KAAK6P,EAAoB,CACzB7P,KAAK2P,EAAmB/B,EAAmBgC,aAC3C5P,KAAK6P,GAAqB,EAEtBrM,EAASC,WACTtD,OAAOuD,SAAS+R,oBAAoB,SAAUzV,KAAKyO,GAGvD,IACIzO,KAAKsP,EAAiB/K,SAASmG,GAAMA,EAAEyC,MAAMnN,KAAM,CAACkH,K,CACtD,MAAOhH,GACLF,KAAKyI,EAAQ5F,IAAIhC,EAASE,MAAO,0CAA0CmG,mBAAuBhH,M,EAG9G,CAEQ4E,SAAiBoC,GACrB,MAAMwO,EAAqB3O,KAAK4O,MAChC,IAAIC,EAA4B,EAC5BC,OAAuBjM,IAAV1C,EAAsBA,EAAQ,IAAInG,MAAM,mDAErD+U,EAAiB9V,KAAK+V,GAAmBH,IAA6B,EAAGC,GAE7E,GAAuB,OAAnBC,EAGA,OAFA9V,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,2GACjCzQ,KAAK+R,GAAe7K,GAYxB,GARAlH,KAAK2P,EAAmB/B,EAAmBwC,aAEvClJ,EACAlH,KAAKyI,EAAQ5F,IAAIhC,EAASwG,YAAa,6CAA6CH,OAEpFlH,KAAKyI,EAAQ5F,IAAIhC,EAASwG,YAAa,4BAGA,IAAvCrH,KAAKuP,EAAuB5K,OAAc,CAC1C,IACI3E,KAAKuP,EAAuBhL,SAASmG,GAAMA,EAAEyC,MAAMnN,KAAM,CAACkH,K,CAC5D,MAAOhH,GACLF,KAAKyI,EAAQ5F,IAAIhC,EAASE,MAAO,iDAAiDmG,mBAAuBhH,M,CAI7G,GAAIF,KAAK2P,IAAqB/B,EAAmBwC,aAE7C,YADApQ,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,wF,CAKzC,KAA0B,OAAnBqF,GAAyB,CAQ5B,GAPA9V,KAAKyI,EAAQ5F,IAAIhC,EAASwG,YAAa,4BAA4BuO,mBAA2CE,eAExG,IAAI/K,SAASE,IACfjL,KAAK8R,GAAwBnI,WAAWsB,EAAS6K,EAAgB,IAErE9V,KAAK8R,QAAwBlI,EAEzB5J,KAAK2P,IAAqB/B,EAAmBwC,aAE7C,YADApQ,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,qFAIrC,IAMI,SALMzQ,KAAK0Q,IAEX1Q,KAAK2P,EAAmB/B,EAAmBgD,UAC3C5Q,KAAKyI,EAAQ5F,IAAIhC,EAASwG,YAAa,2CAEG,IAAtCrH,KAAKwP,EAAsB7K,OAC3B,IACI3E,KAAKwP,EAAsBjL,SAASmG,GAAMA,EAAEyC,MAAMnN,KAAM,CAACA,KAAKqO,WAAW6B,gB,CAC3E,MAAOhQ,GACLF,KAAKyI,EAAQ5F,IAAIhC,EAASE,MAAO,uDAAuDf,KAAKqO,WAAW6B,8BAA8BhQ,M,CAI9I,M,CACF,MAAOA,GAGL,GAFAF,KAAKyI,EAAQ5F,IAAIhC,EAASwG,YAAa,8CAA8CnH,OAEjFF,KAAK2P,IAAqB/B,EAAmBwC,aAM7C,OALApQ,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,4BAA4BzQ,KAAK2P,oFAE9D3P,KAAK2P,IAA4B/B,EAAmBiE,eACpD7R,KAAK+R,MAKb8D,EAAa3V,aAAaa,MAAQb,EAAI,IAAIa,MAAMb,EAAEuE,YAClDqR,EAAiB9V,KAAK+V,GAAmBH,IAA6B7O,KAAK4O,MAAQD,EAAoBG,E,EAI/G7V,KAAKyI,EAAQ5F,IAAIhC,EAASwG,YAAa,+CAA+CN,KAAK4O,MAAQD,YAA6BE,gDAEhI5V,KAAK+R,IACT,CAEQgE,GAAmBC,EAA4BC,EAA6BC,GAChF,IACI,OAAOlW,KAAK8O,EAAkBqH,6BAA6B,CACvDF,sBACAD,qBACAE,e,CAEN,MAAOhW,GAEL,OADAF,KAAKyI,EAAQ5F,IAAIhC,EAASE,MAAO,6CAA6CiV,MAAuBC,mBAAqC/V,OACnI,I,CAEf,CAEQqV,GAA0BrO,GAC9B,MAAMkP,EAAYpW,KAAKoP,EACvBpP,KAAKoP,EAAa,CAAC,EAEnB1P,OAAO4L,KAAK8K,GACP7R,SAAS/E,IACN,MAAMmU,EAAWyC,EAAU5W,GAC3B,IACImU,EAAS,KAAMzM,E,CACjB,MAAOhH,GACLF,KAAKyI,EAAQ5F,IAAIhC,EAASE,MAAO,wCAAwCmG,mBAAuBoB,EAAepI,K,IAG/H,CAEQsR,IACAxR,KAAKyU,KACLtK,aAAanK,KAAKyU,IAClBzU,KAAKyU,QAAoB7K,EAEjC,CAEQuH,IACAnR,KAAKuU,IACLpK,aAAanK,KAAKuU,GAE1B,CAEQrB,GAAkBjB,EAAoBC,EAAamE,EAAsBjE,GAC7E,GAAIiE,EACA,OAAyB,IAArBjE,EAAUzN,OACH,CACH2Q,UAAWpD,EACXE,YACAwC,OAAQ3C,EACRvE,KAAMC,EAAYqG,YAGf,CACHsB,UAAWpD,EACX0C,OAAQ3C,EACRvE,KAAMC,EAAYqG,YAGvB,CACH,MAAMrB,EAAe3S,KAAKyP,EAG1B,OAFAzP,KAAKyP,IAEoB,IAArB2C,EAAUzN,OACH,CACH2Q,UAAWpD,EACXS,aAAcA,EAAalO,WAC3B2N,YACAwC,OAAQ3C,EACRvE,KAAMC,EAAYqG,YAGf,CACHsB,UAAWpD,EACXS,aAAcA,EAAalO,WAC3BmQ,OAAQ3C,EACRvE,KAAMC,EAAYqG,W,CAIlC,CAEQhB,GAAeb,EAA+BK,GAClD,GAAuB,IAAnBL,EAAQxN,OAAZ,CAKK6N,IACDA,EAAezH,QAAQE,WAK3B,IAAK,MAAMqL,KAAYnE,EACnBA,EAAQmE,GAAUpI,UAAU,CACxBD,SAAU,KACNuE,EAAeA,EAAaI,MAAK,IAAM5S,KAAK6S,GAAkB7S,KAAK8U,GAAyBwB,KAAW,EAE3GpP,MAAQ8G,IACJ,IAAIvM,EAEAA,EADAuM,aAAejN,MACLiN,EAAIvM,QACPuM,GAAOA,EAAIvJ,SACRuJ,EAAIvJ,WAEJ,gBAGd+N,EAAeA,EAAaI,MAAK,IAAM5S,KAAK6S,GAAkB7S,KAAK8U,GAAyBwB,EAAU7U,KAAU,EAEpHqM,KAAOC,IACHyE,EAAeA,EAAaI,MAAK,IAAM5S,KAAK6S,GAAkB7S,KAAKuW,GAAyBD,EAAUvI,KAAO,G,CAI7H,CAEQsE,GAAwBH,GAC5B,MAAMC,EAAgC,GAChCC,EAAsB,GAC5B,IAAK,IAAIoE,EAAI,EAAGA,EAAItE,EAAKvN,OAAQ6R,IAAK,CAClC,MAAMC,EAAWvE,EAAKsE,GACtB,GAAIxW,KAAK0W,GAAcD,GAAW,CAC9B,MAAMH,EAAWtW,KAAKyP,EACtBzP,KAAKyP,IAEL0C,EAAQmE,GAAYG,EACpBrE,EAAUjE,KAAKmI,EAAS7R,YAGxByN,EAAK9L,OAAOoQ,EAAG,E,EAIvB,MAAO,CAACrE,EAASC,EACrB,CAEQsE,GAAcC,GAElB,OAAOA,GAAOA,EAAIzI,WAAsC,mBAAlByI,EAAIzI,SAC9C,CAEQqE,GAAwBN,EAAoBC,EAAaE,GAC7D,MAAMO,EAAe3S,KAAKyP,EAG1B,OAFAzP,KAAKyP,IAEoB,IAArB2C,EAAUzN,OACH,CACH2Q,UAAWpD,EACXS,aAAcA,EAAalO,WAC3B2N,YACAwC,OAAQ3C,EACRvE,KAAMC,EAAYiJ,kBAGf,CACHtB,UAAWpD,EACXS,aAAcA,EAAalO,WAC3BmQ,OAAQ3C,EACRvE,KAAMC,EAAYiJ,iBAG9B,CAEQlE,GAAwBmE,GAC5B,MAAO,CACHlE,aAAckE,EACdnJ,KAAMC,EAAYmJ,iBAE1B,CAEQP,GAAyBM,EAAY9I,GACzC,MAAO,CACH4E,aAAckE,EACd9I,OACAL,KAAMC,EAAYuG,WAE1B,CAEQY,GAAyB+B,EAAY3P,EAAakM,GACtD,OAAIlM,EACO,CACHA,QACAyL,aAAckE,EACdnJ,KAAMC,EAAYoF,YAInB,CACHJ,aAAckE,EACdzD,SACA1F,KAAMC,EAAYoF,WAE1B,EEpiCJ,MAAMgE,EAAuC,CAAC,EAAG,IAAM,IAAO,IAAO,MAG9D,MAAMC,EAGThW,YAAYiW,GACRjX,KAAKkX,QAA+BtN,IAAhBqN,EAA4B,IAAIA,EAAa,MAAQF,CAC7E,CAEOZ,6BAA6BgB,GAChC,OAAOnX,KAAKkX,GAAaC,EAAanB,mBAC1C,ECfG,MAAeoB,GACF,EAAAC,cAAgB,gBAChB,EAAAC,OAAS,SCEtB,MAAMC,UAA8BnV,EAKvCpB,YAAYwW,EAAyBC,GACjCrW,QAEApB,KAAK0X,GAAeF,EACpBxX,KAAK2X,GAAsBF,CAC/B,CAEO3S,WAAWqE,GACd,IAAIyO,GAAa,EACb5X,KAAK2X,MAAyB3X,KAAK6X,IAAiB1O,EAAQ9G,KAAO8G,EAAQ9G,IAAI8D,QAAQ,eAAiB,KAExGyR,GAAa,EACb5X,KAAK6X,SAAqB7X,KAAK2X,MAEnC3X,KAAK8X,GAAwB3O,GAC7B,MAAM3D,QAAiBxF,KAAK0X,GAAanV,KAAK4G,GAE9C,OAAIyO,GAAsC,MAAxBpS,EAAStE,YAAsBlB,KAAK2X,IAClD3X,KAAK6X,SAAqB7X,KAAK2X,KAC/B3X,KAAK8X,GAAwB3O,SAChBnJ,KAAK0X,GAAanV,KAAK4G,IAEjC3D,CACX,CAEQsS,GAAwB3O,GACvBA,EAAQhE,UACTgE,EAAQhE,QAAU,CAAC,GAEnBnF,KAAK6X,GACL1O,EAAQhE,QAAQiS,EAAYC,eAAiB,UAAUrX,KAAK6X,KAGvD7X,KAAK2X,IACNxO,EAAQhE,QAAQiS,EAAYC,uBACrBlO,EAAQhE,QAAQiS,EAAYC,cAG/C,CAEO1U,gBAAgBN,GACnB,OAAOrC,KAAK0X,GAAa/U,gBAAgBN,EAC7C,ECjDJ,IAAY0V,EAYAC,GAZZ,SAAYD,GAER,mBAEA,+BAEA,2CAEA,gCACH,CATD,CAAYA,IAAAA,EAAiB,KAY7B,SAAYC,GAER,mBAEA,sBACH,CALD,CAAYA,IAAAA,EAAc,KCRnB,MAAM,EAAb,cACY,KAAAC,IAAsB,EACvB,KAAA1O,QAA+B,IAkB1C,CAhBWC,QACExJ,KAAKiY,KACNjY,KAAKiY,IAAa,EACdjY,KAAKuJ,SACLvJ,KAAKuJ,UAGjB,CAEIW,aACA,OAAOlK,IACX,CAEIqJ,cACA,OAAOrJ,KAAKiY,EAChB,ECfG,MAAMC,EAmBTlX,YAAYkE,EAAwBF,EAAiB1C,GACjDtC,KAAK6L,EAAc3G,EACnBlF,KAAKyI,EAAUzD,EACfhF,KAAKmY,GAAa,IAAI,EACtBnY,KAAKoY,GAAW9V,EAEhBtC,KAAKqY,IAAW,EAEhBrY,KAAKgP,UAAY,KACjBhP,KAAKkP,QAAU,IACnB,CAdWoJ,kBACP,OAAOtY,KAAKmY,GAAW9O,OAC3B,CAcOvE,cAAczC,EAAa4O,GAU9B,GATA/N,EAAIwL,WAAWrM,EAAK,OACpBa,EAAIwL,WAAWuC,EAAgB,kBAC/B/N,EAAIqV,KAAKtH,EAAgB+G,EAAgB,kBAEzChY,KAAKwY,GAAOnW,EAEZrC,KAAKyI,EAAQ5F,IAAIhC,EAASwE,MAAO,uCAG7B4L,IAAmB+G,EAAeS,QACP,oBAAnBtN,gBAA+E,iBAAtC,IAAIA,gBAAiB5F,aACtE,MAAM,IAAIxE,MAAM,8FAGpB,MAAOsC,EAAMzC,GAASwE,IAChBD,EAAU,CAAE,CAAC9B,GAAOzC,KAAUZ,KAAKoY,GAASjT,SAE5CuT,EAA2B,CAC7BtP,YAAapJ,KAAKmY,GAAWjO,OAC7B/E,UACAM,QAAS,IACTC,gBAAiB1F,KAAKoY,GAAS1S,iBAG/BuL,IAAmB+G,EAAeS,SAClCC,EAAYnT,aAAe,eAK/B,MAAMoT,EAAU,GAAGtW,OAAS0E,KAAK4O,QACjC3V,KAAKyI,EAAQ5F,IAAIhC,EAASwE,MAAO,oCAAoCsT,MACrE,MAAMnT,QAAiBxF,KAAK6L,EAAYhM,IAAI8Y,EAASD,GACzB,MAAxBlT,EAAStE,YACTlB,KAAKyI,EAAQ5F,IAAIhC,EAASE,MAAO,qDAAqDyE,EAAStE,eAG/FlB,KAAK4Y,GAAc,IAAI9X,EAAU0E,EAAStD,YAAc,GAAIsD,EAAStE,YACrElB,KAAKqY,IAAW,GAEhBrY,KAAKqY,IAAW,EAGpBrY,KAAK6Y,GAAa7Y,KAAK8Y,GAAM9Y,KAAKwY,GAAME,EAC5C,CAEQ5T,SAAYzC,EAAaqW,GAC7B,IACI,KAAO1Y,KAAKqY,IACR,IACI,MAAMM,EAAU,GAAGtW,OAAS0E,KAAK4O,QACjC3V,KAAKyI,EAAQ5F,IAAIhC,EAASwE,MAAO,oCAAoCsT,MACrE,MAAMnT,QAAiBxF,KAAK6L,EAAYhM,IAAI8Y,EAASD,GAEzB,MAAxBlT,EAAStE,YACTlB,KAAKyI,EAAQ5F,IAAIhC,EAASwG,YAAa,sDAEvCrH,KAAKqY,IAAW,GACe,MAAxB7S,EAAStE,YAChBlB,KAAKyI,EAAQ5F,IAAIhC,EAASE,MAAO,qDAAqDyE,EAAStE,eAG/FlB,KAAK4Y,GAAc,IAAI9X,EAAU0E,EAAStD,YAAc,GAAIsD,EAAStE,YACrElB,KAAKqY,IAAW,GAGZ7S,EAASrD,SACTnC,KAAKyI,EAAQ5F,IAAIhC,EAASwE,MAAO,0CAA0CvB,EAAc0B,EAASrD,QAASnC,KAAKoY,GAAS9S,uBACrHtF,KAAKgP,WACLhP,KAAKgP,UAAUxJ,EAASrD,UAI5BnC,KAAKyI,EAAQ5F,IAAIhC,EAASwE,MAAO,qD,CAG3C,MAAOnF,GACAF,KAAKqY,GAIFnY,aAAaoB,EAEbtB,KAAKyI,EAAQ5F,IAAIhC,EAASwE,MAAO,uDAGjCrF,KAAK4Y,GAAc1Y,EACnBF,KAAKqY,IAAW,GARpBrY,KAAKyI,EAAQ5F,IAAIhC,EAASwE,MAAO,wDAAwDnF,EAAEuB,U,UAcvGzB,KAAKyI,EAAQ5F,IAAIhC,EAASwE,MAAO,6CAI5BrF,KAAKsY,aACNtY,KAAK+Y,I,CAGjB,CAEOjU,WAAWf,GACd,OAAK/D,KAAKqY,GAGHtT,EAAY/E,KAAKyI,EAAS,cAAezI,KAAK6L,EAAa7L,KAAKwY,GAAOzU,EAAM/D,KAAKoY,IAF9ErN,QAAQC,OAAO,IAAIjK,MAAM,gDAGxC,CAEO+D,aACH9E,KAAKyI,EAAQ5F,IAAIhC,EAASwE,MAAO,6CAGjCrF,KAAKqY,IAAW,EAChBrY,KAAKmY,GAAW3O,QAEhB,UACUxJ,KAAK6Y,GAGX7Y,KAAKyI,EAAQ5F,IAAIhC,EAASwE,MAAO,qDAAqDrF,KAAKwY,OAE3F,MAAMrT,EAAiC,CAAC,GACjC9B,EAAMzC,GAASwE,IACtBD,EAAQ9B,GAAQzC,EAEhB,MAAMoY,EAA6B,CAC/B7T,QAAS,IAAKA,KAAYnF,KAAKoY,GAASjT,SACxCM,QAASzF,KAAKoY,GAAS3S,QACvBC,gBAAiB1F,KAAKoY,GAAS1S,uBAE7B1F,KAAK6L,EAAYnJ,OAAO1C,KAAKwY,GAAOQ,GAE1ChZ,KAAKyI,EAAQ5F,IAAIhC,EAASwE,MAAO,+C,SAEjCrF,KAAKyI,EAAQ5F,IAAIhC,EAASwE,MAAO,0CAIjCrF,KAAK+Y,I,CAEb,CAEQA,KACJ,GAAI/Y,KAAKkP,QAAS,CACd,IAAI+J,EAAa,gDACbjZ,KAAK4Y,KACLK,GAAc,WAAajZ,KAAK4Y,IAEpC5Y,KAAKyI,EAAQ5F,IAAIhC,EAASwE,MAAO4T,GACjCjZ,KAAKkP,QAAQlP,KAAK4Y,G,CAE1B,EC3LG,MAAMM,EAWTlY,YAAYkE,EAAwBiU,EAAiCnU,EACzD1C,GACRtC,KAAK6L,EAAc3G,EACnBlF,KAAK6X,GAAesB,EACpBnZ,KAAKyI,EAAUzD,EACfhF,KAAKoY,GAAW9V,EAEhBtC,KAAKgP,UAAY,KACjBhP,KAAKkP,QAAU,IACnB,CAEOpK,cAAczC,EAAa4O,GAc9B,OAbA/N,EAAIwL,WAAWrM,EAAK,OACpBa,EAAIwL,WAAWuC,EAAgB,kBAC/B/N,EAAIqV,KAAKtH,EAAgB+G,EAAgB,kBAEzChY,KAAKyI,EAAQ5F,IAAIhC,EAASwE,MAAO,+BAGjCrF,KAAKwY,GAAOnW,EAERrC,KAAK6X,KACLxV,IAAQA,EAAI8D,QAAQ,KAAO,EAAI,IAAM,KAAO,gBAAgBiT,mBAAmBpZ,KAAK6X,OAGjF,IAAI9M,SAAc,CAACE,EAASD,KAC/B,IAMIqO,EANAC,GAAS,EACb,GAAIrI,IAAmB+G,EAAeuB,KAAtC,CAMA,GAAI/V,EAASC,WAAaD,EAASG,YAC/B0V,EAAc,IAAIrZ,KAAKoY,GAASoB,YAAanX,EAAK,CAAEqD,gBAAiB1F,KAAKoY,GAAS1S,sBAChF,CAEH,MAAM8E,EAAUxK,KAAK6L,EAAYlJ,gBAAgBN,GAC3C8C,EAA0B,CAAC,EACjCA,EAAQmS,OAAS9M,EACjB,MAAOnH,EAAMzC,GAASwE,IACtBD,EAAQ9B,GAAQzC,EAEhByY,EAAc,IAAIrZ,KAAKoY,GAASoB,YAAanX,EAAK,CAAEqD,gBAAiB1F,KAAKoY,GAAS1S,gBAAiBP,QAAS,IAAKA,KAAYnF,KAAKoY,GAASjT,U,CAGhJ,IACIkU,EAAYI,UAAavZ,IACrB,GAAIF,KAAKgP,UACL,IACIhP,KAAKyI,EAAQ5F,IAAIhC,EAASwE,MAAO,kCAAkCvB,EAAc5D,EAAE6D,KAAM/D,KAAKoY,GAAS9S,uBACvGtF,KAAKgP,UAAU9O,EAAE6D,K,CACnB,MAAOmD,GAEL,YADAlH,KAAK0Z,GAAOxS,E,GAOxBmS,EAAY3N,QAAWxL,IAEfoZ,EACAtZ,KAAK0Z,KAEL1O,EAAO,IAAIjK,MAAM,gQ,EAMzBsY,EAAYM,OAAS,KACjB3Z,KAAKyI,EAAQ5F,IAAIhC,EAASwG,YAAa,oBAAoBrH,KAAKwY,MAChExY,KAAK4Z,GAAeP,EACpBC,GAAS,EACTrO,GAAS,C,CAEf,MAAO/K,GAEL,YADA8K,EAAO9K,E,OAlDP8K,EAAO,IAAIjK,MAAM,6E,GAsD7B,CAEO+D,WAAWf,GACd,OAAK/D,KAAK4Z,GAGH7U,EAAY/E,KAAKyI,EAAS,MAAOzI,KAAK6L,EAAa7L,KAAKwY,GAAOzU,EAAM/D,KAAKoY,IAFtErN,QAAQC,OAAO,IAAIjK,MAAM,gDAGxC,CAEO0Q,OAEH,OADAzR,KAAK0Z,KACE3O,QAAQE,SACnB,CAEQyO,GAAOxZ,GACPF,KAAK4Z,KACL5Z,KAAK4Z,GAAaC,QAClB7Z,KAAK4Z,QAAehQ,EAEhB5J,KAAKkP,SACLlP,KAAKkP,QAAQhP,GAGzB,ECnHG,MAAM4Z,EAYT9Y,YAAYkE,EAAwBuS,EAAkEzS,EAC1FM,EAA4ByU,EAA4C5U,GAChFnF,KAAKyI,EAAUzD,EACfhF,KAAK2X,GAAsBF,EAC3BzX,KAAKga,GAAqB1U,EAC1BtF,KAAKia,GAAwBF,EAC7B/Z,KAAK6L,EAAc3G,EAEnBlF,KAAKgP,UAAY,KACjBhP,KAAKkP,QAAU,KACflP,KAAKka,GAAW/U,CACpB,CAEOL,cAAczC,EAAa4O,GAM9B,IAAIkJ,EAKJ,OAVAjX,EAAIwL,WAAWrM,EAAK,OACpBa,EAAIwL,WAAWuC,EAAgB,kBAC/B/N,EAAIqV,KAAKtH,EAAgB+G,EAAgB,kBACzChY,KAAKyI,EAAQ5F,IAAIhC,EAASwE,MAAO,sCAG7BrF,KAAK2X,KACLwC,QAAcna,KAAK2X,MAGhB,IAAI5M,SAAc,CAACE,EAASD,KAE/B,IAAIoP,EADJ/X,EAAMA,EAAIgY,QAAQ,QAAS,MAE3B,MAAM7P,EAAUxK,KAAK6L,EAAYlJ,gBAAgBN,GACjD,IAAIiX,GAAS,EAEb,GAAI9V,EAASK,QAAUL,EAASI,cAAe,CAC3C,MAAMuB,EAAiC,CAAC,GACjC9B,EAAMzC,GAASwE,IACtBD,EAAQ9B,GAAQzC,EACZuZ,IACAhV,EAAQiS,EAAYC,eAAiB,UAAU8C,KAG/C3P,IACArF,EAAQiS,EAAYE,QAAU9M,GAIlC4P,EAAY,IAAIpa,KAAKia,GAAsB5X,OAAKuH,EAAW,CACvDzE,QAAS,IAAKA,KAAYnF,KAAKka,K,MAK/BC,IACA9X,IAAQA,EAAI8D,QAAQ,KAAO,EAAI,IAAM,KAAO,gBAAgBiT,mBAAmBe,MAIlFC,IAEDA,EAAY,IAAIpa,KAAKia,GAAsB5X,IAG3C4O,IAAmB+G,EAAeS,SAClC2B,EAAUE,WAAa,eAG3BF,EAAUT,OAAUY,IAChBva,KAAKyI,EAAQ5F,IAAIhC,EAASwG,YAAa,0BAA0BhF,MACjErC,KAAKwa,GAAaJ,EAClBd,GAAS,EACTrO,GAAS,EAGbmP,EAAU1O,QAAW+O,IACjB,IAAIvT,EAAa,KAGbA,EADsB,oBAAfwT,YAA8BD,aAAiBC,WAC9CD,EAAMvT,MAEN,wCAGZlH,KAAKyI,EAAQ5F,IAAIhC,EAASwG,YAAa,0BAA0BH,KAAS,EAG9EkT,EAAUX,UAAahY,IAEnB,GADAzB,KAAKyI,EAAQ5F,IAAIhC,EAASwE,MAAO,yCAAyCvB,EAAcrC,EAAQsC,KAAM/D,KAAKga,QACvGha,KAAKgP,UACL,IACIhP,KAAKgP,UAAUvN,EAAQsC,K,CACzB,MAAOmD,GAEL,YADAlH,KAAK0Z,GAAOxS,E,GAMxBkT,EAAUlL,QAAWuL,IAGjB,GAAInB,EACAtZ,KAAK0Z,GAAOe,OACT,CACH,IAAIvT,EAAa,KAGbA,EADsB,oBAAfwT,YAA8BD,aAAiBC,WAC9CD,EAAMvT,MAEN,iSAMZ8D,EAAO,IAAIjK,MAAMmG,G,EAExB,GAET,CAEO3E,KAAKwB,GACR,OAAI/D,KAAKwa,IAAcxa,KAAKwa,GAAWG,aAAe3a,KAAKia,GAAsBW,MAC7E5a,KAAKyI,EAAQ5F,IAAIhC,EAASwE,MAAO,wCAAwCvB,EAAcC,EAAM/D,KAAKga,QAClGha,KAAKwa,GAAWjY,KAAKwB,GACdgH,QAAQE,WAGZF,QAAQC,OAAO,qCAC1B,CAEOyG,OAOH,OANIzR,KAAKwa,IAGLxa,KAAK0Z,QAAO9P,GAGTmB,QAAQE,SACnB,CAEQyO,GAAOe,GAEPza,KAAKwa,KAELxa,KAAKwa,GAAWtL,QAAU,OAC1BlP,KAAKwa,GAAWf,UAAY,OAC5BzZ,KAAKwa,GAAW9O,QAAU,OAC1B1L,KAAKwa,GAAWX,QAChB7Z,KAAKwa,QAAa5Q,GAGtB5J,KAAKyI,EAAQ5F,IAAIhC,EAASwE,MAAO,yCAC7BrF,KAAKkP,WACDlP,KAAK6a,GAAcJ,KAA8B,IAAnBA,EAAMK,UAAqC,MAAfL,EAAMM,KAEzDN,aAAiB1Z,MACxBf,KAAKkP,QAAQuL,GAEbza,KAAKkP,UAJLlP,KAAKkP,QAAQ,IAAInO,MAAM,sCAAsC0Z,EAAMM,SAASN,EAAMO,QAAU,wBAOxG,CAEQH,GAAcJ,GAClB,OAAOA,GAAmC,kBAAnBA,EAAMK,UAAgD,iBAAfL,EAAMM,IACxE,EC/IG,MAAME,EA0BTja,YAAYqB,EAAaC,EAAkC,CAAC,GjBsDzD,IAAsB0C,EiB9CrB,GArBI,KAAAkW,GAA4D,OAKpD,KAAA5J,SAAgB,CAAC,EAMhB,KAAA6J,GAA4B,EAGzCjY,EAAIwL,WAAWrM,EAAK,OAEpBrC,KAAKyI,OjBoDMmB,KADU5E,EiBnDO1C,EAAQ0C,QjBqD7B,IAAIwB,EAAc3F,EAASwG,aAGvB,OAAXrC,EACOpC,EAAWI,cAGU4G,IAA3B5E,EAAmBnC,IACbmC,EAGJ,IAAIwB,EAAcxB,GiB/DrBhF,KAAKmQ,QAAUnQ,KAAKob,GAAY/Y,IAEhCC,EAAUA,GAAW,CAAC,GACdgD,uBAAkDsE,IAA9BtH,EAAQgD,mBAA0ChD,EAAQgD,kBAC/C,kBAA5BhD,EAAQoD,sBAA6DkE,IAA5BtH,EAAQoD,gBAGxD,MAAM,IAAI3E,MAAM,mEAFhBuB,EAAQoD,qBAA8CkE,IAA5BtH,EAAQoD,iBAAuCpD,EAAQoD,gBAIrFpD,EAAQmD,aAA8BmE,IAApBtH,EAAQmD,QAAwB,IAAanD,EAAQmD,QAEvE,IAAI4V,EAAuB,KACvBC,EAAyB,KAE7B,GAAI9X,EAASK,OAA0C,CAGnD,MAAM8E,EAA0D,QAChE0S,EAAkB1S,EAAY,MAC9B2S,EAAoB3S,EAAY,c,CAG/BnF,EAASK,QAA+B,oBAAd0X,WAA8BjZ,EAAQiZ,UAE1D/X,EAASK,SAAWvB,EAAQiZ,WAC/BF,IACA/Y,EAAQiZ,UAAYF,GAHxB/Y,EAAQiZ,UAAYA,UAOnB/X,EAASK,QAAiC,oBAAhB2V,aAAgClX,EAAQkX,YAE5DhW,EAASK,SAAWvB,EAAQkX,kBACF,IAAtB8B,IACPhZ,EAAQkX,YAAc8B,GAH1BhZ,EAAQkX,YAAcA,YAO1BxZ,KAAK6L,EAAc,IAAI0L,EAAsBjV,EAAQ4C,YAAc,IAAI0G,EAAkB5L,KAAKyI,GAAUnG,EAAQmV,oBAChHzX,KAAK2P,EAAmB,eACxB3P,KAAK6P,GAAqB,EAC1B7P,KAAKoY,GAAW9V,EAEhBtC,KAAKgP,UAAY,KACjBhP,KAAKkP,QAAU,IACnB,CAIOpK,YAAYmM,GAOf,GANAA,EAAiBA,GAAkB+G,EAAeS,OAElDvV,EAAIqV,KAAKtH,EAAgB+G,EAAgB,kBAEzChY,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,6CAA6CuH,EAAe/G,QAE/D,iBAA1BjR,KAAK2P,EACL,OAAO5E,QAAQC,OAAO,IAAIjK,MAAM,4EASpC,GANAf,KAAK2P,EAAmB,aAExB3P,KAAKwb,GAAwBxb,KAAK0Q,EAAeO,SAC3CjR,KAAKwb,GAG0B,kBAAjCxb,KAAK2P,EAA2D,CAEhE,MAAMlO,EAAU,+DAMhB,OALAzB,KAAKyI,EAAQ5F,IAAIhC,EAASE,MAAOU,SAG3BzB,KAAK2R,GAEJ5G,QAAQC,OAAO,IAAIzJ,EAAWE,G,CAClC,GAAqC,cAAjCzB,KAAK2P,EAAuD,CAEnE,MAAMlO,EAAU,8GAEhB,OADAzB,KAAKyI,EAAQ5F,IAAIhC,EAASE,MAAOU,GAC1BsJ,QAAQC,OAAO,IAAIzJ,EAAWE,G,CAGzCzB,KAAK6P,GAAqB,CAC9B,CAEOtN,KAAKwB,GACR,MAA8B,cAA1B/D,KAAK2P,EACE5E,QAAQC,OAAO,IAAIjK,MAAM,yEAG/Bf,KAAKyb,KACNzb,KAAKyb,GAAa,IAAIC,EAAmB1b,KAAK0B,YAI3C1B,KAAKyb,GAAWlZ,KAAKwB,GAChC,CAEOe,WAAWoC,GACd,MAA8B,iBAA1BlH,KAAK2P,GACL3P,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,+BAA+BvJ,2EACzD6D,QAAQE,WAGW,kBAA1BjL,KAAK2P,GACL3P,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,+BAA+BvJ,4EACzDlH,KAAK2R,KAGhB3R,KAAK2P,EAAmB,gBAExB3P,KAAK2R,GAAe,IAAI5G,SAASE,IAE7BjL,KAAKkb,GAAuBjQ,CAAO,UAIjCjL,KAAK4R,GAAc1K,cACnBlH,KAAK2R,GACf,CAEQ7M,SAAoBoC,GAIxBlH,KAAK2b,GAAazU,EAElB,UACUlH,KAAKwb,E,CACb,MAAOtb,G,CAOT,GAAIF,KAAK0B,UAAW,CAChB,UACU1B,KAAK0B,UAAU+P,M,CACvB,MAAOvR,GACLF,KAAKyI,EAAQ5F,IAAIhC,EAASE,MAAO,gDAAgDb,OACjFF,KAAK4b,I,CAGT5b,KAAK0B,eAAYkI,C,MAEjB5J,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,yFAEzC,CAEQ3L,QAAqBmM,GAGzB,IAAI5O,EAAMrC,KAAKmQ,QACfnQ,KAAK2X,GAAsB3X,KAAKoY,GAASX,mBACzCzX,KAAK6L,EAAY8L,GAAsB3X,KAAK2X,GAE5C,IACI,GAAI3X,KAAKoY,GAASyD,gBAAiB,CAC/B,GAAI7b,KAAKoY,GAAS1W,YAAcqW,EAAkB+D,WAO9C,MAAM,IAAI/a,MAAM,gFALhBf,KAAK0B,UAAY1B,KAAK+b,GAAoBhE,EAAkB+D,kBAGtD9b,KAAKgc,GAAgB3Z,EAAK4O,E,KAIjC,CACH,IAAIgL,EAA+C,KAC/CC,EAAY,EAEhB,EAAG,CAGC,GAFAD,QAA0Bjc,KAAKmc,GAAwB9Z,GAEzB,kBAA1BrC,KAAK2P,GAAgF,iBAA1B3P,KAAK2P,EAChE,MAAM,IAAIpO,EAAW,kDAGzB,GAAI0a,EAAkB/U,MAClB,MAAM,IAAInG,MAAMkb,EAAkB/U,OAGtC,GAAK+U,EAA0BG,gBAC3B,MAAM,IAAIrb,MAAM,gMAOpB,GAJIkb,EAAkB5Z,MAClBA,EAAM4Z,EAAkB5Z,KAGxB4Z,EAAkB9C,YAAa,CAG/B,MAAMA,EAAc8C,EAAkB9C,YACtCnZ,KAAK2X,GAAsB,IAAMwB,EAEjCnZ,KAAK6L,EAAYgM,GAAesB,EAChCnZ,KAAK6L,EAAY8L,QAAsB/N,C,CAG3CsS,G,OAEGD,EAAkB5Z,KAAO6Z,EA5O1B,KA8ON,GA9OM,MA8OFA,GAA+BD,EAAkB5Z,IACjD,MAAM,IAAItB,MAAM,+CAGdf,KAAKqc,GAAiBha,EAAKrC,KAAKoY,GAAS1W,UAAWua,EAAmBhL,E,CAG7EjR,KAAK0B,qBAAqBwW,IAC1BlY,KAAKsR,SAASC,mBAAoB,GAGR,eAA1BvR,KAAK2P,IAGL3P,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,8CACjCzQ,KAAK2P,EAAmB,Y,CAM9B,MAAOzP,GAOL,OANAF,KAAKyI,EAAQ5F,IAAIhC,EAASE,MAAO,mCAAqCb,GACtEF,KAAK2P,EAAmB,eACxB3P,KAAK0B,eAAYkI,EAGjB5J,KAAKkb,KACEnQ,QAAQC,OAAO9K,E,CAE9B,CAEQ4E,SAA8BzC,GAClC,MAAM8C,EAAiC,CAAC,GACjC9B,EAAMzC,GAASwE,IACtBD,EAAQ9B,GAAQzC,EAEhB,MAAM0b,EAAetc,KAAKuc,GAAqBla,GAC/CrC,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,gCAAgC6L,MACjE,IACI,MAAM9W,QAAiBxF,KAAK6L,EAAYpJ,KAAK6Z,EAAc,CACvDna,QAAS,GACTgD,QAAS,IAAKA,KAAYnF,KAAKoY,GAASjT,SACxCM,QAASzF,KAAKoY,GAAS3S,QACvBC,gBAAiB1F,KAAKoY,GAAS1S,kBAGnC,GAA4B,MAAxBF,EAAStE,WACT,OAAO6J,QAAQC,OAAO,IAAIjK,MAAM,mDAAmDyE,EAAStE,gBAGhG,MAAM+a,EAAoBtP,KAAKc,MAAMjI,EAASrD,SAM9C,QALK8Z,EAAkBO,kBAAoBP,EAAkBO,iBAAmB,KAG5EP,EAAkBQ,gBAAkBR,EAAkB/L,cAEnD+L,C,CACT,MAAO/b,GACL,IAAIe,EAAe,mDAAqDf,EAQxE,OAPIA,aAAaY,GACQ,MAAjBZ,EAAEgB,aACFD,GAA8B,uFAGtCjB,KAAKyI,EAAQ5F,IAAIhC,EAASE,MAAOE,GAE1B8J,QAAQC,OAAO,IAAIlJ,EAAiCb,G,CAEnE,CAEQyb,GAAkBra,EAAaoa,GACnC,OAAKA,EAIEpa,IAA6B,IAAtBA,EAAI8D,QAAQ,KAAc,IAAM,KAAO,MAAMsW,IAHhDpa,CAIf,CAEQyC,SAAuBzC,EAAasa,EAAgEV,EAAuCW,GAC/I,IAAIC,EAAa7c,KAAK0c,GAAkBra,EAAK4Z,EAAkBQ,iBAC/D,GAAIzc,KAAK8c,GAAcH,GAMnB,OALA3c,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,2EACjCzQ,KAAK0B,UAAYib,QACX3c,KAAKgc,GAAgBa,EAAYD,QAEvC5c,KAAKkQ,aAAe+L,EAAkB/L,cAI1C,MAAM6M,EAA6B,GAC7BC,EAAaf,EAAkBgB,qBAAuB,GAC5D,IAAIC,EAA4CjB,EAChD,IAAK,MAAMkB,KAAYH,EAAY,CAC/B,MAAMI,EAAmBpd,KAAKqd,GAAyBF,EAAUR,EAAoBC,GACrF,GAAIQ,aAA4Brc,MAE5Bgc,EAAoB5O,KAAK,GAAGgP,EAASzb,qBACrCqb,EAAoB5O,KAAKiP,QACtB,GAAIpd,KAAK8c,GAAcM,GAAmB,CAE7C,GADApd,KAAK0B,UAAY0b,GACZF,EAAW,CACZ,IACIA,QAAkBld,KAAKmc,GAAwB9Z,E,CACjD,MAAOib,GACL,OAAOvS,QAAQC,OAAOsS,E,CAE1BT,EAAa7c,KAAK0c,GAAkBra,EAAK6a,EAAUT,gB,CAEvD,IAGI,aAFMzc,KAAKgc,GAAgBa,EAAYD,QACvC5c,KAAKkQ,aAAegN,EAAUhN,a,CAEhC,MAAOoN,GAKL,GAJAtd,KAAKyI,EAAQ5F,IAAIhC,EAASE,MAAO,kCAAkCoc,EAASzb,eAAe4b,KAC3FJ,OAAYtT,EACZmT,EAAoB5O,KAAK,IAAItM,EAA4B,GAAGsb,EAASzb,qBAAqB4b,IAAMvF,EAAkBoF,EAASzb,aAE7F,eAA1B1B,KAAK2P,EAAiD,CACtD,MAAMlO,EAAU,uDAEhB,OADAzB,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAOhP,GAC1BsJ,QAAQC,OAAO,IAAIzJ,EAAWE,G,IAMrD,OAAIsb,EAAoBpY,OAAS,EACtBoG,QAAQC,OAAO,IAAIjJ,EAAgB,yEAAyEgb,EAAoBpS,KAAK,OAAQoS,IAEjJhS,QAAQC,OAAO,IAAIjK,MAAM,+EACpC,CAEQgb,GAAoBra,GACxB,OAAQA,GACJ,KAAKqW,EAAkB+D,WACnB,IAAK9b,KAAKoY,GAASmD,UACf,MAAM,IAAIxa,MAAM,qDAEpB,OAAO,IAAI+Y,EAAmB9Z,KAAK6L,EAAa7L,KAAK2X,GAAqB3X,KAAKyI,EAASzI,KAAKoY,GAAS9S,kBAAoBtF,KAAKoY,GAASmD,UAAWvb,KAAKoY,GAASjT,SAAW,CAAC,GACjL,KAAK4S,EAAkBwF,iBACnB,IAAKvd,KAAKoY,GAASoB,YACf,MAAM,IAAIzY,MAAM,uDAEpB,OAAO,IAAImY,EAA0BlZ,KAAK6L,EAAa7L,KAAK6L,EAAYgM,GAAc7X,KAAKyI,EAASzI,KAAKoY,IAC7G,KAAKL,EAAkByF,YACnB,OAAO,IAAItF,EAAqBlY,KAAK6L,EAAa7L,KAAKyI,EAASzI,KAAKoY,IACzE,QACI,MAAM,IAAIrX,MAAM,sBAAsBW,MAElD,CAEQsa,GAAgB3Z,EAAa4O,GAGjC,OAFAjR,KAAK0B,UAAWsN,UAAYhP,KAAKgP,UACjChP,KAAK0B,UAAWwN,QAAWhP,GAAMF,KAAK4b,GAAgB1b,GAC/CF,KAAK0B,UAAW+b,QAAQpb,EAAK4O,EACxC,CAEQoM,GAAyBF,EAA+BR,EAAmDC,GAC/G,MAAMlb,EAAYqW,EAAkBoF,EAASzb,WAC7C,GAAIA,QAEA,OADA1B,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,uBAAuB0M,EAASzb,0DAC1D,IAAIX,MAAM,uBAAuBoc,EAASzb,0DAEjD,IA0HZ,SAA0Bib,EAAmDe,GACzE,OAAQf,GAAkE,IAA1Ce,EAAkBf,EACtD,CA5HgBgB,CAAiBhB,EAAoBjb,GAqBrC,OADA1B,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,uBAAuBsH,EAAkBrW,8CACnE,IAAIE,EAAuB,IAAImW,EAAkBrW,iCAA0CA,GAnBlG,KADwByb,EAASS,gBAAgBC,KAAKC,GAAM9F,EAAe8F,KACvD3X,QAAQyW,IAA4B,GAepD,OADA5c,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,uBAAuBsH,EAAkBrW,kEAA0EsW,EAAe4E,QAC5J,IAAI7b,MAAM,IAAIgX,EAAkBrW,wBAAgCsW,EAAe4E,OAdtF,GAAKlb,IAAcqW,EAAkB+D,aAAe9b,KAAKoY,GAASmD,WAC7D7Z,IAAcqW,EAAkBwF,mBAAqBvd,KAAKoY,GAASoB,YAEpE,OADAxZ,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,uBAAuBsH,EAAkBrW,yDACnE,IAAIF,EAA0B,IAAIuW,EAAkBrW,4CAAqDA,GAEhH1B,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,wBAAwBsH,EAAkBrW,QAC3E,IACI,OAAO1B,KAAK+b,GAAoBra,E,CAClC,MAAO4b,GACL,OAAOA,C,CAY/B,CAEQR,GAAcpb,GAClB,OAAOA,GAAoC,iBAAhB,GAA4B,YAAaA,CACxE,CAEQka,GAAgB1U,GASpB,GARAlH,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,iCAAiCvJ,4BAAgClH,KAAK2P,MAEvG3P,KAAK0B,eAAYkI,EAGjB1C,EAAQlH,KAAK2b,IAAczU,EAC3BlH,KAAK2b,QAAa/R,EAEY,iBAA1B5J,KAAK2P,EAAT,CAKA,GAA8B,eAA1B3P,KAAK2P,EAEL,MADA3P,KAAKyI,EAAQ5F,IAAIhC,EAASsG,QAAS,yCAAyCD,2EACtE,IAAInG,MAAM,iCAAiCmG,wEAyBrD,GAtB8B,kBAA1BlH,KAAK2P,GAGL3P,KAAKkb,KAGLhU,EACAlH,KAAKyI,EAAQ5F,IAAIhC,EAASE,MAAO,uCAAuCmG,OAExElH,KAAKyI,EAAQ5F,IAAIhC,EAASwG,YAAa,4BAGvCrH,KAAKyb,KACLzb,KAAKyb,GAAWhK,OAAOnL,OAAOpG,IAC1BF,KAAKyI,EAAQ5F,IAAIhC,EAASE,MAAO,0CAA0Cb,MAAM,IAErFF,KAAKyb,QAAa7R,GAGtB5J,KAAKkQ,kBAAetG,EACpB5J,KAAK2P,EAAmB,eAEpB3P,KAAK6P,EAAoB,CACzB7P,KAAK6P,GAAqB,EAC1B,IACQ7P,KAAKkP,SACLlP,KAAKkP,QAAQhI,E,CAEnB,MAAOhH,GACLF,KAAKyI,EAAQ5F,IAAIhC,EAASE,MAAO,0BAA0BmG,mBAAuBhH,M,QAtCtFF,KAAKyI,EAAQ5F,IAAIhC,EAAS4P,MAAO,yCAAyCvJ,8EAyClF,CAEQkU,GAAY/Y,GAEhB,GAAuC,IAAnCA,EAAI0b,YAAY,WAAY,IAA8C,IAAlC1b,EAAI0b,YAAY,UAAW,GACnE,OAAO1b,EAGX,IAAKmB,EAASC,UACV,MAAM,IAAI1C,MAAM,mBAAmBsB,OAQvC,MAAM2b,EAAO7d,OAAOuD,SAASua,cAAc,KAI3C,OAHAD,EAAKE,KAAO7b,EAEZrC,KAAKyI,EAAQ5F,IAAIhC,EAASwG,YAAa,gBAAgBhF,UAAY2b,EAAKE,UACjEF,EAAKE,IAChB,CAEQ3B,GAAqBla,GACzB,MAAM4D,EAAQ5D,EAAI8D,QAAQ,KAC1B,IAAImW,EAAeja,EAAImL,UAAU,GAAc,IAAXvH,EAAe5D,EAAIsC,OAASsB,GAWhE,MAV8C,MAA1CqW,EAAaA,EAAa3X,OAAS,KACnC2X,GAAgB,KAEpBA,GAAgB,YAChBA,IAA2B,IAAXrW,EAAe,GAAK5D,EAAImL,UAAUvH,IAEA,IAA9CqW,EAAanW,QAAQ,sBACrBmW,IAA2B,IAAXrW,EAAe,IAAM,IACrCqW,GAAgB,oBAAsBtc,KAAKmb,IAExCmB,CACX,EAQG,MAAMZ,EAOT1a,YAA6Bmd,GAAA,KAAAA,GAAAA,EANrB,KAAAC,GAAiB,GAEjB,KAAAC,IAAsB,EAK1Bre,KAAKse,GAAoB,IAAIC,EAC7Bve,KAAKwe,GAAmB,IAAID,EAE5Bve,KAAKye,GAAmBze,KAAK0e,IACjC,CAEOnc,KAAKwB,GAKR,OAJA/D,KAAK2e,GAAY5a,GACZ/D,KAAKwe,KACNxe,KAAKwe,GAAmB,IAAID,GAEzBve,KAAKwe,GAAiBI,OACjC,CAEOnN,OAGH,OAFAzR,KAAKqe,IAAa,EAClBre,KAAKse,GAAkBrT,UAChBjL,KAAKye,EAChB,CAEQE,GAAY5a,GAChB,GAAI/D,KAAKoe,GAAQzZ,eAAiB3E,KAAKoe,GAAQ,WAAc,EACzD,MAAM,IAAIrd,MAAM,sCAAsCf,KAAY,6BAA2B,KAGjGA,KAAKoe,GAAQjQ,KAAKpK,GAClB/D,KAAKse,GAAkBrT,SAC3B,CAEQnG,WACJ,OAAa,CAGT,SAFM9E,KAAKse,GAAkBM,SAExB5e,KAAKqe,GAAY,CACdre,KAAKwe,IACLxe,KAAKwe,GAAiBxT,OAAO,uBAGjC,K,CAGJhL,KAAKse,GAAoB,IAAIC,EAE7B,MAAMM,EAAkB7e,KAAKwe,GAC7Bxe,KAAKwe,QAAmB5U,EAExB,MAAM7F,EAAmC,iBAArB/D,KAAKoe,GAAQ,GAC7Bpe,KAAKoe,GAAQzT,KAAK,IAClB+Q,EAAmBoD,GAAe9e,KAAKoe,IAE3Cpe,KAAKoe,GAAQzZ,OAAS,EAEtB,UACU3E,KAAKme,GAAW5b,KAAKwB,GAC3B8a,EAAgB5T,S,CAClB,MAAO/D,GACL2X,EAAgB7T,OAAO9D,E,EAGnC,CAEQ/D,UAAsB4b,GAC1B,MAAMC,EAAcD,EAAalB,KAAKoB,GAAMA,EAAE9a,aAAY+a,QAAO,CAACC,EAAGF,IAAME,EAAIF,IACzE7L,EAAS,IAAI/O,WAAW2a,GAC9B,IAAII,EAAS,EACb,IAAK,MAAMrR,KAAQgR,EACf3L,EAAOiM,IAAI,IAAIhb,WAAW0J,GAAOqR,GACjCA,GAAUrR,EAAK5J,WAGnB,OAAOiP,EAAO9F,MAClB,EAGJ,MAAMiR,EAKFvd,cACIhB,KAAK4e,QAAU,IAAI7T,SAAQ,CAACE,EAASD,KAAYhL,KAAKsf,GAAWtf,KAAKuf,IAAa,CAACtU,EAASD,IACjG,CAEOC,UACHjL,KAAKsf,IACT,CAEOtU,OAAOgQ,GACVhb,KAAKuf,GAAWvE,EACpB,EC9oBG,MAAMwE,EAAb,cAGoB,KAAAnc,KANmB,OAQnB,KAAAsE,QAAkB,EAGlB,KAAAsJ,eAAiC+G,EAAeuB,IAmGpE,CA5FWxF,cAAc9H,EAAejH,GAEhC,GAAqB,iBAAViH,EACP,MAAM,IAAIlL,MAAM,2DAGpB,IAAKkL,EACD,MAAO,GAGI,OAAXjH,IACAA,EAASpC,EAAWI,UAIxB,MAAMkJ,EAAWJ,EAAkB2B,MAAMxB,GAEnCwT,EAAc,GACpB,IAAK,MAAMhe,KAAWyK,EAAU,CAC5B,MAAMwT,EAAgB/S,KAAKc,MAAMhM,GACjC,GAAkC,iBAAvBie,EAAchS,KACrB,MAAM,IAAI3M,MAAM,oBAEpB,OAAQ2e,EAAchS,MAClB,KAAKC,EAAYqG,WACbhU,KAAK2f,GAAqBD,GAC1B,MACJ,KAAK/R,EAAYuG,WACblU,KAAK4f,GAAqBF,GAC1B,MACJ,KAAK/R,EAAYoF,WACb/S,KAAK6f,GAAqBH,GAC1B,MACJ,KAAK/R,EAAYqC,KAGjB,KAAKrC,EAAYwG,MAEb,MACJ,QAEInP,EAAOnC,IAAIhC,EAASwG,YAAa,yBAA2BqY,EAAchS,KAAO,cACjF,SAER+R,EAAYtR,KAAKuR,E,CAGrB,OAAOD,CACX,CAOO1P,aAAatO,GAChB,OAAOqK,EAAkBY,MAAMC,KAAKC,UAAUnL,GAClD,CAEQke,GAAqBle,GACzBzB,KAAK8f,GAAsBre,EAAQmT,OAAQ,gDAEdhL,IAAzBnI,EAAQkR,cACR3S,KAAK8f,GAAsBre,EAAQkR,aAAc,0CAEzD,CAEQiN,GAAqBne,GAGzB,GAFAzB,KAAK8f,GAAsBre,EAAQkR,aAAc,gDAE5B/I,IAAjBnI,EAAQsM,KACR,MAAM,IAAIhN,MAAM,0CAExB,CAEQ8e,GAAqBpe,GACzB,GAAIA,EAAQ2R,QAAU3R,EAAQyF,MAC1B,MAAM,IAAInG,MAAM,4CAGfU,EAAQ2R,QAAU3R,EAAQyF,OAC3BlH,KAAK8f,GAAsBre,EAAQyF,MAAO,2CAG9ClH,KAAK8f,GAAsBre,EAAQkR,aAAc,0CACrD,CAEQmN,GAAsBlf,EAAYK,GACtC,GAAqB,iBAAVL,GAAgC,KAAVA,EAC7B,MAAM,IAAIG,MAAME,EAExB,ECvGJ,MAAM8e,EAA+C,CACjDC,MAAOnf,EAASwE,MAChB4a,MAAOpf,EAAS4P,MAChBnJ,KAAMzG,EAASwG,YACf6Y,YAAarf,EAASwG,YACtBD,KAAMvG,EAASsG,QACfgZ,QAAStf,EAASsG,QAClBD,MAAOrG,EAASE,MAChBqf,SAAUvf,EAASoG,SACnBoZ,KAAMxf,EAASyf,MAgBZ,MAAMC,EA0CFC,iBAAiBC,GAGpB,GAFAvd,EAAIwL,WAAW+R,EAAS,gBAoIN7W,IAlIL6W,EAkIH5d,IAjIN7C,KAAKgF,OAASyb,OACX,GAAuB,iBAAZA,EAAsB,CACpC,MAAM5Z,EA7DlB,SAAuBxD,GAInB,MAAMqd,EAAUX,EAAoB1c,EAAKkQ,eACzC,QAAuB,IAAZmN,EACP,OAAOA,EAEP,MAAM,IAAI3f,MAAM,sBAAsBsC,IAE9C,CAmD6Bsd,CAAcF,GAC/BzgB,KAAKgF,OAAS,IAAIwB,EAAcK,E,MAEhC7G,KAAKgF,OAAS,IAAIwB,EAAcia,GAGpC,OAAOzgB,IACX,CA0BO4gB,QAAQve,EAAawe,GAiBxB,OAhBA3d,EAAIwL,WAAWrM,EAAK,OACpBa,EAAI4d,WAAWze,EAAK,OAEpBrC,KAAKqC,IAAMA,EAKPrC,KAAK+gB,sBAD6B,iBAA3BF,EACsB,IAAK7gB,KAAK+gB,yBAA0BF,GAEpC,IACtB7gB,KAAK+gB,sBACRrf,UAAWmf,GAIZ7gB,IACX,CAMOghB,gBAAgB1S,GAInB,OAHApL,EAAIwL,WAAWJ,EAAU,YAEzBtO,KAAKsO,SAAWA,EACTtO,IACX,CAmBOihB,uBAAuBC,GAC1B,GAAIlhB,KAAKuO,gBACL,MAAM,IAAIxN,MAAM,2CAWpB,OARKmgB,EAEM9T,MAAM+T,QAAQD,GACrBlhB,KAAKuO,gBAAkB,IAAIyI,EAAuBkK,GAElDlhB,KAAKuO,gBAAkB2S,EAJvBlhB,KAAKuO,gBAAkB,IAAIyI,EAOxBhX,IACX,CAMOohB,QAGH,MAAML,EAAwB/gB,KAAK+gB,uBAAyB,CAAC,EAS7D,QANqCnX,IAAjCmX,EAAsB/b,SAEtB+b,EAAsB/b,OAAShF,KAAKgF,SAInChF,KAAKqC,IACN,MAAM,IAAItB,MAAM,4FAEpB,MAAMsN,EAAa,IAAI4M,EAAejb,KAAKqC,IAAK0e,GAEhD,OAAO3S,EAAciT,OACjBhT,EACArO,KAAKgF,QAAUpC,EAAWI,SAC1BhD,KAAKsO,UAAY,IAAIkR,EACrBxf,KAAKuO,gBACb,E,OC3MClK,WAAW/D,UAAU6F,SACtBzG,OAAOC,eAAe0E,WAAW/D,UAAW,UAAW,CACnDM,MAAOwM,MAAM9M,UAAU6F,QACvBmb,UAAU,IAGbjd,WAAW/D,UAAU+M,OACtB3N,OAAOC,eAAe0E,WAAW/D,UAAW,QAAS,CAGjDM,MAAO,SAASyP,EAAgBkR,GAAgB,OAAO,IAAIld,WAAW+I,MAAM9M,UAAU+M,MAAM7M,KAAKR,KAAMqQ,EAAOkR,GAAO,EACrHD,UAAU,IAGbjd,WAAW/D,UAAUiE,SACtB7E,OAAOC,eAAe0E,WAAW/D,UAAW,UAAW,CACnDM,MAAOwM,MAAM9M,UAAUiE,QACvB+c,UAAU,I,M9BxBK,iBAAZhiB,SAA0C,iBAAXkiB,OACxCA,OAAOliB,QAAUH,IACQ,mBAAXsiB,QAAyBA,OAAOC,IAC9CD,OAAO,GAAItiB,GACe,iBAAZG,QACdA,QAAiB,QAAIH,IAErBD,EAAc,QAAIC", "sources": ["webpack://signalR/webpack/universalModuleDefinition", "webpack://signalR/webpack/bootstrap", "webpack://signalR/webpack/runtime/define property getters", "webpack://signalR/webpack/runtime/global", "webpack://signalR/webpack/runtime/hasOwnProperty shorthand", "webpack://signalR/webpack/runtime/make namespace object", "webpack://signalR/src/ILogger.ts", "webpack://signalR/src/Errors.ts", "webpack://signalR/src/HttpClient.ts", "webpack://signalR/src/Loggers.ts", "webpack://signalR/src/Utils.ts", "webpack://signalR/src/FetchHttpClient.ts", "webpack://signalR/src/XhrHttpClient.ts", "webpack://signalR/src/DefaultHttpClient.ts", "webpack://signalR/src/TextMessageFormat.ts", "webpack://signalR/src/HandshakeProtocol.ts", "webpack://signalR/src/IHubProtocol.ts", "webpack://signalR/src/HubConnection.ts", "webpack://signalR/src/Subject.ts", "webpack://signalR/src/DefaultReconnectPolicy.ts", "webpack://signalR/src/HeaderNames.ts", "webpack://signalR/src/AccessTokenHttpClient.ts", "webpack://signalR/src/ITransport.ts", "webpack://signalR/src/AbortController.ts", "webpack://signalR/src/LongPollingTransport.ts", "webpack://signalR/src/ServerSentEventsTransport.ts", "webpack://signalR/src/WebSocketTransport.ts", "webpack://signalR/src/HttpConnection.ts", "webpack://signalR/src/JsonHubProtocol.ts", "webpack://signalR/src/HubConnectionBuilder.ts", "webpack://signalR/src/browser-index.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"signalR\"] = factory();\n\telse\n\t\troot[\"signalR\"] = factory();\n})(self, () => {\nreturn ", "// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// These values are designed to match the ASP.NET Log Levels since that's the pattern we're emulating here.\r\n/** Indicates the severity of a log message.\r\n *\r\n * Log Levels are ordered in increasing severity. So `Debug` is more severe than `Trace`, etc.\r\n */\r\nexport enum LogLevel {\r\n    /** Log level for very low severity diagnostic messages. */\r\n    Trace = 0,\r\n    /** Log level for low severity diagnostic messages. */\r\n    Debug = 1,\r\n    /** Log level for informational diagnostic messages. */\r\n    Information = 2,\r\n    /** Log level for diagnostic messages that indicate a non-fatal problem. */\r\n    Warning = 3,\r\n    /** Log level for diagnostic messages that indicate a failure in the current operation. */\r\n    Error = 4,\r\n    /** Log level for diagnostic messages that indicate a failure that will terminate the entire application. */\r\n    Critical = 5,\r\n    /** The highest possible log level. Used when configuring logging to indicate that no log messages should be emitted. */\r\n    None = 6,\r\n}\r\n\r\n/** An abstraction that provides a sink for diagnostic messages. */\r\nexport interface ILogger {\r\n    /** Called by the framework to emit a diagnostic message.\r\n     *\r\n     * @param {LogLevel} logLevel The severity level of the message.\r\n     * @param {string} message The message.\r\n     */\r\n    log(logLevel: LogLevel, message: string): void;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HttpTransportType } from \"./ITransport\";\r\n\r\n/** Error thrown when an HTTP request fails. */\r\nexport class HttpError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The HTTP status code represented by this error. */\r\n    public statusCode: number;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     * @param {number} statusCode The HTTP status code represented by this error.\r\n     */\r\n    constructor(errorMessage: string, statusCode: number) {\r\n        const trueProto = new.target.prototype;\r\n        super(`${errorMessage}: Status code '${statusCode}'`);\r\n        this.statusCode = statusCode;\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when a timeout elapses. */\r\nexport class TimeoutError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.TimeoutError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     */\r\n    constructor(errorMessage: string = \"A timeout occurred.\") {\r\n        const trueProto = new.target.prototype;\r\n        super(errorMessage);\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when an action is aborted. */\r\nexport class AbortError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** Constructs a new instance of {@link AbortError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     */\r\n    constructor(errorMessage: string = \"An abort occurred.\") {\r\n        const trueProto = new.target.prototype;\r\n        super(errorMessage);\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the selected transport is unsupported by the browser. */\r\n/** @private */\r\nexport class UnsupportedTransportError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The {@link @microsoft/signalr.HttpTransportType} this error occurred on. */\r\n    public transport: HttpTransportType;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.UnsupportedTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n     */\r\n    constructor(message: string, transport: HttpTransportType) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'UnsupportedTransportError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the selected transport is disabled by the browser. */\r\n/** @private */\r\nexport class DisabledTransportError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The {@link @microsoft/signalr.HttpTransportType} this error occurred on. */\r\n    public transport: HttpTransportType;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.DisabledTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n     */\r\n    constructor(message: string, transport: HttpTransportType) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'DisabledTransportError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the selected transport cannot be started. */\r\n/** @private */\r\nexport class FailedToStartTransportError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The {@link @microsoft/signalr.HttpTransportType} this error occurred on. */\r\n    public transport: HttpTransportType;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.FailedToStartTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n     */\r\n    constructor(message: string, transport: HttpTransportType) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'FailedToStartTransportError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the negotiation with the server failed to complete. */\r\n/** @private */\r\nexport class FailedToNegotiateWithServerError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.FailedToNegotiateWithServerError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     */\r\n    constructor(message: string) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.errorType = 'FailedToNegotiateWithServerError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when multiple errors have occurred. */\r\n/** @private */\r\nexport class AggregateErrors extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The collection of errors this error is aggregating. */\r\n    public innerErrors: Error[];\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.AggregateErrors}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {Error[]} innerErrors The collection of errors this error is aggregating.\r\n     */\r\n    constructor(message: string, innerErrors: Error[]) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n\r\n        this.innerErrors = innerErrors;\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AbortSignal } from \"./AbortController\";\r\nimport { MessageHeaders } from \"./IHubProtocol\";\r\n\r\n/** Represents an HTTP request. */\r\nexport interface HttpRequest {\r\n    /** The HTTP method to use for the request. */\r\n    method?: string;\r\n\r\n    /** The URL for the request. */\r\n    url?: string;\r\n\r\n    /** The body content for the request. May be a string or an ArrayBuffer (for binary data). */\r\n    content?: string | ArrayBuffer;\r\n\r\n    /** An object describing headers to apply to the request. */\r\n    headers?: MessageHeaders;\r\n\r\n    /** The XMLHttpRequestResponseType to apply to the request. */\r\n    responseType?: XMLHttpRequestResponseType;\r\n\r\n    /** An AbortSignal that can be monitored for cancellation. */\r\n    abortSignal?: AbortSignal;\r\n\r\n    /** The time to wait for the request to complete before throwing a TimeoutError. Measured in milliseconds. */\r\n    timeout?: number;\r\n\r\n    /** This controls whether credentials such as cookies are sent in cross-site requests. */\r\n    withCredentials?: boolean;\r\n}\r\n\r\n/** Represents an HTTP response. */\r\nexport class HttpResponse {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     */\r\n    constructor(statusCode: number);\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code and message.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     * @param {string} statusText The status message of the response.\r\n     */\r\n    constructor(statusCode: number, statusText: string);\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code, message and string content.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     * @param {string} statusText The status message of the response.\r\n     * @param {string} content The content of the response.\r\n     */\r\n    constructor(statusCode: number, statusText: string, content: string);\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code, message and binary content.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     * @param {string} statusText The status message of the response.\r\n     * @param {ArrayBuffer} content The content of the response.\r\n     */\r\n    constructor(statusCode: number, statusText: string, content: ArrayBuffer);\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code, message and binary content.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     * @param {string} statusText The status message of the response.\r\n     * @param {string | ArrayBuffer} content The content of the response.\r\n     */\r\n    constructor(statusCode: number, statusText: string, content: string | ArrayBuffer);\r\n    constructor(\r\n        public readonly statusCode: number,\r\n        public readonly statusText?: string,\r\n        public readonly content?: string | ArrayBuffer) {\r\n    }\r\n}\r\n\r\n/** Abstraction over an HTTP client.\r\n *\r\n * This class provides an abstraction over an HTTP client so that a different implementation can be provided on different platforms.\r\n */\r\nexport abstract class HttpClient {\r\n    /** Issues an HTTP GET request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public get(url: string): Promise<HttpResponse>;\r\n\r\n    /** Issues an HTTP GET request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @param {HttpRequest} options Additional options to configure the request. The 'url' field in this object will be overridden by the url parameter.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public get(url: string, options: HttpRequest): Promise<HttpResponse>;\r\n    public get(url: string, options?: HttpRequest): Promise<HttpResponse> {\r\n        return this.send({\r\n            ...options,\r\n            method: \"GET\",\r\n            url,\r\n        });\r\n    }\r\n\r\n    /** Issues an HTTP POST request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public post(url: string): Promise<HttpResponse>;\r\n\r\n    /** Issues an HTTP POST request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @param {HttpRequest} options Additional options to configure the request. The 'url' field in this object will be overridden by the url parameter.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public post(url: string, options: HttpRequest): Promise<HttpResponse>;\r\n    public post(url: string, options?: HttpRequest): Promise<HttpResponse> {\r\n        return this.send({\r\n            ...options,\r\n            method: \"POST\",\r\n            url,\r\n        });\r\n    }\r\n\r\n    /** Issues an HTTP DELETE request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public delete(url: string): Promise<HttpResponse>;\r\n\r\n    /** Issues an HTTP DELETE request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @param {HttpRequest} options Additional options to configure the request. The 'url' field in this object will be overridden by the url parameter.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public delete(url: string, options: HttpRequest): Promise<HttpResponse>;\r\n    public delete(url: string, options?: HttpRequest): Promise<HttpResponse> {\r\n        return this.send({\r\n            ...options,\r\n            method: \"DELETE\",\r\n            url,\r\n        });\r\n    }\r\n\r\n    /** Issues an HTTP request to the specified URL, returning a {@link Promise} that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {HttpRequest} request An {@link @microsoft/signalr.HttpRequest} describing the request to send.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an HttpResponse describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public abstract send(request: HttpRequest): Promise<HttpResponse>;\r\n\r\n    /** Gets all cookies that apply to the specified URL.\r\n     *\r\n     * @param url The URL that the cookies are valid for.\r\n     * @returns {string} A string containing all the key-value cookie pairs for the specified URL.\r\n     */\r\n    // @ts-ignore\r\n    public getCookieString(url: string): string {\r\n        return \"\";\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\n\r\n/** A logger that does nothing when log messages are sent to it. */\r\nexport class NullLogger implements ILogger {\r\n    /** The singleton instance of the {@link @microsoft/signalr.NullLogger}. */\r\n    public static instance: ILogger = new NullLogger();\r\n\r\n    private constructor() {}\r\n\r\n    /** @inheritDoc */\r\n    // eslint-disable-next-line\r\n    public log(_logLevel: LogLevel, _message: string): void {\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { NullLogger } from \"./Loggers\";\r\nimport { IStreamSubscriber, ISubscription } from \"./Stream\";\r\nimport { Subject } from \"./Subject\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\n\r\n// Version token that will be replaced by the prepack command\r\n/** The version of the SignalR client. */\r\n\r\nexport const VERSION: string = \"0.0.0-DEV_BUILD\";\r\n/** @private */\r\nexport class Arg {\r\n    public static isRequired(val: any, name: string): void {\r\n        if (val === null || val === undefined) {\r\n            throw new Error(`The '${name}' argument is required.`);\r\n        }\r\n    }\r\n    public static isNotEmpty(val: string, name: string): void {\r\n        if (!val || val.match(/^\\s*$/)) {\r\n            throw new Error(`The '${name}' argument should not be empty.`);\r\n        }\r\n    }\r\n\r\n    public static isIn(val: any, values: any, name: string): void {\r\n        // TypeScript enums have keys for **both** the name and the value of each enum member on the type itself.\r\n        if (!(val in values)) {\r\n            throw new Error(`Unknown ${name} value: ${val}.`);\r\n        }\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport class Platform {\r\n    // react-native has a window but no document so we should check both\r\n    public static get isBrowser(): boolean {\r\n        return typeof window === \"object\" && typeof window.document === \"object\";\r\n    }\r\n\r\n    // WebWorkers don't have a window object so the isBrowser check would fail\r\n    public static get isWebWorker(): boolean {\r\n        return typeof self === \"object\" && \"importScripts\" in self;\r\n    }\r\n\r\n    // react-native has a window but no document\r\n    static get isReactNative(): boolean {\r\n        return typeof window === \"object\" && typeof window.document === \"undefined\";\r\n    }\r\n\r\n    // Node apps shouldn't have a window object, but WebWorkers don't either\r\n    // so we need to check for both WebWorker and window\r\n    public static get isNode(): boolean {\r\n        return !this.isBrowser && !this.isWebWorker && !this.isReactNative;\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport function getDataDetail(data: any, includeContent: boolean): string {\r\n    let detail = \"\";\r\n    if (isArrayBuffer(data)) {\r\n        detail = `Binary data of length ${data.byteLength}`;\r\n        if (includeContent) {\r\n            detail += `. Content: '${formatArrayBuffer(data)}'`;\r\n        }\r\n    } else if (typeof data === \"string\") {\r\n        detail = `String data of length ${data.length}`;\r\n        if (includeContent) {\r\n            detail += `. Content: '${data}'`;\r\n        }\r\n    }\r\n    return detail;\r\n}\r\n\r\n/** @private */\r\nexport function formatArrayBuffer(data: ArrayBuffer): string {\r\n    const view = new Uint8Array(data);\r\n\r\n    // Uint8Array.map only supports returning another Uint8Array?\r\n    let str = \"\";\r\n    view.forEach((num) => {\r\n        const pad = num < 16 ? \"0\" : \"\";\r\n        str += `0x${pad}${num.toString(16)} `;\r\n    });\r\n\r\n    // Trim of trailing space.\r\n    return str.substr(0, str.length - 1);\r\n}\r\n\r\n// Also in signalr-protocol-msgpack/Utils.ts\r\n/** @private */\r\nexport function isArrayBuffer(val: any): val is ArrayBuffer {\r\n    return val && typeof ArrayBuffer !== \"undefined\" &&\r\n        (val instanceof ArrayBuffer ||\r\n            // Sometimes we get an ArrayBuffer that doesn't satisfy instanceof\r\n            (val.constructor && val.constructor.name === \"ArrayBuffer\"));\r\n}\r\n\r\n/** @private */\r\nexport async function sendMessage(logger: ILogger, transportName: string, httpClient: HttpClient, url: string,\r\n                                  content: string | ArrayBuffer, options: IHttpConnectionOptions): Promise<void> {\r\n    const headers: {[k: string]: string} = {};\r\n\r\n    const [name, value] = getUserAgentHeader();\r\n    headers[name] = value;\r\n\r\n    logger.log(LogLevel.Trace, `(${transportName} transport) sending data. ${getDataDetail(content, options.logMessageContent!)}.`);\r\n\r\n    const responseType = isArrayBuffer(content) ? \"arraybuffer\" : \"text\";\r\n    const response = await httpClient.post(url, {\r\n        content,\r\n        headers: { ...headers, ...options.headers},\r\n        responseType,\r\n        timeout: options.timeout,\r\n        withCredentials: options.withCredentials,\r\n    });\r\n\r\n    logger.log(LogLevel.Trace, `(${transportName} transport) request complete. Response status: ${response.statusCode}.`);\r\n}\r\n\r\n/** @private */\r\nexport function createLogger(logger?: ILogger | LogLevel): ILogger {\r\n    if (logger === undefined) {\r\n        return new ConsoleLogger(LogLevel.Information);\r\n    }\r\n\r\n    if (logger === null) {\r\n        return NullLogger.instance;\r\n    }\r\n\r\n    if ((logger as ILogger).log !== undefined) {\r\n        return logger as ILogger;\r\n    }\r\n\r\n    return new ConsoleLogger(logger as LogLevel);\r\n}\r\n\r\n/** @private */\r\nexport class SubjectSubscription<T> implements ISubscription<T> {\r\n    private _subject: Subject<T>;\r\n    private _observer: IStreamSubscriber<T>;\r\n\r\n    constructor(subject: Subject<T>, observer: IStreamSubscriber<T>) {\r\n        this._subject = subject;\r\n        this._observer = observer;\r\n    }\r\n\r\n    public dispose(): void {\r\n        const index: number = this._subject.observers.indexOf(this._observer);\r\n        if (index > -1) {\r\n            this._subject.observers.splice(index, 1);\r\n        }\r\n\r\n        if (this._subject.observers.length === 0 && this._subject.cancelCallback) {\r\n            this._subject.cancelCallback().catch((_) => { });\r\n        }\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport class ConsoleLogger implements ILogger {\r\n    private readonly _minLevel: LogLevel;\r\n\r\n    // Public for testing purposes.\r\n    public out: {\r\n        error(message: any): void,\r\n        warn(message: any): void,\r\n        info(message: any): void,\r\n        log(message: any): void,\r\n    };\r\n\r\n    constructor(minimumLogLevel: LogLevel) {\r\n        this._minLevel = minimumLogLevel;\r\n        this.out = console;\r\n    }\r\n\r\n    public log(logLevel: LogLevel, message: string): void {\r\n        if (logLevel >= this._minLevel) {\r\n            const msg = `[${new Date().toISOString()}] ${LogLevel[logLevel]}: ${message}`;\r\n            switch (logLevel) {\r\n                case LogLevel.Critical:\r\n                case LogLevel.Error:\r\n                    this.out.error(msg);\r\n                    break;\r\n                case LogLevel.Warning:\r\n                    this.out.warn(msg);\r\n                    break;\r\n                case LogLevel.Information:\r\n                    this.out.info(msg);\r\n                    break;\r\n                default:\r\n                    // console.debug only goes to attached debuggers in Node, so we use console.log for Trace and Debug\r\n                    this.out.log(msg);\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport function getUserAgentHeader(): [string, string] {\r\n    let userAgentHeaderName = \"X-SignalR-User-Agent\";\r\n    if (Platform.isNode) {\r\n        userAgentHeaderName = \"User-Agent\";\r\n    }\r\n    return [ userAgentHeaderName, constructUserAgent(VERSION, getOsName(), getRuntime(), getRuntimeVersion()) ];\r\n}\r\n\r\n/** @private */\r\nexport function constructUserAgent(version: string, os: string, runtime: string, runtimeVersion: string | undefined): string {\r\n    // Microsoft SignalR/[Version] ([Detailed Version]; [Operating System]; [Runtime]; [Runtime Version])\r\n    let userAgent: string = \"Microsoft SignalR/\";\r\n\r\n    const majorAndMinor = version.split(\".\");\r\n    userAgent += `${majorAndMinor[0]}.${majorAndMinor[1]}`;\r\n    userAgent += ` (${version}; `;\r\n\r\n    if (os && os !== \"\") {\r\n        userAgent += `${os}; `;\r\n    } else {\r\n        userAgent += \"Unknown OS; \";\r\n    }\r\n\r\n    userAgent += `${runtime}`;\r\n\r\n    if (runtimeVersion) {\r\n        userAgent += `; ${runtimeVersion}`;\r\n    } else {\r\n        userAgent += \"; Unknown Runtime Version\";\r\n    }\r\n\r\n    userAgent += \")\";\r\n    return userAgent;\r\n}\r\n\r\n// eslint-disable-next-line spaced-comment\r\n/*#__PURE__*/ function getOsName(): string {\r\n    if (Platform.isNode) {\r\n        switch (process.platform) {\r\n            case \"win32\":\r\n                return \"Windows NT\";\r\n            case \"darwin\":\r\n                return \"macOS\";\r\n            case \"linux\":\r\n                return \"Linux\";\r\n            default:\r\n                return process.platform;\r\n        }\r\n    } else {\r\n        return \"\";\r\n    }\r\n}\r\n\r\n// eslint-disable-next-line spaced-comment\r\n/*#__PURE__*/ function getRuntimeVersion(): string | undefined {\r\n    if (Platform.isNode) {\r\n        return process.versions.node;\r\n    }\r\n    return undefined;\r\n}\r\n\r\nfunction getRuntime(): string {\r\n    if (Platform.isNode) {\r\n        return \"NodeJS\";\r\n    } else {\r\n        return \"Browser\";\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport function getErrorString(e: any): string {\r\n    if (e.stack) {\r\n        return e.stack;\r\n    } else if (e.message) {\r\n        return e.message;\r\n    }\r\n    return `${e}`;\r\n}\r\n\r\n/** @private */\r\nexport function getGlobalThis(): unknown {\r\n    // globalThis is semi-new and not available in Node until v12\r\n    if (typeof globalThis !== \"undefined\") {\r\n        return globalThis;\r\n    }\r\n    if (typeof self !== \"undefined\") {\r\n        return self;\r\n    }\r\n    if (typeof window !== \"undefined\") {\r\n        return window;\r\n    }\r\n    if (typeof global !== \"undefined\") {\r\n        return global;\r\n    }\r\n    throw new Error(\"could not find global\");\r\n}", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// @ts-ignore: This will be removed from built files and is here to make the types available during dev work\r\nimport { CookieJar } from \"@types/tough-cookie\";\r\n\r\nimport { AbortError, HttpError, TimeoutError } from \"./Errors\";\r\nimport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { Platform, getGlobalThis, isArrayBuffer } from \"./Utils\";\r\n\r\nexport class FetchHttpClient extends HttpClient {\r\n    private readonly _abortControllerType: { prototype: AbortController, new(): AbortController };\r\n    private readonly _fetchType: (input: RequestInfo, init?: RequestInit) => Promise<Response>;\r\n    private readonly _jar?: CookieJar;\r\n\r\n    private readonly _logger: ILogger;\r\n\r\n    public constructor(logger: ILogger) {\r\n        super();\r\n        this._logger = logger;\r\n\r\n        if (typeof fetch === \"undefined\") {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: T<PERSON> doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n\r\n            // Cookies aren't automatically handled in Node so we need to add a CookieJar to preserve cookies across requests\r\n            this._jar = new (requireFunc(\"tough-cookie\")).CookieJar();\r\n            this._fetchType = requireFunc(\"node-fetch\");\r\n\r\n            // node-fetch doesn't have a nice API for getting and setting cookies\r\n            // fetch-cookie will wrap a fetch implementation with a default CookieJar or a provided one\r\n            this._fetchType = requireFunc(\"fetch-cookie\")(this._fetchType, this._jar);\r\n        } else {\r\n            this._fetchType = fetch.bind(getGlobalThis());\r\n        }\r\n        if (typeof AbortController === \"undefined\") {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: TS doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n\r\n            // Node needs EventListener methods on AbortController which our custom polyfill doesn't provide\r\n            this._abortControllerType = requireFunc(\"abort-controller\");\r\n        } else {\r\n            this._abortControllerType = AbortController;\r\n        }\r\n    }\r\n\r\n    /** @inheritDoc */\r\n    public async send(request: HttpRequest): Promise<HttpResponse> {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            throw new AbortError();\r\n        }\r\n\r\n        if (!request.method) {\r\n            throw new Error(\"No method defined.\");\r\n        }\r\n        if (!request.url) {\r\n            throw new Error(\"No url defined.\");\r\n        }\r\n\r\n        const abortController = new this._abortControllerType();\r\n\r\n        let error: any;\r\n        // Hook our abortSignal into the abort controller\r\n        if (request.abortSignal) {\r\n            request.abortSignal.onabort = () => {\r\n                abortController.abort();\r\n                error = new AbortError();\r\n            };\r\n        }\r\n\r\n        // If a timeout has been passed in, setup a timeout to call abort\r\n        // Type needs to be any to fit window.setTimeout and NodeJS.setTimeout\r\n        let timeoutId: any = null;\r\n        if (request.timeout) {\r\n            const msTimeout = request.timeout!;\r\n            timeoutId = setTimeout(() => {\r\n                abortController.abort();\r\n                this._logger.log(LogLevel.Warning, `Timeout from HTTP request.`);\r\n                error = new TimeoutError();\r\n            }, msTimeout);\r\n        }\r\n\r\n        if (request.content === \"\") {\r\n            request.content = undefined;\r\n        }\r\n        if (request.content) {\r\n            // Explicitly setting the Content-Type header for React Native on Android platform.\r\n            request.headers = request.headers || {};\r\n            if (isArrayBuffer(request.content)) {\r\n                request.headers[\"Content-Type\"] = \"application/octet-stream\";\r\n            } else {\r\n                request.headers[\"Content-Type\"] = \"text/plain;charset=UTF-8\";\r\n            }\r\n        }\r\n\r\n        let response: Response;\r\n        try {\r\n            response = await this._fetchType(request.url!, {\r\n                body: request.content,\r\n                cache: \"no-cache\",\r\n                credentials: request.withCredentials === true ? \"include\" : \"same-origin\",\r\n                headers: {\r\n                    \"X-Requested-With\": \"XMLHttpRequest\",\r\n                    ...request.headers,\r\n                },\r\n                method: request.method!,\r\n                mode: \"cors\",\r\n                redirect: \"follow\",\r\n                signal: abortController.signal,\r\n            });\r\n        } catch (e) {\r\n            if (error) {\r\n                throw error;\r\n            }\r\n            this._logger.log(\r\n                LogLevel.Warning,\r\n                `Error from HTTP request. ${e}.`,\r\n            );\r\n            throw e;\r\n        } finally {\r\n            if (timeoutId) {\r\n                clearTimeout(timeoutId);\r\n            }\r\n            if (request.abortSignal) {\r\n                request.abortSignal.onabort = null;\r\n            }\r\n        }\r\n\r\n        if (!response.ok) {\r\n            const errorMessage = await deserializeContent(response, \"text\") as string;\r\n            throw new HttpError(errorMessage || response.statusText, response.status);\r\n        }\r\n\r\n        const content = deserializeContent(response, request.responseType);\r\n        const payload = await content;\r\n\r\n        return new HttpResponse(\r\n            response.status,\r\n            response.statusText,\r\n            payload,\r\n        );\r\n    }\r\n\r\n    public getCookieString(url: string): string {\r\n        let cookies: string = \"\";\r\n        if (Platform.isNode && this._jar) {\r\n            // @ts-ignore: unused variable\r\n            this._jar.getCookies(url, (e, c) => cookies = c.join(\"; \"));\r\n        }\r\n        return cookies;\r\n    }\r\n}\r\n\r\nfunction deserializeContent(response: Response, responseType?: XMLHttpRequestResponseType): Promise<string | ArrayBuffer> {\r\n    let content;\r\n    switch (responseType) {\r\n        case \"arraybuffer\":\r\n            content = response.arrayBuffer();\r\n            break;\r\n        case \"text\":\r\n            content = response.text();\r\n            break;\r\n        case \"blob\":\r\n        case \"document\":\r\n        case \"json\":\r\n            throw new Error(`${responseType} is not supported.`);\r\n        default:\r\n            content = response.text();\r\n            break;\r\n    }\r\n\r\n    return content;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AbortError, HttpError, TimeoutError } from \"./Errors\";\r\nimport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { isArrayBuffer } from \"./Utils\";\r\n\r\nexport class XhrHttpClient extends HttpClient {\r\n    private readonly _logger: ILogger;\r\n\r\n    public constructor(logger: ILogger) {\r\n        super();\r\n        this._logger = logger;\r\n    }\r\n\r\n    /** @inheritDoc */\r\n    public send(request: HttpRequest): Promise<HttpResponse> {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            return Promise.reject(new AbortError());\r\n        }\r\n\r\n        if (!request.method) {\r\n            return Promise.reject(new Error(\"No method defined.\"));\r\n        }\r\n        if (!request.url) {\r\n            return Promise.reject(new Error(\"No url defined.\"));\r\n        }\r\n\r\n        return new Promise<HttpResponse>((resolve, reject) => {\r\n            const xhr = new XMLHttpRequest();\r\n\r\n            xhr.open(request.method!, request.url!, true);\r\n            xhr.withCredentials = request.withCredentials === undefined ? true : request.withCredentials;\r\n            xhr.setRequestHeader(\"X-Requested-With\", \"XMLHttpRequest\");\r\n            if (request.content === \"\") {\r\n                request.content = undefined;\r\n            }\r\n            if (request.content) {\r\n                // Explicitly setting the Content-Type header for React Native on Android platform.\r\n                if (isArrayBuffer(request.content)) {\r\n                    xhr.setRequestHeader(\"Content-Type\", \"application/octet-stream\");\r\n                } else {\r\n                    xhr.setRequestHeader(\"Content-Type\", \"text/plain;charset=UTF-8\");\r\n                }\r\n            }\r\n\r\n            const headers = request.headers;\r\n            if (headers) {\r\n                Object.keys(headers)\r\n                    .forEach((header) => {\r\n                        xhr.setRequestHeader(header, headers[header]);\r\n                    });\r\n            }\r\n\r\n            if (request.responseType) {\r\n                xhr.responseType = request.responseType;\r\n            }\r\n\r\n            if (request.abortSignal) {\r\n                request.abortSignal.onabort = () => {\r\n                    xhr.abort();\r\n                    reject(new AbortError());\r\n                };\r\n            }\r\n\r\n            if (request.timeout) {\r\n                xhr.timeout = request.timeout;\r\n            }\r\n\r\n            xhr.onload = () => {\r\n                if (request.abortSignal) {\r\n                    request.abortSignal.onabort = null;\r\n                }\r\n\r\n                if (xhr.status >= 200 && xhr.status < 300) {\r\n                    resolve(new HttpResponse(xhr.status, xhr.statusText, xhr.response || xhr.responseText));\r\n                } else {\r\n                    reject(new HttpError(xhr.response || xhr.responseText || xhr.statusText, xhr.status));\r\n                }\r\n            };\r\n\r\n            xhr.onerror = () => {\r\n                this._logger.log(LogLevel.Warning, `Error from HTTP request. ${xhr.status}: ${xhr.statusText}.`);\r\n                reject(new HttpError(xhr.statusText, xhr.status));\r\n            };\r\n\r\n            xhr.ontimeout = () => {\r\n                this._logger.log(LogLevel.Warning, `Timeout from HTTP request.`);\r\n                reject(new TimeoutError());\r\n            };\r\n\r\n            xhr.send(request.content);\r\n        });\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AbortError } from \"./Errors\";\r\nimport { FetchHttpClient } from \"./FetchHttpClient\";\r\nimport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\nimport { ILogger } from \"./ILogger\";\r\nimport { Platform } from \"./Utils\";\r\nimport { XhrHttpClient } from \"./XhrHttpClient\";\r\n\r\n/** Default implementation of {@link @microsoft/signalr.HttpClient}. */\r\nexport class DefaultHttpClient extends HttpClient {\r\n    private readonly _httpClient: HttpClient;\r\n\r\n    /** Creates a new instance of the {@link @microsoft/signalr.DefaultHttpClient}, using the provided {@link @microsoft/signalr.ILogger} to log messages. */\r\n    public constructor(logger: ILogger) {\r\n        super();\r\n\r\n        if (typeof fetch !== \"undefined\" || Platform.isNode) {\r\n            this._httpClient = new FetchHttpClient(logger);\r\n        } else if (typeof XMLHttpRequest !== \"undefined\") {\r\n            this._httpClient = new XhrHttpClient(logger);\r\n        } else {\r\n            throw new Error(\"No usable HttpClient found.\");\r\n        }\r\n    }\r\n\r\n    /** @inheritDoc */\r\n    public send(request: HttpRequest): Promise<HttpResponse> {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            return Promise.reject(new AbortError());\r\n        }\r\n\r\n        if (!request.method) {\r\n            return Promise.reject(new Error(\"No method defined.\"));\r\n        }\r\n        if (!request.url) {\r\n            return Promise.reject(new Error(\"No url defined.\"));\r\n        }\r\n\r\n        return this._httpClient.send(request);\r\n    }\r\n\r\n    public getCookieString(url: string): string {\r\n        return this._httpClient.getCookieString(url);\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Not exported from index\r\n/** @private */\r\nexport class TextMessageFormat {\r\n    public static RecordSeparatorCode = 0x1e;\r\n    public static RecordSeparator = String.fromCharCode(TextMessageFormat.RecordSeparatorCode);\r\n\r\n    public static write(output: string): string {\r\n        return `${output}${TextMessageFormat.RecordSeparator}`;\r\n    }\r\n\r\n    public static parse(input: string): string[] {\r\n        if (input[input.length - 1] !== TextMessageFormat.RecordSeparator) {\r\n            throw new Error(\"Message is incomplete.\");\r\n        }\r\n\r\n        const messages = input.split(TextMessageFormat.RecordSeparator);\r\n        messages.pop();\r\n        return messages;\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { TextMessageFormat } from \"./TextMessageFormat\";\r\nimport { isArrayBuffer } from \"./Utils\";\r\n\r\n/** @private */\r\nexport interface HandshakeRequestMessage {\r\n    readonly protocol: string;\r\n    readonly version: number;\r\n}\r\n\r\n/** @private */\r\nexport interface HandshakeResponseMessage {\r\n    readonly error: string;\r\n    readonly minorVersion: number;\r\n}\r\n\r\n/** @private */\r\nexport class HandshakeProtocol {\r\n    // Handshake request is always JSON\r\n    public writeHandshakeRequest(handshakeRequest: HandshakeRequestMessage): string {\r\n        return TextMessageFormat.write(JSON.stringify(handshakeRequest));\r\n    }\r\n\r\n    public parseHandshakeResponse(data: any): [any, HandshakeResponseMessage] {\r\n        let messageData: string;\r\n        let remainingData: any;\r\n\r\n        if (isArrayBuffer(data)) {\r\n            // Format is binary but still need to read JSON text from handshake response\r\n            const binaryData = new Uint8Array(data);\r\n            const separatorIndex = binaryData.indexOf(TextMessageFormat.RecordSeparatorCode);\r\n            if (separatorIndex === -1) {\r\n                throw new Error(\"Message is incomplete.\");\r\n            }\r\n\r\n            // content before separator is handshake response\r\n            // optional content after is additional messages\r\n            const responseLength = separatorIndex + 1;\r\n            messageData = String.fromCharCode.apply(null, Array.prototype.slice.call(binaryData.slice(0, responseLength)));\r\n            remainingData = (binaryData.byteLength > responseLength) ? binaryData.slice(responseLength).buffer : null;\r\n        } else {\r\n            const textData: string = data;\r\n            const separatorIndex = textData.indexOf(TextMessageFormat.RecordSeparator);\r\n            if (separatorIndex === -1) {\r\n                throw new Error(\"Message is incomplete.\");\r\n            }\r\n\r\n            // content before separator is handshake response\r\n            // optional content after is additional messages\r\n            const responseLength = separatorIndex + 1;\r\n            messageData = textData.substring(0, responseLength);\r\n            remainingData = (textData.length > responseLength) ? textData.substring(responseLength) : null;\r\n        }\r\n\r\n        // At this point we should have just the single handshake message\r\n        const messages = TextMessageFormat.parse(messageData);\r\n        const response = JSON.parse(messages[0]);\r\n        if (response.type) {\r\n            throw new Error(\"Expected a handshake response from the server.\");\r\n        }\r\n        const responseMessage: HandshakeResponseMessage = response;\r\n\r\n        // multiple messages could have arrived with handshake\r\n        // return additional data to be parsed as usual, or null if all parsed\r\n        return [remainingData, responseMessage];\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { ILogger } from \"./ILogger\";\r\nimport { TransferFormat } from \"./ITransport\";\r\n\r\n/** Defines the type of a Hub Message. */\r\nexport enum MessageType {\r\n    /** Indicates the message is an Invocation message and implements the {@link @microsoft/signalr.InvocationMessage} interface. */\r\n    Invocation = 1,\r\n    /** Indicates the message is a StreamItem message and implements the {@link @microsoft/signalr.StreamItemMessage} interface. */\r\n    StreamItem = 2,\r\n    /** Indicates the message is a Completion message and implements the {@link @microsoft/signalr.CompletionMessage} interface. */\r\n    Completion = 3,\r\n    /** Indicates the message is a Stream Invocation message and implements the {@link @microsoft/signalr.StreamInvocationMessage} interface. */\r\n    StreamInvocation = 4,\r\n    /** Indicates the message is a Cancel Invocation message and implements the {@link @microsoft/signalr.CancelInvocationMessage} interface. */\r\n    CancelInvocation = 5,\r\n    /** Indicates the message is a Ping message and implements the {@link @microsoft/signalr.PingMessage} interface. */\r\n    Ping = 6,\r\n    /** Indicates the message is a Close message and implements the {@link @microsoft/signalr.CloseMessage} interface. */\r\n    Close = 7,\r\n}\r\n\r\n/** Defines a dictionary of string keys and string values representing headers attached to a Hub message. */\r\nexport interface MessageHeaders {\r\n    /** Gets or sets the header with the specified key. */\r\n    [key: string]: string;\r\n}\r\n\r\n/** Union type of all known Hub messages. */\r\nexport type HubMessage =\r\n    InvocationMessage |\r\n    StreamInvocationMessage |\r\n    StreamItemMessage |\r\n    CompletionMessage |\r\n    CancelInvocationMessage |\r\n    PingMessage |\r\n    CloseMessage;\r\n\r\n/** Defines properties common to all Hub messages. */\r\nexport interface HubMessageBase {\r\n    /** A {@link @microsoft/signalr.MessageType} value indicating the type of this message. */\r\n    readonly type: MessageType;\r\n}\r\n\r\n/** Defines properties common to all Hub messages relating to a specific invocation. */\r\nexport interface HubInvocationMessage extends HubMessageBase {\r\n    /** A {@link @microsoft/signalr.MessageHeaders} dictionary containing headers attached to the message. */\r\n    readonly headers?: MessageHeaders;\r\n    /** The ID of the invocation relating to this message.\r\n     *\r\n     * This is expected to be present for {@link @microsoft/signalr.StreamInvocationMessage} and {@link @microsoft/signalr.CompletionMessage}. It may\r\n     * be 'undefined' for an {@link @microsoft/signalr.InvocationMessage} if the sender does not expect a response.\r\n     */\r\n    readonly invocationId?: string;\r\n}\r\n\r\n/** A hub message representing a non-streaming invocation. */\r\nexport interface InvocationMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.Invocation;\r\n    /** The target method name. */\r\n    readonly target: string;\r\n    /** The target method arguments. */\r\n    readonly arguments: any[];\r\n    /** The target methods stream IDs. */\r\n    readonly streamIds?: string[];\r\n}\r\n\r\n/** A hub message representing a streaming invocation. */\r\nexport interface StreamInvocationMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.StreamInvocation;\r\n\r\n    /** The invocation ID. */\r\n    readonly invocationId: string;\r\n    /** The target method name. */\r\n    readonly target: string;\r\n    /** The target method arguments. */\r\n    readonly arguments: any[];\r\n    /** The target methods stream IDs. */\r\n    readonly streamIds?: string[];\r\n}\r\n\r\n/** A hub message representing a single item produced as part of a result stream. */\r\nexport interface StreamItemMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.StreamItem;\r\n\r\n    /** The invocation ID. */\r\n    readonly invocationId: string;\r\n\r\n    /** The item produced by the server. */\r\n    readonly item?: any;\r\n}\r\n\r\n/** A hub message representing the result of an invocation. */\r\nexport interface CompletionMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.Completion;\r\n    /** The invocation ID. */\r\n    readonly invocationId: string;\r\n    /** The error produced by the invocation, if any.\r\n     *\r\n     * Either {@link @microsoft/signalr.CompletionMessage.error} or {@link @microsoft/signalr.CompletionMessage.result} must be defined, but not both.\r\n     */\r\n    readonly error?: string;\r\n    /** The result produced by the invocation, if any.\r\n     *\r\n     * Either {@link @microsoft/signalr.CompletionMessage.error} or {@link @microsoft/signalr.CompletionMessage.result} must be defined, but not both.\r\n     */\r\n    readonly result?: any;\r\n}\r\n\r\n/** A hub message indicating that the sender is still active. */\r\nexport interface PingMessage extends HubMessageBase {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.Ping;\r\n}\r\n\r\n/** A hub message indicating that the sender is closing the connection.\r\n *\r\n * If {@link @microsoft/signalr.CloseMessage.error} is defined, the sender is closing the connection due to an error.\r\n */\r\nexport interface CloseMessage extends HubMessageBase {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.Close;\r\n    /** The error that triggered the close, if any.\r\n     *\r\n     * If this property is undefined, the connection was closed normally and without error.\r\n     */\r\n    readonly error?: string;\r\n\r\n    /** If true, clients with automatic reconnects enabled should attempt to reconnect after receiving the CloseMessage. Otherwise, they should not. */\r\n    readonly allowReconnect?: boolean;\r\n}\r\n\r\n/** A hub message sent to request that a streaming invocation be canceled. */\r\nexport interface CancelInvocationMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.CancelInvocation;\r\n    /** The invocation ID. */\r\n    readonly invocationId: string;\r\n}\r\n\r\n/** A protocol abstraction for communicating with SignalR Hubs.  */\r\nexport interface IHubProtocol {\r\n    /** The name of the protocol. This is used by SignalR to resolve the protocol between the client and server. */\r\n    readonly name: string;\r\n    /** The version of the protocol. */\r\n    readonly version: number;\r\n    /** The {@link @microsoft/signalr.TransferFormat} of the protocol. */\r\n    readonly transferFormat: TransferFormat;\r\n\r\n    /** Creates an array of {@link @microsoft/signalr.HubMessage} objects from the specified serialized representation.\r\n     *\r\n     * If {@link @microsoft/signalr.IHubProtocol.transferFormat} is 'Text', the `input` parameter must be a string, otherwise it must be an ArrayBuffer.\r\n     *\r\n     * @param {string | ArrayBuffer} input A string or ArrayBuffer containing the serialized representation.\r\n     * @param {ILogger} logger A logger that will be used to log messages that occur during parsing.\r\n     */\r\n    parseMessages(input: string | ArrayBuffer, logger: ILogger): HubMessage[];\r\n\r\n    /** Writes the specified {@link @microsoft/signalr.HubMessage} to a string or ArrayBuffer and returns it.\r\n     *\r\n     * If {@link @microsoft/signalr.IHubProtocol.transferFormat} is 'Text', the result of this method will be a string, otherwise it will be an ArrayBuffer.\r\n     *\r\n     * @param {HubMessage} message The message to write.\r\n     * @returns {string | ArrayBuffer} A string or ArrayBuffer containing the serialized representation of the message.\r\n     */\r\n    writeMessage(message: HubMessage): string | ArrayBuffer;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HandshakeProtocol, HandshakeRequestMessage, HandshakeResponseMessage } from \"./HandshakeProtocol\";\r\nimport { IConnection } from \"./IConnection\";\r\nimport { AbortError } from \"./Errors\";\r\nimport { CancelInvocationMessage, CompletionMessage, IHubProtocol, InvocationMessage, MessageType, StreamInvocationMessage, StreamItemMessage } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { IRetryPolicy } from \"./IRetryPolicy\";\r\nimport { IStreamResult } from \"./Stream\";\r\nimport { Subject } from \"./Subject\";\r\nimport { Arg, getErrorString, Platform } from \"./Utils\";\r\n\r\nconst DEFAULT_TIMEOUT_IN_MS: number = 30 * 1000;\r\nconst DEFAULT_PING_INTERVAL_IN_MS: number = 15 * 1000;\r\n\r\n/** Describes the current state of the {@link HubConnection} to the server. */\r\nexport enum HubConnectionState {\r\n    /** The hub connection is disconnected. */\r\n    Disconnected = \"Disconnected\",\r\n    /** The hub connection is connecting. */\r\n    Connecting = \"Connecting\",\r\n    /** The hub connection is connected. */\r\n    Connected = \"Connected\",\r\n    /** The hub connection is disconnecting. */\r\n    Disconnecting = \"Disconnecting\",\r\n    /** The hub connection is reconnecting. */\r\n    Reconnecting = \"Reconnecting\",\r\n}\r\n\r\n/** Represents a connection to a SignalR Hub. */\r\nexport class HubConnection {\r\n    private readonly _cachedPingMessage: string | ArrayBuffer;\r\n    // Needs to not start with _ for tests\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private readonly connection: IConnection;\r\n    private readonly _logger: ILogger;\r\n    private readonly _reconnectPolicy?: IRetryPolicy;\r\n    private _protocol: IHubProtocol;\r\n    private _handshakeProtocol: HandshakeProtocol;\r\n    private _callbacks: { [invocationId: string]: (invocationEvent: StreamItemMessage | CompletionMessage | null, error?: Error) => void };\r\n    private _methods: { [name: string]: (((...args: any[]) => void) | ((...args: any[]) => any))[] };\r\n    private _invocationId: number;\r\n\r\n    private _closedCallbacks: ((error?: Error) => void)[];\r\n    private _reconnectingCallbacks: ((error?: Error) => void)[];\r\n    private _reconnectedCallbacks: ((connectionId?: string) => void)[];\r\n\r\n    private _receivedHandshakeResponse: boolean;\r\n    private _handshakeResolver!: (value?: PromiseLike<{}>) => void;\r\n    private _handshakeRejecter!: (reason?: any) => void;\r\n    private _stopDuringStartError?: Error;\r\n\r\n    private _connectionState: HubConnectionState;\r\n    // connectionStarted is tracked independently from connectionState, so we can check if the\r\n    // connection ever did successfully transition from connecting to connected before disconnecting.\r\n    private _connectionStarted: boolean;\r\n    private _startPromise?: Promise<void>;\r\n    private _stopPromise?: Promise<void>;\r\n    private _nextKeepAlive: number = 0;\r\n\r\n    // The type of these a) doesn't matter and b) varies when building in browser and node contexts\r\n    // Since we're building the WebPack bundle directly from the TypeScript, this matters (previously\r\n    // we built the bundle from the compiled JavaScript).\r\n    private _reconnectDelayHandle?: any;\r\n    private _timeoutHandle?: any;\r\n    private _pingServerHandle?: any;\r\n\r\n    private _freezeEventListener = () =>\r\n    {\r\n        this._logger.log(LogLevel.Warning, \"The page is being frozen, this will likely lead to the connection being closed and messages being lost. For more information see the docs at https://docs.microsoft.com/aspnet/core/signalr/javascript-client#bsleep\");\r\n    };\r\n\r\n    /** The server timeout in milliseconds.\r\n     *\r\n     * If this timeout elapses without receiving any messages from the server, the connection will be terminated with an error.\r\n     * The default timeout value is 30,000 milliseconds (30 seconds).\r\n     */\r\n    public serverTimeoutInMilliseconds: number;\r\n\r\n    /** Default interval at which to ping the server.\r\n     *\r\n     * The default value is 15,000 milliseconds (15 seconds).\r\n     * Allows the server to detect hard disconnects (like when a client unplugs their computer).\r\n     * The ping will happen at most as often as the server pings.\r\n     * If the server pings every 5 seconds, a value lower than 5 will ping every 5 seconds.\r\n     */\r\n    public keepAliveIntervalInMilliseconds: number;\r\n\r\n    /** @internal */\r\n    // Using a public static factory method means we can have a private constructor and an _internal_\r\n    // create method that can be used by HubConnectionBuilder. An \"internal\" constructor would just\r\n    // be stripped away and the '.d.ts' file would have no constructor, which is interpreted as a\r\n    // public parameter-less constructor.\r\n    public static create(connection: IConnection, logger: ILogger, protocol: IHubProtocol, reconnectPolicy?: IRetryPolicy): HubConnection {\r\n        return new HubConnection(connection, logger, protocol, reconnectPolicy);\r\n    }\r\n\r\n    private constructor(connection: IConnection, logger: ILogger, protocol: IHubProtocol, reconnectPolicy?: IRetryPolicy) {\r\n        Arg.isRequired(connection, \"connection\");\r\n        Arg.isRequired(logger, \"logger\");\r\n        Arg.isRequired(protocol, \"protocol\");\r\n\r\n        this.serverTimeoutInMilliseconds = DEFAULT_TIMEOUT_IN_MS;\r\n        this.keepAliveIntervalInMilliseconds = DEFAULT_PING_INTERVAL_IN_MS;\r\n\r\n        this._logger = logger;\r\n        this._protocol = protocol;\r\n        this.connection = connection;\r\n        this._reconnectPolicy = reconnectPolicy;\r\n        this._handshakeProtocol = new HandshakeProtocol();\r\n\r\n        this.connection.onreceive = (data: any) => this._processIncomingData(data);\r\n        this.connection.onclose = (error?: Error) => this._connectionClosed(error);\r\n\r\n        this._callbacks = {};\r\n        this._methods = {};\r\n        this._closedCallbacks = [];\r\n        this._reconnectingCallbacks = [];\r\n        this._reconnectedCallbacks = [];\r\n        this._invocationId = 0;\r\n        this._receivedHandshakeResponse = false;\r\n        this._connectionState = HubConnectionState.Disconnected;\r\n        this._connectionStarted = false;\r\n\r\n        this._cachedPingMessage = this._protocol.writeMessage({ type: MessageType.Ping });\r\n    }\r\n\r\n    /** Indicates the state of the {@link HubConnection} to the server. */\r\n    get state(): HubConnectionState {\r\n        return this._connectionState;\r\n    }\r\n\r\n    /** Represents the connection id of the {@link HubConnection} on the server. The connection id will be null when the connection is either\r\n     *  in the disconnected state or if the negotiation step was skipped.\r\n     */\r\n    get connectionId(): string | null {\r\n        return this.connection ? (this.connection.connectionId || null) : null;\r\n    }\r\n\r\n    /** Indicates the url of the {@link HubConnection} to the server. */\r\n    get baseUrl(): string {\r\n        return this.connection.baseUrl || \"\";\r\n    }\r\n\r\n    /**\r\n     * Sets a new url for the HubConnection. Note that the url can only be changed when the connection is in either the Disconnected or\r\n     * Reconnecting states.\r\n     * @param {string} url The url to connect to.\r\n     */\r\n    set baseUrl(url: string) {\r\n        if (this._connectionState !== HubConnectionState.Disconnected && this._connectionState !== HubConnectionState.Reconnecting) {\r\n            throw new Error(\"The HubConnection must be in the Disconnected or Reconnecting state to change the url.\");\r\n        }\r\n\r\n        if (!url) {\r\n            throw new Error(\"The HubConnection url must be a valid url.\");\r\n        }\r\n\r\n        this.connection.baseUrl = url;\r\n    }\r\n\r\n    /** Starts the connection.\r\n     *\r\n     * @returns {Promise<void>} A Promise that resolves when the connection has been successfully established, or rejects with an error.\r\n     */\r\n    public start(): Promise<void> {\r\n        this._startPromise = this._startWithStateTransitions();\r\n        return this._startPromise;\r\n    }\r\n\r\n    private async _startWithStateTransitions(): Promise<void> {\r\n        if (this._connectionState !== HubConnectionState.Disconnected) {\r\n            return Promise.reject(new Error(\"Cannot start a HubConnection that is not in the 'Disconnected' state.\"));\r\n        }\r\n\r\n        this._connectionState = HubConnectionState.Connecting;\r\n        this._logger.log(LogLevel.Debug, \"Starting HubConnection.\");\r\n\r\n        try {\r\n            await this._startInternal();\r\n\r\n            if (Platform.isBrowser) {\r\n                // Log when the browser freezes the tab so users know why their connection unexpectedly stopped working\r\n                window.document.addEventListener(\"freeze\", this._freezeEventListener);\r\n            }\r\n\r\n            this._connectionState = HubConnectionState.Connected;\r\n            this._connectionStarted = true;\r\n            this._logger.log(LogLevel.Debug, \"HubConnection connected successfully.\");\r\n        } catch (e) {\r\n            this._connectionState = HubConnectionState.Disconnected;\r\n            this._logger.log(LogLevel.Debug, `HubConnection failed to start successfully because of error '${e}'.`);\r\n            return Promise.reject(e);\r\n        }\r\n    }\r\n\r\n    private async _startInternal() {\r\n        this._stopDuringStartError = undefined;\r\n        this._receivedHandshakeResponse = false;\r\n        // Set up the promise before any connection is (re)started otherwise it could race with received messages\r\n        const handshakePromise = new Promise((resolve, reject) => {\r\n            this._handshakeResolver = resolve;\r\n            this._handshakeRejecter = reject;\r\n        });\r\n\r\n        await this.connection.start(this._protocol.transferFormat);\r\n\r\n        try {\r\n            const handshakeRequest: HandshakeRequestMessage = {\r\n                protocol: this._protocol.name,\r\n                version: this._protocol.version,\r\n            };\r\n\r\n            this._logger.log(LogLevel.Debug, \"Sending handshake request.\");\r\n\r\n            await this._sendMessage(this._handshakeProtocol.writeHandshakeRequest(handshakeRequest));\r\n\r\n            this._logger.log(LogLevel.Information, `Using HubProtocol '${this._protocol.name}'.`);\r\n\r\n            // defensively cleanup timeout in case we receive a message from the server before we finish start\r\n            this._cleanupTimeout();\r\n            this._resetTimeoutPeriod();\r\n            this._resetKeepAliveInterval();\r\n\r\n            await handshakePromise;\r\n\r\n            // It's important to check the stopDuringStartError instead of just relying on the handshakePromise\r\n            // being rejected on close, because this continuation can run after both the handshake completed successfully\r\n            // and the connection was closed.\r\n            if (this._stopDuringStartError) {\r\n                // It's important to throw instead of returning a rejected promise, because we don't want to allow any state\r\n                // transitions to occur between now and the calling code observing the exceptions. Returning a rejected promise\r\n                // will cause the calling continuation to get scheduled to run later.\r\n                // eslint-disable-next-line @typescript-eslint/no-throw-literal\r\n                throw this._stopDuringStartError;\r\n            }\r\n\r\n            if (!this.connection.features.inherentKeepAlive) {\r\n                await this._sendMessage(this._cachedPingMessage);\r\n            }\r\n        } catch (e) {\r\n            this._logger.log(LogLevel.Debug, `Hub handshake failed with error '${e}' during start(). Stopping HubConnection.`);\r\n\r\n            this._cleanupTimeout();\r\n            this._cleanupPingTimer();\r\n\r\n            // HttpConnection.stop() should not complete until after the onclose callback is invoked.\r\n            // This will transition the HubConnection to the disconnected state before HttpConnection.stop() completes.\r\n            await this.connection.stop(e);\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /** Stops the connection.\r\n     *\r\n     * @returns {Promise<void>} A Promise that resolves when the connection has been successfully terminated, or rejects with an error.\r\n     */\r\n    public async stop(): Promise<void> {\r\n        // Capture the start promise before the connection might be restarted in an onclose callback.\r\n        const startPromise = this._startPromise;\r\n\r\n        this._stopPromise = this._stopInternal();\r\n        await this._stopPromise;\r\n\r\n        try {\r\n            // Awaiting undefined continues immediately\r\n            await startPromise;\r\n        } catch (e) {\r\n            // This exception is returned to the user as a rejected Promise from the start method.\r\n        }\r\n    }\r\n\r\n    private _stopInternal(error?: Error): Promise<void> {\r\n        if (this._connectionState === HubConnectionState.Disconnected) {\r\n            this._logger.log(LogLevel.Debug, `Call to HubConnection.stop(${error}) ignored because it is already in the disconnected state.`);\r\n            return Promise.resolve();\r\n        }\r\n\r\n        if (this._connectionState === HubConnectionState.Disconnecting) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnecting state.`);\r\n            return this._stopPromise!;\r\n        }\r\n\r\n        this._connectionState = HubConnectionState.Disconnecting;\r\n\r\n        this._logger.log(LogLevel.Debug, \"Stopping HubConnection.\");\r\n\r\n        if (this._reconnectDelayHandle) {\r\n            // We're in a reconnect delay which means the underlying connection is currently already stopped.\r\n            // Just clear the handle to stop the reconnect loop (which no one is waiting on thankfully) and\r\n            // fire the onclose callbacks.\r\n            this._logger.log(LogLevel.Debug, \"Connection stopped during reconnect delay. Done reconnecting.\");\r\n\r\n            clearTimeout(this._reconnectDelayHandle);\r\n            this._reconnectDelayHandle = undefined;\r\n\r\n            this._completeClose();\r\n            return Promise.resolve();\r\n        }\r\n\r\n        this._cleanupTimeout();\r\n        this._cleanupPingTimer();\r\n        this._stopDuringStartError = error || new AbortError(\"The connection was stopped before the hub handshake could complete.\");\r\n\r\n        // HttpConnection.stop() should not complete until after either HttpConnection.start() fails\r\n        // or the onclose callback is invoked. The onclose callback will transition the HubConnection\r\n        // to the disconnected state if need be before HttpConnection.stop() completes.\r\n        return this.connection.stop(error);\r\n    }\r\n\r\n    /** Invokes a streaming hub method on the server using the specified name and arguments.\r\n     *\r\n     * @typeparam T The type of the items returned by the server.\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {IStreamResult<T>} An object that yields results from the server as they are received.\r\n     */\r\n    public stream<T = any>(methodName: string, ...args: any[]): IStreamResult<T> {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const invocationDescriptor = this._createStreamInvocation(methodName, args, streamIds);\r\n\r\n        // eslint-disable-next-line prefer-const\r\n        let promiseQueue: Promise<void>;\r\n\r\n        const subject = new Subject<T>();\r\n        subject.cancelCallback = () => {\r\n            const cancelInvocation: CancelInvocationMessage = this._createCancelInvocation(invocationDescriptor.invocationId);\r\n\r\n            delete this._callbacks[invocationDescriptor.invocationId];\r\n\r\n            return promiseQueue.then(() => {\r\n                return this._sendWithProtocol(cancelInvocation);\r\n            });\r\n        };\r\n\r\n        this._callbacks[invocationDescriptor.invocationId] = (invocationEvent: CompletionMessage | StreamItemMessage | null, error?: Error) => {\r\n            if (error) {\r\n                subject.error(error);\r\n                return;\r\n            } else if (invocationEvent) {\r\n                // invocationEvent will not be null when an error is not passed to the callback\r\n                if (invocationEvent.type === MessageType.Completion) {\r\n                    if (invocationEvent.error) {\r\n                        subject.error(new Error(invocationEvent.error));\r\n                    } else {\r\n                        subject.complete();\r\n                    }\r\n                } else {\r\n                    subject.next((invocationEvent.item) as T);\r\n                }\r\n            }\r\n        };\r\n\r\n        promiseQueue = this._sendWithProtocol(invocationDescriptor)\r\n            .catch((e) => {\r\n                subject.error(e);\r\n                delete this._callbacks[invocationDescriptor.invocationId];\r\n            });\r\n\r\n        this._launchStreams(streams, promiseQueue);\r\n\r\n        return subject;\r\n    }\r\n\r\n    private _sendMessage(message: any) {\r\n        this._resetKeepAliveInterval();\r\n        return this.connection.send(message);\r\n    }\r\n\r\n    /**\r\n     * Sends a js object to the server.\r\n     * @param message The js object to serialize and send.\r\n     */\r\n    private _sendWithProtocol(message: any) {\r\n        return this._sendMessage(this._protocol.writeMessage(message));\r\n    }\r\n\r\n    /** Invokes a hub method on the server using the specified name and arguments. Does not wait for a response from the receiver.\r\n     *\r\n     * The Promise returned by this method resolves when the client has sent the invocation to the server. The server may still\r\n     * be processing the invocation.\r\n     *\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {Promise<void>} A Promise that resolves when the invocation has been successfully sent, or rejects with an error.\r\n     */\r\n    public send(methodName: string, ...args: any[]): Promise<void> {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const sendPromise = this._sendWithProtocol(this._createInvocation(methodName, args, true, streamIds));\r\n\r\n        this._launchStreams(streams, sendPromise);\r\n\r\n        return sendPromise;\r\n    }\r\n\r\n    /** Invokes a hub method on the server using the specified name and arguments.\r\n     *\r\n     * The Promise returned by this method resolves when the server indicates it has finished invoking the method. When the promise\r\n     * resolves, the server has finished invoking the method. If the server method returns a result, it is produced as the result of\r\n     * resolving the Promise.\r\n     *\r\n     * @typeparam T The expected return type.\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {Promise<T>} A Promise that resolves with the result of the server method (if any), or rejects with an error.\r\n     */\r\n    public invoke<T = any>(methodName: string, ...args: any[]): Promise<T> {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const invocationDescriptor = this._createInvocation(methodName, args, false, streamIds);\r\n\r\n        const p = new Promise<any>((resolve, reject) => {\r\n            // invocationId will always have a value for a non-blocking invocation\r\n            this._callbacks[invocationDescriptor.invocationId!] = (invocationEvent: StreamItemMessage | CompletionMessage | null, error?: Error) => {\r\n                if (error) {\r\n                    reject(error);\r\n                    return;\r\n                } else if (invocationEvent) {\r\n                    // invocationEvent will not be null when an error is not passed to the callback\r\n                    if (invocationEvent.type === MessageType.Completion) {\r\n                        if (invocationEvent.error) {\r\n                            reject(new Error(invocationEvent.error));\r\n                        } else {\r\n                            resolve(invocationEvent.result);\r\n                        }\r\n                    } else {\r\n                        reject(new Error(`Unexpected message type: ${invocationEvent.type}`));\r\n                    }\r\n                }\r\n            };\r\n\r\n            const promiseQueue = this._sendWithProtocol(invocationDescriptor)\r\n                .catch((e) => {\r\n                    reject(e);\r\n                    // invocationId will always have a value for a non-blocking invocation\r\n                    delete this._callbacks[invocationDescriptor.invocationId!];\r\n                });\r\n\r\n            this._launchStreams(streams, promiseQueue);\r\n        });\r\n\r\n        return p;\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the hub method with the specified method name is invoked.\r\n     *\r\n     * @param {string} methodName The name of the hub method to define.\r\n     * @param {Function} newMethod The handler that will be raised when the hub method is invoked.\r\n     */\r\n    public on(methodName: string, newMethod: (...args: any[]) => any): void\r\n    public on(methodName: string, newMethod: (...args: any[]) => void): void {\r\n        if (!methodName || !newMethod) {\r\n            return;\r\n        }\r\n\r\n        methodName = methodName.toLowerCase();\r\n        if (!this._methods[methodName]) {\r\n            this._methods[methodName] = [];\r\n        }\r\n\r\n        // Preventing adding the same handler multiple times.\r\n        if (this._methods[methodName].indexOf(newMethod) !== -1) {\r\n            return;\r\n        }\r\n\r\n        this._methods[methodName].push(newMethod);\r\n    }\r\n\r\n    /** Removes all handlers for the specified hub method.\r\n     *\r\n     * @param {string} methodName The name of the method to remove handlers for.\r\n     */\r\n    public off(methodName: string): void;\r\n\r\n    /** Removes the specified handler for the specified hub method.\r\n     *\r\n     * You must pass the exact same Function instance as was previously passed to {@link @microsoft/signalr.HubConnection.on}. Passing a different instance (even if the function\r\n     * body is the same) will not remove the handler.\r\n     *\r\n     * @param {string} methodName The name of the method to remove handlers for.\r\n     * @param {Function} method The handler to remove. This must be the same Function instance as the one passed to {@link @microsoft/signalr.HubConnection.on}.\r\n     */\r\n    public off(methodName: string, method: (...args: any[]) => void): void;\r\n    public off(methodName: string, method?: (...args: any[]) => void): void {\r\n        if (!methodName) {\r\n            return;\r\n        }\r\n\r\n        methodName = methodName.toLowerCase();\r\n        const handlers = this._methods[methodName];\r\n        if (!handlers) {\r\n            return;\r\n        }\r\n        if (method) {\r\n            const removeIdx = handlers.indexOf(method);\r\n            if (removeIdx !== -1) {\r\n                handlers.splice(removeIdx, 1);\r\n                if (handlers.length === 0) {\r\n                    delete this._methods[methodName];\r\n                }\r\n            }\r\n        } else {\r\n            delete this._methods[methodName];\r\n        }\r\n\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the connection is closed.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection is closed. Optionally receives a single argument containing the error that caused the connection to close (if any).\r\n     */\r\n    public onclose(callback: (error?: Error) => void): void {\r\n        if (callback) {\r\n            this._closedCallbacks.push(callback);\r\n        }\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the connection starts reconnecting.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection starts reconnecting. Optionally receives a single argument containing the error that caused the connection to start reconnecting (if any).\r\n     */\r\n    public onreconnecting(callback: (error?: Error) => void): void {\r\n        if (callback) {\r\n            this._reconnectingCallbacks.push(callback);\r\n        }\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the connection successfully reconnects.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection successfully reconnects.\r\n     */\r\n    public onreconnected(callback: (connectionId?: string) => void): void {\r\n        if (callback) {\r\n            this._reconnectedCallbacks.push(callback);\r\n        }\r\n    }\r\n\r\n    private _processIncomingData(data: any) {\r\n        this._cleanupTimeout();\r\n\r\n        if (!this._receivedHandshakeResponse) {\r\n            data = this._processHandshakeResponse(data);\r\n            this._receivedHandshakeResponse = true;\r\n        }\r\n\r\n        // Data may have all been read when processing handshake response\r\n        if (data) {\r\n            // Parse the messages\r\n            const messages = this._protocol.parseMessages(data, this._logger);\r\n\r\n            for (const message of messages) {\r\n                switch (message.type) {\r\n                    case MessageType.Invocation:\r\n                        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n                        this._invokeClientMethod(message);\r\n                        break;\r\n                    case MessageType.StreamItem:\r\n                    case MessageType.Completion: {\r\n                        const callback = this._callbacks[message.invocationId];\r\n                        if (callback) {\r\n                            if (message.type === MessageType.Completion) {\r\n                                delete this._callbacks[message.invocationId];\r\n                            }\r\n                            try {\r\n                                callback(message);\r\n                            } catch (e) {\r\n                                this._logger.log(LogLevel.Error, `Stream callback threw error: ${getErrorString(e)}`);\r\n                            }\r\n                        }\r\n                        break;\r\n                    }\r\n                    case MessageType.Ping:\r\n                        // Don't care about pings\r\n                        break;\r\n                    case MessageType.Close: {\r\n                        this._logger.log(LogLevel.Information, \"Close message received from server.\");\r\n\r\n                        const error = message.error ? new Error(\"Server returned an error on close: \" + message.error) : undefined;\r\n\r\n                        if (message.allowReconnect === true) {\r\n                            // It feels wrong not to await connection.stop() here, but processIncomingData is called as part of an onreceive callback which is not async,\r\n                            // this is already the behavior for serverTimeout(), and HttpConnection.Stop() should catch and log all possible exceptions.\r\n\r\n                            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n                            this.connection.stop(error);\r\n                        } else {\r\n                            // We cannot await stopInternal() here, but subsequent calls to stop() will await this if stopInternal() is still ongoing.\r\n                            this._stopPromise = this._stopInternal(error);\r\n                        }\r\n\r\n                        break;\r\n                    }\r\n                    default:\r\n                        this._logger.log(LogLevel.Warning, `Invalid message type: ${message.type}.`);\r\n                        break;\r\n                }\r\n            }\r\n        }\r\n\r\n        this._resetTimeoutPeriod();\r\n    }\r\n\r\n    private _processHandshakeResponse(data: any): any {\r\n        let responseMessage: HandshakeResponseMessage;\r\n        let remainingData: any;\r\n\r\n        try {\r\n            [remainingData, responseMessage] = this._handshakeProtocol.parseHandshakeResponse(data);\r\n        } catch (e) {\r\n            const message = \"Error parsing handshake response: \" + e;\r\n            this._logger.log(LogLevel.Error, message);\r\n\r\n            const error = new Error(message);\r\n            this._handshakeRejecter(error);\r\n            throw error;\r\n        }\r\n        if (responseMessage.error) {\r\n            const message = \"Server returned handshake error: \" + responseMessage.error;\r\n            this._logger.log(LogLevel.Error, message);\r\n\r\n            const error = new Error(message);\r\n            this._handshakeRejecter(error);\r\n            throw error;\r\n        } else {\r\n            this._logger.log(LogLevel.Debug, \"Server handshake complete.\");\r\n        }\r\n\r\n        this._handshakeResolver();\r\n        return remainingData;\r\n    }\r\n\r\n    private _resetKeepAliveInterval() {\r\n        if (this.connection.features.inherentKeepAlive) {\r\n            return;\r\n        }\r\n\r\n        // Set the time we want the next keep alive to be sent\r\n        // Timer will be setup on next message receive\r\n        this._nextKeepAlive = new Date().getTime() + this.keepAliveIntervalInMilliseconds;\r\n\r\n        this._cleanupPingTimer();\r\n    }\r\n\r\n    private _resetTimeoutPeriod() {\r\n        if (!this.connection.features || !this.connection.features.inherentKeepAlive) {\r\n            // Set the timeout timer\r\n            this._timeoutHandle = setTimeout(() => this.serverTimeout(), this.serverTimeoutInMilliseconds);\r\n\r\n            // Set keepAlive timer if there isn't one\r\n            if (this._pingServerHandle === undefined)\r\n            {\r\n                let nextPing = this._nextKeepAlive - new Date().getTime();\r\n                if (nextPing < 0) {\r\n                    nextPing = 0;\r\n                }\r\n\r\n                // The timer needs to be set from a networking callback to avoid Chrome timer throttling from causing timers to run once a minute\r\n                this._pingServerHandle = setTimeout(async () => {\r\n                    if (this._connectionState === HubConnectionState.Connected) {\r\n                        try {\r\n                            await this._sendMessage(this._cachedPingMessage);\r\n                        } catch {\r\n                            // We don't care about the error. It should be seen elsewhere in the client.\r\n                            // The connection is probably in a bad or closed state now, cleanup the timer so it stops triggering\r\n                            this._cleanupPingTimer();\r\n                        }\r\n                    }\r\n                }, nextPing);\r\n            }\r\n        }\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private serverTimeout() {\r\n        // The server hasn't talked to us in a while. It doesn't like us anymore ... :(\r\n        // Terminate the connection, but we don't need to wait on the promise. This could trigger reconnecting.\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this.connection.stop(new Error(\"Server timeout elapsed without receiving a message from the server.\"));\r\n    }\r\n\r\n    private async _invokeClientMethod(invocationMessage: InvocationMessage) {\r\n        const methodName = invocationMessage.target.toLowerCase();\r\n        const methods = this._methods[methodName];\r\n        if (!methods) {\r\n            this._logger.log(LogLevel.Warning, `No client method with the name '${methodName}' found.`);\r\n\r\n            // No handlers provided by client but the server is expecting a response still, so we send an error\r\n            if (invocationMessage.invocationId) {\r\n                this._logger.log(LogLevel.Warning, `No result given for '${methodName}' method and invocation ID '${invocationMessage.invocationId}'.`);\r\n                await this._sendWithProtocol(this._createCompletionMessage(invocationMessage.invocationId, \"Client didn't provide a result.\", null));\r\n            }\r\n            return;\r\n        }\r\n\r\n        // Avoid issues with handlers removing themselves thus modifying the list while iterating through it\r\n        const methodsCopy = methods.slice();\r\n\r\n        // Server expects a response\r\n        const expectsResponse = invocationMessage.invocationId ? true : false;\r\n        // We preserve the last result or exception but still call all handlers\r\n        let res;\r\n        let exception;\r\n        let completionMessage;\r\n        for (const m of methodsCopy) {\r\n            try {\r\n                const prevRes = res;\r\n                res = await m.apply(this, invocationMessage.arguments);\r\n                if (expectsResponse && res && prevRes) {\r\n                    this._logger.log(LogLevel.Error, `Multiple results provided for '${methodName}'. Sending error to server.`);\r\n                    completionMessage = this._createCompletionMessage(invocationMessage.invocationId!, `Client provided multiple results.`, null);\r\n                }\r\n                // Ignore exception if we got a result after, the exception will be logged\r\n                exception = undefined;\r\n            } catch (e) {\r\n                exception = e;\r\n                this._logger.log(LogLevel.Error, `A callback for the method '${methodName}' threw error '${e}'.`);\r\n            }\r\n        }\r\n        if (completionMessage) {\r\n            await this._sendWithProtocol(completionMessage);\r\n        } else if (expectsResponse) {\r\n            // If there is an exception that means either no result was given or a handler after a result threw\r\n            if (exception) {\r\n                completionMessage = this._createCompletionMessage(invocationMessage.invocationId!, `${exception}`, null);\r\n            } else if (res !== undefined) {\r\n                completionMessage = this._createCompletionMessage(invocationMessage.invocationId!, null, res);\r\n            } else {\r\n                this._logger.log(LogLevel.Warning, `No result given for '${methodName}' method and invocation ID '${invocationMessage.invocationId}'.`);\r\n                // Client didn't provide a result or throw from a handler, server expects a response so we send an error\r\n                completionMessage = this._createCompletionMessage(invocationMessage.invocationId!, \"Client didn't provide a result.\", null);\r\n            }\r\n            await this._sendWithProtocol(completionMessage);\r\n        } else {\r\n            if (res) {\r\n                this._logger.log(LogLevel.Error, `Result given for '${methodName}' method but server is not expecting a result.`);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _connectionClosed(error?: Error) {\r\n        this._logger.log(LogLevel.Debug, `HubConnection.connectionClosed(${error}) called while in state ${this._connectionState}.`);\r\n\r\n        // Triggering this.handshakeRejecter is insufficient because it could already be resolved without the continuation having run yet.\r\n        this._stopDuringStartError = this._stopDuringStartError || error || new AbortError(\"The underlying connection was closed before the hub handshake could complete.\");\r\n\r\n        // If the handshake is in progress, start will be waiting for the handshake promise, so we complete it.\r\n        // If it has already completed, this should just noop.\r\n        if (this._handshakeResolver) {\r\n            this._handshakeResolver();\r\n        }\r\n\r\n        this._cancelCallbacksWithError(error || new Error(\"Invocation canceled due to the underlying connection being closed.\"));\r\n\r\n        this._cleanupTimeout();\r\n        this._cleanupPingTimer();\r\n\r\n        if (this._connectionState === HubConnectionState.Disconnecting) {\r\n            this._completeClose(error);\r\n        } else if (this._connectionState === HubConnectionState.Connected && this._reconnectPolicy) {\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            this._reconnect(error);\r\n        } else if (this._connectionState === HubConnectionState.Connected) {\r\n            this._completeClose(error);\r\n        }\r\n\r\n        // If none of the above if conditions were true were called the HubConnection must be in either:\r\n        // 1. The Connecting state in which case the handshakeResolver will complete it and stopDuringStartError will fail it.\r\n        // 2. The Reconnecting state in which case the handshakeResolver will complete it and stopDuringStartError will fail the current reconnect attempt\r\n        //    and potentially continue the reconnect() loop.\r\n        // 3. The Disconnected state in which case we're already done.\r\n    }\r\n\r\n    private _completeClose(error?: Error) {\r\n        if (this._connectionStarted) {\r\n            this._connectionState = HubConnectionState.Disconnected;\r\n            this._connectionStarted = false;\r\n\r\n            if (Platform.isBrowser) {\r\n                window.document.removeEventListener(\"freeze\", this._freezeEventListener);\r\n            }\r\n\r\n            try {\r\n                this._closedCallbacks.forEach((c) => c.apply(this, [error]));\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `An onclose callback called with error '${error}' threw error '${e}'.`);\r\n            }\r\n        }\r\n    }\r\n\r\n    private async _reconnect(error?: Error) {\r\n        const reconnectStartTime = Date.now();\r\n        let previousReconnectAttempts = 0;\r\n        let retryError = error !== undefined ? error : new Error(\"Attempting to reconnect due to a unknown error.\");\r\n\r\n        let nextRetryDelay = this._getNextRetryDelay(previousReconnectAttempts++, 0, retryError);\r\n\r\n        if (nextRetryDelay === null) {\r\n            this._logger.log(LogLevel.Debug, \"Connection not reconnecting because the IRetryPolicy returned null on the first reconnect attempt.\");\r\n            this._completeClose(error);\r\n            return;\r\n        }\r\n\r\n        this._connectionState = HubConnectionState.Reconnecting;\r\n\r\n        if (error) {\r\n            this._logger.log(LogLevel.Information, `Connection reconnecting because of error '${error}'.`);\r\n        } else {\r\n            this._logger.log(LogLevel.Information, \"Connection reconnecting.\");\r\n        }\r\n\r\n        if (this._reconnectingCallbacks.length !== 0) {\r\n            try {\r\n                this._reconnectingCallbacks.forEach((c) => c.apply(this, [error]));\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `An onreconnecting callback called with error '${error}' threw error '${e}'.`);\r\n            }\r\n\r\n            // Exit early if an onreconnecting callback called connection.stop().\r\n            if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                this._logger.log(LogLevel.Debug, \"Connection left the reconnecting state in onreconnecting callback. Done reconnecting.\");\r\n                return;\r\n            }\r\n        }\r\n\r\n        while (nextRetryDelay !== null) {\r\n            this._logger.log(LogLevel.Information, `Reconnect attempt number ${previousReconnectAttempts} will start in ${nextRetryDelay} ms.`);\r\n\r\n            await new Promise((resolve) => {\r\n                this._reconnectDelayHandle = setTimeout(resolve, nextRetryDelay!);\r\n            });\r\n            this._reconnectDelayHandle = undefined;\r\n\r\n            if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                this._logger.log(LogLevel.Debug, \"Connection left the reconnecting state during reconnect delay. Done reconnecting.\");\r\n                return;\r\n            }\r\n\r\n            try {\r\n                await this._startInternal();\r\n\r\n                this._connectionState = HubConnectionState.Connected;\r\n                this._logger.log(LogLevel.Information, \"HubConnection reconnected successfully.\");\r\n\r\n                if (this._reconnectedCallbacks.length !== 0) {\r\n                    try {\r\n                        this._reconnectedCallbacks.forEach((c) => c.apply(this, [this.connection.connectionId]));\r\n                    } catch (e) {\r\n                        this._logger.log(LogLevel.Error, `An onreconnected callback called with connectionId '${this.connection.connectionId}; threw error '${e}'.`);\r\n                    }\r\n                }\r\n\r\n                return;\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Information, `Reconnect attempt failed because of error '${e}'.`);\r\n\r\n                if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                    this._logger.log(LogLevel.Debug, `Connection moved to the '${this._connectionState}' from the reconnecting state during reconnect attempt. Done reconnecting.`);\r\n                    // The TypeScript compiler thinks that connectionState must be Connected here. The TypeScript compiler is wrong.\r\n                    if (this._connectionState as any === HubConnectionState.Disconnecting) {\r\n                        this._completeClose();\r\n                    }\r\n                    return;\r\n                }\r\n\r\n                retryError = e instanceof Error ? e : new Error(e.toString());\r\n                nextRetryDelay = this._getNextRetryDelay(previousReconnectAttempts++, Date.now() - reconnectStartTime, retryError);\r\n            }\r\n        }\r\n\r\n        this._logger.log(LogLevel.Information, `Reconnect retries have been exhausted after ${Date.now() - reconnectStartTime} ms and ${previousReconnectAttempts} failed attempts. Connection disconnecting.`);\r\n\r\n        this._completeClose();\r\n    }\r\n\r\n    private _getNextRetryDelay(previousRetryCount: number, elapsedMilliseconds: number, retryReason: Error) {\r\n        try {\r\n            return this._reconnectPolicy!.nextRetryDelayInMilliseconds({\r\n                elapsedMilliseconds,\r\n                previousRetryCount,\r\n                retryReason,\r\n            });\r\n        } catch (e) {\r\n            this._logger.log(LogLevel.Error, `IRetryPolicy.nextRetryDelayInMilliseconds(${previousRetryCount}, ${elapsedMilliseconds}) threw error '${e}'.`);\r\n            return null;\r\n        }\r\n    }\r\n\r\n    private _cancelCallbacksWithError(error: Error) {\r\n        const callbacks = this._callbacks;\r\n        this._callbacks = {};\r\n\r\n        Object.keys(callbacks)\r\n            .forEach((key) => {\r\n                const callback = callbacks[key];\r\n                try {\r\n                    callback(null, error);\r\n                } catch (e) {\r\n                    this._logger.log(LogLevel.Error, `Stream 'error' callback called with '${error}' threw error: ${getErrorString(e)}`);\r\n                }\r\n            });\r\n    }\r\n\r\n    private _cleanupPingTimer(): void {\r\n        if (this._pingServerHandle) {\r\n            clearTimeout(this._pingServerHandle);\r\n            this._pingServerHandle = undefined;\r\n        }\r\n    }\r\n\r\n    private _cleanupTimeout(): void {\r\n        if (this._timeoutHandle) {\r\n            clearTimeout(this._timeoutHandle);\r\n        }\r\n    }\r\n\r\n    private _createInvocation(methodName: string, args: any[], nonblocking: boolean, streamIds: string[]): InvocationMessage {\r\n        if (nonblocking) {\r\n            if (streamIds.length !== 0) {\r\n                return {\r\n                    arguments: args,\r\n                    streamIds,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            } else {\r\n                return {\r\n                    arguments: args,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            }\r\n        } else {\r\n            const invocationId = this._invocationId;\r\n            this._invocationId++;\r\n\r\n            if (streamIds.length !== 0) {\r\n                return {\r\n                    arguments: args,\r\n                    invocationId: invocationId.toString(),\r\n                    streamIds,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            } else {\r\n                return {\r\n                    arguments: args,\r\n                    invocationId: invocationId.toString(),\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            }\r\n        }\r\n    }\r\n\r\n    private _launchStreams(streams: IStreamResult<any>[], promiseQueue: Promise<void>): void {\r\n        if (streams.length === 0) {\r\n            return;\r\n        }\r\n\r\n        // Synchronize stream data so they arrive in-order on the server\r\n        if (!promiseQueue) {\r\n            promiseQueue = Promise.resolve();\r\n        }\r\n\r\n        // We want to iterate over the keys, since the keys are the stream ids\r\n        // eslint-disable-next-line guard-for-in\r\n        for (const streamId in streams) {\r\n            streams[streamId].subscribe({\r\n                complete: () => {\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createCompletionMessage(streamId)));\r\n                },\r\n                error: (err) => {\r\n                    let message: string;\r\n                    if (err instanceof Error) {\r\n                        message = err.message;\r\n                    } else if (err && err.toString) {\r\n                        message = err.toString();\r\n                    } else {\r\n                        message = \"Unknown error\";\r\n                    }\r\n\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createCompletionMessage(streamId, message)));\r\n                },\r\n                next: (item) => {\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createStreamItemMessage(streamId, item)));\r\n                },\r\n            });\r\n        }\r\n    }\r\n\r\n    private _replaceStreamingParams(args: any[]): [IStreamResult<any>[], string[]] {\r\n        const streams: IStreamResult<any>[] = [];\r\n        const streamIds: string[] = [];\r\n        for (let i = 0; i < args.length; i++) {\r\n            const argument = args[i];\r\n            if (this._isObservable(argument)) {\r\n                const streamId = this._invocationId;\r\n                this._invocationId++;\r\n                // Store the stream for later use\r\n                streams[streamId] = argument;\r\n                streamIds.push(streamId.toString());\r\n\r\n                // remove stream from args\r\n                args.splice(i, 1);\r\n            }\r\n        }\r\n\r\n        return [streams, streamIds];\r\n    }\r\n\r\n    private _isObservable(arg: any): arg is IStreamResult<any> {\r\n        // This allows other stream implementations to just work (like rxjs)\r\n        return arg && arg.subscribe && typeof arg.subscribe === \"function\";\r\n    }\r\n\r\n    private _createStreamInvocation(methodName: string, args: any[], streamIds: string[]): StreamInvocationMessage {\r\n        const invocationId = this._invocationId;\r\n        this._invocationId++;\r\n\r\n        if (streamIds.length !== 0) {\r\n            return {\r\n                arguments: args,\r\n                invocationId: invocationId.toString(),\r\n                streamIds,\r\n                target: methodName,\r\n                type: MessageType.StreamInvocation,\r\n            };\r\n        } else {\r\n            return {\r\n                arguments: args,\r\n                invocationId: invocationId.toString(),\r\n                target: methodName,\r\n                type: MessageType.StreamInvocation,\r\n            };\r\n        }\r\n    }\r\n\r\n    private _createCancelInvocation(id: string): CancelInvocationMessage {\r\n        return {\r\n            invocationId: id,\r\n            type: MessageType.CancelInvocation,\r\n        };\r\n    }\r\n\r\n    private _createStreamItemMessage(id: string, item: any): StreamItemMessage {\r\n        return {\r\n            invocationId: id,\r\n            item,\r\n            type: MessageType.StreamItem,\r\n        };\r\n    }\r\n\r\n    private _createCompletionMessage(id: string, error?: any, result?: any): CompletionMessage {\r\n        if (error) {\r\n            return {\r\n                error,\r\n                invocationId: id,\r\n                type: MessageType.Completion,\r\n            };\r\n        }\r\n\r\n        return {\r\n            invocationId: id,\r\n            result,\r\n            type: MessageType.Completion,\r\n        };\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { IStreamResult, IStreamSubscriber, ISubscription } from \"./Stream\";\r\nimport { SubjectSubscription } from \"./Utils\";\r\n\r\n/** Stream implementation to stream items to the server. */\r\nexport class Subject<T> implements IStreamResult<T> {\r\n    /** @internal */\r\n    public observers: IStreamSubscriber<T>[];\r\n\r\n    /** @internal */\r\n    public cancelCallback?: () => Promise<void>;\r\n\r\n    constructor() {\r\n        this.observers = [];\r\n    }\r\n\r\n    public next(item: T): void {\r\n        for (const observer of this.observers) {\r\n            observer.next(item);\r\n        }\r\n    }\r\n\r\n    public error(err: any): void {\r\n        for (const observer of this.observers) {\r\n            if (observer.error) {\r\n                observer.error(err);\r\n            }\r\n        }\r\n    }\r\n\r\n    public complete(): void {\r\n        for (const observer of this.observers) {\r\n            if (observer.complete) {\r\n                observer.complete();\r\n            }\r\n        }\r\n    }\r\n\r\n    public subscribe(observer: IStreamSubscriber<T>): ISubscription<T> {\r\n        this.observers.push(observer);\r\n        return new SubjectSubscription(this, observer);\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { IRetryPolicy, RetryContext } from \"./IRetryPolicy\";\r\n\r\n// 0, 2, 10, 30 second delays before reconnect attempts.\r\nconst DEFAULT_RETRY_DELAYS_IN_MILLISECONDS = [0, 2000, 10000, 30000, null];\r\n\r\n/** @private */\r\nexport class DefaultReconnectPolicy implements IRetryPolicy {\r\n    private readonly _retryDelays: (number | null)[];\r\n\r\n    constructor(retryDelays?: number[]) {\r\n        this._retryDelays = retryDelays !== undefined ? [...retryDelays, null] : DEFAULT_RETRY_DELAYS_IN_MILLISECONDS;\r\n    }\r\n\r\n    public nextRetryDelayInMilliseconds(retryContext: RetryContext): number | null {\r\n        return this._retryDelays[retryContext.previousRetryCount];\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nexport abstract class HeaderNames {\r\n    static readonly Authorization = \"Authorization\";\r\n    static readonly Cookie = \"<PERSON>ie\";\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HeaderNames } from \"./HeaderNames\";\r\nimport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\n\r\n/** @private */\r\nexport class AccessTokenHttpClient extends HttpClient {\r\n    private _innerClient: HttpClient;\r\n    _accessToken: string | undefined;\r\n    _accessTokenFactory: (() => string | Promise<string>) | undefined;\r\n\r\n    constructor(innerClient: HttpClient, accessTokenFactory: (() => string | Promise<string>) | undefined) {\r\n        super();\r\n\r\n        this._innerClient = innerClient;\r\n        this._accessTokenFactory = accessTokenFactory;\r\n    }\r\n\r\n    public async send(request: HttpRequest): Promise<HttpResponse> {\r\n        let allowRetry = true;\r\n        if (this._accessTokenFactory && (!this._accessToken || (request.url && request.url.indexOf(\"/negotiate?\") > 0))) {\r\n            // don't retry if the request is a negotiate or if we just got a potentially new token from the access token factory\r\n            allowRetry = false;\r\n            this._accessToken = await this._accessTokenFactory();\r\n        }\r\n        this._setAuthorizationHeader(request);\r\n        const response = await this._innerClient.send(request);\r\n\r\n        if (allowRetry && response.statusCode === 401 && this._accessTokenFactory) {\r\n            this._accessToken = await this._accessTokenFactory();\r\n            this._setAuthorizationHeader(request);\r\n            return await this._innerClient.send(request);\r\n        }\r\n        return response;\r\n    }\r\n\r\n    private _setAuthorizationHeader(request: HttpRequest) {\r\n        if (!request.headers) {\r\n            request.headers = {};\r\n        }\r\n        if (this._accessToken) {\r\n            request.headers[HeaderNames.Authorization] = `Bearer ${this._accessToken}`\r\n        }\r\n        // don't remove the header if there isn't an access token factory, the user manually added the header in this case\r\n        else if (this._accessTokenFactory) {\r\n            if (request.headers[HeaderNames.Authorization]) {\r\n                delete request.headers[HeaderNames.Authorization];\r\n            }\r\n        }\r\n    }\r\n\r\n    public getCookieString(url: string): string {\r\n        return this._innerClient.getCookieString(url);\r\n    }\r\n}", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// This will be treated as a bit flag in the future, so we keep it using power-of-two values.\r\n/** Specifies a specific HTTP transport type. */\r\nexport enum HttpTransportType {\r\n    /** Specifies no transport preference. */\r\n    None = 0,\r\n    /** Specifies the WebSockets transport. */\r\n    WebSockets = 1,\r\n    /** Specifies the Server-Sent Events transport. */\r\n    ServerSentEvents = 2,\r\n    /** Specifies the Long Polling transport. */\r\n    LongPolling = 4,\r\n}\r\n\r\n/** Specifies the transfer format for a connection. */\r\nexport enum TransferFormat {\r\n    /** Specifies that only text data will be transmitted over the connection. */\r\n    Text = 1,\r\n    /** Specifies that binary data will be transmitted over the connection. */\r\n    Binary = 2,\r\n}\r\n\r\n/** An abstraction over the behavior of transports. This is designed to support the framework and not intended for use by applications. */\r\nexport interface ITransport {\r\n    connect(url: string, transferFormat: TransferFormat): Promise<void>;\r\n    send(data: any): Promise<void>;\r\n    stop(): Promise<void>;\r\n    onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    onclose: ((error?: Error) => void) | null;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Rough polyfill of https://developer.mozilla.org/en-US/docs/Web/API/AbortController\r\n// We don't actually ever use the API being polyfilled, we always use the polyfill because\r\n// it's a very new API right now.\r\n\r\n// Not exported from index.\r\n/** @private */\r\nexport class AbortController implements AbortSignal {\r\n    private _isAborted: boolean = false;\r\n    public onabort: (() => void) | null = null;\r\n\r\n    public abort(): void {\r\n        if (!this._isAborted) {\r\n            this._isAborted = true;\r\n            if (this.onabort) {\r\n                this.onabort();\r\n            }\r\n        }\r\n    }\r\n\r\n    get signal(): AbortSignal {\r\n        return this;\r\n    }\r\n\r\n    get aborted(): boolean {\r\n        return this._isAborted;\r\n    }\r\n}\r\n\r\n/** Represents a signal that can be monitored to determine if a request has been aborted. */\r\nexport interface AbortSignal {\r\n    /** Indicates if the request has been aborted. */\r\n    aborted: boolean;\r\n    /** Set this to a handler that will be invoked when the request is aborted. */\r\n    onabort: (() => void) | null;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AbortController } from \"./AbortController\";\r\nimport { HttpError, TimeoutError } from \"./Errors\";\r\nimport { HttpClient, HttpRequest } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { ITransport, TransferFormat } from \"./ITransport\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, sendMessage } from \"./Utils\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\n\r\n// Not exported from 'index', this type is internal.\r\n/** @private */\r\nexport class LongPollingTransport implements ITransport {\r\n    private readonly _httpClient: HttpClient;\r\n    private readonly _logger: ILogger;\r\n    private readonly _options: IHttpConnectionOptions;\r\n    private readonly _pollAbort: AbortController;\r\n\r\n    private _url?: string;\r\n    private _running: boolean;\r\n    private _receiving?: Promise<void>;\r\n    private _closeError?: Error;\r\n\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((error?: Error) => void) | null;\r\n\r\n    // This is an internal type, not exported from 'index' so this is really just internal.\r\n    public get pollAborted(): boolean {\r\n        return this._pollAbort.aborted;\r\n    }\r\n\r\n    constructor(httpClient: HttpClient, logger: ILogger, options: IHttpConnectionOptions) {\r\n        this._httpClient = httpClient;\r\n        this._logger = logger;\r\n        this._pollAbort = new AbortController();\r\n        this._options = options;\r\n\r\n        this._running = false;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n\r\n    public async connect(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n\r\n        this._url = url;\r\n\r\n        this._logger.log(LogLevel.Trace, \"(LongPolling transport) Connecting.\");\r\n\r\n        // Allow binary format on Node and Browsers that support binary content (indicated by the presence of responseType property)\r\n        if (transferFormat === TransferFormat.Binary &&\r\n            (typeof XMLHttpRequest !== \"undefined\" && typeof new XMLHttpRequest().responseType !== \"string\")) {\r\n            throw new Error(\"Binary protocols over XmlHttpRequest not implementing advanced features are not supported.\");\r\n        }\r\n\r\n        const [name, value] = getUserAgentHeader();\r\n        const headers = { [name]: value, ...this._options.headers };\r\n\r\n        const pollOptions: HttpRequest = {\r\n            abortSignal: this._pollAbort.signal,\r\n            headers,\r\n            timeout: 100000,\r\n            withCredentials: this._options.withCredentials,\r\n        };\r\n\r\n        if (transferFormat === TransferFormat.Binary) {\r\n            pollOptions.responseType = \"arraybuffer\";\r\n        }\r\n\r\n        // Make initial long polling request\r\n        // Server uses first long polling request to finish initializing connection and it returns without data\r\n        const pollUrl = `${url}&_=${Date.now()}`;\r\n        this._logger.log(LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\r\n        const response = await this._httpClient.get(pollUrl, pollOptions);\r\n        if (response.statusCode !== 200) {\r\n            this._logger.log(LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`);\r\n\r\n            // Mark running as false so that the poll immediately ends and runs the close logic\r\n            this._closeError = new HttpError(response.statusText || \"\", response.statusCode);\r\n            this._running = false;\r\n        } else {\r\n            this._running = true;\r\n        }\r\n\r\n        this._receiving = this._poll(this._url, pollOptions);\r\n    }\r\n\r\n    private async _poll(url: string, pollOptions: HttpRequest): Promise<void> {\r\n        try {\r\n            while (this._running) {\r\n                try {\r\n                    const pollUrl = `${url}&_=${Date.now()}`;\r\n                    this._logger.log(LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\r\n                    const response = await this._httpClient.get(pollUrl, pollOptions);\r\n\r\n                    if (response.statusCode === 204) {\r\n                        this._logger.log(LogLevel.Information, \"(LongPolling transport) Poll terminated by server.\");\r\n\r\n                        this._running = false;\r\n                    } else if (response.statusCode !== 200) {\r\n                        this._logger.log(LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`);\r\n\r\n                        // Unexpected status code\r\n                        this._closeError = new HttpError(response.statusText || \"\", response.statusCode);\r\n                        this._running = false;\r\n                    } else {\r\n                        // Process the response\r\n                        if (response.content) {\r\n                            this._logger.log(LogLevel.Trace, `(LongPolling transport) data received. ${getDataDetail(response.content, this._options.logMessageContent!)}.`);\r\n                            if (this.onreceive) {\r\n                                this.onreceive(response.content);\r\n                            }\r\n                        } else {\r\n                            // This is another way timeout manifest.\r\n                            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\r\n                        }\r\n                    }\r\n                } catch (e) {\r\n                    if (!this._running) {\r\n                        // Log but disregard errors that occur after stopping\r\n                        this._logger.log(LogLevel.Trace, `(LongPolling transport) Poll errored after shutdown: ${e.message}`);\r\n                    } else {\r\n                        if (e instanceof TimeoutError) {\r\n                            // Ignore timeouts and reissue the poll.\r\n                            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\r\n                        } else {\r\n                            // Close the connection with the error as the result.\r\n                            this._closeError = e;\r\n                            this._running = false;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        } finally {\r\n            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Polling complete.\");\r\n\r\n            // We will reach here with pollAborted==false when the server returned a response causing the transport to stop.\r\n            // If pollAborted==true then client initiated the stop and the stop method will raise the close event after DELETE is sent.\r\n            if (!this.pollAborted) {\r\n                this._raiseOnClose();\r\n            }\r\n        }\r\n    }\r\n\r\n    public async send(data: any): Promise<void> {\r\n        if (!this._running) {\r\n            return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\r\n        }\r\n        return sendMessage(this._logger, \"LongPolling\", this._httpClient, this._url!, data, this._options);\r\n    }\r\n\r\n    public async stop(): Promise<void> {\r\n        this._logger.log(LogLevel.Trace, \"(LongPolling transport) Stopping polling.\");\r\n\r\n        // Tell receiving loop to stop, abort any current request, and then wait for it to finish\r\n        this._running = false;\r\n        this._pollAbort.abort();\r\n\r\n        try {\r\n            await this._receiving;\r\n\r\n            // Send DELETE to clean up long polling on the server\r\n            this._logger.log(LogLevel.Trace, `(LongPolling transport) sending DELETE request to ${this._url}.`);\r\n\r\n            const headers: {[k: string]: string} = {};\r\n            const [name, value] = getUserAgentHeader();\r\n            headers[name] = value;\r\n\r\n            const deleteOptions: HttpRequest = {\r\n                headers: { ...headers, ...this._options.headers },\r\n                timeout: this._options.timeout,\r\n                withCredentials: this._options.withCredentials,\r\n            };\r\n            await this._httpClient.delete(this._url!, deleteOptions);\r\n\r\n            this._logger.log(LogLevel.Trace, \"(LongPolling transport) DELETE request sent.\");\r\n        } finally {\r\n            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Stop finished.\");\r\n\r\n            // Raise close event here instead of in polling\r\n            // It needs to happen after the DELETE request is sent\r\n            this._raiseOnClose();\r\n        }\r\n    }\r\n\r\n    private _raiseOnClose() {\r\n        if (this.onclose) {\r\n            let logMessage = \"(LongPolling transport) Firing onclose event.\";\r\n            if (this._closeError) {\r\n                logMessage += \" Error: \" + this._closeError;\r\n            }\r\n            this._logger.log(LogLevel.Trace, logMessage);\r\n            this.onclose(this._closeError);\r\n        }\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { MessageHeaders } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { ITransport, TransferFormat } from \"./ITransport\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, Platform, sendMessage } from \"./Utils\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\n\r\n/** @private */\r\nexport class ServerSentEventsTransport implements ITransport {\r\n    private readonly _httpClient: HttpClient;\r\n    private readonly _accessToken: string | undefined;\r\n    private readonly _logger: ILogger;\r\n    private readonly _options: IHttpConnectionOptions;\r\n    private _eventSource?: EventSource;\r\n    private _url?: string;\r\n\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((error?: Error) => void) | null;\r\n\r\n    constructor(httpClient: HttpClient, accessToken: string | undefined, logger: ILogger,\r\n                options: IHttpConnectionOptions) {\r\n        this._httpClient = httpClient;\r\n        this._accessToken = accessToken;\r\n        this._logger = logger;\r\n        this._options = options;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n\r\n    public async connect(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n\r\n        this._logger.log(LogLevel.Trace, \"(SSE transport) Connecting.\");\r\n\r\n        // set url before accessTokenFactory because this._url is only for send and we set the auth header instead of the query string for send\r\n        this._url = url;\r\n\r\n        if (this._accessToken) {\r\n            url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + `access_token=${encodeURIComponent(this._accessToken)}`;\r\n        }\r\n\r\n        return new Promise<void>((resolve, reject) => {\r\n            let opened = false;\r\n            if (transferFormat !== TransferFormat.Text) {\r\n                reject(new Error(\"The Server-Sent Events transport only supports the 'Text' transfer format\"));\r\n                return;\r\n            }\r\n\r\n            let eventSource: EventSource;\r\n            if (Platform.isBrowser || Platform.isWebWorker) {\r\n                eventSource = new this._options.EventSource!(url, { withCredentials: this._options.withCredentials });\r\n            } else {\r\n                // Non-browser passes cookies via the dictionary\r\n                const cookies = this._httpClient.getCookieString(url);\r\n                const headers: MessageHeaders = {};\r\n                headers.Cookie = cookies;\r\n                const [name, value] = getUserAgentHeader();\r\n                headers[name] = value;\r\n\r\n                eventSource = new this._options.EventSource!(url, { withCredentials: this._options.withCredentials, headers: { ...headers, ...this._options.headers} } as EventSourceInit);\r\n            }\r\n\r\n            try {\r\n                eventSource.onmessage = (e: MessageEvent) => {\r\n                    if (this.onreceive) {\r\n                        try {\r\n                            this._logger.log(LogLevel.Trace, `(SSE transport) data received. ${getDataDetail(e.data, this._options.logMessageContent!)}.`);\r\n                            this.onreceive(e.data);\r\n                        } catch (error) {\r\n                            this._close(error);\r\n                            return;\r\n                        }\r\n                    }\r\n                };\r\n\r\n                // @ts-ignore: not using event on purpose\r\n                eventSource.onerror = (e: Event) => {\r\n                    // EventSource doesn't give any useful information about server side closes.\r\n                    if (opened) {\r\n                        this._close();\r\n                    } else {\r\n                        reject(new Error(\"EventSource failed to connect. The connection could not be found on the server,\"\r\n                        + \" either the connection ID is not present on the server, or a proxy is refusing/buffering the connection.\"\r\n                        + \" If you have multiple servers check that sticky sessions are enabled.\"));\r\n                    }\r\n                };\r\n\r\n                eventSource.onopen = () => {\r\n                    this._logger.log(LogLevel.Information, `SSE connected to ${this._url}`);\r\n                    this._eventSource = eventSource;\r\n                    opened = true;\r\n                    resolve();\r\n                };\r\n            } catch (e) {\r\n                reject(e);\r\n                return;\r\n            }\r\n        });\r\n    }\r\n\r\n    public async send(data: any): Promise<void> {\r\n        if (!this._eventSource) {\r\n            return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\r\n        }\r\n        return sendMessage(this._logger, \"SSE\", this._httpClient, this._url!, data, this._options);\r\n    }\r\n\r\n    public stop(): Promise<void> {\r\n        this._close();\r\n        return Promise.resolve();\r\n    }\r\n\r\n    private _close(e?: Error) {\r\n        if (this._eventSource) {\r\n            this._eventSource.close();\r\n            this._eventSource = undefined;\r\n\r\n            if (this.onclose) {\r\n                this.onclose(e);\r\n            }\r\n        }\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HeaderNames } from \"./HeaderNames\";\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { MessageHeaders } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { ITransport, TransferFormat } from \"./ITransport\";\r\nimport { WebSocketConstructor } from \"./Polyfills\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, Platform } from \"./Utils\";\r\n\r\n/** @private */\r\nexport class WebSocketTransport implements ITransport {\r\n    private readonly _logger: ILogger;\r\n    private readonly _accessTokenFactory: (() => string | Promise<string>) | undefined;\r\n    private readonly _logMessageContent: boolean;\r\n    private readonly _webSocketConstructor: WebSocketConstructor;\r\n    private readonly _httpClient: HttpClient;\r\n    private _webSocket?: WebSocket;\r\n    private _headers: MessageHeaders;\r\n\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((error?: Error) => void) | null;\r\n\r\n    constructor(httpClient: HttpClient, accessTokenFactory: (() => string | Promise<string>) | undefined, logger: ILogger,\r\n                logMessageContent: boolean, webSocketConstructor: WebSocketConstructor, headers: MessageHeaders) {\r\n        this._logger = logger;\r\n        this._accessTokenFactory = accessTokenFactory;\r\n        this._logMessageContent = logMessageContent;\r\n        this._webSocketConstructor = webSocketConstructor;\r\n        this._httpClient = httpClient;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n        this._headers = headers;\r\n    }\r\n\r\n    public async connect(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n        this._logger.log(LogLevel.Trace, \"(WebSockets transport) Connecting.\");\r\n\r\n        let token: string;\r\n        if (this._accessTokenFactory) {\r\n            token = await this._accessTokenFactory();\r\n        }\r\n\r\n        return new Promise<void>((resolve, reject) => {\r\n            url = url.replace(/^http/, \"ws\");\r\n            let webSocket: WebSocket | undefined;\r\n            const cookies = this._httpClient.getCookieString(url);\r\n            let opened = false;\r\n\r\n            if (Platform.isNode || Platform.isReactNative) {\r\n                const headers: {[k: string]: string} = {};\r\n                const [name, value] = getUserAgentHeader();\r\n                headers[name] = value;\r\n                if (token) {\r\n                    headers[HeaderNames.Authorization] = `Bearer ${token}`;\r\n                }\r\n\r\n                if (cookies) {\r\n                    headers[HeaderNames.Cookie] = cookies;\r\n                }\r\n\r\n                // Only pass headers when in non-browser environments\r\n                webSocket = new this._webSocketConstructor(url, undefined, {\r\n                    headers: { ...headers, ...this._headers },\r\n                });\r\n            }\r\n            else\r\n            {\r\n                if (token) {\r\n                    url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + `access_token=${encodeURIComponent(token)}`;\r\n                }\r\n            }\r\n\r\n            if (!webSocket) {\r\n                // Chrome is not happy with passing 'undefined' as protocol\r\n                webSocket = new this._webSocketConstructor(url);\r\n            }\r\n\r\n            if (transferFormat === TransferFormat.Binary) {\r\n                webSocket.binaryType = \"arraybuffer\";\r\n            }\r\n\r\n            webSocket.onopen = (_event: Event) => {\r\n                this._logger.log(LogLevel.Information, `WebSocket connected to ${url}.`);\r\n                this._webSocket = webSocket;\r\n                opened = true;\r\n                resolve();\r\n            };\r\n\r\n            webSocket.onerror = (event: Event) => {\r\n                let error: any = null;\r\n                // ErrorEvent is a browser only type we need to check if the type exists before using it\r\n                if (typeof ErrorEvent !== \"undefined\" && event instanceof ErrorEvent) {\r\n                    error = event.error;\r\n                } else {\r\n                    error = \"There was an error with the transport\";\r\n                }\r\n\r\n                this._logger.log(LogLevel.Information, `(WebSockets transport) ${error}.`);\r\n            };\r\n\r\n            webSocket.onmessage = (message: MessageEvent) => {\r\n                this._logger.log(LogLevel.Trace, `(WebSockets transport) data received. ${getDataDetail(message.data, this._logMessageContent)}.`);\r\n                if (this.onreceive) {\r\n                    try {\r\n                        this.onreceive(message.data);\r\n                    } catch (error) {\r\n                        this._close(error);\r\n                        return;\r\n                    }\r\n                }\r\n            };\r\n\r\n            webSocket.onclose = (event: CloseEvent) => {\r\n                // Don't call close handler if connection was never established\r\n                // We'll reject the connect call instead\r\n                if (opened) {\r\n                    this._close(event);\r\n                } else {\r\n                    let error: any = null;\r\n                    // ErrorEvent is a browser only type we need to check if the type exists before using it\r\n                    if (typeof ErrorEvent !== \"undefined\" && event instanceof ErrorEvent) {\r\n                        error = event.error;\r\n                    } else {\r\n                        error = \"WebSocket failed to connect. The connection could not be found on the server,\"\r\n                        + \" either the endpoint may not be a SignalR endpoint,\"\r\n                        + \" the connection ID is not present on the server, or there is a proxy blocking WebSockets.\"\r\n                        + \" If you have multiple servers check that sticky sessions are enabled.\";\r\n                    }\r\n\r\n                    reject(new Error(error));\r\n                }\r\n            };\r\n        });\r\n    }\r\n\r\n    public send(data: any): Promise<void> {\r\n        if (this._webSocket && this._webSocket.readyState === this._webSocketConstructor.OPEN) {\r\n            this._logger.log(LogLevel.Trace, `(WebSockets transport) sending data. ${getDataDetail(data, this._logMessageContent)}.`);\r\n            this._webSocket.send(data);\r\n            return Promise.resolve();\r\n        }\r\n\r\n        return Promise.reject(\"WebSocket is not in the OPEN state\");\r\n    }\r\n\r\n    public stop(): Promise<void> {\r\n        if (this._webSocket) {\r\n            // Manually invoke onclose callback inline so we know the HttpConnection was closed properly before returning\r\n            // This also solves an issue where websocket.onclose could take 18+ seconds to trigger during network disconnects\r\n            this._close(undefined);\r\n        }\r\n\r\n        return Promise.resolve();\r\n    }\r\n\r\n    private _close(event?: CloseEvent | Error): void {\r\n        // webSocket will be null if the transport did not start successfully\r\n        if (this._webSocket) {\r\n            // Clear websocket handlers because we are considering the socket closed now\r\n            this._webSocket.onclose = () => {};\r\n            this._webSocket.onmessage = () => {};\r\n            this._webSocket.onerror = () => {};\r\n            this._webSocket.close();\r\n            this._webSocket = undefined;\r\n        }\r\n\r\n        this._logger.log(LogLevel.Trace, \"(WebSockets transport) socket closed.\");\r\n        if (this.onclose) {\r\n            if (this._isCloseEvent(event) && (event.wasClean === false || event.code !== 1000)) {\r\n                this.onclose(new Error(`WebSocket closed with status code: ${event.code} (${event.reason || \"no reason given\"}).`));\r\n            } else if (event instanceof Error) {\r\n                this.onclose(event);\r\n            } else {\r\n                this.onclose();\r\n            }\r\n        }\r\n    }\r\n\r\n    private _isCloseEvent(event?: any): event is CloseEvent {\r\n        return event && typeof event.wasClean === \"boolean\" && typeof event.code === \"number\";\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AccessTokenHttpClient } from \"./AccessTokenHttpClient\";\r\nimport { DefaultHttpClient } from \"./DefaultHttpClient\";\r\nimport { AggregateErrors, DisabledTransportError, FailedToNegotiateWithServerError, FailedToStartTransportError, HttpError, UnsupportedTransportError, AbortError } from \"./Errors\";\r\nimport { IConnection } from \"./IConnection\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { HttpTransportType, ITransport, TransferFormat } from \"./ITransport\";\r\nimport { LongPollingTransport } from \"./LongPollingTransport\";\r\nimport { ServerSentEventsTransport } from \"./ServerSentEventsTransport\";\r\nimport { Arg, createLogger, getUserAgentHeader, Platform } from \"./Utils\";\r\nimport { WebSocketTransport } from \"./WebSocketTransport\";\r\n\r\n/** @private */\r\nconst enum ConnectionState {\r\n    Connecting = \"Connecting\",\r\n    Connected = \"Connected\",\r\n    Disconnected = \"Disconnected\",\r\n    Disconnecting = \"Disconnecting\",\r\n}\r\n\r\n/** @private */\r\nexport interface INegotiateResponse {\r\n    connectionId?: string;\r\n    connectionToken?: string;\r\n    negotiateVersion?: number;\r\n    availableTransports?: IAvailableTransport[];\r\n    url?: string;\r\n    accessToken?: string;\r\n    error?: string;\r\n}\r\n\r\n/** @private */\r\nexport interface IAvailableTransport {\r\n    transport: keyof typeof HttpTransportType;\r\n    transferFormats: (keyof typeof TransferFormat)[];\r\n}\r\n\r\nconst MAX_REDIRECTS = 100;\r\n\r\n/** @private */\r\nexport class HttpConnection implements IConnection {\r\n    private _connectionState: ConnectionState;\r\n    // connectionStarted is tracked independently from connectionState, so we can check if the\r\n    // connection ever did successfully transition from connecting to connected before disconnecting.\r\n    private _connectionStarted: boolean;\r\n    private readonly _httpClient: AccessTokenHttpClient;\r\n    private readonly _logger: ILogger;\r\n    private readonly _options: IHttpConnectionOptions;\r\n    // Needs to not start with _ to be available for tests\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private transport?: ITransport;\r\n    private _startInternalPromise?: Promise<void>;\r\n    private _stopPromise?: Promise<void>;\r\n    private _stopPromiseResolver: (value?: PromiseLike<void>) => void = () => {};\r\n    private _stopError?: Error;\r\n    private _accessTokenFactory?: () => string | Promise<string>;\r\n    private _sendQueue?: TransportSendQueue;\r\n\r\n    public readonly features: any = {};\r\n    public baseUrl: string;\r\n    public connectionId?: string;\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((e?: Error) => void) | null;\r\n\r\n    private readonly _negotiateVersion: number = 1;\r\n\r\n    constructor(url: string, options: IHttpConnectionOptions = {}) {\r\n        Arg.isRequired(url, \"url\");\r\n\r\n        this._logger = createLogger(options.logger);\r\n        this.baseUrl = this._resolveUrl(url);\r\n\r\n        options = options || {};\r\n        options.logMessageContent = options.logMessageContent === undefined ? false : options.logMessageContent;\r\n        if (typeof options.withCredentials === \"boolean\" || options.withCredentials === undefined) {\r\n            options.withCredentials = options.withCredentials === undefined ? true : options.withCredentials;\r\n        } else {\r\n            throw new Error(\"withCredentials option was not a 'boolean' or 'undefined' value\");\r\n        }\r\n        options.timeout = options.timeout === undefined ? 100 * 1000 : options.timeout;\r\n\r\n        let webSocketModule: any = null;\r\n        let eventSourceModule: any = null;\r\n\r\n        if (Platform.isNode && typeof require !== \"undefined\") {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: TS doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n            webSocketModule = requireFunc(\"ws\");\r\n            eventSourceModule = requireFunc(\"eventsource\");\r\n        }\r\n\r\n        if (!Platform.isNode && typeof WebSocket !== \"undefined\" && !options.WebSocket) {\r\n            options.WebSocket = WebSocket;\r\n        } else if (Platform.isNode && !options.WebSocket) {\r\n            if (webSocketModule) {\r\n                options.WebSocket = webSocketModule;\r\n            }\r\n        }\r\n\r\n        if (!Platform.isNode && typeof EventSource !== \"undefined\" && !options.EventSource) {\r\n            options.EventSource = EventSource;\r\n        } else if (Platform.isNode && !options.EventSource) {\r\n            if (typeof eventSourceModule !== \"undefined\") {\r\n                options.EventSource = eventSourceModule;\r\n            }\r\n        }\r\n\r\n        this._httpClient = new AccessTokenHttpClient(options.httpClient || new DefaultHttpClient(this._logger), options.accessTokenFactory);\r\n        this._connectionState = ConnectionState.Disconnected;\r\n        this._connectionStarted = false;\r\n        this._options = options;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n\r\n    public start(): Promise<void>;\r\n    public start(transferFormat: TransferFormat): Promise<void>;\r\n    public async start(transferFormat?: TransferFormat): Promise<void> {\r\n        transferFormat = transferFormat || TransferFormat.Binary;\r\n\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n\r\n        this._logger.log(LogLevel.Debug, `Starting connection with transfer format '${TransferFormat[transferFormat]}'.`);\r\n\r\n        if (this._connectionState !== ConnectionState.Disconnected) {\r\n            return Promise.reject(new Error(\"Cannot start an HttpConnection that is not in the 'Disconnected' state.\"));\r\n        }\r\n\r\n        this._connectionState = ConnectionState.Connecting;\r\n\r\n        this._startInternalPromise = this._startInternal(transferFormat);\r\n        await this._startInternalPromise;\r\n\r\n        // The TypeScript compiler thinks that connectionState must be Connecting here. The TypeScript compiler is wrong.\r\n        if (this._connectionState as any === ConnectionState.Disconnecting) {\r\n            // stop() was called and transitioned the client into the Disconnecting state.\r\n            const message = \"Failed to start the HttpConnection before stop() was called.\";\r\n            this._logger.log(LogLevel.Error, message);\r\n\r\n            // We cannot await stopPromise inside startInternal since stopInternal awaits the startInternalPromise.\r\n            await this._stopPromise;\r\n\r\n            return Promise.reject(new AbortError(message));\r\n        } else if (this._connectionState as any !== ConnectionState.Connected) {\r\n            // stop() was called and transitioned the client into the Disconnecting state.\r\n            const message = \"HttpConnection.startInternal completed gracefully but didn't enter the connection into the connected state!\";\r\n            this._logger.log(LogLevel.Error, message);\r\n            return Promise.reject(new AbortError(message));\r\n        }\r\n\r\n        this._connectionStarted = true;\r\n    }\r\n\r\n    public send(data: string | ArrayBuffer): Promise<void> {\r\n        if (this._connectionState !== ConnectionState.Connected) {\r\n            return Promise.reject(new Error(\"Cannot send data if the connection is not in the 'Connected' State.\"));\r\n        }\r\n\r\n        if (!this._sendQueue) {\r\n            this._sendQueue = new TransportSendQueue(this.transport!);\r\n        }\r\n\r\n        // Transport will not be null if state is connected\r\n        return this._sendQueue.send(data);\r\n    }\r\n\r\n    public async stop(error?: Error): Promise<void> {\r\n        if (this._connectionState === ConnectionState.Disconnected) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnected state.`);\r\n            return Promise.resolve();\r\n        }\r\n\r\n        if (this._connectionState === ConnectionState.Disconnecting) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnecting state.`);\r\n            return this._stopPromise;\r\n        }\r\n\r\n        this._connectionState = ConnectionState.Disconnecting;\r\n\r\n        this._stopPromise = new Promise((resolve) => {\r\n            // Don't complete stop() until stopConnection() completes.\r\n            this._stopPromiseResolver = resolve;\r\n        });\r\n\r\n        // stopInternal should never throw so just observe it.\r\n        await this._stopInternal(error);\r\n        await this._stopPromise;\r\n    }\r\n\r\n    private async _stopInternal(error?: Error): Promise<void> {\r\n        // Set error as soon as possible otherwise there is a race between\r\n        // the transport closing and providing an error and the error from a close message\r\n        // We would prefer the close message error.\r\n        this._stopError = error;\r\n\r\n        try {\r\n            await this._startInternalPromise;\r\n        } catch (e) {\r\n            // This exception is returned to the user as a rejected Promise from the start method.\r\n        }\r\n\r\n        // The transport's onclose will trigger stopConnection which will run our onclose event.\r\n        // The transport should always be set if currently connected. If it wasn't set, it's likely because\r\n        // stop was called during start() and start() failed.\r\n        if (this.transport) {\r\n            try {\r\n                await this.transport.stop();\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `HttpConnection.transport.stop() threw error '${e}'.`);\r\n                this._stopConnection();\r\n            }\r\n\r\n            this.transport = undefined;\r\n        } else {\r\n            this._logger.log(LogLevel.Debug, \"HttpConnection.transport is undefined in HttpConnection.stop() because start() failed.\");\r\n        }\r\n    }\r\n\r\n    private async _startInternal(transferFormat: TransferFormat): Promise<void> {\r\n        // Store the original base url and the access token factory since they may change\r\n        // as part of negotiating\r\n        let url = this.baseUrl;\r\n        this._accessTokenFactory = this._options.accessTokenFactory;\r\n        this._httpClient._accessTokenFactory = this._accessTokenFactory;\r\n\r\n        try {\r\n            if (this._options.skipNegotiation) {\r\n                if (this._options.transport === HttpTransportType.WebSockets) {\r\n                    // No need to add a connection ID in this case\r\n                    this.transport = this._constructTransport(HttpTransportType.WebSockets);\r\n                    // We should just call connect directly in this case.\r\n                    // No fallback or negotiate in this case.\r\n                    await this._startTransport(url, transferFormat);\r\n                } else {\r\n                    throw new Error(\"Negotiation can only be skipped when using the WebSocket transport directly.\");\r\n                }\r\n            } else {\r\n                let negotiateResponse: INegotiateResponse | null = null;\r\n                let redirects = 0;\r\n\r\n                do {\r\n                    negotiateResponse = await this._getNegotiationResponse(url);\r\n                    // the user tries to stop the connection when it is being started\r\n                    if (this._connectionState === ConnectionState.Disconnecting || this._connectionState === ConnectionState.Disconnected) {\r\n                        throw new AbortError(\"The connection was stopped during negotiation.\");\r\n                    }\r\n\r\n                    if (negotiateResponse.error) {\r\n                        throw new Error(negotiateResponse.error);\r\n                    }\r\n\r\n                    if ((negotiateResponse as any).ProtocolVersion) {\r\n                        throw new Error(\"Detected a connection attempt to an ASP.NET SignalR Server. This client only supports connecting to an ASP.NET Core SignalR Server. See https://aka.ms/signalr-core-differences for details.\");\r\n                    }\r\n\r\n                    if (negotiateResponse.url) {\r\n                        url = negotiateResponse.url;\r\n                    }\r\n\r\n                    if (negotiateResponse.accessToken) {\r\n                        // Replace the current access token factory with one that uses\r\n                        // the returned access token\r\n                        const accessToken = negotiateResponse.accessToken;\r\n                        this._accessTokenFactory = () => accessToken;\r\n                        // set the factory to undefined so the AccessTokenHttpClient won't retry with the same token, since we know it won't change until a connection restart\r\n                        this._httpClient._accessToken = accessToken;\r\n                        this._httpClient._accessTokenFactory = undefined;\r\n                    }\r\n\r\n                    redirects++;\r\n                }\r\n                while (negotiateResponse.url && redirects < MAX_REDIRECTS);\r\n\r\n                if (redirects === MAX_REDIRECTS && negotiateResponse.url) {\r\n                    throw new Error(\"Negotiate redirection limit exceeded.\");\r\n                }\r\n\r\n                await this._createTransport(url, this._options.transport, negotiateResponse, transferFormat);\r\n            }\r\n\r\n            if (this.transport instanceof LongPollingTransport) {\r\n                this.features.inherentKeepAlive = true;\r\n            }\r\n\r\n            if (this._connectionState === ConnectionState.Connecting) {\r\n                // Ensure the connection transitions to the connected state prior to completing this.startInternalPromise.\r\n                // start() will handle the case when stop was called and startInternal exits still in the disconnecting state.\r\n                this._logger.log(LogLevel.Debug, \"The HttpConnection connected successfully.\");\r\n                this._connectionState = ConnectionState.Connected;\r\n            }\r\n\r\n            // stop() is waiting on us via this.startInternalPromise so keep this.transport around so it can clean up.\r\n            // This is the only case startInternal can exit in neither the connected nor disconnected state because stopConnection()\r\n            // will transition to the disconnected state. start() will wait for the transition using the stopPromise.\r\n        } catch (e) {\r\n            this._logger.log(LogLevel.Error, \"Failed to start the connection: \" + e);\r\n            this._connectionState = ConnectionState.Disconnected;\r\n            this.transport = undefined;\r\n\r\n            // if start fails, any active calls to stop assume that start will complete the stop promise\r\n            this._stopPromiseResolver();\r\n            return Promise.reject(e);\r\n        }\r\n    }\r\n\r\n    private async _getNegotiationResponse(url: string): Promise<INegotiateResponse> {\r\n        const headers: {[k: string]: string} = {};\r\n        const [name, value] = getUserAgentHeader();\r\n        headers[name] = value;\r\n\r\n        const negotiateUrl = this._resolveNegotiateUrl(url);\r\n        this._logger.log(LogLevel.Debug, `Sending negotiation request: ${negotiateUrl}.`);\r\n        try {\r\n            const response = await this._httpClient.post(negotiateUrl, {\r\n                content: \"\",\r\n                headers: { ...headers, ...this._options.headers },\r\n                timeout: this._options.timeout,\r\n                withCredentials: this._options.withCredentials,\r\n            });\r\n\r\n            if (response.statusCode !== 200) {\r\n                return Promise.reject(new Error(`Unexpected status code returned from negotiate '${response.statusCode}'`));\r\n            }\r\n\r\n            const negotiateResponse = JSON.parse(response.content as string) as INegotiateResponse;\r\n            if (!negotiateResponse.negotiateVersion || negotiateResponse.negotiateVersion < 1) {\r\n                // Negotiate version 0 doesn't use connectionToken\r\n                // So we set it equal to connectionId so all our logic can use connectionToken without being aware of the negotiate version\r\n                negotiateResponse.connectionToken = negotiateResponse.connectionId;\r\n            }\r\n            return negotiateResponse;\r\n        } catch (e) {\r\n            let errorMessage = \"Failed to complete negotiation with the server: \" + e;\r\n            if (e instanceof HttpError) {\r\n                if (e.statusCode === 404) {\r\n                    errorMessage = errorMessage + \" Either this is not a SignalR endpoint or there is a proxy blocking the connection.\";\r\n                }\r\n            }\r\n            this._logger.log(LogLevel.Error, errorMessage);\r\n\r\n            return Promise.reject(new FailedToNegotiateWithServerError(errorMessage));\r\n        }\r\n    }\r\n\r\n    private _createConnectUrl(url: string, connectionToken: string | null | undefined) {\r\n        if (!connectionToken) {\r\n            return url;\r\n        }\r\n\r\n        return url + (url.indexOf(\"?\") === -1 ? \"?\" : \"&\") + `id=${connectionToken}`;\r\n    }\r\n\r\n    private async _createTransport(url: string, requestedTransport: HttpTransportType | ITransport | undefined, negotiateResponse: INegotiateResponse, requestedTransferFormat: TransferFormat): Promise<void> {\r\n        let connectUrl = this._createConnectUrl(url, negotiateResponse.connectionToken);\r\n        if (this._isITransport(requestedTransport)) {\r\n            this._logger.log(LogLevel.Debug, \"Connection was provided an instance of ITransport, using that directly.\");\r\n            this.transport = requestedTransport;\r\n            await this._startTransport(connectUrl, requestedTransferFormat);\r\n\r\n            this.connectionId = negotiateResponse.connectionId;\r\n            return;\r\n        }\r\n\r\n        const transportExceptions: any[] = [];\r\n        const transports = negotiateResponse.availableTransports || [];\r\n        let negotiate: INegotiateResponse | undefined = negotiateResponse;\r\n        for (const endpoint of transports) {\r\n            const transportOrError = this._resolveTransportOrError(endpoint, requestedTransport, requestedTransferFormat);\r\n            if (transportOrError instanceof Error) {\r\n                // Store the error and continue, we don't want to cause a re-negotiate in these cases\r\n                transportExceptions.push(`${endpoint.transport} failed:`);\r\n                transportExceptions.push(transportOrError);\r\n            } else if (this._isITransport(transportOrError)) {\r\n                this.transport = transportOrError;\r\n                if (!negotiate) {\r\n                    try {\r\n                        negotiate = await this._getNegotiationResponse(url);\r\n                    } catch (ex) {\r\n                        return Promise.reject(ex);\r\n                    }\r\n                    connectUrl = this._createConnectUrl(url, negotiate.connectionToken);\r\n                }\r\n                try {\r\n                    await this._startTransport(connectUrl, requestedTransferFormat);\r\n                    this.connectionId = negotiate.connectionId;\r\n                    return;\r\n                } catch (ex) {\r\n                    this._logger.log(LogLevel.Error, `Failed to start the transport '${endpoint.transport}': ${ex}`);\r\n                    negotiate = undefined;\r\n                    transportExceptions.push(new FailedToStartTransportError(`${endpoint.transport} failed: ${ex}`, HttpTransportType[endpoint.transport]));\r\n\r\n                    if (this._connectionState !== ConnectionState.Connecting) {\r\n                        const message = \"Failed to select transport before stop() was called.\";\r\n                        this._logger.log(LogLevel.Debug, message);\r\n                        return Promise.reject(new AbortError(message));\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        if (transportExceptions.length > 0) {\r\n            return Promise.reject(new AggregateErrors(`Unable to connect to the server with any of the available transports. ${transportExceptions.join(\" \")}`, transportExceptions));\r\n        }\r\n        return Promise.reject(new Error(\"None of the transports supported by the client are supported by the server.\"));\r\n    }\r\n\r\n    private _constructTransport(transport: HttpTransportType): ITransport {\r\n        switch (transport) {\r\n            case HttpTransportType.WebSockets:\r\n                if (!this._options.WebSocket) {\r\n                    throw new Error(\"'WebSocket' is not supported in your environment.\");\r\n                }\r\n                return new WebSocketTransport(this._httpClient, this._accessTokenFactory, this._logger, this._options.logMessageContent!, this._options.WebSocket, this._options.headers || {});\r\n            case HttpTransportType.ServerSentEvents:\r\n                if (!this._options.EventSource) {\r\n                    throw new Error(\"'EventSource' is not supported in your environment.\");\r\n                }\r\n                return new ServerSentEventsTransport(this._httpClient, this._httpClient._accessToken, this._logger, this._options);\r\n            case HttpTransportType.LongPolling:\r\n                return new LongPollingTransport(this._httpClient, this._logger, this._options);\r\n            default:\r\n                throw new Error(`Unknown transport: ${transport}.`);\r\n        }\r\n    }\r\n\r\n    private _startTransport(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        this.transport!.onreceive = this.onreceive;\r\n        this.transport!.onclose = (e) => this._stopConnection(e);\r\n        return this.transport!.connect(url, transferFormat);\r\n    }\r\n\r\n    private _resolveTransportOrError(endpoint: IAvailableTransport, requestedTransport: HttpTransportType | undefined, requestedTransferFormat: TransferFormat): ITransport | Error {\r\n        const transport = HttpTransportType[endpoint.transport];\r\n        if (transport === null || transport === undefined) {\r\n            this._logger.log(LogLevel.Debug, `Skipping transport '${endpoint.transport}' because it is not supported by this client.`);\r\n            return new Error(`Skipping transport '${endpoint.transport}' because it is not supported by this client.`);\r\n        } else {\r\n            if (transportMatches(requestedTransport, transport)) {\r\n                const transferFormats = endpoint.transferFormats.map((s) => TransferFormat[s]);\r\n                if (transferFormats.indexOf(requestedTransferFormat) >= 0) {\r\n                    if ((transport === HttpTransportType.WebSockets && !this._options.WebSocket) ||\r\n                        (transport === HttpTransportType.ServerSentEvents && !this._options.EventSource)) {\r\n                        this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it is not supported in your environment.'`);\r\n                        return new UnsupportedTransportError(`'${HttpTransportType[transport]}' is not supported in your environment.`, transport);\r\n                    } else {\r\n                        this._logger.log(LogLevel.Debug, `Selecting transport '${HttpTransportType[transport]}'.`);\r\n                        try {\r\n                            return this._constructTransport(transport);\r\n                        } catch (ex) {\r\n                            return ex;\r\n                        }\r\n                    }\r\n                } else {\r\n                    this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it does not support the requested transfer format '${TransferFormat[requestedTransferFormat]}'.`);\r\n                    return new Error(`'${HttpTransportType[transport]}' does not support ${TransferFormat[requestedTransferFormat]}.`);\r\n                }\r\n            } else {\r\n                this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it was disabled by the client.`);\r\n                return new DisabledTransportError(`'${HttpTransportType[transport]}' is disabled by the client.`, transport);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _isITransport(transport: any): transport is ITransport {\r\n        return transport && typeof (transport) === \"object\" && \"connect\" in transport;\r\n    }\r\n\r\n    private _stopConnection(error?: Error): void {\r\n        this._logger.log(LogLevel.Debug, `HttpConnection.stopConnection(${error}) called while in state ${this._connectionState}.`);\r\n\r\n        this.transport = undefined;\r\n\r\n        // If we have a stopError, it takes precedence over the error from the transport\r\n        error = this._stopError || error;\r\n        this._stopError = undefined;\r\n\r\n        if (this._connectionState === ConnectionState.Disconnected) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stopConnection(${error}) was ignored because the connection is already in the disconnected state.`);\r\n            return;\r\n        }\r\n\r\n        if (this._connectionState === ConnectionState.Connecting) {\r\n            this._logger.log(LogLevel.Warning, `Call to HttpConnection.stopConnection(${error}) was ignored because the connection is still in the connecting state.`);\r\n            throw new Error(`HttpConnection.stopConnection(${error}) was called while the connection is still in the connecting state.`);\r\n        }\r\n\r\n        if (this._connectionState === ConnectionState.Disconnecting) {\r\n            // A call to stop() induced this call to stopConnection and needs to be completed.\r\n            // Any stop() awaiters will be scheduled to continue after the onclose callback fires.\r\n            this._stopPromiseResolver();\r\n        }\r\n\r\n        if (error) {\r\n            this._logger.log(LogLevel.Error, `Connection disconnected with error '${error}'.`);\r\n        } else {\r\n            this._logger.log(LogLevel.Information, \"Connection disconnected.\");\r\n        }\r\n\r\n        if (this._sendQueue) {\r\n            this._sendQueue.stop().catch((e) => {\r\n                this._logger.log(LogLevel.Error, `TransportSendQueue.stop() threw error '${e}'.`);\r\n            });\r\n            this._sendQueue = undefined;\r\n        }\r\n\r\n        this.connectionId = undefined;\r\n        this._connectionState = ConnectionState.Disconnected;\r\n\r\n        if (this._connectionStarted) {\r\n            this._connectionStarted = false;\r\n            try {\r\n                if (this.onclose) {\r\n                    this.onclose(error);\r\n                }\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `HttpConnection.onclose(${error}) threw error '${e}'.`);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _resolveUrl(url: string): string {\r\n        // startsWith is not supported in IE\r\n        if (url.lastIndexOf(\"https://\", 0) === 0 || url.lastIndexOf(\"http://\", 0) === 0) {\r\n            return url;\r\n        }\r\n\r\n        if (!Platform.isBrowser) {\r\n            throw new Error(`Cannot resolve '${url}'.`);\r\n        }\r\n\r\n        // Setting the url to the href propery of an anchor tag handles normalization\r\n        // for us. There are 3 main cases.\r\n        // 1. Relative path normalization e.g \"b\" -> \"http://localhost:5000/a/b\"\r\n        // 2. Absolute path normalization e.g \"/a/b\" -> \"http://localhost:5000/a/b\"\r\n        // 3. Networkpath reference normalization e.g \"//localhost:5000/a/b\" -> \"http://localhost:5000/a/b\"\r\n        const aTag = window.document.createElement(\"a\");\r\n        aTag.href = url;\r\n\r\n        this._logger.log(LogLevel.Information, `Normalizing '${url}' to '${aTag.href}'.`);\r\n        return aTag.href;\r\n    }\r\n\r\n    private _resolveNegotiateUrl(url: string): string {\r\n        const index = url.indexOf(\"?\");\r\n        let negotiateUrl = url.substring(0, index === -1 ? url.length : index);\r\n        if (negotiateUrl[negotiateUrl.length - 1] !== \"/\") {\r\n            negotiateUrl += \"/\";\r\n        }\r\n        negotiateUrl += \"negotiate\";\r\n        negotiateUrl += index === -1 ? \"\" : url.substring(index);\r\n\r\n        if (negotiateUrl.indexOf(\"negotiateVersion\") === -1) {\r\n            negotiateUrl += index === -1 ? \"?\" : \"&\";\r\n            negotiateUrl += \"negotiateVersion=\" + this._negotiateVersion;\r\n        }\r\n        return negotiateUrl;\r\n    }\r\n}\r\n\r\nfunction transportMatches(requestedTransport: HttpTransportType | undefined, actualTransport: HttpTransportType) {\r\n    return !requestedTransport || ((actualTransport & requestedTransport) !== 0);\r\n}\r\n\r\n/** @private */\r\nexport class TransportSendQueue {\r\n    private _buffer: any[] = [];\r\n    private _sendBufferedData: PromiseSource;\r\n    private _executing: boolean = true;\r\n    private _transportResult?: PromiseSource;\r\n    private _sendLoopPromise: Promise<void>;\r\n\r\n    constructor(private readonly _transport: ITransport) {\r\n        this._sendBufferedData = new PromiseSource();\r\n        this._transportResult = new PromiseSource();\r\n\r\n        this._sendLoopPromise = this._sendLoop();\r\n    }\r\n\r\n    public send(data: string | ArrayBuffer): Promise<void> {\r\n        this._bufferData(data);\r\n        if (!this._transportResult) {\r\n            this._transportResult = new PromiseSource();\r\n        }\r\n        return this._transportResult.promise;\r\n    }\r\n\r\n    public stop(): Promise<void> {\r\n        this._executing = false;\r\n        this._sendBufferedData.resolve();\r\n        return this._sendLoopPromise;\r\n    }\r\n\r\n    private _bufferData(data: string | ArrayBuffer): void {\r\n        if (this._buffer.length && typeof(this._buffer[0]) !== typeof(data)) {\r\n            throw new Error(`Expected data to be of type ${typeof(this._buffer)} but was of type ${typeof(data)}`);\r\n        }\r\n\r\n        this._buffer.push(data);\r\n        this._sendBufferedData.resolve();\r\n    }\r\n\r\n    private async _sendLoop(): Promise<void> {\r\n        while (true) {\r\n            await this._sendBufferedData.promise;\r\n\r\n            if (!this._executing) {\r\n                if (this._transportResult) {\r\n                    this._transportResult.reject(\"Connection stopped.\");\r\n                }\r\n\r\n                break;\r\n            }\r\n\r\n            this._sendBufferedData = new PromiseSource();\r\n\r\n            const transportResult = this._transportResult!;\r\n            this._transportResult = undefined;\r\n\r\n            const data = typeof(this._buffer[0]) === \"string\" ?\r\n                this._buffer.join(\"\") :\r\n                TransportSendQueue._concatBuffers(this._buffer);\r\n\r\n            this._buffer.length = 0;\r\n\r\n            try {\r\n                await this._transport.send(data);\r\n                transportResult.resolve();\r\n            } catch (error) {\r\n                transportResult.reject(error);\r\n            }\r\n        }\r\n    }\r\n\r\n    private static _concatBuffers(arrayBuffers: ArrayBuffer[]): ArrayBuffer {\r\n        const totalLength = arrayBuffers.map((b) => b.byteLength).reduce((a, b) => a + b);\r\n        const result = new Uint8Array(totalLength);\r\n        let offset = 0;\r\n        for (const item of arrayBuffers) {\r\n            result.set(new Uint8Array(item), offset);\r\n            offset += item.byteLength;\r\n        }\r\n\r\n        return result.buffer;\r\n    }\r\n}\r\n\r\nclass PromiseSource {\r\n    private _resolver?: () => void;\r\n    private _rejecter!: (reason?: any) => void;\r\n    public promise: Promise<void>;\r\n\r\n    constructor() {\r\n        this.promise = new Promise((resolve, reject) => [this._resolver, this._rejecter] = [resolve, reject]);\r\n    }\r\n\r\n    public resolve(): void {\r\n        this._resolver!();\r\n    }\r\n\r\n    public reject(reason?: any): void {\r\n        this._rejecter!(reason);\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { CompletionMessage, HubMessage, IHubProtocol, InvocationMessage, MessageType, StreamItemMessage } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { TransferFormat } from \"./ITransport\";\r\nimport { NullLogger } from \"./Loggers\";\r\nimport { TextMessageFormat } from \"./TextMessageFormat\";\r\n\r\nconst JSON_HUB_PROTOCOL_NAME: string = \"json\";\r\n\r\n/** Implements the JSON Hub Protocol. */\r\nexport class JsonHubProtocol implements IHubProtocol {\r\n\r\n    /** @inheritDoc */\r\n    public readonly name: string = JSON_HUB_PROTOCOL_NAME;\r\n    /** @inheritDoc */\r\n    public readonly version: number = 1;\r\n\r\n    /** @inheritDoc */\r\n    public readonly transferFormat: TransferFormat = TransferFormat.Text;\r\n\r\n    /** Creates an array of {@link @microsoft/signalr.HubMessage} objects from the specified serialized representation.\r\n     *\r\n     * @param {string} input A string containing the serialized representation.\r\n     * @param {ILogger} logger A logger that will be used to log messages that occur during parsing.\r\n     */\r\n    public parseMessages(input: string, logger: ILogger): HubMessage[] {\r\n        // The interface does allow \"ArrayBuffer\" to be passed in, but this implementation does not. So let's throw a useful error.\r\n        if (typeof input !== \"string\") {\r\n            throw new Error(\"Invalid input for JSON hub protocol. Expected a string.\");\r\n        }\r\n\r\n        if (!input) {\r\n            return [];\r\n        }\r\n\r\n        if (logger === null) {\r\n            logger = NullLogger.instance;\r\n        }\r\n\r\n        // Parse the messages\r\n        const messages = TextMessageFormat.parse(input);\r\n\r\n        const hubMessages = [];\r\n        for (const message of messages) {\r\n            const parsedMessage = JSON.parse(message) as HubMessage;\r\n            if (typeof parsedMessage.type !== \"number\") {\r\n                throw new Error(\"Invalid payload.\");\r\n            }\r\n            switch (parsedMessage.type) {\r\n                case MessageType.Invocation:\r\n                    this._isInvocationMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.StreamItem:\r\n                    this._isStreamItemMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.Completion:\r\n                    this._isCompletionMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.Ping:\r\n                    // Single value, no need to validate\r\n                    break;\r\n                case MessageType.Close:\r\n                    // All optional values, no need to validate\r\n                    break;\r\n                default:\r\n                    // Future protocol changes can add message types, old clients can ignore them\r\n                    logger.log(LogLevel.Information, \"Unknown message type '\" + parsedMessage.type + \"' ignored.\");\r\n                    continue;\r\n            }\r\n            hubMessages.push(parsedMessage);\r\n        }\r\n\r\n        return hubMessages;\r\n    }\r\n\r\n    /** Writes the specified {@link @microsoft/signalr.HubMessage} to a string and returns it.\r\n     *\r\n     * @param {HubMessage} message The message to write.\r\n     * @returns {string} A string containing the serialized representation of the message.\r\n     */\r\n    public writeMessage(message: HubMessage): string {\r\n        return TextMessageFormat.write(JSON.stringify(message));\r\n    }\r\n\r\n    private _isInvocationMessage(message: InvocationMessage): void {\r\n        this._assertNotEmptyString(message.target, \"Invalid payload for Invocation message.\");\r\n\r\n        if (message.invocationId !== undefined) {\r\n            this._assertNotEmptyString(message.invocationId, \"Invalid payload for Invocation message.\");\r\n        }\r\n    }\r\n\r\n    private _isStreamItemMessage(message: StreamItemMessage): void {\r\n        this._assertNotEmptyString(message.invocationId, \"Invalid payload for StreamItem message.\");\r\n\r\n        if (message.item === undefined) {\r\n            throw new Error(\"Invalid payload for StreamItem message.\");\r\n        }\r\n    }\r\n\r\n    private _isCompletionMessage(message: CompletionMessage): void {\r\n        if (message.result && message.error) {\r\n            throw new Error(\"Invalid payload for Completion message.\");\r\n        }\r\n\r\n        if (!message.result && message.error) {\r\n            this._assertNotEmptyString(message.error, \"Invalid payload for Completion message.\");\r\n        }\r\n\r\n        this._assertNotEmptyString(message.invocationId, \"Invalid payload for Completion message.\");\r\n    }\r\n\r\n    private _assertNotEmptyString(value: any, errorMessage: string): void {\r\n        if (typeof value !== \"string\" || value === \"\") {\r\n            throw new Error(errorMessage);\r\n        }\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { DefaultReconnectPolicy } from \"./DefaultReconnectPolicy\";\r\nimport { HttpConnection } from \"./HttpConnection\";\r\nimport { HubConnection } from \"./HubConnection\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\nimport { IHubProtocol } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { IRetryPolicy } from \"./IRetryPolicy\";\r\nimport { HttpTransportType } from \"./ITransport\";\r\nimport { JsonHubProtocol } from \"./JsonHubProtocol\";\r\nimport { NullLogger } from \"./Loggers\";\r\nimport { Arg, ConsoleLogger } from \"./Utils\";\r\n\r\nconst LogLevelNameMapping: {[k: string]: LogLevel} = {\r\n    trace: LogLevel.Trace,\r\n    debug: LogLevel.Debug,\r\n    info: LogLevel.Information,\r\n    information: LogLevel.Information,\r\n    warn: LogLevel.Warning,\r\n    warning: LogLevel.Warning,\r\n    error: LogLevel.Error,\r\n    critical: LogLevel.Critical,\r\n    none: LogLevel.None,\r\n};\r\n\r\nfunction parseLogLevel(name: string): LogLevel {\r\n    // Case-insensitive matching via lower-casing\r\n    // Yes, I know case-folding is a complicated problem in Unicode, but we only support\r\n    // the ASCII strings defined in LogLevelNameMapping anyway, so it's fine -anurse.\r\n    const mapping = LogLevelNameMapping[name.toLowerCase()];\r\n    if (typeof mapping !== \"undefined\") {\r\n        return mapping;\r\n    } else {\r\n        throw new Error(`Unknown log level: ${name}`);\r\n    }\r\n}\r\n\r\n/** A builder for configuring {@link @microsoft/signalr.HubConnection} instances. */\r\nexport class HubConnectionBuilder {\r\n    /** @internal */\r\n    public protocol?: IHubProtocol;\r\n    /** @internal */\r\n    public httpConnectionOptions?: IHttpConnectionOptions;\r\n    /** @internal */\r\n    public url?: string;\r\n    /** @internal */\r\n    public logger?: ILogger;\r\n\r\n    /** If defined, this indicates the client should automatically attempt to reconnect if the connection is lost. */\r\n    /** @internal */\r\n    public reconnectPolicy?: IRetryPolicy;\r\n\r\n    /** Configures console logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {LogLevel} logLevel The minimum level of messages to log. Anything at this level, or a more severe level, will be logged.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public configureLogging(logLevel: LogLevel): HubConnectionBuilder;\r\n\r\n    /** Configures custom logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {ILogger} logger An object implementing the {@link @microsoft/signalr.ILogger} interface, which will be used to write all log messages.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public configureLogging(logger: ILogger): HubConnectionBuilder;\r\n\r\n    /** Configures custom logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {string} logLevel A string representing a LogLevel setting a minimum level of messages to log.\r\n     *    See {@link https://docs.microsoft.com/aspnet/core/signalr/configuration#configure-logging|the documentation for client logging configuration} for more details.\r\n     */\r\n    public configureLogging(logLevel: string): HubConnectionBuilder;\r\n\r\n    /** Configures custom logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {LogLevel | string | ILogger} logging A {@link @microsoft/signalr.LogLevel}, a string representing a LogLevel, or an object implementing the {@link @microsoft/signalr.ILogger} interface.\r\n     *    See {@link https://docs.microsoft.com/aspnet/core/signalr/configuration#configure-logging|the documentation for client logging configuration} for more details.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public configureLogging(logging: LogLevel | string | ILogger): HubConnectionBuilder;\r\n    public configureLogging(logging: LogLevel | string | ILogger): HubConnectionBuilder {\r\n        Arg.isRequired(logging, \"logging\");\r\n\r\n        if (isLogger(logging)) {\r\n            this.logger = logging;\r\n        } else if (typeof logging === \"string\") {\r\n            const logLevel = parseLogLevel(logging);\r\n            this.logger = new ConsoleLogger(logLevel);\r\n        } else {\r\n            this.logger = new ConsoleLogger(logging);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use HTTP-based transports to connect to the specified URL.\r\n     *\r\n     * The transport will be selected automatically based on what the server and client support.\r\n     *\r\n     * @param {string} url The URL the connection will use.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public withUrl(url: string): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use the specified HTTP-based transport to connect to the specified URL.\r\n     *\r\n     * @param {string} url The URL the connection will use.\r\n     * @param {HttpTransportType} transportType The specific transport to use.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public withUrl(url: string, transportType: HttpTransportType): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use HTTP-based transports to connect to the specified URL.\r\n     *\r\n     * @param {string} url The URL the connection will use.\r\n     * @param {IHttpConnectionOptions} options An options object used to configure the connection.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public withUrl(url: string, options: IHttpConnectionOptions): HubConnectionBuilder;\r\n    public withUrl(url: string, transportTypeOrOptions?: IHttpConnectionOptions | HttpTransportType): HubConnectionBuilder {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isNotEmpty(url, \"url\");\r\n\r\n        this.url = url;\r\n\r\n        // Flow-typing knows where it's at. Since HttpTransportType is a number and IHttpConnectionOptions is guaranteed\r\n        // to be an object, we know (as does TypeScript) this comparison is all we need to figure out which overload was called.\r\n        if (typeof transportTypeOrOptions === \"object\") {\r\n            this.httpConnectionOptions = { ...this.httpConnectionOptions, ...transportTypeOrOptions };\r\n        } else {\r\n            this.httpConnectionOptions = {\r\n                ...this.httpConnectionOptions,\r\n                transport: transportTypeOrOptions,\r\n            };\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use the specified Hub Protocol.\r\n     *\r\n     * @param {IHubProtocol} protocol The {@link @microsoft/signalr.IHubProtocol} implementation to use.\r\n     */\r\n    public withHubProtocol(protocol: IHubProtocol): HubConnectionBuilder {\r\n        Arg.isRequired(protocol, \"protocol\");\r\n\r\n        this.protocol = protocol;\r\n        return this;\r\n    }\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to automatically attempt to reconnect if the connection is lost.\r\n     * By default, the client will wait 0, 2, 10 and 30 seconds respectively before trying up to 4 reconnect attempts.\r\n     */\r\n    public withAutomaticReconnect(): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to automatically attempt to reconnect if the connection is lost.\r\n     *\r\n     * @param {number[]} retryDelays An array containing the delays in milliseconds before trying each reconnect attempt.\r\n     * The length of the array represents how many failed reconnect attempts it takes before the client will stop attempting to reconnect.\r\n     */\r\n    public withAutomaticReconnect(retryDelays: number[]): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to automatically attempt to reconnect if the connection is lost.\r\n     *\r\n     * @param {IRetryPolicy} reconnectPolicy An {@link @microsoft/signalR.IRetryPolicy} that controls the timing and number of reconnect attempts.\r\n     */\r\n    public withAutomaticReconnect(reconnectPolicy: IRetryPolicy): HubConnectionBuilder;\r\n    public withAutomaticReconnect(retryDelaysOrReconnectPolicy?: number[] | IRetryPolicy): HubConnectionBuilder {\r\n        if (this.reconnectPolicy) {\r\n            throw new Error(\"A reconnectPolicy has already been set.\");\r\n        }\r\n\r\n        if (!retryDelaysOrReconnectPolicy) {\r\n            this.reconnectPolicy = new DefaultReconnectPolicy();\r\n        } else if (Array.isArray(retryDelaysOrReconnectPolicy)) {\r\n            this.reconnectPolicy = new DefaultReconnectPolicy(retryDelaysOrReconnectPolicy);\r\n        } else {\r\n            this.reconnectPolicy = retryDelaysOrReconnectPolicy;\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /** Creates a {@link @microsoft/signalr.HubConnection} from the configuration options specified in this builder.\r\n     *\r\n     * @returns {HubConnection} The configured {@link @microsoft/signalr.HubConnection}.\r\n     */\r\n    public build(): HubConnection {\r\n        // If httpConnectionOptions has a logger, use it. Otherwise, override it with the one\r\n        // provided to configureLogger\r\n        const httpConnectionOptions = this.httpConnectionOptions || {};\r\n\r\n        // If it's 'null', the user **explicitly** asked for null, don't mess with it.\r\n        if (httpConnectionOptions.logger === undefined) {\r\n            // If our logger is undefined or null, that's OK, the HttpConnection constructor will handle it.\r\n            httpConnectionOptions.logger = this.logger;\r\n        }\r\n\r\n        // Now create the connection\r\n        if (!this.url) {\r\n            throw new Error(\"The 'HubConnectionBuilder.withUrl' method must be called before building the connection.\");\r\n        }\r\n        const connection = new HttpConnection(this.url, httpConnectionOptions);\r\n\r\n        return HubConnection.create(\r\n            connection,\r\n            this.logger || NullLogger.instance,\r\n            this.protocol || new JsonHubProtocol(),\r\n            this.reconnectPolicy);\r\n    }\r\n}\r\n\r\nfunction isLogger(logger: any): logger is ILogger {\r\n    return logger.log !== undefined;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// This is where we add any polyfills we'll need for the browser. It is the entry module for browser-specific builds.\r\n\r\n// Copy from Array.prototype into Uint8Array to polyfill on IE. It's OK because the implementations of indexOf and slice use properties\r\n// that exist on Uint8Array with the same name, and JavaScript is magic.\r\n// We make them 'writable' because the Buffer polyfill messes with it as well.\r\nif (!Uint8Array.prototype.indexOf) {\r\n    Object.defineProperty(Uint8Array.prototype, \"indexOf\", {\r\n        value: Array.prototype.indexOf,\r\n        writable: true,\r\n    });\r\n}\r\nif (!Uint8Array.prototype.slice) {\r\n    Object.defineProperty(Uint8Array.prototype, \"slice\", {\r\n        // wrap the slice in Uint8Array so it looks like a Uint8Array.slice call\r\n        // eslint-disable-next-line object-shorthand\r\n        value: function(start?: number, end?: number) { return new Uint8Array(Array.prototype.slice.call(this, start, end)); },\r\n        writable: true,\r\n    });\r\n}\r\nif (!Uint8Array.prototype.forEach) {\r\n    Object.defineProperty(Uint8Array.prototype, \"forEach\", {\r\n        value: Array.prototype.forEach,\r\n        writable: true,\r\n    });\r\n}\r\n\r\nexport * from \"./index\";\r\n"], "names": ["root", "factory", "self", "__webpack_require__", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "g", "globalThis", "this", "Function", "e", "window", "obj", "prop", "prototype", "hasOwnProperty", "call", "r", "Symbol", "toStringTag", "value", "LogLevel", "HttpError", "Error", "constructor", "errorMessage", "statusCode", "trueProto", "super", "__proto__", "TimeoutError", "AbortError", "UnsupportedTransportError", "message", "transport", "errorType", "DisabledTransportError", "FailedToStartTransportError", "FailedToNegotiateWithServerError", "AggregateErrors", "innerErrors", "HttpResponse", "statusText", "content", "HttpClient", "url", "options", "send", "method", "post", "delete", "getCookieString", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "log", "_logLevel", "_message", "instance", "VERSION", "Arg", "static", "val", "name", "match", "values", "Platform", "<PERSON><PERSON><PERSON><PERSON>", "document", "isWebWorker", "isReactNative", "isNode", "getDataDetail", "data", "<PERSON><PERSON><PERSON><PERSON>", "detail", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "byteLength", "view", "Uint8Array", "str", "for<PERSON>ach", "num", "toString", "substr", "length", "formatA<PERSON>y<PERSON>uffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "async", "sendMessage", "logger", "transportName", "httpClient", "headers", "getUserAgentHeader", "Trace", "logMessageContent", "responseType", "response", "timeout", "withCredentials", "SubjectSubscription", "subject", "observer", "_subject", "_observer", "dispose", "index", "observers", "indexOf", "splice", "cancelCallback", "catch", "_", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minimumLogLevel", "_minLevel", "out", "console", "logLevel", "msg", "Date", "toISOString", "Critical", "error", "Warning", "warn", "Information", "info", "userAgentHeaderName", "constructUserAgent", "getOsName", "getRuntimeVersion", "version", "os", "runtime", "runtimeVersion", "userAgent", "majorAndMinor", "split", "process", "platform", "versions", "node", "getErrorString", "stack", "FetchHttpClient", "_logger", "fetch", "requireFunc", "_jar", "<PERSON><PERSON><PERSON><PERSON>", "_fetchType", "bind", "getGlobalThis", "AbortController", "_abortControllerType", "request", "abortSignal", "aborted", "abortController", "<PERSON>ab<PERSON>", "abort", "timeoutId", "msTimeout", "setTimeout", "undefined", "body", "cache", "credentials", "mode", "redirect", "signal", "clearTimeout", "ok", "deserializeContent", "status", "payload", "cookies", "getCookies", "c", "join", "arrayBuffer", "text", "XhrHttpClient", "Promise", "reject", "resolve", "xhr", "XMLHttpRequest", "open", "setRequestHeader", "keys", "header", "onload", "responseText", "onerror", "ontimeout", "DefaultHttpClient", "_httpClient", "TextMessageFormat", "output", "RecordSeparator", "input", "messages", "pop", "RecordSeparatorCode", "String", "fromCharCode", "HandshakeProtocol", "writeHandshakeRequest", "handshakeRequest", "write", "JSON", "stringify", "parseHandshakeResponse", "messageData", "remainingData", "binaryData", "separatorIndex", "responseLength", "apply", "Array", "slice", "buffer", "textData", "substring", "parse", "type", "MessageType", "HubConnectionState", "Subject", "next", "item", "err", "complete", "subscribe", "push", "HubConnection", "connection", "protocol", "reconnectPolicy", "_nextKeepAlive", "_freezeEventListener", "isRequired", "serverTimeoutInMilliseconds", "keepAliveIntervalInMilliseconds", "_protocol", "_reconnectPolicy", "_handshakeProtocol", "onreceive", "_processIncomingData", "onclose", "_connectionClosed", "_callbacks", "_methods", "_closedCallbacks", "_reconnectingCallbacks", "_reconnectedCallbacks", "_invocationId", "_receivedHandshakeResponse", "_connectionState", "Disconnected", "_connectionStarted", "_cachedPingMessage", "writeMessage", "<PERSON>", "state", "connectionId", "baseUrl", "Reconnecting", "start", "_startPromise", "_startWithStateTransitions", "Connecting", "Debug", "_startInternal", "addEventListener", "Connected", "_stopDuringStartError", "handshakePromise", "_handshakeResolver", "_handshake<PERSON><PERSON><PERSON><PERSON>", "transferFormat", "_sendMessage", "_cleanupTimeout", "_resetTimeoutPeriod", "_resetKeepAliveInterval", "features", "inherentKeepAlive", "_cleanupPingTimer", "stop", "startPromise", "_stopPromise", "_stopInternal", "Disconnecting", "_reconnectDelayHandle", "_completeClose", "stream", "methodName", "args", "streams", "streamIds", "_replaceStreamingParams", "invocationDescriptor", "_createStreamInvocation", "promiseQueue", "cancelInvocation", "_createCancelInvocation", "invocationId", "then", "_sendWithProtocol", "invocationEvent", "Completion", "_launchStreams", "sendPromise", "_createInvocation", "invoke", "result", "on", "newMethod", "toLowerCase", "off", "handlers", "removeIdx", "callback", "onreconnecting", "onreconnected", "_processHandshakeResponse", "parseMessages", "Invocation", "_invokeClientMethod", "StreamItem", "Close", "allowReconnect", "responseMessage", "getTime", "_timeoutHandle", "serverTimeout", "_pingServerHandle", "nextPing", "invocationMessage", "target", "methods", "_createCompletionMessage", "methodsCopy", "expectsResponse", "res", "exception", "completionMessage", "m", "prevRes", "arguments", "_cancelCallbacksWithError", "_reconnect", "removeEventListener", "reconnectStartTime", "now", "previousReconnectAttempts", "retryError", "nextRetryDelay", "_getNextRetryDelay", "previousRetryCount", "elapsedMilliseconds", "retryReason", "nextRetryDelayInMilliseconds", "callbacks", "nonblocking", "streamId", "_createStreamItemMessage", "i", "argument", "_isObservable", "arg", "StreamInvocation", "id", "CancelInvocation", "DEFAULT_RETRY_DELAYS_IN_MILLISECONDS", "DefaultReconnectPolicy", "re<PERSON><PERSON><PERSON><PERSON>", "_retryD<PERSON>ys", "retryContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Authorization", "<PERSON><PERSON>", "AccessTokenHttpClient", "innerClient", "accessTokenFactory", "_innerClient", "_accessTokenFactory", "allowRetry", "_accessToken", "_setAuthorizationHeader", "HttpTransportType", "TransferFormat", "_isAborted", "LongPollingTransport", "_pollAbort", "_options", "_running", "pollAborted", "isIn", "_url", "Binary", "pollOptions", "pollUrl", "_closeError", "_receiving", "_poll", "_raiseOnClose", "deleteOptions", "logMessage", "ServerSentEventsTransport", "accessToken", "encodeURIComponent", "eventSource", "opened", "Text", "EventSource", "onmessage", "_close", "onopen", "_eventSource", "close", "WebSocketTransport", "webSocketConstructor", "_logMessageContent", "_webSocketConstructor", "_headers", "token", "webSocket", "replace", "binaryType", "_event", "_webSocket", "event", "ErrorEvent", "readyState", "OPEN", "_isCloseEvent", "<PERSON><PERSON><PERSON>", "code", "reason", "HttpConnection", "_stopPromiseResolver", "_negotiateVersion", "_resolveUrl", "webSocketModule", "eventSourceModule", "WebSocket", "_startInternalPromise", "_sendQueue", "TransportSendQueue", "_stopError", "_stopConnection", "skipNegotiation", "WebSockets", "_constructTransport", "_startTransport", "negotiateResponse", "redirects", "_getNegotiationResponse", "ProtocolVersion", "_createTransport", "negotiateUrl", "_resolveNegotiateUrl", "negotiateVersion", "connectionToken", "_createConnectUrl", "requestedTransport", "requestedTransferFormat", "connectUrl", "_isITransport", "transportExceptions", "transports", "availableTransports", "negotiate", "endpoint", "transportOrError", "_resolveTransportOrError", "ex", "ServerSentEvents", "LongPolling", "connect", "actualTransport", "transportMatches", "transferFormats", "map", "s", "lastIndexOf", "aTag", "createElement", "href", "_transport", "_buffer", "_executing", "_sendBufferedData", "PromiseSource", "_transportResult", "_sendLoopPromise", "_sendLoop", "_bufferData", "promise", "transportResult", "_concatBuffers", "arrayBuffers", "totalLength", "b", "reduce", "a", "offset", "set", "_resolver", "_rejecter", "JsonHubProtocol", "hubMessages", "parsedMessage", "_isInvocationMessage", "_isStreamItemMessage", "_isCompletionMessage", "_assertNotEmptyString", "LogLevelNameMapping", "trace", "debug", "information", "warning", "critical", "none", "None", "HubConnectionBuilder", "configureLogging", "logging", "mapping", "parseLogLevel", "withUrl", "transportTypeOrOptions", "isNotEmpty", "httpConnectionOptions", "withHubProtocol", "withAutomaticReconnect", "retryDelaysOrReconnectPolicy", "isArray", "build", "create", "writable", "end", "module", "define", "amd"], "sourceRoot": ""}