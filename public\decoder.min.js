class Decoder{constructor(){this.wasmLoaded=!1,this.codecType=0,this.width=1920,this.height=1080,this.url="",this.cacheBuffer=null,this.videoCallback=null,this.webSocket=null,this.pause=!1}onWasmLoaded(){this.wasmLoaded||(this.wasmLoaded=!0,this.openDecoder(),this.connectVideoServer())}openDecoder(){if(this.wasmLoaded){this.videoCallback=Module.addFunction((function(addr_y,addr_u,addr_v,stride_y,stride_u,stride_v,width,height){console.log("In video callback, size = %d * %d",width,height);let out_y=HEAPU8.subarray(addr_y,addr_y+stride_y*height),out_u=HEAPU8.subarray(addr_u,addr_u+stride_u*height/2),out_v=HEAPU8.subarray(addr_v,addr_v+stride_v*height/2),buf_y,buf_u,buf_v;var y={dtype:0,ydata:new Uint8Array(out_y),udata:new Uint8Array(out_u),vdata:new Uint8Array(out_v),width:width,height:height,time:self.decoder.decoderTime};self.postMessage(y)}),"viiiiiiii");var ret=Module._openDecoder(this.codecType,this.videoCallback,1);0==ret?console.log("open decoder success"):console.error("open decoder failed with error",ret)}else console.log("[open] decoder wasm not loaded")}processDecoder(i){if(this.wasmLoaded){console.log("[receive data] "+i.length);var t=Module._malloc(i.length);Module.HEAPU8.set(i,t),Module._decodeData(t,i.length),null!=t&&(Module._free(t),t=null)}else console.log("[process] decoder wasm not loaded")}closeDecoder(){this.wasmLoaded?(this.webSocket&&this.webSocket.onclose(),Module._flushDecoder(),Module._closeDecoder(),this.wasmLoaded=!1,this.cacheBuffer=null):console.log("[close] decoder wasm not loaded")}connectVideoServer(){this.webSocket=new WebSocket(this.url),this.webSocket.binaryType="arraybuffer",this.webSocket.onmessage=function(n){var u=new Uint8Array(n.data);if(!(u.length<2)){var d=!1;if(0==u[0]&&1==u[1]||(d=!0),d){var t,i;self.decoder.pause||(t=Date.now(),self.decoder.processDecoder(u),i=Date.now(),self.decoder.decoderTime=i-t)}else{var b=u.slice(2,u.length-2),l,y={dtype:1,url:self.decoder.binary2str(b)};self.postMessage(y)}}},this.webSocket.onopen=function(){console.log("[video url] connect success")},this.webSocket.onclose=function(){console.log("[video url] connect closed")},this.webSocket.onerror=function(){console.log("[video url] connect error!")}}startRecord(){this.wasmLoaded&&this.webSocket&&(this.webSocket.readyState==WebSocket.OPEN?(this.webSocket.send(1),console.log("start record success")):console.log("[ERROR] start record failed, connect closed"))}stopRecord(){this.wasmLoaded&&this.webSocket&&(this.webSocket.readyState==WebSocket.OPEN?(this.webSocket.send(2),console.log("stop record success")):console.log("[ERROR] stop record failed, connect closed"))}binary2str(b){var r=[];for(let i=0;i<b.length;i++){let c=String.fromCharCode(b[i]);r.push(c)}return r.join("")}}function onWasmLoaded(){self.decoder?self.decoder.onWasmLoaded():console.log("[ERROR] load wasm failed!")}self.Module={onRuntimeInitialized:function(){onWasmLoaded()}},self.importScripts("libffmpeg.js"),self.decoder=new Decoder,self.onmessage=function(n){switch(n.data.type){case 0:console.log("init decode with video url:"+n.data.url),self.decoder.url=n.data.url,self.decoder.codecType=n.data.codecType,self.decoder.width=n.data.width,self.decoder.height=n.data.height;break;case 1:console.log("uninit decode"),self.decoder.closeDecoder();break;case 2:console.log("pause decode"),self.decoder.pause=!0;break;case 3:console.log("resume decode"),self.decoder.pause=!1;case 4:console.log("strat record"),self.decoder.startRecord();break;case 5:console.log("stop record"),self.decoder.stopRecord()}};