class Player {
  constructor() {
    this.activityChange();
  }
  initDecodeWorker() {
    var n = this;
    (this.decode<PERSON><PERSON><PERSON> = new Worker(this.baseUrl + "/decoder.min.js")),
      (this.decodeWorker.onmessage = function (t) {
        0 == t.data.dtype
          ? n.bufferLength <= 0
            ? n.renderFrame2(
                t.data.ydata,
                t.data.udata,
                t.data.vdata,
                t.data.width,
                t.data.height
              )
            : n.videoBuffer.push(t)
          : n.download(t.data.url);
      }),
      this.decodeWorker.postMessage({
        type: 0,
        codecType: this.codecType,
        width: this.videoWidth,
        height: this.videoHeight,
        url: this.videoUrl,
      });
  }
  download(link) {
    let date = new Date(),
      y,
      m,
      d,
      h,
      min,
      s,
      ms,
      f =
        date.getFullYear() +
        "_" +
        (date.getMonth() + 1) +
        "_" +
        date.getDate() +
        "_" +
        date.getHours() +
        "_" +
        date.getMinutes() +
        "_" +
        date.getSeconds() +
        "_" +
        date.getMilliseconds() +
        ".mp4";
    fetch(link)
      .then((r) => r.blob())
      .then((blob) => {
        console.log("download onload", link);
        let url = window.URL.createObjectURL(blob),
          a = document.createElement("a");
        (a.href = url),
          (a.download = f),
          a.click(),
          window.URL.revokeObjectURL(url),
          a.remove();
      });
  }
  play(n) {
    (this.canvas = n.canvas),
      (this.videoWidth = n.videoWidth),
      (this.videoHeight = n.videoHeight),
      (this.canvasWidth = n.canvas.width),
      (this.canvasHeight = n.canvas.height),
      (this.codecType = n.codecType),
      (this.videoUrl = n.videoUrl),
      (this.baseUrl = n.baseUrl),
      (this.bufferLength = n.bufferLength ? n.bufferLength : 0),
      (this.videoBuffer = []),
      (this.renderTimer = null),
      (this.startPlay = !0),
      this.bufferLength > 0 && this.renderBuffer(),
      this.initRender(),
      this.initDecodeWorker();
  }
  stop() {
    this.decodeWorker && this.decodeWorker.postMessage({ type: 1 }),
      this.clearFrame(),
      this.decodeWorker.terminate(),
      (this.startPlay = !1),
      (this.videoBuffer.length = 0);
  }
  pause() {
    this.decodeWorker && this.decodeWorker.postMessage({ type: 2 });
  }
  resume() {
    this.decodeWorker && this.decodeWorker.postMessage({ type: 3 });
  }
  startRecord() {
    this.decodeWorker && this.decodeWorker.postMessage({ type: 4 });
  }
  stopRecord() {
    this.decodeWorker && this.decodeWorker.postMessage({ type: 5 });
  }
  activityChange() {
    var n = this,
      t =
        "hidden" in document
          ? "hidden"
          : "webkitHidden" in document
          ? "webkitHidden"
          : "mozHidden" in document
          ? "mozHidden"
          : null,
      i = t.replace(/hidden/i, "visibilitychange"),
      r = function () {
        document[t]
          ? n.decodeWorker && n.decodeWorker.postMessage({ type: 2 })
          : n.decodeWorker && n.decodeWorker.postMessage({ type: 3 });
      };
    document.addEventListener(i, r);
  }
  initRender() {
    var n, e, i, u, f, o, s;
    if (
      ((this.gl =
        this.canvas.getContext("webgl") ||
        this.canvas.getContext("experimental-webgl")),
      this.gl)
    ) {
      (n = this.gl).pixelStorei(n.UNPACK_ALIGNMENT, 1);
      var t = n.createProgram(),
        h =
          "attribute highp vec4 aVertexPosition;\nattribute vec2 aTextureCoord;\nvarying highp vec2 vTextureCoord;\nvoid main(void) {\n gl_Position = aVertexPosition;\n vTextureCoord = aTextureCoord;\n}",
        r = n.createShader(n.VERTEX_SHADER);
      n.shaderSource(r, h),
        n.compileShader(r),
        (e =
          "precision highp float;\nvarying lowp vec2 vTextureCoord;\nuniform sampler2D YTexture;\nuniform sampler2D UTexture;\nuniform sampler2D VTexture;\nconst mat4 YUV2RGB = mat4\n(\n 1.1643828125, 0, 1.59602734375, -.87078515625,\n 1.1643828125, -.39176171875, -.81296875, .52959375,\n 1.1643828125, 2.017234375, 0, -1.081390625,\n 0, 0, 0, 1\n);\nvoid main(void) {\n gl_FragColor = vec4( texture2D(YTexture, vTextureCoord).x, texture2D(UTexture, vTextureCoord).x, texture2D(VTexture, vTextureCoord).x, 1) * YUV2RGB;\n}"),
        (i = n.createShader(n.FRAGMENT_SHADER)),
        n.shaderSource(i, e),
        n.compileShader(i),
        n.attachShader(t, r),
        n.attachShader(t, i),
        n.linkProgram(t),
        n.useProgram(t),
        n.getProgramParameter(t, n.LINK_STATUS) ||
          console.log("[ER] Shader link failed."),
        (u = n.getAttribLocation(t, "aVertexPosition")),
        n.enableVertexAttribArray(u),
        (f = n.getAttribLocation(t, "aTextureCoord")),
        n.enableVertexAttribArray(f),
        (o = n.createBuffer()),
        n.bindBuffer(n.ARRAY_BUFFER, o),
        n.bufferData(
          n.ARRAY_BUFFER,
          new Float32Array([1, 1, 0, -1, 1, 0, 1, -1, 0, -1, -1, 0]),
          n.STATIC_DRAW
        ),
        n.vertexAttribPointer(u, 3, n.FLOAT, !1, 0, 0),
        (s = n.createBuffer()),
        n.bindBuffer(n.ARRAY_BUFFER, s),
        n.bufferData(
          n.ARRAY_BUFFER,
          new Float32Array([1, 0, 0, 0, 1, 1, 0, 1]),
          n.STATIC_DRAW
        ),
        n.vertexAttribPointer(f, 2, n.FLOAT, !1, 0, 0),
        (n.y = new Texture(n)),
        (n.u = new Texture(n)),
        (n.v = new Texture(n)),
        n.y.bind(0, t, "YTexture"),
        n.u.bind(1, t, "UTexture"),
        n.v.bind(2, t, "VTexture");
    } else console.log("[ER] WebGL not supported.");
  }
  renderFrame(n, t, i, r, u) {
    if (this.gl) {
      var f = this.gl;
      f.viewport(0, 0, f.canvas.width, f.canvas.height),
        f.clearColor(0, 0, 0, 0),
        f.clear(f.COLOR_BUFFER_BIT),
        f.y.fill(t, i, n.subarray(0, r)),
        f.u.fill(t >> 1, i >> 1, n.subarray(r, r + u)),
        f.v.fill(t >> 1, i >> 1, n.subarray(r + u, n.length)),
        f.drawArrays(f.TRIANGLE_STRIP, 0, 4);
    } else
      console.log("[ERROR] Render frame failed due to WebGL not supported.");
  }
  renderFrame2(n, t, i, r, u) {
    if (this.gl) {
      var f = this.gl;
      f.viewport(0, 0, f.canvas.width, f.canvas.height),
        f.clearColor(0, 0, 0, 0),
        f.clear(f.COLOR_BUFFER_BIT),
        f.y.fill(r, u, n),
        f.u.fill(r >> 1, u >> 1, t),
        f.v.fill(r >> 1, u >> 1, i),
        f.drawArrays(f.TRIANGLE_STRIP, 0, 4);
    } else
      console.log("[ERROR] Render frame failed due to WebGL not supported.");
  }
  renderBuffer() {
    function t() {
      var u, f, o;
      window.clearTimeout(n.renderTimer);
      var e = n.videoBuffer.length,
        r = 40,
        i = n.videoBuffer.shift();
      i &&
        (e > n.bufferLength
          ? ((u = Date.now()),
            n.renderFrame2(
              i.data.ydata,
              i.data.udata,
              i.data.vdata,
              i.data.width,
              i.data.height
            ),
            (o = (f = Date.now()) - u),
            (r -= e - n.bufferLength) < 4 && (r = 4))
          : ((u = Date.now()),
            n.renderFrame2(
              i.data.ydata,
              i.data.udata,
              i.data.vdata,
              i.data.width,
              i.data.height
            ),
            (o = (f = Date.now()) - u))),
        n.startPlay && (n.renderTimer = window.setTimeout(t, r));
    }
    var n = this;
    this.renderTimer = window.setTimeout(t, 4);
  }
  clearFrame() {
    if (this.gl) {
      var n = this.gl;
      n.viewport(0, 0, n.canvas.width, n.canvas.height),
        n.clearColor(0, 0, 0, 0),
        n.clear(n.COLOR_BUFFER_BIT);
    } else
      console.log("[ERROR] Render frame failed due to WebGL not supported.");
  }
  fullscreen() {
    var n = this.canvas;
    n.RequestFullScreen
      ? n.RequestFullScreen()
      : n.webkitRequestFullScreen
      ? n.webkitRequestFullScreen()
      : n.mozRequestFullScreen
      ? n.mozRequestFullScreen()
      : n.msRequestFullscreen
      ? n.msRequestFullscreen()
      : alert("This browser doesn't supporter fullscreen");
    var heightNew = window.innerHeight,
      widthNew = (heightNew / this.videoHeight) * this.videoWidth;
    n.setAttribute("width", widthNew),
      n.setAttribute("height", heightNew),
      console.log("full screen: %d * %d", n.width, n.height);
  }
  exitfullscreen() {
    document.exitFullscreen
      ? document.exitFullscreen()
      : document.webkitExitFullscreen
      ? document.webkitExitFullscreen()
      : document.mozCancelFullScreen
      ? document.mozCancelFullScreen()
      : document.msExitFullscreen
      ? document.msExitFullscreen()
      : alert("Exit fullscreen doesn't work");
    var n = this.canvas;
    n.setAttribute("width", this.canvasWidth),
      n.setAttribute("height", this.canvasHeight),
      console.log("exit full screen: %d * %d", n.width, n.height);
  }
  defaultscreen() {
    var n = this.canvas;
    n.setAttribute("width", this.canvasWidth),
      n.setAttribute("height", this.canvasHeight),
      console.log("defalut screen: %d * %d", n.width, n.height);
  }
  exitfullscreenHandler(n) {
    document.fullscreenElement ||
      document.webkitIsFullScreen ||
      document.mozFullScreen ||
      document.msFullscreenElement ||
      n.defaultscreen();
  }
}
class Texture {
  constructor(n) {
    (this.gl = n),
      (this.texture = n.createTexture()),
      n.bindTexture(n.TEXTURE_2D, this.texture),
      n.texParameteri(n.TEXTURE_2D, n.TEXTURE_MAG_FILTER, n.LINEAR),
      n.texParameteri(n.TEXTURE_2D, n.TEXTURE_MIN_FILTER, n.LINEAR),
      n.texParameteri(n.TEXTURE_2D, n.TEXTURE_WRAP_S, n.CLAMP_TO_EDGE),
      n.texParameteri(n.TEXTURE_2D, n.TEXTURE_WRAP_T, n.CLAMP_TO_EDGE);
  }
  bind(n, t, i) {
    var r = this.gl;
    r.activeTexture([r.TEXTURE0, r.TEXTURE1, r.TEXTURE2][n]),
      r.bindTexture(r.TEXTURE_2D, this.texture),
      r.uniform1i(r.getUniformLocation(t, i), n);
  }
  fill(n, t, i) {
    var r = this.gl;
    r.bindTexture(r.TEXTURE_2D, this.texture),
      r.texImage2D(
        r.TEXTURE_2D,
        0,
        r.LUMINANCE,
        n,
        t,
        0,
        r.LUMINANCE,
        r.UNSIGNED_BYTE,
        i
      );
  }
}
