<html>
<h1>测试</h1>

</html>

<script src="Libs/signalr.min.js"></script>

<script>

	var connection = new signalR.HubConnectionBuilder().withUrl("http://*************:20000/clientHub").withAutomaticReconnect().build();

	//注册告警数据事件
	connection.on("ReceiveAlarmInfo", function (code, data) {
		console.info(code);
		console.info(data);
	});
	//注册风速数据事件
	connection.on("ReceiveWindInfo", function (code, data) {
		console.info(code);
		console.info(data);
	});
	//注册安全监控数据事件
	connection.on("ReceiveSafetyInfo", function (code, data) {
		console.info(code);
		console.info(data);
	});
	//注册PCL数据事件(通风设施)
	connection.on("ReceivePlcInfo", function (code, data) {
		console.info(code);
		console.info(data);
	});
	//注册扩展数据事件
	connection.on("ReceiveExtendInfo", function (code, data) {
		console.info(code);
		console.info(data);
	});

	//连接关闭事件
	connection.onclose(function () {
		console.info("closed");
	});

	//开始重连连接事件
	connection.onreconnecting(function () {
		console.info("onreconnecting");
	});

	//重连成功事件
	connection.onreconnected(function () {
		console.info("onreconnected");
	});

	//启动连接
	connection.start().then(function () {
		console.info("success");
		//连接成功
		//与服务端交互行为
		//注册数据 0:扩展数据;1:告警数据;7:PLC点位数据;8:安全监控数据;9:风速数据
		connection.invoke("RegisterAll", 0).catch(function (err) {
			return console.error(err.toString());
		});
		//取消注册数据
		connection.invoke("CancelRegisterAll", 0).catch(function (err) {
			return console.error(err.toString());
		});
	}).catch(function (err) {
		return console.error(err.toString());
	});

	//关闭连接
	//connection.stop();


</script>