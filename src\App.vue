<template>
  <!-- 全局loading -->
  <!-- <div v-show="store.$state.loadingShow" class="z-99 fixed top-0 left-0 w-100vw h-100vh" v-loading></div> -->
   <div @contextmenu.prevent>
   
  <router-view></router-view>
</div>
<!-- <router-view></router-view> -->
  <!-- <router-view v-slot="{ Component }"> -->
  <!-- <keep-alive v-if="shouldKeepAlive">
      <component :is="Component" />
    </keep-alive> -->
  <!-- <component v-else :is="Component" /> -->
  <!-- <component :is="Component" />
  </router-view> -->
</template>
<script setup>
import { watch, ref } from 'vue';
import { useThereVentilation } from './store/thereVentilation';
import { useRoute } from 'vue-router';
const route = useRoute();
const store = useThereVentilation()
const shouldKeepAlive = ref()
watch(
  () => route.name,
  (val) => {
    if (!val) return;
    if (route.name == 'threeVentilate') {
      shouldKeepAlive.value = true;
    }
  }
);

</script>

<style lang="scss"></style>
