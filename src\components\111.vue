<template>
  <div class="custom-tabs-container">
    

    <!-- 底部标签栏 -->
    <div class="bottom-wrapper fixed bottom-20 w-full">
      <div class="flex items-center justify-between px-2">
        <!-- 左滚动按钮 -->
        <el-icon 
          @click="scrollToPrevPage" 
          class="scroll-arrow cursor-pointer"
          :class="{ disabled: !canScrollPrev }"
        >
          <ArrowLeft />
        </el-icon>
        
        <!-- 标签容器 - 隐藏原生滚动条 -->
        <div ref="scrollContainer" class="custom-scroll-container flex-1 mx-2 overflow-hidden">
          <div ref="contentRef" class="scrollbar-flex-content">
            <p 
              v-for="(v, i) in bottomTabList" 
              :key="v.id" 
              
              :class="['scrollbar-demo-item', { 'scrollbar-item__active': tabsIndex === v.id }]" 
              @click="handleTab(v)"
            >
              {{ v.name }}
            </p>
          </div>
        </div>
        
        <!-- 右滚动按钮 -->
        <el-icon 
          @click="scrollToNextPage" 
          class="scroll-arrow cursor-pointer"
          :class="{ disabled: !canScrollNext }"
        >
          <ArrowRight />
        </el-icon>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import { ElIcon, ElButton } from 'element-plus';
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';

const scrollContainer = ref(null);
const contentRef = ref(null);
const tabsIndex = ref(1);

// 模拟标签数据
const bottomTabList = reactive([
  { id: 1, name: '一氧化碳' },
  { id: 2, name: '二氧化碳' },
  { id: 3, name: '氧气' },
  { id: 4, name: '氮气' },
  { id: 5, name: '甲烷' },
  { id: 6, name: '乙烯' },
  { id: 7, name: '乙炔' },
  { id: 8, name: '氢气' },
  { id: 9, name: '温度' },
  { id: 10, name: '湿度' },
  { id: 11, name: '风速' },
  { id: 12, name: '风向' },
]);

// 计算当前页是否可以向前/向后滚动
const canScrollPrev = computed(() => {
  if (!scrollContainer.value) return false;
  return scrollContainer.value.scrollLeft > 0;
});

const canScrollNext = computed(() => {
  if (!scrollContainer.value || !contentRef.value) return false;
  return scrollContainer.value.scrollLeft + scrollContainer.value.clientWidth < contentRef.value.offsetWidth;
});

// 标签点击处理
const handleTab = (tab) => {
  tabsIndex.value = tab.id;
  
  // 选中标签后自动滚动到可见区域并居中
  nextTick(() => {
    const activeElement = document.querySelector('.scrollbar-item__active');
    if (activeElement) {
      const container = scrollContainer.value;
      const containerRect = container.getBoundingClientRect();
      const elementRect = activeElement.getBoundingClientRect();
      
      // 计算将元素居中需要滚动的距离
      const centerOffset = elementRect.left + elementRect.width/2 - containerRect.left - containerRect.width/2;
      container.scrollBy({
        left: centerOffset,
        behavior: 'smooth'
      });
    }
  });
};

// 向前滚动一页
const scrollToPrevPage = () => {
  if (!canScrollPrev.value) return;
  
  const container = scrollContainer.value;
  const pageWidth = container.clientWidth * 0.9; // 滚动90%的可视宽度作为一页
  
  container.scrollBy({
    left: -pageWidth,
    behavior: 'smooth'
  });
};

// 向后滚动一页
const scrollToNextPage = () => {
  if (!canScrollNext.value) return;
  
  const container = scrollContainer.value;
  const pageWidth = container.clientWidth * 0.9; // 滚动90%的可视宽度作为一页
  
  container.scrollBy({
    left: pageWidth,
    behavior: 'smooth'
  });
};

// 初始化：选中第一个标签并滚动到可见区域
onMounted(() => {
  handleTab(bottomTabList[0]);
});
</script>

<style scoped>
.header-bar {
  border-bottom: 1px solid #e4e7ed;
}

.bottom-wrapper {
  z-index: 10;
  background-color: white;
  padding: 8px 0;
  box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
}

/* 隐藏原生滚动条但保留滚动功能 */
.custom-scroll-container {
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
}
.custom-scroll-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

/* 标签内容居中排列 */
.scrollbar-flex-content {
  display: flex;
  justify-content: center;
  gap: 8px;
  padding: 4px 0;
  width: max-content; /* 关键：让内容决定宽度，触发滚动 */
}

/* 标签样式 */
.scrollbar-demo-item {
  white-space: nowrap;
  padding: 6px 12px;
  border-radius: 4px;
  background-color: #f5f7fa;
  cursor: pointer;
  transition: all 0.3s;
}

.scrollbar-item__active {
  background-color: #409eff;
  color: white;
}

/* 滚动箭头样式 */
.scroll-arrow {
  font-size: 20px;
  color: #606266;
  transition: all 0.3s;
}
.scroll-arrow:hover:not(.disabled) {
  color: #409eff;
}
.scroll-arrow.disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}
</style>    