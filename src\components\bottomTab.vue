<template>
  <!-- <div class="fixed bottom-20">
    <div class="flex justify-around w-745px mx-auto mb-[-36px]  backBottom">
      <div class="back-bottom-wrap">
        <div v-for="(v, i) in bottomTabList" :key="i" class="cursor-pointer" @click="handleTab(v)">
        <div class="bottomTabc flex  mb-5px" :class="{'bottom_tab_name_active': tabsIndex === v.id}">
          <div class="bottom_tab_name">
            <p>{{ v.name }}</p>
          </div>
        </div>
      </div>
      </div>
      
    </div>
   
  </div> -->
  <div class="bottom-wrapper fixed bottom-20 w-full">
    <el-scrollbar  >
    <div class="scrollbar-flex-content">
      <p v-for="(v,i) in bottomTabList" :key="i" :class="['scrollbar-demo-item',{'scrollbar-item__active':tabsIndex === v.id}]" @click="handleTab(v)">
        {{ v.name }}
      </p>
    </div>
  </el-scrollbar>
  </div>
  

  <!-- <transition leave-active-class="animate__animated animate__fadeOutLeft "
        enter-active-class="animate__animated animate__fadeInLeftBig">
        <div v-show="isEqualCurrentIndex(0)" class="fixed left-25px top-135px">
           
        </div>
    </transition> -->
</template>
<script setup>
import { ref, onMounted,watch } from "vue";

// 注册父组件的change方法
const emit = defineEmits(['change'])

// 底部菜单栏, 在mian组件中，通过props传进来， 不传就是默认值
const props = defineProps({
  bottomTabList: {
    type:Array,
     required: true
    
  },
  allData:{
    type:Array,
    required: true
  }
})

watch(()=>props.bottomTabList,(newVal,oldVal)=>{
  if(Array.isArray(newVal)&&newVal.length>0){
    tabsIndex.value = newVal[0].id 
  }
},{deep:true})


const tabsIndex = ref(null) // 当前tab值

// 点击tab事件，修改当前tab值，并通知父组件
const handleTab = (item) => {
  tabsIndex.value = item.id
  emit('change', {
    item,
    allData:props.allData
  })
}

onMounted(() => {});
</script>

<style lang="scss" scoped>

.bottom-wrapper{
  left: 23vw;
  background-image: url("../../assets/img/common/bottom-menu.png");
  background-size: 100% 100%;
  width: 54vw;
  height: 11vh; 
  padding-top: 20px;
  box-sizing: border-box;
}
:deep(.el-scrollbar){
  width: 800px;
  margin-left: 100px;
}
:deep(.el-scrollbar__wrap){
  margin-left: 100rem;
}
.custom-scrollbar {
  ::v-deep .el-scrollbar__bar {
    display: none;
  }
}
.scrollbar-item__active {
  @extend .scrollbar-demo-item;
  margin-left: 23vw;
  background: #d29613;
  color: #000;
  transform: translateY(-2px);
  /* 可选呼吸动画 */
  animation: breath 2s infinite ease-in-out;
  // background: url("../../assets/img/common/bottom-menu.png") no-repeat !important;
  // background-size: 100% 100% !important;
}
.scrollbar-flex-content {
  display: flex;
  width: fit-content;
  height: 8vh;
  align-items: center;
  padding: 0 80px 0 10px;
  transform: translate(-50px,10px);
}
.scrollbar-demo-item {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  // width: 100px;
  height: 50px;
  margin: 10px;
  text-align: center;
  border-radius: 4px;
  color: #fff;
  padding: 8px 24px;
  border-radius: 8px;
  // background: linear-gradient(135deg, #2b2b2b, #1f1f1f);
  transition: all 0.3s;
  cursor: pointer;
  // cursor: pointer;
  // background: url("../../assets//img/common/fan_icon.png") no-repeat;
  // background-size: 100% 100%;
}
// .icon_bg {
//   width: 43px;
//   height: 30px;
//   background-image: url("../../assets/img/common/fan_icon-active.png");
//   background-size: 100% 100%;

// }

// .icon_bg_active {
//   @extend .icon_bg;
//   // background-image: url("../../assets/img/common/fan_icon.png");
//   width: 43px;
//   height: 30px;
//   // background-size: 100% 100%;
// }

.backBottom{
  margin-left: 23vw;
   background: linear-gradient(135deg, #ffb800, #ff9f00);
  color: #000;
  transform: translateY(-2px);
  /* 可选呼吸动画 */
  animation: breath 2s infinite ease-in-out;
  // background-image: url("../../assets/img/common/bottom-menu.png");
  // background-size: 100% 100%;
  width: 54vw;
  height: 11vh;
  align-items: center;
  // gap:20px;
  .back-bottom-wrap {
    width: 60%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap:40px;
  }
}
.bottom_tab_name_active {
  // width: 133px;
  text-align: center;
  // height: 17px;
  font-size: 18px;
  font-family: AlimamaShuHeiTi;
  font-weight: bold;
  font-style: italic;
  // color: #76f7ff;
  // line-height: 38px;
  // padding-top: 20px;
  
  // background-color: rgba($color: #C6870A, $alpha: 0.2);
}
@keyframes breath {
  0%, 100% { transform: translateY(-2px) scale(1); }
  50% { transform: translateY(-2px) scale(1.02); }
}
.bottom_tab_name {
  @extend .bottom_tab_name_active;
  color: #F8EBC7;

  p{
    // width: 133px;
    white-space: nowrap;
  // text-align: center;
  // height: 17px;
  // font-size: 18px;
  // font-family: AlimamaShuHeiTi;
  // font-weight: bold;
  // font-style: italic;
  // // color: #76f7ff;
  // line-height: 38px;
  //padding-top: -10px;
  // margin-left:0px ;
  }
}
.cursor-pointer {
  z-index: 1000;
  flex-shrink: 0;
}


</style>