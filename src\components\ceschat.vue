<template>
    <div>
      <div @click="chatMes" @mouseleave="showMessages = false">
        <img src="./common/assest/img/ai_zn.png" class="w-30px h-30px" />
      </div>
  
      <!-- 聊天框 -->
      <!-- <div class="deepChat">
        <div v-if="showMessages" class="chatCon">
       </div>
      </div> -->
  
      <el-dialog
        destroy-on-close
        draggable
        v-model="dialogShow"
        @close="dialogShowClose"
        :title="聊天室"
        style="width: 800rem; height: 850rem; position: relative"
      >
       
        <div class="flex chatContent">
          <!-- <div><h3 class="" style="text-align: center;">对话</h3></div> -->
          
          <!-- 左边历史记录 -->
          <!-- <div class="left_his">
          <div class="introduce">
          <ul>
            <li><img src="../assets/img/introduce.png">功能介绍</li> -->
          <!-- <li> </li>
            <li> </li>
            <li></li>
            <li> </li>
          </ul>
        </div>
        </div> -->
          <!-- <div class="split-line"></div> -->
          <div class="right">
            <el-scrollbar height="700rem">
              <!-- 所有的回复 -->
              <div
                class="allReply"
                v-for="(item, index) in messages"
                :key="item.id"
                style="position: relative;margin-top: 20px;"
              >
                <!-- 右侧自己的输入信息 -->
                <div class="chat2" v-if="!item.deleted2">
                  
                  <div class="chat-messages2" style="width: 100%;text-align: right;
                justify-content: flex-end;margin-bottom: 15px">
                    
                      <div class="content" >{{ item.content }}</div>
  
                      <!-- <div class="delate2" @click="delateMsg(index, 'self')">
                  <div class="text">删除</div>
   
                  <img src="../../assets/image/删除.png" alt="" />
                </div>-->
                      <div class="copy2" @click="copyMsg(index)">
                        <!-- <div class="text">复制</div> -->
                        <!-- <img src="./common/assest/img/copy_icon.png" alt="" /> -->
                      </div>
                    
                  </div>
                </div>
  
                <!-- 左侧系统的回复 -->
                <div class="chat" v-if="!item.deleted">
                  <div class="chat-messages" style="text-align: left;display: flex;align-items: center;
                    width: 100%;justify-content: flex-start;" >
                    <img
                          class="w-30px h-28px"
                          src="./common/assest/img/ai_head.png"
                        />
                      <div class="content1">
                        
                        <sapn class="ml-5px">{{ item.textSystem }}</sapn>
                      </div>
                      <!-- <div class="date">
                  {{ formatTimestamp(item.timestamp) }}
                </div> -->
                      <!-- <div class="delate" @click="delateMsg(index, 'system')">
                  <img src="../../assets/image/删除.png" alt="" />
                  <div class="text">删除</div>
                </div>
                <div class="copy" @click="copyMsg(index)">
                  <img src="../../assets/image/复制.png" alt="" />
                  <div class="text">复制</div>
                </div> -->
                    
                  </div>
                </div>
              </div>
            </el-scrollbar>
  
            
            <div class="chat-input">
            
                <el-input
                v-model="inputMessage"
                @keydown.enter.prevent="sendMessage"
                placeholder="给deepseek发送消息"
                class="inputtext"
                maxlength="70"
                type="textarea"
                :autosize="{ minRows: 4, maxRows: 6}"
                style="color: aliceblue;text-align: left;padding-top: -100px;"
               
              >
              
              </el-input>
             
              <el-button
                class="button1"
                type="primary"
                @click="sendMessage"
                :icon="Position"
                >发送</el-button
              >
            </div>
             
            
              
           
          </div>
        </div>
      </el-dialog>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, reactive, computed, nextTick } from "vue";
  import {
    ElButton,
    ElScrollbar,
    ElInput,
    ElMessageBox,
    ElMessage,
  } from "element-plus";
  
  import axios from 'axios'
  
  import { Position } from "@element-plus/icons-vue";
  
  import { chatMessages } from "@/https/v1";
  
  const dialogShow = ref(false);
  const chatMes = () => {
    dialogShow.value = true;
  };
  
  const messages = ref([]); //总回复
  
  const inputMessage = ref("");
  
  const isGptShow = ref(false);
  const sendMessage = async () => {
   
    if (!inputMessage.value.trim()) return
  //你这里messages怎么不对 message是你发送的问题？
    const userMessage = { role: 'user', content: inputMessage.value }
    messages.value.push(userMessage)
    console.log(inputMessage.value,'inputMessage.value')
  chatMessages( inputMessage.value).then(res=>{
    console.log(res,'resresresresres')
  })
    // try {
    //   const response = await axios.post('http://192.168.10.28/v1/chat-messages', {
    //     message: inputMessage.value,
    //     context: messages.value
    //   })
    //   const aiMessage = { role: 'assistant', content: response.data.message }
    //   messages.value.push(aiMessage)
    // } catch (error) {
    //   console.error('发送消息失败:', error)
    // }
  
    inputMessage.value = ''
  }
  
  // const sendMessage = async () => {
  //   isGptShow.value = true;
  //   if (input.value.trim() !== "") {
  //     const newMessage = {
  //       id: Date.now(),
  //       text: input.value,
  //       textSystem: "请问有什么需要我帮你的...",
  //       timestamp: new Date(),
  //     };
  
  //     messages.value.push(newMessage);
  
  //     input.value = "";
  
  //     await nextTick(); // 等待DOM更新完成  非常重要
  
  //     const messageElements = document.getElementsByClassName("message");
  //     const lastMessageElement = messageElements[messageElements.length - 1];
  //     lastMessageElement.scrollIntoView({ behavior: "smooth", block: "end" });
  //   }
  // };
  
  //  const clearInput = () => {
  //    input.value = ''
  //  }
  
  const formatTimestamp = (timestamp) => {
    const options = {
      year: "numeric",
      month: "numeric",
      day: "numeric",
      hour: "numeric",
      minute: "numeric",
      second: "numeric",
    };
    return new Intl.DateTimeFormat("default", options).format(timestamp);
  };
  </script>
  
  <style  scoped  lang="scss">
  .chatContent {
    display: flex;
    width: 1100px;
    height: 750px;
    margin-top: -70px;
    box-sizing: border-box;
  }
  
  .left_his {
    width: 32%;
    font-size: 16px;
  
    // opacity: 0.3;
    text-align: left;
  
    img {
      width: 25px;
      height: 25px;
    }
  }
  
  // .split-line {
  //   width: 1px; /* 竖线的宽度 */
  //   height:100%; /* 竖线的高度，根据需要调整 */
  //   background-color: #ece9e9; /* 竖线的颜色 */
  //   position: relative; /* 或者使用fixed，根据需要 */
  //   left: 5px; /* 竖线的位置 */
  //   top: 0; /* 顶部对齐 */
  // }
  .right {
    width: 70%;
    position: relative;
    .content {
      display: inline-block;
      box-sizing: border-box;
      padding: 9px 13px;
      background-color:  #2A2A2A;
      position: relative;
      font-size: 16px;
      line-height: 26px;
      letter-spacing: .6px;
      color:  #C8BEA0;
      text-align: left;
      border-radius: 5px;
      word-break: break-all;
      margin-right: 40px;
       
      }
          
  .content1{
   
    display: inline-block;
      box-sizing: border-box;
      // padding: 9px 13px;
      // background-color: #ddd9ff;
      position: relative;
      font-size: 16px;
      // line-height: 26px;
      // letter-spacing: .6px;
      color: #e5e4e7;
      text-align: right;
      border-radius: 5px;
      word-break: break-all;
      margin-left: 10px;
    // background-color: #fff;
    // border-radius: 7px;
  }
  
    // 公共区域
    .message {
      display: flex;
      flex-direction: column;
      margin-top: 20px;
      position: relative;
  
  
      .delate {
        position: absolute;
        align-self: flex-end;
        bottom: 0px;
        right: -60px;
        display: flex;
        align-items: center;
        img {
          width: 20px;
          height: 20px;
        }
        .text {
          color: #1296db;
          opacity: 0;
          padding-left: 5px;
        }
      }
      .delate:hover {
        cursor: pointer;
      }
      .delate:hover .text {
        opacity: 1;
      }
      .copy {
        position: absolute;
        align-self: flex-end;
        bottom: 30px;
        right: -60px;
        display: flex;
        align-items: center;
        img {
          width: 20px;
          height: 20px;
        }
        .text {
          color: #1296db;
          opacity: 0;
          padding-left: 5px;
        }
      }
      .copy:hover {
        cursor: pointer;
      }
      .copy:hover .text {
        opacity: 1;
      }
    }
  
    // 左侧系统的输出
    .chat {
      padding: 10px;
      display: flex;
      flex-direction: column;
      // position: relative;
      margin-left: 20px;
  
      // .chat-messages {
      //   flex: 1;
      //   margin-right: 200px;
      //   height: 400px;
      // }
    }
  
    // 右侧自己的输出
    .chat2 {
      // padding: 10px;
      // display: flex;
      // flex-direction: column;
      // justify-content: flex-start;
      .chat-messages2 {
        // flex: 1;
        // margin-left: 200px;
        // height: 100px;
        // width: 50px;
        color: #f7f1f1;
  
        .delate2 {
          position: absolute;
          align-self: flex-end;
          bottom: 0px;
          left: -60px;
          display: flex;
          align-items: center;
          img {
            width: 20px;
            height: 20px;
          }
          .text {
            color: #1296db;
            opacity: 0;
            padding-left: 5px;
          }
        }
        .delate2:hover {
          cursor: pointer;
        }
        .delate2:hover .text {
          opacity: 1;
        }
      }
    }
  
    // 输入框区域
    .chat-input {
      display: flex;
    
    
      z-index: 1000;
      position: absolute;
      bottom: 10px;
      width: 95%;
      bottom: 70px;
      // height: 100px;
      left: 30px;
      // text-align: center;
  
  
         
      
      .inputtext {
        margin-right: 30px;
        // height: 100px;
        background-color: #2A2A2A;
        // border-color: rgba(232, 157, 6, 0.8);
         color: #eff6ff;
         margin-top: 10px;
      
        // border-top: 1px solid #ccc;
      }
     
      .button1 {
        padding: 15px;
        position: absolute;
        right: 39px;
        top: 60px;
        // margin-left: -100px;
        // margin-top: 63px;
        z-index: 999;
        background-color: rgba(255, 179, 15, 0.3) !important;
        border: 1px solid rgba(232, 157, 6, 0.8) !important;
      }
    }
  }
  :deep(.el-card__body) {
    display: flex;
    width: 100%;
  }
  // 文本框
  :deep(.el-textarea__inner ){
    background-color: #2A2A2A; 
    color: #eff6ff;
    box-shadow: 0 0 0 1rem rgba(232, 157, 6, 0.8); 
   
  }
  :deep(.el-textarea__inner.is-focus){
    box-shadow: 0 0 0 1rem rgba(232, 157, 6, 0.8); 
  }
  :deep(.el-input__inner) {
    // white-space: pre-wrap;
    // word-break: break-all;
    height: 27px;
    color: #eff6ff;
    
  }
  ::v-deep .el-button + .el-button {
    background-color: rgba(255, 179, 15, 0.3) !important;
    border: 1px solid rgba(232, 157, 6, 0.8) !important;
  }
  
  
  </style>