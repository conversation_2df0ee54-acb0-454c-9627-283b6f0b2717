<template>
  <div>
    <div @click="chatMes" @mouseleave="showMessages = false">
      <img src="./common/assest/img/ai_zn.png" class="w-30px h-30px" />
    </div>

    <el-dialog
      destroy-on-close
      draggable
      v-model="dialogShow"
      @close="dialogShowClose"
      :title="'智能问答'"
      style="width: 900px; height: 820px; position: relative"
      body-class="aa"
    >
      <div class="flex chatContent">
        <!-- 历史列表 -->
        <!--<div class="left_content">
        <div v-for="hisDa in list" :key="hisDa.id">
          
        </div>
        </div>-->
        <div class="right">
          <!-- <el-scrollbar height="700px"> -->
          <!-- 所有的回复开始 -->
          <div class="allReply">
            <div
              v-for="(item, index) in messages"
              :key="item.id"
              style="position: relative; margin-top: 20px"
            >
              <!-- 右侧自己的输入信息 -->
              <div class="chat2" v-if="!item.deleted2">
                <div
                  class="chat-messages2"
                  style="
                    text-align: right;
                    justify-content: flex-end;
                    margin-bottom: 15px;
                  "
                >
                  <div class="content">{{ item.content }}</div>
                  <div class="copy2" @click="copyMsg(index)"></div>
                </div>
              </div>

              <!-- 左侧系统的回复 -->
              <div class="chat" v-if="!item.deleted" ref="chatRef">
                <div
                  class="chat-messages"
                  style="
                    text-align: left;
                    display: flex;
                    width: 100%;
                    justify-content: flex-start;
                  "
                >
                  <img
                    class="w-40px h-31px ml-5px mr-10px mt-5px"
                    src="./common/assest/img/icon_ai.png"
                  />
                  <div class="content1">
                    <Collapse :title="title">
                      <div class="ml-5px" v-html="renderMarkdown(getThikString(item.textSystem))"></div>
                    </Collapse>
                    <div
                      class="ml-5px"
                      v-html="renderMarkdown(item.textSystem)"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 所有的回复结束 -->

          <!-- /* 输入框开始 */ -->
          <div class="chat-input">
            <el-input
              v-model="inputMessage"
              @keydown.enter.prevent="sendMessage"
              placeholder="给deepseek发送消息"
              class="inputtext"
              maxlength="70"
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 6 }"
              style="color: aliceblue; text-align: left"
            ></el-input>

            <el-button
              class="button1"
              type="primary"
              @click="sendMessage"
              :icon="Position"
              >发送</el-button
            >
          </div>
          <!-- 输入框结束 -->
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed, nextTick, watch } from "vue";
import {
  ElButton,
  ElScrollbar,
  ElInput,
  ElMessageBox,
  ElMessage,
} from "element-plus";
import MarkdownIt from "markdown-it";
import hljs from "highlight.js";
import "highlight.js/styles/default.css";
import DOMPurify from "dompurify";
import { marked } from "marked";
import { Position } from "@element-plus/icons-vue";
import { chatMessages } from "@/https/v1";
import Collapse from "./collapse.vue";
import { tr } from "element-plus/es/locale";

const dialogShow = ref(false);
const chatMes = () => {
  dialogShow.value = true;
};

// test code ----
const timer = ref([new Date(), new Date()]);
const title = ref('已深度思考(19.6s)');

// test code end ----
const messages = ref([]); //总回复
//定义 自动滚动
const chatRef = ref(null);
const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
});

const inputMessage = ref("");
const isGptShow = ref(false);
//发送消息
const sendMessage = async () => {
  if (!inputMessage.value.trim()) return;
  const userMessage = { role: "user", content: inputMessage.value };
  messages.value.push(userMessage);

  const requestBody = {
    inputs: {},
    query: inputMessage.value,
    response_mode: "streaming",
    conversation_id: "",
    user: "abc-123",
    files: [],
  };

  fetch("http://192.168.10.28/v1/chat-messages", {
    method: "POST",
    headers: {
      Authorization: "Bearer app-5GrrJ1nlmEQYndPppCWDOAbN",
      "Content-Type": "application/json",
    },
    body: JSON.stringify(requestBody),
  })
    .then(async (response) => {
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }

      if (!response.body) {
        throw new Error("Response body is not available");
      }

      const reader = response.body.getReader();
      const textDecoder = new TextDecoder();
      let result = true;
      while (result) {
        const { done, value } = await reader.read();
        if (done) {
          console.log("Stream ended");
          result = false;
          break;
        }

        let output = textDecoder.decode(value);
        output = output.split("data: ");
        let outputList = output.filter((item) => item !== "");
        outputList.forEach((item) => {
          try {
            let parsedData = JSON.parse(item);
            if (parsedData.event === "agent_message") {
              const eventData = filterTags(parsedData.answer);
              messages.value[messages.value.length - 1].textSystem += eventData;
            }
          } catch (error) {
            return;
          }
        });
      }
    })
    .catch((error) => console.error("Error:", error));

  inputMessage.value = "";
};

// 定义去除标签的方法
const removeTags = (text) => {
  let tempText = text;

  if (text === undefined || text === null) {
    return "";
  }
  // 去除 undefined 字符串
  tempText = tempText.replace(/undefined/g, "");
  //去除标签
  tempText = tempText.replace(/<[^>]*>/g, "");
  return tempText;
};
// 将markdown转换为html
const renderMarkdown = (text) => {
  console.log(text, "markdown");
  if (typeof text !== "string") {
    // 如果不是字符串，将其转换为字符串
    text = String(text);
  }
  //使用正则去除字符
  text = removeTags(text);

  const md = new MarkdownIt({
    html: true, // 允许HTML标签
    breaks: true, // 转换换行符为<br>
    linkify: true,
  });
  return md.render(text);
};
// 处理折叠/展开逻辑
const handleFoldToggle = (e) => {
  console.log("折叠/展开操作触发", e);
  const toggleBtn = e.target.closest(".fold-toggle");
  if (!toggleBtn) return;

  const targetId = toggleBtn.getAttribute("data-target");
  const contentEl = document.getElementById(targetId);
  const iconEl = toggleBtn.querySelector("i");

  if (contentEl) {
    const isExpanded = contentEl.style.display === "block";
    contentEl.style.display = isExpanded ? "none" : "block";
    iconEl.className = isExpanded ? "fa fa-chevron-down" : "fa fa-chevron-up";
    toggleBtn.textContent = isExpanded ? " 展开" : " 折叠";
  }
};

// 向messages响应式数据写入消息时，过滤关键字
const filterTags = (str) => {
  if (!str) {
    return "";
  }
  if (str.endsWith("<think>")) {
    return "";
  }
  if (str.endsWith("</think>")) {
    return "";
  }
  return str;
};

// 分析message中的思考部分和结果部分
const getTextType = (str) => {
  const thinkParts = [];
  const resultParts = [];
  let startThink = false,
    str;
  const strParts = str.split(/(<think>)|(<\/think>)/).filter((s) => !!s);
  while ((str = strParts.shift())) {
    if (str === "<think>") {
      startThink = true;
    }
    thinkParts.push(str);
    if (str === "</think>") {
      startThink = false;
    }
    resultParts.push(str);
  }
  return {think: thinkParts.join(''), result: resultParts.join('')}
};

const getThikString = str => {
  const { think } = getTextType(str);
  return think;
}

const getResultString = str => {
  const { result } = getTextType(str);
  return result;
}
</script>

<style lang="scss" scoped>
.aa {
  border: 50px green;
}
.chatContent {
  display: flex;
  width: 100%;
  height: 750px;
  margin-top: -70px;
  box-sizing: border-box;
}
.left_content {
  width: 20%;
  height: 100%;
  background-color: #a39797;
  opacity: 0.6;
}
.right {
  width: 100%;
  position: relative;

  .content {
    // display: inline-block;
    box-sizing: border-box;
    padding: 10px 15px;
    background-color: #2a2a2a;
    position: relative;
    font-size: 16px;
    line-height: 20px;
    color: #c8bea0;
    text-align: left; /* 文本左对齐 */
    border-radius: 5px;
    word-break: break-word; /* 保证长单词会换行 */
    margin-right: 10px; /* 给每条消息添加间距 */
  }
  .img_dic {
    position: relative;
    top: 45px;
    border-radius: 50%; /* 圆形头像 */
    width: 70px; /* 头像大小 */
    height: 35px; /* 头像大小 */
    overflow: hidden; /* 隐藏超出部分 */
  }
  .img_dic img {
    position: absolute;
    top: 20%;
    left: 15%;
    // transform: translate(-50%, -50%);
  }
  .content1 {
    display: inline-block;
    box-sizing: border-box;
    padding: 10px 15px;
    font-family: ui-sans-serif, system-ui, sans-serif, Apple Color Emoji,
      Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
    -webkit-tap-highlight-color: transparent;
    background-color: #3a3a3a;
    // background-color: #594f4f8f;
    position: relative;
    font-size: 16px;
    line-height: 37px;
    color: #f1f1f1;
    text-align: left; /* 系统消息左对齐 */
    border-radius: 15px;
    word-break: break-word;
    margin-left: 10px;
  }
  .content1 span p :nth-of-type(1) {
    margin-top: -20px;
  }
  .chat {
    padding: 10px;
    display: flex;
    flex-direction: column;
    margin-left: 30px;
    margin-right: 90px;
  }
  // 右侧发送信息
  .chat2 {
    justify-content: flex-end;
    margin-bottom: 16px;
    // padding-bottom: 32px;
    display: flex;
    margin-top: 20px;
    margin-right: 30px;
    position: relative;
    .chat-messages2 {
      color: #f7f1f1;

      max-width: 50%; /* 限制最大宽度 */
    }
  }

  .chat-input {
    display: flex;
    z-index: 1000;
    position: relative;
    // bottom: -130px;
    // right: 20px;
    // left: 45px;
    // margin-top: 40px;
    // margin-left: 45px;
    width: 95%;
    margin: 0 auto;
    // left: 30px;

    .inputtext {
      margin-right: 30px;
      background-color: #2a2a2a;
      color: #eff6ff;
      margin-top: 10px;
    }

    .button1 {
      padding: 15px;
      position: absolute;
      right: 39px;
      top: 60px;
      z-index: 999;
      background-color: rgba(255, 179, 15, 0.3) !important;
      border: 1px solid rgba(232, 157, 6, 0.8) !important;
    }
  }
}

.allReply {
  height: 720px;
  max-height: 750px;
  overflow-y: auto;
  // 滚动条的颜色的大小
  &::-webkit-scrollbar {
    width: 2px;
  }

  &::-webkit-scrollbar-track {
    background-color: #555;
  }

  &::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 1px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }
}

:deep(.el-card__body) {
  display: flex;
  width: 100%;
}
:deep(.el-overlay-dialog) {
  top: -70px;
}
:deep(.el-textarea__inner) {
  background-color: #2a2a2a;
  color: #eff6ff;
  box-shadow: 0 0 0 1rem rgba(232, 157, 6, 0.8);
}

:deep(.el-textarea__inner.is-focus) {
  box-shadow: 0 0 0 1rem rgba(232, 157, 6, 0.8);
}

:deep(.el-input__inner) {
  height: 27px;
  color: #eff6ff;
}

::v-deep .el-button + .el-button {
  background-color: rgba(255, 179, 15, 0.3) !important;
  border: 1px solid rgba(232, 157, 6, 0.8) !important;
}

// markdown格式
.foldable-paragraph {
  margin: 1rem 0;
  border: 1px solid #eee;
  border-radius: 4px;
  overflow: hidden;
}

.fold-toggle {
  width: 100%;
  padding: 0.5rem;
  background: #f8f8f8;
  border: none;
  cursor: pointer;
  text-align: left;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.fold-toggle i {
  margin-right: 0.5rem;
}

.folded-p {
  margin: 0;
  padding: 1rem;
  background: #fff;
}
</style>
