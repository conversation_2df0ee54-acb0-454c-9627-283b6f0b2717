<template>
  <div>
    <div @click="chatMes" @mouseleave="showMessages = false">
      <img src="./common/assest/img/ai_zn.png" class="w-30px h-30px" />
    </div>

    <!-- 聊天框 -->
    <!-- <div class="deepChat">
      <div v-if="showMessages" class="chatCon">
     </div>
    </div> -->

    <el-dialog
      destroy-on-close
      draggable
      v-model="dialogShow"
      @close="dialogShowClose"
      :title="聊天室"
      style="width: 800rem; height: 850rem; position: relative"
    >
      <!-- 视频播放 -->
      <div class="flex chatContent">
        <!-- 左边历史记录 -->
        <!-- <div class="left_his">
        <div class="introduce">
        <ul>
          <li><img src="../assets/img/introduce.png">功能介绍</li> -->
        <!-- <li> </li>
          <li> </li>
          <li></li>
          <li> </li>
        </ul>
      </div>
      </div> -->
        <!-- <div class="split-line"></div> -->
        <div class="right">
          <el-scrollbar height="700rem">
            <!-- 所有的回复 -->
            <div
              class="allReply"
              v-for="(item, index) in messages"
              :key="item.id"
            >
              <!-- 右侧自己的回复 -->
              <div class="chat2" v-if="!item.deleted2">
                <div class="chat-messages2">
                  <div class="message">
                    <div class="content">{{ item.text }}</div>

                    <!-- <div class="delate2" @click="delateMsg(index, 'self')">
                <div class="text">删除</div>
 
                <img src="../../assets/image/删除.png" alt="" />
              </div>-->
                    <div class="copy2" @click="copyMsg(index)">
                      <!-- <div class="text">复制</div> -->
                      <!-- <img src="./common/assest/img/copy_icon.png" alt="" /> -->
                    </div>
                  </div>
                </div>
              </div>

              <!-- 左侧系统的回复 -->
              <div class="chat" v-if="!item.deleted">
                <div class="chat-messages">
                  <div class="message">
                    <div class="content1">
                      <img
                        class="w-25px h-25px mt-2px"
                        src="./common/assest/img/ai_icon.png"
                      />
                      <sapn class="ml-5px">{{ item.textSystem }}</sapn>
                    </div>
                    <!-- <div class="date">
                {{ formatTimestamp(item.timestamp) }}
              </div> -->
                    <!-- <div class="delate" @click="delateMsg(index, 'system')">
                <img src="../../assets/image/删除.png" alt="" />
                <div class="text">删除</div>
              </div>
              <div class="copy" @click="copyMsg(index)">
                <img src="../../assets/image/复制.png" alt="" />
                <div class="text">复制</div>
              </div> -->
                  </div>
                </div>
              </div>
            </div>
          </el-scrollbar>

          
          <div class="chat-input">
          
              <el-input
              v-model="input"
              @keydown.enter.prevent="sendMessage"
              placeholder="给deepseek发送消息"
              class="inputtext"
              type="input"
              style="height: 100px;color: aliceblue;"
             
            >
            
            </el-input>
           
            <el-button
              class="button1"
              type="primary"
              @click="sendMessage"
              :icon="Position"
              >发送</el-button
            >
          </div>
           
          
            
         
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed, nextTick } from "vue";
import {
  ElButton,
  ElScrollbar,
  ElInput,
  ElMessageBox,
  ElMessage,
} from "element-plus";
import { Position } from "@element-plus/icons-vue";

const dialogShow = ref(false);
const chatMes = () => {
  dialogShow.value = true;
};

const messages = ref([]); //总回复

const input = ref("");
const text = ref("");
const isGptShow = ref(false);

const sendMessage = async () => {
  isGptShow.value = true;
  if (input.value.trim() !== "") {
    const newMessage = {
      id: Date.now(),
      text: input.value,
      textSystem: "请问有什么需要我帮你的...",
      timestamp: new Date(),
    };

    messages.value.push(newMessage);

    input.value = "";

    await nextTick(); // 等待DOM更新完成  非常重要

    const messageElements = document.getElementsByClassName("message");
    const lastMessageElement = messageElements[messageElements.length - 1];
    lastMessageElement.scrollIntoView({ behavior: "smooth", block: "end" });
  }
};

//  const clearInput = () => {
//    input.value = ''
//  }

const formatTimestamp = (timestamp) => {
  const options = {
    year: "numeric",
    month: "numeric",
    day: "numeric",
    hour: "numeric",
    minute: "numeric",
    second: "numeric",
  };
  return new Intl.DateTimeFormat("default", options).format(timestamp);
};
</script>

<style  scoped  lang="scss">
.chatContent {
  display: flex;
  width: 1100px;
  height: 750px;
  margin-top: -70px;
}

.left_his {
  width: 32%;
  font-size: 16px;

  // opacity: 0.3;
  text-align: left;

  img {
    width: 25px;
    height: 25px;
  }
}

// .split-line {
//   width: 1px; /* 竖线的宽度 */
//   height:100%; /* 竖线的高度，根据需要调整 */
//   background-color: #ece9e9; /* 竖线的颜色 */
//   position: relative; /* 或者使用fixed，根据需要 */
//   left: 5px; /* 竖线的位置 */
//   top: 0; /* 顶部对齐 */
// }
.right {
  width: 70%;
  // padding: 0 30px;
  position: relative;
  // background-color: #686999;
  // opacity: 0.3;
  // height: 500px;

  // 公共区域
  .message {
    display: flex;
    flex-direction: column;
    margin-top: 20px;
    position: relative;

    .content {
      width: 360px;
      height: 60px;
      background: #2A2A2A;
      display: flex;
      color: #C8BEA0;
    // border: 1rem solid rgba(232, 157, 6, 0.8);
      //  padding: 7px 0 0 10px;
      padding-left: 10px;
      padding-top: -20px;
      border-radius: 7px;
      word-wrap: break-word;
      flex-wrap: wrap;
      margin-left: 366px;
    }
.content1{
  width: 360px;
  height: 60px;
  margin-left: -70px;
  // background-color: #fff;
  // border-radius: 7px;
}
    .delate {
      position: absolute;
      align-self: flex-end;
      bottom: 0px;
      right: -60px;
      display: flex;
      align-items: center;
      img {
        width: 20px;
        height: 20px;
      }
      .text {
        color: #1296db;
        opacity: 0;
        padding-left: 5px;
      }
    }
    .delate:hover {
      cursor: pointer;
    }
    .delate:hover .text {
      opacity: 1;
    }
    .copy {
      position: absolute;
      align-self: flex-end;
      bottom: 30px;
      right: -60px;
      display: flex;
      align-items: center;
      img {
        width: 20px;
        height: 20px;
      }
      .text {
        color: #1296db;
        opacity: 0;
        padding-left: 5px;
      }
    }
    .copy:hover {
      cursor: pointer;
    }
    .copy:hover .text {
      opacity: 1;
    }
  }

  // 左侧系统的输出
  .chat {
    padding: 10px;
    display: flex;
    flex-direction: column;
    // position: relative;
    margin-left: 20px;

    .chat-messages {
      flex: 1;
      margin-right: 200px;
      height: 400px;
    }
  }

  // 右侧自己的输出
  .chat2 {
    padding: 10px;
    display: flex;
    flex-direction: column;

    .chat-messages2 {
      // flex: 1;
      // margin-left: 200px;
      height: 100px;
      width: 50px;
      color: #f7f1f1;

      .delate2 {
        position: absolute;
        align-self: flex-end;
        bottom: 0px;
        left: -60px;
        display: flex;
        align-items: center;
        img {
          width: 20px;
          height: 20px;
        }
        .text {
          color: #1296db;
          opacity: 0;
          padding-left: 5px;
        }
      }
      .delate2:hover {
        cursor: pointer;
      }
      .delate2:hover .text {
        opacity: 1;
      }
    }
  }

  // 输入框区域
  .chat-input {
    display: flex;
    align-items: center;
    z-index: 1000;
    position: absolute;
    bottom: 10px;
    width: 95%;
    // height: 100px;
    left: 30px;
    text-align: center;
   ::v-deep .el-input__wrapper  {
      background-color: #2A2A2A;
      box-shadow: 0 0 0 1rem rgba(232, 157, 6, 0.8);
      
    }
    ::v-deep .el-input__wrapper.is-focus{
      box-shadow: 0 0 0 1rem rgba(232, 157, 6, 0.8);
}


       
    
    .inputtext {
      margin-right: 30px;
      // height: 100px;
      background-color: #2A2A2A;
      // border-color: rgba(232, 157, 6, 0.8);
       color: #eff6ff;
      // border-top: 1px solid #ccc;
      margin-top: 10px;
    }
    .el-textarea__inner {
      background-color: #000;
      border-color: rgba(232, 157, 6, 0.8);
      color: #eff6ff;
    }
    .button1 {
      padding: 15px;
      position: absolute;
      right: 60px;
      top: 70px;
      // margin-left: -100px;
      // margin-top: 63px;
      z-index: 999;
      background-color: rgba(255, 179, 15, 0.3) !important;
      border: 1px solid rgba(232, 157, 6, 0.8) !important;
    }
  }
}
:deep(.el-card__body) {
  display: flex;
  width: 100%;
}
:deep(.el-input__inner) {
  white-space: pre-wrap;
  word-break: break-all;
  height: 27px;
  color: #eff6ff;
}
::v-deep .el-button + .el-button {
  background-color: rgba(255, 179, 15, 0.3) !important;
  border: 1px solid rgba(232, 157, 6, 0.8) !important;
}

// .deepChat{
// position: relative;
// }

// .chatCon{
//   width: 300px;
//   height: 300px;
//   background-color: #fff;
//   overflow-y: scroll;
//   position: absolute;
// }
// ::v-deep .el-input__wrapper {
//   background-color: #F3F4F6;
//   // opacity: 0.8;
// }
// ::v-deep .el-dialog {
//   // width: 850px;
//   // height: 500px;

//   background: rgb(0, 0, 0) !important;
//   box-shadow: rgb(255, 179, 15) 0px 0px 20px inset !important;
//   border: 1px solid rgba(255, 179, 15, 0.3) !important;
//   border-radius: 10px !important;
// }
// ::v-deep .el-button + .el-button {
//   background-color: rgba(255, 179, 15, 0.3) !important;
//   border: 1px solid rgba(232, 157, 6, 0.8) !important;
// }

// .deepseek-message {
//   border: 1px solid #e5e7eb;
//   border-radius: 8px;
//   // padding: 16px;
//   max-width: 600px;
//   margin: 0 auto;
//   background-color: #f9fafb3b;
//   font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
//   position: absolute;
//   bottom: 15px;
//   width: 410px;
//   height: 100px;
//   left: 20px;
// }

// .message-title {
//   font-size: 18px;
//   font-weight: 600;
//   margin-bottom: 16px;
//   color: #111827;
// }
</style>