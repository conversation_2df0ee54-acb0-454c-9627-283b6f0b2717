<template>
  <div class="container" :class="{ collapsed: isCollapsed }">
    <div class="header" @click="toggleCollapse">
      <div class="header-content">
        <span class="arrow-icon" :class="{ collapsed: isCollapsed }">{{
          ">"
        }}</span>
        <span class="collapse-title">{{ title }}</span>
      </div>
    </div>
    <div class="content" v-show="!isCollapsed">
      <slot></slot>
      {{ str }}
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref, watch } from "vue";

const props = defineProps({
  title: {
    type: String,
    default: "折叠面板标题",
  },
  collapsed: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["collapseChange"]);

const isCollapsed = ref(false);

const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

onMounted(() => {
  isCollapsed.value = props.collapsed;
});

watch(
  () => props.collapsed,
  (newVal) => {
    // console.log("🌷🌷🌷🌷🌷🌷🌷🌷collapsed watch", props.collapsed);
    isCollapsed.value = newVal;
  }
);
</script>

<style scoped>
.container {
  padding: 4px 8px;
  /* border-left: 1px solid #ccc; */
}

.container.collapsed {
  background-color: transparent;
  border: none;
  padding-left: 0;
}

.container.collapsed .header {
  border: 1px solid;
  color: gray;
  padding: 4px 8px;
  border-radius: 6px;
}

.collapse-title {
  margin-left: 8px;
}

.header {
  border: none;
  cursor: pointer;
}
</style>