<template>
    <div class="center_title flex justify-center ">
        <div class="title w-237px"><span>{{ title }}</span></div>
    </div>
</template>
<script setup>
const props = defineProps(['title'])
</script>
<style lang="scss" scoped>
.center_title {
    position: fixed;
    top: 161px;
    width: 100vw;
    left: 0;
    right: 0;

    title {
        background-color: rgba($color: #161A1F, $alpha: 0.4);
        border: 1px solid #161A1F;
        color: #F8EBC7;
        // background-image: url('../common/assest/img/tianx-title-bg.png');
        // background-size: 237px 36px;
    }

    span {
        width: 84px;
        height: 20px;
        font-size: 20px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #F8EBC7;
        line-height: 20px;
    }
}

.center_title ::before {
    content: "";
    position: absolute;
    width: 10px;
    height: 10px;
    top: -2px;
    left: -2px;
    border-left: 2px solid #D29613;
    border-top: 2px solid #D29613;
}

.center_title ::after {
    content: "";
    position: absolute;
    width: 10px;
    height: 10px;
    bottom: -2px;
    right: -2px;
    border-bottom: 2px solid #D29613;
    border-right: 2px solid #D29613;

}
</style>