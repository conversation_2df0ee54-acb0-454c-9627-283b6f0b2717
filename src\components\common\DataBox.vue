<template>
    <div ref="box">
        <div class="fan_title" :style="{ width: bgWidth }">
            <span class="sapn_title">{{ title }}</span>
            <div v-if="tabNameList.length" class="flex absolute  left-300px top-20px" >
                <div v-for="(v, i) in tabNameList" :key="i"
                    :class="tabNameList.length > 1 && fanActive == i ? 'fanbtn_active' : 'fanbtn'"
                    @click="changeFanActive(i)">
                    <span>{{ v }}</span>
                </div>
            </div>
        </div>

        <slot name="mainFan"></slot>
    </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
const props = defineProps({
    title: {
        type: String,
        default: '-'
    },
    tabNameList: {
        type: Array,
        default: []
    },
    bgWidth: {
        type: String,
        default: 'auto'
    }
})
const emit = defineEmits(['sendFanActive'])
const fanActive = ref(0)
function changeFanActive(num) {
    fanActive.value = num;
    // console.log(fanActive.value, 'fanActive.value');
    emit('sendFanActive', fanActive.value)
}

</script>
<style lang="scss" scoped>
.fan_title {
    width: 100%;
    height: 50px;
    font-size: 18px;
    margin: 3px 0 0 10px;
    font-family: AlimamaShuHeiTi;
    font-weight: bold;
    color: #EBF5F8;
    line-height: 30px;
    background-image: url(./assest/img/title_common.png);
    background-size: 100% 100%;
    // background-size: cover;
    // background-repeat: no-repeat;
    // background-position: center 0;
    display: flex;
    flex-direction: row;

    .sapn_title {
        margin-left: 7%;
        // margin-left: 40px;
        margin-top: 10px;
    }
}



.fanbtn_common {
    width: 80px;
    height: 30px;
    cursor: pointer;
    text-align: center;
    
}

.fanbtn_active {
    @extend .fanbtn_common;
    border: 1px solid #E98650;
    background-image: url('./img/btn_bg.png');
    background-size: 80px 30px;
    // border-radius: 20px 0px 0px 20px;

    span {
        width: 80px;
        height: 30px;
        font-size: 14px;

        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #E68550;
        line-height: 30px;
    }
}

.fanbtn {
    @extend .fanbtn_common;
    border: 1px solid #08658D;
    // border-radius: 0px 20px 20px 0px;
    // border-radius: 20px 0px 0px 20px;
    border-image: linear-gradient(0deg, #08658D) 10 10;
    // background: linear-gradient(98deg, rgba(14, 57, 90, 0.99) 52%);

    span {
        width: 60px;
        height: 15px;
        font-size: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #D0F2F7;
        line-height: 30px;
    }


}
</style>