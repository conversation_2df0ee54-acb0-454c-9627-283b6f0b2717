<template>
    <!-- 悬浮窗 -->
    <div class="container" id="tips_mainvent_trouble_1" ref="infoDiv"
        style="width: 500px; height: 70px; left: 825.359px; top: 450.091px;">
        <div class="container_beacon animate__animated animate__fadeInBottomLeft" style="height: 50%; width: 140px;">
            <div class="container_breathe pulse">
                <div class="ring"></div>
                <div class="ring"></div>
                <div class="ring"></div>
            </div>
        </div>
        <div class="container_body animate__animated animate__fadeInTopRight" style="width: 360px; height: 70px;">
            <div class="container">
                <span class="title">{{ data.label }}</span>
                <span class="value">{{ data.value }}</span>
                <span class="unit">{{ data.unit }}</span>
           
            </div>
        </div>
    </div>
</template>
<script setup>
import { ref, defineEmits, onMounted, defineProps } from 'vue'
const props = defineProps({
    data: {
        type: Object || Array,
        default: { name: '-', value: '-', unit: 'm/s' }
    }
})
const infoDiv = ref(null)
const emit = defineEmits(['labelDialogEL'])
onMounted(() => {
    emit('labelDialogEL', infoDiv)
})
</script>
<style lang="scss" scoped>
.container {
    font-size: 12px;
    position: fixed;
    opacity: 0.8;
    background: transparent;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.container_beacon {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKgAAABVCAYAAADdR69zAAAFBElEQVR4nO3d2YscVRiG8edUJstkQcQNERFEQRQVREUU1wR3ccMF44Zb3DH34pW3xqAYjai4o4iIeO2N/4AgowmoIAougcQYdWaSme6W0/0VluUsvVRVn1P1PtBUz3RPd/X0j+9UzzDTrjU9hYqnL3Y+fSXwCnAE0AIOAXN28ucPZrb506ydljufvzx7u9lTer/z6faiLc+2ivxmCmhxXQLcAkyUeSd7vvtmc7vTXuOgDa6D627brrdtOZe0cJ22w29dy1/u/OeTxMNpue7neh87l3hYbZck87ike1n3lPRup3db/vIV884l7d757vXavcu72zbOdfz9p+cLfLj7Sv1mNqhLgU3AM8DeMh/27s/fvyYzPdPplZ9sg07Qmcx1ZuzrZ3Nfu9AUnctN0cInaFLkjTW0FOe2snFaDwK/AH5SOdumudx18x/nc7nrLXSbZC5jkcv6vb+B0xI/WpcBGyvE2bi0xA/fRpuez/ljpVgfROgJ6HBtshdFHufvMT6AWNIx6OB5nBcLZzVpgg7W5cCFdswpnBUkoP13BXCBTc79sex07GmJ768U5zbhrDZN0OXzv1o83ybnH6HvbN0S0KW7CjhPOMeXgC7e1cC5hvNAqDtZ9wR04fzvu8+xY07hHGMC+v+uBc4WzjAS0P92HXCWLet/hrRjTU1A/+164EybnMIZSPo5aC+P8wzgeeEMK01QuAE4HdgunOHVdKA3AqfZ5PwrgP1RuZoM9CbgVMP5dwD7oxaoqcegwhlJTZygNwOnCGccNQ2o/7Pgkw3ndAD7o5apSUt8inO7cMZTEyaoM5wn2eScCWCfVJ/VHajHeStwonDGWZ2Bepy3ASfYsi6cEVbXY9AszheEM97qOEE9ztuB44Uz/uoG1OO8AzjOcM4GsE9qhOoE1OPcDBwrnPWpLkDzOA8GsE+qgOoA1OO8EzhGOOtX7EA9zruAo4EXhbN+xQw0xXmUTc5DAeyTKrhYgSaG80ibnMJZ02IE6nHeDRwunPUvNqAe5z3AYcBLwln/YvpVZxbnDuFsRrFMUI/zXmCDcDarGIB6nPcBaw3nXAD7pCoqdKAe5/3AJPCycDavkIFmce6wdzJTDStUoB7nA8Bq4Wx2IQJdYThX2rIunA0utB8zpTgn7C2nhbPhhTRBJ+yY0293CqciIKATNjkT4VTZQljiPc6H7PyrwqmyjXuCpjj9m+C/Lpwq3zgnqMe5RTjVUo0LqMf5sKF8TTjVYo1jiff3+Yj9eYbH2dazoxar6gkqnGqgqpygKw3njB1zCqdatqqApjj9/+V8QzhVv1UB1ON81N7i5U3hVINUNtBVNjmFUw1VmS+SVtnkPCCcatjKAupxPgbsB94STjVsZQD1OB8H9gFvC6capaKBepxPAHuBd4RTjVqRL5JWG849hrOjZ0eNWlFAU5y/Ae8KpyqqIoCmOH8F3hNOVWSjHoOuAZ4UTlVWowBNcf4snKqshl3iU5w/AR8IpyqrYYBO2jGncKrSG3SJn7TJ+aNwqioaBKjH+RTwA/ChcKoq6neJT3F+D3wknKqq+pmg/v9ybhVONY6Wm6BrbXJ+aziVqrSlgK4znLuBj/W0qHG02BIvnCqIFgK6zo45dwmnGnf5JX69Tc6vgU/07Khxl52g621yTgmnCqV0gm6wyfkV8KmeHRVKieHcKpwqxCbsTbK+BD7TM6RCy7Wmpybt/yUpFVbAP+puUqALBXsEAAAAAElFTkSuQmCC);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: 0 100%;
    position: absolute;
    left: 0;
    bottom: 0;
}

.container_body {
    pointer-events: auto;
    background-color: var(--bg);
    z-index: 9;
    border-radius: 20px;
    border: 2px solid var(--activeColorRgba);
    float: right;
    position: relative;

    .container {
        position: absolute;
        width: 98%;
        height: 100%;
        pointer-events: auto;
        font-family: PangMenZhengDao;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        padding: 0 12px;

        .value {
            font-size: 20px;
            font-weight: 700;
            color: #0ff;
        }

        .title {
            font-size: 18px;
            color: #fff;
        }

        .unit {
            font-size: 18px;
        }

        .button {
            border-radius: 10px;
            border: 1px solid var(--activeColor);
            padding: 5px 10px;
            cursor: pointer;
        }
    }
}

.container_breathe {
    width: 20px;
    height: 20px;
    position: absolute;
    bottom: -10px;
    left: -10px;
    background: var(--activeColor);
    border-radius: 100%;
}

.ring {
    position: absolute;
    background-color: inherit;
    height: 100%;
    width: 100%;
    border-radius: 100%;
    opacity: .8;
    -webkit-animation: pulsing-5b412b8b 2s ease-out infinite;
    animation: pulsing-5b412b8b 2s ease-out infinite;
}

.ring:first-of-type {
    -webkit-animation-delay: -.5s;
    animation-delay: -.5s;
}

.ring:nth-of-type(2) {
    -webkit-animation-delay: -1s;
    animation-delay: -1s;
}

.ring:nth-of-type(3) {
    -webkit-animation-delay: -1.5s;
    animation-delay: -1.5s;
}

@-webkit-keyframes pulsing-5b412b8b {
    to {
        transform: scale(2.75);
        opacity: 0;
    }
}

@keyframes pulsing-5b412b8b {
    to {
        transform: scale(2.75);
        opacity: 0;
    }
}
</style>