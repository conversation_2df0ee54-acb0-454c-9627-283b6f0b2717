<template>
    <div class="z-99 fixed top-0 left-0 w-100vw h-100vh" v-loading>456</div>
</template>
<script setup>
import mitt from '@/utils/eventHub';
import { ref, onMounted, watch } from 'vue';
import { onBeforeRouteLeave, useRouter } from 'vue-router';
import { useThereVentilation } from '@/store/thereVentilation';
const store = useThereVentilation()
const router = useRouter();

onMounted(() => {

})

</script>
<style lang="scss" scoped></style>