<template>
  <div class="nav_bar  w-100vw fixed top-0 z-2">
    <div class="nav_bar_bg flex items-center justify-around">
      <div class="text-25px font-extrabold fixed left-70px span_kj">
        <span class="backdrop-blur-sm bg-clip-text text-transparent bg-gradient-to-r from-#ece9e6 to-#ffffff">
          <span>矿&nbsp;井&nbsp;智&nbsp;能&nbsp;通&nbsp;风&nbsp;平&nbsp;台</span>
        </span>
      </div>
      <div class="flex justify-around items-center w-65% cursor-pointer fixed right-100px top-20px">
        <div @mouseover="hoverRouter(i, v)"      @mouseleave=" (selectShow = false)"
          v-for="(v, i) in navBarList" :key="v.Url" class="relative flex items-center mr-23px"
          @click="v.children ? '' : changeRouter(i, v.Url)">
          <div class="flex items-center">
          
            <div :class="menuCurrentIndex == i ? 'nav_bar_menu_active' : 'nav_bar_menu'
              " class="mt-5px">
              <span>{{ v.Name }}</span>
            </div>
            <!-- <div v-show="menuCurrentIndex != i">
              <img src="/src/assets/png/menu_dectora.png" class=" w-19px h-30px" />
            </div> -->
            <!-- <div v-show="menuCurrentIndex == i"> -->
            <!-- <img src="/src/assets/png/menu_dectora_active.png" class=" w-19px h-30px" /> -->
            <!-- </div> -->
            <div v-if="v.children" class="absolute top-35px">
              <transition v-if="v.Name != '业务中心'" leave-active-class=" "
                enter-active-class="animate__animated animate__fadeIn">
                <div v-show="menuCurrentIndex == i && selectShow" class="nav_select">
                  <div :class="route.name == v1.Url
                    ? 'nav_select_item_active'
                    : 'nav_select_item'
                    " class="nav_select_item" @click="changeRouter(i, v1.Url)" v-for="(v1, i) in v.children" :key="v1">
                    <span>{{ v1.Name }}</span>
                  </div>
                </div>
              </transition>
              <transition v-else leave-active-class=" " enter-active-class="animate__animated animate__fadeIn">
                <div v-show="menuCurrentIndex == i && selectShow" class="nav_select1 ">
                  <div class="flex items-center border-b-2 mx-5px border-solid border-orange-500/30 "
                    v-for="(title, index) in titleList">
                    <div
                      class=" text-[#ffffffe6] nav_select1_box shadow-xl text-right  text-18px font-bold h-50px w-85px mr-20px flex items-center justify-center">
                      {{ title }}</div>
                    <div class="flex justify-start items-center w-300px ">
                      
                      <div :class="route.name == v1.Url
                        ? 'nav_select_item_active'
                        : 'nav_select_item'
                        " class="nav_select_item" @click="changeRouter(i, v1.Url)"
                        v-for="(v1, i) in filterData(index + 1, v.children)">
                        <span>{{ v1.Name }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </transition>
            </div>
          </div>
        </div>
      </div>
      <div class="fixed top-10px right-10px flex items-center">
        <div class="relative">
          <div class="w-100px" @click="alarm">
            <div>
              <!-- 未读消息提示 -->
              <messageTip />
            </div>
          </div>
        </div>
        <el-tooltip class="item" effect="dark" :content="'当前账号 : ' + store.token.Account" placement="bottom">
          <div class="text-16px font-extrabold cursor-pointer  qu_x" text="#fff" @click="quitLogin">
            注销
          </div>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>
<script setup>
// 获取title数据
import { ref, onMounted, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useWindowSize } from "@vueuse/core";
import { useNavBar } from "@/store/navBar";
import { ElMessage, ElMessageBox } from "element-plus";

import messageTip from "../messageTip.vue";

// 获取登录tokne
import { useCommonStore } from "@/store/common";
import mitt from "@/utils/eventHub";
import messageTipVue from "../messageTip.vue";
const store = useCommonStore();
const { width, height } = useWindowSize();

// 筛业务组
const titleList = ['权限', '报表', '报警', '三维配置', '三维编辑']
function filterData(flagType, list = []) {
  let str = "";
  switch (flagType) {
    case 1: str = '管理'; break;
    case 2: str = '报'; break;
    case 3: str = '报警'; break;
    case 4: str = '配置'; break;
    case 5: str = '编辑'; break;
    // case 5: str = '报警'; break;
  }
  return list.filter((v) => {
    if (str == "报") {
      return v.Name.indexOf(str) !== -1 && v.Name.length == 2;
    } else if (str == "配置") {
      return v.Name.indexOf(str) !== -1 && v.Name.indexOf("报警") == -1;
    } else return v.Name.indexOf(str) !== -1;
  });
}

const router = useRouter();
const route = useRoute();
const menuCurrentIndex = ref(-1);
const selectShow = ref(false);

// 当前时间
const currentTime = ref();
function refresh() {
  setInterval(() => {
    const now = new Date(); // 创建一个代表当前时间的 Date 对象
    // 获取年、月、日、时、分、秒
    const year = now.getFullYear();
    const month = formatTime(now.getMonth() + 1); // 月份是从 0 开始的，所以需要加 1
    const day = formatTime(now.getDate());
    const hours = formatTime(now.getHours());
    const minutes = formatTime(now.getMinutes());
    const seconds = formatTime(now.getSeconds());
    // 根据需要格式化时间字符串
    currentTime.value = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }, 1000);
  function formatTime(v) {
    return v > 9 ? v : "0" + v;
  }
}
onMounted(() => {
  watch(
    () => store.ModuleRoleList,
    (val) => {
      if (val.length != 0) {
        setMenu();
      }
    }
  );
  setMenu();
});
function setMenu() {
  let arr = [];
  arr = store.ModuleRoleList;
  for (let i = arr.length - 1; i >= 0; i--) {
    const obj = arr[i];
    if (obj.ModuleId) {
      const index = arr.findIndex((item) => item.ID === obj.ModuleId);
      if (index !== -1) {
        const matchedObj = arr[index];
        if (!matchedObj.children) {
          matchedObj.children = [];
        }
        matchedObj.children.push(obj);
        arr.splice(i, 1);

        // 处理 matchedObj.children，确保每个对象的 ID 是唯一的
        if (matchedObj.children.length > 1) {
          let seenIds = new Set();
          matchedObj.children = matchedObj.children.filter((child) => {
            if (!seenIds.has(child.ID)) {
              seenIds.add(child.ID);
              return true;
            }
            return false;
          });
        }
      }
    }
  }

  navBarList.value = arr.sort((v1, v2) => v1.SortCode - v2.SortCode); // 排序
  const activeName = route.name;
  active(activeName);
  refresh();
}
function removeDuplicateNames(arr) { }

// 监听屏幕高度变化
const styleChange = ref("ml-900px");
watch(height, (val) => {
  if (val >= 1080) {
    styleChange.value = "ml-700px";
  } else {
    styleChange.value = "ml-900px";
  }
});
const navBarList = ref([
  // { name: "controlCenter", text: "管控中心" },
  // {
  //   name: "",
  //   text: "三维通风",
  //   children: [
  //     { name: "threeVentilate", text: "三维通风" },
  //     { name: "smartAnemometry", text: "智能测风" },
  //   ],
  // },
  // { name: "ventilationNetwork", text: "通风网络" },
  // {
  //   name: "",
  //   text: "通风设施",
  //   children: [
  //     { name: "mainFan", text: "主要通风机" },
  //     { name: "localFan", text: "局部通风机" },
  //     { name: "airDoor", text: "智能风门" },
  //     { name: "airWindow", text: "智能风窗" },
  //   ],
  // },
  // { name: "smartDust", text: "智能降尘" },
  // {
  //   name: "",
  //   text: "智能防火",
  //   children: [
  //     { name: "fireProof", text: "光纤测温" },
  //     { name: "fireproofOnline", text: "防火在线" },
  //   ],
  // },
  // { name: "smartMonitoring", text: "智能监控" },
  // // { name: "smartExtraction", text: "智能抽采" },
  // {
  //   name: "",
  //   text: "业务中心",
  //   children: [
  //     { name: "day", text: "日报" },
  //     // { name: "weeks", text: "周报" },
  //     // { name: "month", text: "月报" },
  //     // { name: "years", text: "年报" },
  //     { name: "roleManagement", text: "用户管理" },
  //     { name: "user", text: "角色管理" },
  //     { name: "privilegeManagement", text: "管理" },
  //   ],
  // },
]);
function hoverRouter(i, v) {
 // menuCurrentIndex.value = i;
  if (v.children) {
    selectShow.value = true;
  }
}

function changeRouter(index, name) {
  menuCurrentIndex.value = index;

  if (!name) return;
  router.push({ name });
  useNavBar().$patch({ currentRouteName: name });
  selectShow.value = false;
}

// 退出登录
function quitLogin() {
  router.push({ name: "logIn" });
  // 发送退出登录的全局事件
  mitt.emit("quitLogin", true);
  // 清空菜单列表
  store.$patch({
    ModuleRoleList: [],
  });
}
function to() {
  ElMessageBox.confirm("您确定退出吗", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      router.push("/");
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "已取消",
      });
    });
}

watch(
  () => route.name,
  (val) => {
    if (!val) return;
    const activeName = route.name;
    active(activeName);
  }
);

function active(activeName) {
  let index = -1;
  navBarList.value.forEach((v, i) => {
    if (v.Url == activeName) {
      index = i;
    }
    if (v.children) {
      v.children.forEach((v1, i1) => {
        if (v1.Url == activeName) {
          index = i;
        }
      });
    }
  });
  menuCurrentIndex.value = index;
}
</script>
<style lang="scss" scoped>
.nav_bar_bg {
  width: 100vw;
  height: 71px;
  background-image: url("@/assets/img/common/head-nav-title.png");
  background-size: 100vw 71px;
  text-align: center;
  line-height: 71px;
  font-style: italic;
}

.nav_select {
  width: 120px;
  height: 100%;
  margin-left: -10px;
  text-align: center;
  background-image: url("../../assets/png/nav_select1.png");
  // 上 左 下 右
  padding: 0px 15px 15px 0px;

  background-size: 100% 100%;
  font-style: normal;


  .nav_select_item {
    height: 40px;
    /* text-align: center; */
    opacity: 0.7;
    color: #fefeff;

    span {
      text-align: center;

      width: 63px;
      height: 36px;
      font-size: 16px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      // line-height: 36px;
    }

    &:hover {
      color: #fda97b;
      opacity: 0.9;
    }
  }


  .nav_select_item_active {
    @extend .nav_select_item;
    color: #fda97b;
    opacity: 0.9;
  }
}

.nav_select1 {
  @extend .nav_select;
  margin-left: -345px;
  width: 500px;
  height: 50%;
  padding: 10px;
  background-size: 100% 100%;

  .nav_select1_box {
    /* background-color: #fff3cd; */
    /* 浅黄色背景 */
    border-radius: 12px;
    /* 圆角 */
    box-shadow:
      0 4px 6px rgba(255, 193, 7, 0.3),
      /* 浅黄色阴影 */
      0 6px 12px rgba(255, 193, 7, 0.4),
      /* 中等黄色阴影 */
      0 12px 24px rgba(255, 193, 7, 0.6);
    /* 深黄色阴影 */
    transition: box-shadow 0.3s ease-in-out;
    /* 添加过渡效果 */
  }

  .nav_select_item {
    height: 30%;
    margin: 0 5px 0 5px;
    text-align: left;
    opacity: 0.7;
    color: #fefeff;

    span {
      height: 36px;
      font-size: 16px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      line-height: 36px;
    }
  }
}

.nav_bar_menu_common {
  width: auto;
  padding-right: 2px;
  height: 38px;
  font-size: 20px;
  font-family: Source Han Sans CN;
  font-weight: bolder;
  color: #ffffff;
  line-height: 38px;
  opacity: 0.7;
  background-image: -webkit-gradient(linear, left top, right top, color-stop(0, transparent), 
  color-stop(30%, rgba(0, 0, 0, .2)), color-stop(60%, rgba(0, 0, 0, .2)), to(transparent));

  font-style: oblique;
  &:hover {
    color: #fda97b;
    
  }
}


.nav_bar_menu {
  @extend .nav_bar_menu_common;



  &:hover {
    color: #fda97b;
  }
}

.nav_bar_menu_common::before {
  content: "";
  position: absolute;
  top: 5px;
  left: 0;
  width: 100%;
  height: 2px;
  background-image: -webkit-gradient(linear, left top, right top, color-stop(0, rgba(255, 179, 15, 0)), color-stop(30%, #f9ecc8), color-stop(60%, #f9ecc8), to(rgba(255, 179, 15, 0)));
}

/* 使用 ::after 伪元素创建下横线 */
.nav_bar_menu_common::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-image: -webkit-gradient(linear, left top, right top, color-stop(0, rgba(255, 179, 15, 0)), color-stop(30%, #f9ecc8), color-stop(60%, #f9ecc8), to(rgba(255, 179, 15, 0)));
}

.nav_bar_menu_active {
  @extend .nav_bar_menu_common, ;
  background-image: -webkit-gradient(linear, left top, right top, color-stop(0, rgba(140, 91, 19, 0)), color-stop(30%, rgba(140, 91, 19, .5)), color-stop(60%, rgba(140, 91, 19, .7)), to(rgba(140, 91, 19, 0)));
  color: #fda97b;

}

// 矿井大标题
.span_kj {
  span {
    background-image: linear-gradient(0deg, #ffb30f 20%, #fff 65%);
    font-family: pang;
    padding-left: 5px;
    font-style: oblique;
    font-size: 42px;
    /* 将背景裁剪到文本 */
    -webkit-background-clip: text;
    background-clip: text;
    /* 使文本透明 */
    -webkit-text-fill-color: transparent;
  }
}

.nav_bar_title {
  span {
    width: 268px;
    height: 26px;
    font-size: 28px;
    font-family: PangMenZhengDao-3;
    font-weight: 600;
    color: #ffffff;
    line-height: 38px;
  }
}


//  
.qu_x {
  &:hover {
    color: red;
  }
}

.tiem {
  background: url("../../assets/png/nav_select.png") !important;
  background-size: 100% 100%;
  color: #fff !important;
}

::v-deep .el-dropdown-menu__item:not(.is-disabled):focus {
  color: #fda97b;
  background-color: transparent;
}

::v-deep .el-dropdown-menu__item {
  color: #fff;
}

::v-deep .el-popper__arrow::before {
  width: 0px !important;
  height: 0px !important;
}
</style>