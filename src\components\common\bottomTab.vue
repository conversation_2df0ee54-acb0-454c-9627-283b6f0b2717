<template>
  <div class="bottom-wrapper fixed bottom-20">
    <img
      class="arrow-left w-25px h-25px"
      src="../common/assest/img/arrow_icon.png"
      @click="handleChange()"
    />
    <div
      :class="['scroll-content', { 'scroll-content__overflow': isOverflow }]"
      ref="scrollContentRef"
    >
      <div
        :class="['scroll-item', { 'scroll-item__active': currentId === v.id }]"
        v-for="(v, i) in bottomTabList"
        :key="i"
        @click="handleTab(v)"
        ref="tabItems"
      >
        {{ v.name }}
      </div>
    </div>
    <img
      class="arrow-right w-25px h-25px"
      src="../common/assest/img/next_right.png"
      @click="handleChange(true)"
    />
  </div>
</template>
<script setup>
import { ref, onMounted, watch, onBeforeUnmount } from "vue";
// 注册父组件的change方法
const emit = defineEmits(["change"]);

// 底部菜单栏, 在mian组件中，通过props传进来， 不传就是默认值
const props = defineProps({
  bottomTabList: {
    type: Array,
    required: true,
  },
  allData: {
    type: Array,
    required: true,
  },
});

const currentId = ref(null);
const scrollContentRef = ref(null);
const isOverflow = ref(false);
const tabItems = ref([]);

watch(
  () => props.bottomTabList,
  (newVal, oldVal) => {
    if (Array.isArray(newVal) && newVal.length > 0) {
      currentId.value = newVal[0].id;
    }
  },
  { deep: true }
);

const handleTab = (item) => {
  currentId.value = item.id;
  // console.log('currentId.value===',currentId.value,item.id)
  emit("change", {
    item,
    allData: props.allData,
  });
};

const checkOverflow = () => {
  if (scrollContentRef.value) {
    isOverflow.value =
      scrollContentRef.value.scrollWidth > scrollContentRef.value.clientWidth;
    console.log(isOverflow.value, "isOverflow.value");
  }
};

const scrollToCurrent = (index) => {
  const currentElement = tabItems.value[index];
  if (currentElement) {
    currentElement.scrollIntoView({
      behavior: "smooth",
      block: "nearest",
      inline: "start",
    });
  }
};

const handleChange = (isNext = false) => {
  let temp = 0;
  const index = props.bottomTabList.findIndex(
    (item) => item.id === currentId.value
  );
  if (isNext) {
    if (index < props.bottomTabList.length - 1) {
      temp = index + 1;
    }
  } else {
    if (index > 0) {
      temp = index - 1;
    } else {
      temp = props.bottomTabList.length - 1;
    }
  }
  console.log(temp, "temp");
  scrollToCurrent(temp);
  handleTab(props.bottomTabList[temp]);
};

onMounted(() => {
  setTimeout(() => {
    checkOverflow();
  }, 200);

  window.addEventListener("resize", checkOverflow);
});
onBeforeUnmount(() => {
  window.removeEventListener("resize", checkOverflow);
});

watch(scrollContentRef, checkOverflow);
</script>

<style lang="scss" scoped>
.bottom-wrapper {
  left: 23vw;
  background-image: url("../../assets/img/common/bottom-menu.png");
  background-size: 100% 100%;
  width: 54vw;
  height: 11vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 50px;
  .arrow-left {
    cursor: pointer;
  }
  .arrow-right {
    cursor: pointer;
  }
  .scroll-content {
    width: 80%;
    overflow-x: auto;
    display: flex;
    gap: 20px;
    justify-content: center;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }
    &__overflow {
      justify-content: flex-start;
    }
    .scroll-item {
      display: inline-block;
      padding: 8px 16px;
      margin: 0 8px;
      text-decoration: none;
      color: #efefef; /* 文字颜色 */
      font-size: 14px;
      background: linear-gradient(
        to bottom,
        #384241,
        #524f46
      ); /* 金色渐变背景，可根据实际调整色值 */
      border: 1px solid #bf8f5d; /* 边框颜色，营造立体感 */
      border-radius: 6px;
      position: relative;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 45px;
      margin-left: 56px;
      margin-top: 12px;
      text-align: center;
      border-radius: 4px;
      padding: 8px 26px;
      transition: all 0.3s;
      cursor: pointer;
      &__active {
        background: linear-gradient(to bottom, #524526, #ba810f);
        transform: translateY(-2px);
      }
    }
  }
}
</style>