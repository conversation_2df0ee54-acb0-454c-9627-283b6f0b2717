<template>
  <div class="four-container">
    <div class="fourBorder border1"></div>
    <div class="fourBorder border2"></div>
    <div class="fourBorder border3"></div>
    <div class="fourBorder border4"></div>
    <slot></slot>
  </div>
</template>

<script lang="ts" setup></script>

<style lang="scss" scoped>
/* $bg: var(--el-color-primary); */
$bg: #838B93;
$angleBg: #e4b22b;
/* $bg: transprent; */
/* $angleBg: #7429ff; */

.four-container {
  width: 100%;
  height: 100%;
  position: relative;
  box-sizing: border-box;
  background: rgba(75, 139, 247, 0.05);
  /* box-shadow: 1px 1px 1px $bg inset; */
  border: 1px solid #e4b22b66;

  .fourBorder {
    position: absolute;
    width: 10px;
    height: 10px;
  }

  .border1 {
    left: -1px;
    top: -1px;
    border-left: 2px solid $angleBg;
    border-top: 2px solid $angleBg;
  }

  .border2 {
    right: -1px;
    top: -1px;
    border-right: 2px solid $angleBg;
    border-top: 2px solid $angleBg;
  }

  .border3 {
    left: -1px;
    bottom: -1px;
    border-left: 2px solid $angleBg;
    border-bottom: 2px solid $angleBg;
  }

  .border4 {
    right: -1px;
    bottom: -1px;
    border-right: 2px solid $angleBg;
    border-bottom: 2px solid $angleBg;
  }
}
</style>
