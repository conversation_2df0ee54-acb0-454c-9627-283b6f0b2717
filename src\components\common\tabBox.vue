<template>
    <div ref="box">
        <div class="fan_title">
            <span>{{ title }}</span>
        </div>
        <div v-if="tabNameList.length" class="flex justify-end mt-31px mr-9px">
            <div v-for="(v, i) in tabNameList" :key="i"
                :class="tabNameList.length > 1 && fanActive == i ? 'fanbtn_active' : 'fanbtn'"
                @click="changeFanActive(i)">
                <span>{{ v }}</span>
            </div>
        </div>
        <slot name="mainFan"></slot>
    </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
const props = defineProps({
    title: {
        type: String,
        default: '-'
    },
    tabNameList: {
        type: Array,
        default: []
    },
})
const emit = defineEmits(['sendFanActive'])
const fanActive = ref(0)
function changeFanActive(num) {
    fanActive.value = num;
    console.log(fanActive.value, 'fanActive.value');
    emit('sendFanActive', fanActive.value)
}

</script>
<style lang="scss" scoped>
.fan_title {
    width: 150px;
    height: 17px;
    font-size: 18px;
    margin: 3px 0 0 37px;
    font-family: AlimamaShuHeiTi;
    font-weight: bold;
    color: #EBF5F8;
    line-height: 30px;
}

.fanbtn_common {
    width: 90px;
    height: 36px;
    cursor: pointer;
    text-align: center;
}

.fanbtn_active {
    @extend .fanbtn_common;
    border: 1px solid #E98650;
    background-image: url('./img/btn_bg.png');
    background-size: 90px 36px;

    span {
        width: 90px;
        height: 36px;
        font-size: 16px;

        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #E68550;
        line-height: 36px;
    }
}

.fanbtn {
    @extend .fanbtn_common;
    border: 1px solid #08658D;
    border-image: linear-gradient(0deg, #08658D) 10 10;
    background: linear-gradient(98deg, rgba(14, 57, 90, 0.99) 52%);

    span {
        width: 60px;
        height: 15px;
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #D0F2F7;
        line-height: 36px;
    }


}
</style>