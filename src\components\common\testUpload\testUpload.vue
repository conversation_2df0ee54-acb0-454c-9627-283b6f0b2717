<template>
    <div pt-100px>
        <input ref="excel_upload" class="excel-upload-input" type="file" accept=".csv,.xlsx, .xls" multiple
            @change="handleClick">
        <div class="drop" @drop="handleDrop" @dragover="handleDragover" @dragenter="handleDragover">
            <div @click="handleUpload" class="flex justify-center items-center cursor-pointer w-full h-full">
                <div>
                    <el-icon size="50rem">
                        <UploadFilled class="icon" />
                    </el-icon>
                    <div class="text-14px   icon-label">点击或者拖拽上传</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>

import { ElMessage } from 'element-plus';
import { ref, reactive } from 'vue';
import * as XLSX from 'xlsx'
import { UploadFilled } from '@element-plus/icons-vue';
import mitt from "@/utils/eventHub";
const emit = defineEmits('result')
const { fileSize } = defineProps(['fileSize'])


let loading = ref(false)
let excel_upload = ref(null)
let excelData = reactive({
    header: null,
    results: null
})

function beforeUpload(file) {
    // const isLt = file.size / 1024 / 1024 < (fileSize ?? 2)
    // if (isLt) {
    //     return true
    // }
    // ElMessage.warning('上传文件大小不能超过' + (fileSize ?? 2) + 'M')
    // return false

    return true
}

function onSuccess({ results, header }) {
    emit('result', results)
    console.log(results, 'onSuccess');
    mitt.emit('excelUpload', results);
}


function generateData(header, results) {
    excelData.header = header
    excelData.results = results
    // console.log(results, 'results');
    onSuccess && onSuccess(excelData)
}

function handleDrop(e) {
    e.stopPropagation()
    e.preventDefault()
    if (loading.value) return
    if (!e.dataTransfer) return
    const files = e.dataTransfer.files
    if (files) {
        for (const item of files) {
            readerData(item)
        }
    }
    e.stopPropagation()
    e.preventDefault()
}

function handleDragover(e) {
    e.stopPropagation()
    e.preventDefault()
    if (e.dataTransfer) {
        e.dataTransfer.dropEffect = 'copy'
    }
}

function handleUpload() {
    excel_upload.value.click()
}
const fileList = ref()
function handleClick(e) {
    const files = e.target.files
    fileList.value = files;
    if (files) {
        // const rawFile = files[0] // only use files[0]
        for (const item of files) {
            console.log(item, 'element');
            readerData(item)
        }
    }
}

function upload(rawFile) {

}

function readerData(rawFile) {
    loading.value = true
    const reader = new FileReader()
    reader.onload = e => {
        const data = e.target.result
        const workbook = XLSX.read(data, { type: 'array' })
        const firstSheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[firstSheetName]
        const header = getHeaderRow(worksheet)
        const results = XLSX.utils.sheet_to_json(worksheet)
        //手动设置
        // const header = ['x', 'y', 'z']
        // const results = XLSX.utils.sheet_to_json(worksheet).map(v => { return Object.values(v) }).map((v, i) => {
        //   const obj = {};
        //   v.forEach((v2, j2) => {
        //     obj[header[j2]] = v2;
        //   })
        //   return obj
        // });
        // .map(v => v.values())
        generateData(header, results)
        loading.value = false
    }
    reader.readAsArrayBuffer(rawFile)
}

function getHeaderRow(sheet) {
    const headers = []
    const range = XLSX.utils.decode_range(sheet['!ref'])
    const R = range.s.r
    // start in the first row
    for (let C = range.s.c; C <= range.e.c; ++C) { // walk every column in the range
        const cell = sheet[XLSX.utils.encode_cell({ c: C, r: R })]
        // find the cell in the first row
        let hdr = ''
        if (cell && cell.t) hdr = XLSX.utils.format_cell(cell)
        if (hdr === '') {
            hdr = 'UNKNOWN ' + C // replace with your desired default
        }
        headers.push(hdr)
    }
    return headers
}

function isExcel(file) {
    return /\.(xlsx|xls|csv)$/.test(file.name)
}

</script>

<style lang="scss" scoped>
.excel-upload-input {
    display: none;
    z-index: -9999;
}

.drop {
    border: 2px dashed #bbb;
    width: 600px;
    height: 160px;
    margin: 0 auto;
    font-size: 24px;
    border-radius: 5px;
    text-align: center;
    color: #bbb;
    position: relative;

    .icon-label {
        color: white;
    }

    .icon {
        color: white;
    }

    &:hover {
        border-color: #4B9CFB;

        .icon-label {
            color: #4B9CFB;
        }

        .icon {
            color: #4B9CFB;
        }
    }

}
</style>