<template>
  <div>
    <input ref="excel_upload" id="fileInput" class="excel-upload-input" type="file" accept=".csv,.xlsx, .xls"
      @change="handleClick" />
    <!-- 图标元素 -->
    <div @drop="handleDrop" @dragover="handleDragover" @dragenter="handleDragover" @click="handleUpload">
      <slot name="icon">
        <el-icon size="32rem">
          <UploadFilled class="icon" />
        </el-icon>
      </slot>
    </div>

    <!-- 拖拽框 -->
    <!-- <div class="drop" @drop="handleDrop" @dragover="handleDragover" @dragenter="handleDragover">
      <div @click="handleUpload" class="flex justify-center items-center cursor-pointer w-full h-full">
        <div>
          <el-icon size="50rem">
            <UploadFilled class="icon" />
          </el-icon>
          <div class="text-14px   icon-label">拖拽上传</div>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script setup>

import { ElMessage } from 'element-plus';
import { ref, reactive } from 'vue';
import * as XLSX from 'xlsx'
import { UploadFilled } from '@element-plus/icons-vue';
const emit = defineEmits('result')
const { fileSize } = defineProps(['fileSize'])


let loading = ref(false)
let excel_upload = ref(null)
let excelData = reactive({
  header: null,
  results: null
})

function beforeUpload(file) {
  const isLt = file.size / 1024 / 1024 < (fileSize ?? 2)
  if (isLt) {
    return true
  }
  ElMessage.warning('上传文件大小不能超过' + (fileSize ?? 2) + 'M')
  return false
}

function onSuccess({ results, header }) {
  emit('result', results)
}


function generateData(header, results) {
  excelData.header = header
  excelData.results = results
  onSuccess && onSuccess(excelData)
}

function handleDrop(e) {
  e.stopPropagation()
  e.preventDefault()
  if (loading.value) return
  if (!e.dataTransfer) return
  const files = e.dataTransfer.files
  if (files.length !== 1) {
    ElMessage.error('只支持上传一个文件!')
    return
  }
  const rawFile = files[0] // only use files[0]

  if (!isExcel(rawFile)) {
    ElMessage.error('只支持上传 .xlsx, .xls, .csv 格式文件!')
    return false
  }
  upload(rawFile)
  e.stopPropagation()
  e.preventDefault()
}

function handleDragover(e) {
  e.stopPropagation()
  e.preventDefault()
  if (e.dataTransfer) {
    e.dataTransfer.dropEffect = 'copy'
  }
}

function handleUpload() {
  excel_upload.value.click()
}

function handleClick(e) {
  const files = e.target.files
  if (files) {
    const rawFile = files[0] // only use files[0]
    upload(rawFile)
  }
}

function upload(rawFile) {
  excel_upload.value.value = '' // Fixes can't select the same excel
  if (!beforeUpload) {
    readerData(rawFile)
    return
  }
  const before = beforeUpload(rawFile)
  if (before) {
    readerData(rawFile)
  }
}

function readerData(rawFile) {
  loading.value = true
  const reader = new FileReader()
  reader.onload = e => {
    const data = e.target.result
    const workbook = XLSX.read(data, { type: 'array' })
    const firstSheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[firstSheetName]
    const header = getHeaderRow(worksheet)
    const results = XLSX.utils.sheet_to_json(worksheet)
    //手动设置
    // const header = ['x', 'y', 'z']
    // const results = XLSX.utils.sheet_to_json(worksheet).map(v => { return Object.values(v) }).map((v, i) => {
    //   const obj = {};
    //   v.forEach((v2, j2) => {
    //     obj[header[j2]] = v2;
    //   })
    //   return obj
    // });
    // .map(v => v.values())
    generateData(header, results)
    // console.log(results);
    // console.log(header);
    loading.value = false
  }
  reader.readAsArrayBuffer(rawFile)
}

function getHeaderRow(sheet) {
  const headers = []
  const range = XLSX.utils.decode_range(sheet['!ref'])
  const R = range.s.r
  // start in the first row
  for (let C = range.s.c; C <= range.e.c; ++C) { // walk every column in the range
    const cell = sheet[XLSX.utils.encode_cell({ c: C, r: R })]
    // find the cell in the first row
    let hdr = ''
    if (cell && cell.t) hdr = XLSX.utils.format_cell(cell)
    if (hdr === '') {
      hdr = 'UNKNOWN ' + C // replace with your desired default
    }
    headers.push(hdr)
  }
  return headers
}

function isExcel(file) {
  return /\.(xlsx|xls|csv)$/.test(file.name)
}

</script>

<style lang="scss" scoped>
.excel-upload-input {
  display: none;
  z-index: -9999;

}

/* 隐藏 input 元素 */
#fileInput {
  display: none;
}

/* 设置图标的样式 */
.upload-icon {
  font-size: 30px;
  color: #007BFF;
  cursor: pointer;
}

/* 鼠标悬停时的样式 */
.upload-icon:hover {
  color: #0056b3;
}

.drop {
  border: 2px dashed #bbb;
  width: 500px;
  height: 160px;
  margin: 0 auto;
  font-size: 24px;
  border-radius: 5px;
  text-align: center;
  color: #bbb;
  position: relative;

  .icon-label {
    color: white;
  }

  .icon {
    color: white;
  }

  &:hover {
    border-color: #4B9CFB;

    .icon-label {
      color: #4B9CFB;
    }

    .icon {
      color: #4B9CFB;
    }
  }

}
</style>
