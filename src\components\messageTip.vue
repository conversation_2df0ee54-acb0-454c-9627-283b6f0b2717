<template>
    <div >
      <!--  -->
      <!--  -->
      <div class=""  @click="toggleMessages" @mouseleave="showMessages = false" >
        <!-- <i  src="../assets/icon/alarm_icon.png" class="bell-icon" @click="toggleMessages"></i> -->
        <img class="w-25px h-25px padding-bottom-5px" src="../assets/icon/alarm_icon.png" />
       
        <!-- 报警告警列表 -->
        <div v-if="showMessages" class="message-list">
      <ul class="message_ul">
    
        <li class="message_li" v-for="(item ,index) in msgList" :key="index">{{ item.msg }}</li>
      </ul>
    </div>
    <!-- 报警告警列表结束 -->

    <!-- 未知数 -->
    <div>

      <div v-if="unreadCount > 99" class="dot"><span class="dot_text">99+</span>
   
  </div>
  <div v-else class="dot"><span class="dot_text">{{unreadCount}}</span>
 
</div>
    </div>
    
      
      </div>
    
    </div>
  </template>
   
  <script setup>

  import {ref,onMounted,reactive,computed} from 'vue'



    onMounted  (()=>{
        // change()

})
    const showMessages = ref(false);
    const unreadCount = ref(0);
    const count = ref()
    const msgList = ref([]);
  
    const toggleMessages = () => {
      showMessages.value =!showMessages.value;
    };

    
 const msg = ref()
// 创建signalr连接
 var connection = new signalR.HubConnectionBuilder().withUrl("http://*************:20000/clientHub").withAutomaticReconnect().build();

	//注册告警数据事件
	connection.on("ReceiveAlarmInfo", function (code, data) {
   
		console.info(code);
		console.info(data);
     count.value = data
    msg.value = data.msg
      
    msgList.value.push(data)
    console.info(msgList,"11111122222接收数据")
    console.info(unreadCount.value,'EEEEEEEEEEEEEEEEEE')
    if(count.value.status == 0){
      console.info(count.value.status,'$$$$$$$$$$测试')
      unreadCount.value = unreadCount.value + 1
      
    
      console.log(unreadCount.value,'$11$$$$$$$$$$$$$$')
    }
  
    
	});
	// //注册风速数据事件
	// connection.on("ReceiveWindInfo", function (code, data) {
	// 	console.info(code);
	// 	console.info(data);
	// });
	// //注册安全监控数据事件
	// connection.on("ReceiveSafetyInfo", function (code, data) {
	// 	console.info(code);
	// 	console.info(data);
	// });
	// //注册PCL数据事件(通风设施)
	// connection.on("ReceivePlcInfo", function (code, data) {
	// 	console.info(code);
	// 	console.info(data);
	// });
	//注册扩展数据事件
	connection.on("ReceiveExtendInfo", function (code, data) {
		console.info(code);
		console.info(data);
	});

	//连接关闭事件
	connection.onclose(function () {
		console.info("closed");
	});

	//开始重连连接事件
	connection.onreconnecting(function () {
		console.info("onreconnecting");
	});

	//重连成功事件
	connection.onreconnected(function () {
		console.info("onreconnected");
	});

 
	//启动连接
	connection.start().then(function () {
		console.info("success");
		//连接成功
		//与服务端交互行为
		//注册数据 0:扩展数据;1:告警数据;7:PLC点位数据;8:安全监控数据;9:风速数据
		connection.invoke("RegisterAll", 1).catch(function (err) {
      console.info("!!!!接收数据")
			return console.error(err.toString());
		});
  //   connection.onMessageReceived((message) => {
  //   console.info(message,'聊天信息');
  // });
		//取消注册数据
		// connection.invoke("CancelRegisterAll", 0).catch(function (err) {
		// 	return console.error(err.toString());
		// });
	}).catch(function (err) {
		return console.error(err.toString());
	});

 
  </script>
   
  <style scoped  lang="scss">
  .icon-message{
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: red;
   
    color: #fff;
    margin-top: -15px;
    // position: absolute;
    // top: 10px;
    // left: 10px;
  }

  .bell-icon {
  font-size: 24px;
  cursor: pointer;
}

.message-list {
  position: absolute;
  width: 450px;
  height: 300px;
  overflow-y: scroll;
  top: 55px;
  right:10px;
  background-image: url("../assets/png/nav_select1.png");
  background-size: 100% 100%;
  // background-color: white;
  // border: 1px solid #ccc;
  // padding: 10px;
  // box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);

  &::-webkit-scrollbar {
    width: 2px;
  }

  &::-webkit-scrollbar-track {
    background-color: #232324;
  }

  &::-webkit-scrollbar-thumb {
    background: #232324;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }
}
.message_ul{
  display: flex;
  flex-direction: column;
  color: #fff;
  opacity: 0.8;
 
  
  width: 100%;

}
.message_li{
   color: white; /* 白色文字 */
 border-bottom: 0.5px solid rgba(255, 255, 255, 0.267); /* 白色边框 */;
  display: flex;
  flex-direction: row;
  // font-family: monospace !important; /* 设置为等宽字体，类似图片效果 */
  letter-spacing: 0.5px; /* 字符间距，让文字更舒展，可按需调整 */
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5); /* 文字阴影，增加立体感 */
  margin-top: 10px;
  width: 100%;
  height: 30px;
  margin-top: 20px;
  font-size: 14px; /* 文字大小，可按需调整 */
  line-height: 2; 
  padding-left: 6px;
  font-style: normal;
 
}
.dot {
  width: 20px;
  height: 20px;
  background-color: red;
  border-radius: 50%;
  position: absolute;
  top: 15px;
  right: 2px;

  .dot_text{
    position: absolute;
    top: -27px;
    left: 5px;
    color: #fff;
    font-size: 10px;
  }
}
  </style>