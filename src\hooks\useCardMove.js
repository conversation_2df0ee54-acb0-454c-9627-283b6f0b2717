import { computed, reactive, toRaw, ref } from "vue";

function useCardMove(initialState) {
  const size = reactive(initialState);
  const dragging = ref(false);
  function handleMouseMove(e) {
    size.left = e.clientX;
    size.top = e.clientY;
  }
  function handleMouseDown() {
    dragging.value = true;
  }
  function handleMouseUp() {
    dragging.value = false;
  }
  const width = computed(() => size.width + "px");
  const height = computed(() => size.height + "px");
  const transform = computed(() => `translate(${size.left}px, ${size.top}px)`);
  return {
    size,
    width,
    height,
    transform,
    dragging,

    handleMouseDown,
    handleMouseUp,
    handleMouseMove,
  };
}

export default useCardMove;
