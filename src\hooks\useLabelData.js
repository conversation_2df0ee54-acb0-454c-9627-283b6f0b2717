import { ref, watch } from 'vue';
export function changeLabelPosition(isDataShow) {
    const isShow = ref(false)
    // 弹窗dom
    const labelDiv = ref(null)
    function getLabelDialogEL(val) {
        labelDiv.value = val.value
    }
    // 监听 isDataShow 修改弹窗坐标位置
    watch(isDataShow, (val) => {
        if (val) {
            const { left, top } = val
            isShow.value = val.show
            // 设置弹框的位置
            labelDiv.value && (labelDiv.value.style.left = left + 'px')
            labelDiv.value && (labelDiv.value.style.top = top + 'px')
        } else {
            // 隐藏
            isShow.value = false
        }
    })
    return { getLabelDialogEL, isShow, labelDiv }
}


