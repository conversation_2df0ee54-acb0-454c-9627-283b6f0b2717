import { useClipboard } from "@vueuse/core"
import { ElMessage } from "element-plus";

// 全局loading组件
const vLoading = ((_this) => {
    _this.directive('loading', (el, binding) => {
        let { show } = binding.value ?? { show: true };
        let { style } = binding.value ?? { style: 1 };
        let { wait } = binding.value ?? { wait: 0 };
        let { text } = binding.value ?? { text: '加载中...' };
        style = `loading-${style}`
        var state = show ? 'flex' : 'none'
        el.style.position = 'relative'
        let dom = '';

        switch (style) {
            case 'loading-1':
                dom = `
                <div id="loading-1" class="loading-1" style="display:${state}">
                    <div style="margin:auto">
                        <div class="box">
                            <span></span>
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <div class="text">${text}</div >
                    </div>
                </div>
            `
                break;
            case 'loading-2':
                dom = `
            <div id="loading-2" class="loading-2" style="display:${state}">
               <div style="margin:auto">
                 <div class="balls">
                    <div></div>
                    <div></div>
                    <div></div>
                 </div>
                 <div class="text">${text}</div >
               </div >
            </div >

    `
                break;
        }
        //延时加载
        if (wait) {
            let frag = document.createRange().createContextualFragment(dom);
            el.appendChild(frag)
            const childrenEl = document.getElementsByClassName(style)
            setTimeout(() => {
                let flag = true;
                for (let i = 0; i < childrenEl.length; i++) {
                    if (flag) {
                        if (childrenEl[i] instanceof HTMLElement) {
                            el.removeChild(childrenEl[i])
                            flag = false;
                        }
                    }
                }
            }, wait)
        } else {
            const childrenEl = document.getElementById(style);
            if (show) {
                //将字符串转为dom元素
                if (!childrenEl) {
                    let frag = document.createRange().createContextualFragment(dom);
                    el.appendChild(frag)
                }
            } else {
                if (childrenEl) {
                    el.removeChild(childrenEl)
                }
            }
        }
    })
})
// 复制指令
const vCopy = ((_this) => {
    _this.directive('copy', (el, binding) => {
        const { isSupported, copy } = useClipboard()
        el.onclick = (() => {
            if (binding.value) {
                copy(binding.value)
                ElMessage({
                    type: 'success',
                    message: "复制成功"
                })
            }
        })
    })
})
// 全屏指令
const vFullscreen = ((_this) => {
    _this.directive('fullscreen', (el, binding) => {
        let isFullscreen = binding.value;
        const element = document.documentElement;
        let doc = document;
        // 判断全屏状态的变量
        el.onclick = (() => {
            // 如果是全屏状态
            if (isFullscreen) {
                // 如果浏览器有这个Function
                if (doc.exitFullscreen) {
                    doc.exitFullscreen()
                } else if (doc.webkitCancelFullScreen) {
                    doc.webkitCancelFullScreen()
                } else if (doc.mozCancelFullScreen) {
                    doc.mozCancelFullScreen()
                } else if (doc.msExitFullscreen) {
                    doc.msExitFullscreen()
                }
            } else {
                // 如果浏览器有这个Function
                if (element.requestFullscreen) {
                    element.requestFullscreen()
                } else if (element.webkitRequestFullScreen) {
                    element.webkitRequestFullScreen()
                } else if (element.mozRequestFullScreen) {
                    element.mozRequestFullScreen()
                } else if (element.msRequestFullscreen) {
                    element.msRequestFullscreen()
                }
            }
        })
    })
})
const vJelly = ((_this) => {
    _this.directive('jelly', (el, binding) => {
        el.classList.add('jelly');
    })
})

// 注册指令到app上
export default (app) => {
    vLoading(app)
    vCopy(app)
    vFullscreen(app)
    vJelly(app)
}