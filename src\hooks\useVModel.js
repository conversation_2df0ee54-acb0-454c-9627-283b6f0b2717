import { computed, watch, ref } from "vue";
// export default function useVModel(props, propsName, emit) {
//   return computed({
//     get() {
//       if (
//         Object.prototype.toString.call(props[propsName]) === "[object Object]"
//       ) {
//         return new Proxy(props[propsName], {
//           set(target, key, newValue) {
//             emit(
//               "update:" + propsName,
//               Object.assign(target, { [key]: newValue })
//             );
//             return true;
//           },
//         });
//       }
//       return props[propsName];
//     },
//     set(value) {
//       emit("update:" + propsName, value);
//     },
//   });
// }

export default function useVModel(props, propsName, emit) {
  const modelValue = ref(props[propsName]);
  watch(modelValue, (newVal) => emit("update:" + propsName, newVal), {
    deep: true,
  });
  return modelValue;
}
