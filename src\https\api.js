import { get, post, del, postByFromForm } from './request'
// 登录
export const Log = (accout, timeStr, token) => {
    let api = `/api/v1/client/login`;
    let p = `?accout=${accout}&timeStr=${timeStr}&token=${token}`
    return post({
        api: api,
        url: api + p
    })
}
// 巷道信息更新 
export const getupdate = (data) => {
    let api = `/api/v4/gis/roadway/update`;
    return postByFromForm({
        api: api,
        url: api
    }, { parm: JSON.stringify(data) })
}
// 设备异常查询接口
export const getabnormal = () => {
    let api = `/api/v4/gis/roadway/list`;
    return get({
        api: api,
        url: api
    })
}

// 巷道
export const roadway = () => {
    let api = `/api/v4/gis/roadway/list`;
    return get({
        api: api,
        url: api
    })
}
// 通风解算
export const ApiVentilationCalculation = async () => {
    let api = `/api/v4/gis/tuns/list`;
    return get({
        api: api,
        url: api
    })
}
// 巷道id查询详细信息
// tunid ID
export const getRoadwayIDInquire = (tunid) => {
    let api = `/api/v4/gis/roadway/id`;
    let p = `?tunid=${tunid}`
    return get({
        api: api,
        url: api + p
    })
}
// 三维巷道列表查询
export const getRoadwayLis = () => {
    let api = `/api/v4/gis/roadway/list`;
    return get({
        api: api,
        url: api
    })
}
// 采取列表查询
export const getMiningSectionList = () => {
    let api = `/api/v4/gis/areas/list`;
    return get({
        api: api,
        url: api
    })
}
// 用途列表查询
export const getUseList = () => {
    let api = `/api/v4/gis/lxtype/list`;
    return get({
        api: api,
        url: api
    })
}
// 形状列表查询 
export const getShapeList = () => {
    let api = `/api/v4/gis/shapetype/list`;
    return get({
        api: api,
        url: api
    })
}
// 支护类型列表查询
export const getSupportTypeList = () => {
    let api = `/api/v4/gis/shoringtype/list`;
    return get({
        api: api,
        url: api
    })
}
// 巷道类型列表查询
export const getRoadwayTypeList = () => {
    let api = `/api/v4/gis/usetype/list`;
    return get({
        api: api,
        url: api
    })
}
// 在线阻力误差分析
export const getInlineResistanceAnalyze = () => {
    let api = `/api/v4/gis/system/list`;
    return get({
        api: api,
        url: api
    })
}
// 在线阻力列表
export const getInlineResistanceList = () => {
    let api = `/api/v/gis/hmoitorRoute/list`;
    return get({
        api: api,
        url: api
    })
}
// 在线风速
export const getInlineWindspeed = () => {
    let api = `/api/v4/gis/listAirVolume`;
    return get({
        api: api,
        url: api
    })
}
// 在线CO
export const getInlineCO = () => {
    let api = `/api/v4/gis/listCO`;
    return get({
        api: api,
        url: api
    })
}
// 在线瓦斯
export const getInlineGas = () => {
    let api = `/api/v4/gis/listCH4`;
    return get({
        api: api,
        url: api
    })
}
// 三区分布
export const getThreezonesDistribution = () => {
    let api = `/api/v4/gis/getHList`;
    return get({
        api: api,
        url: api
    })
}
// 困难路线
export const getHardroute = () => {
    let api = `/api/v4/gis/hmax/list`;
    return get({
        api: api,
        url: api
    })
}

// 用风地点查询
export const getWindsite = () => {
    let api = `/api/v4/gis/getUseWindAddress`;
    return get({
        api: api,
        url: api
    })
}
//用风地点列表查询
export const getWindlocaList = () => {
    let api = `/api/v4/windlocation/querryList`;
    return get({
        api: api,
        url: api
    }) 
}
// 用风地点添加
export const getWindlocaAdd = (windDa) => {
    let api = `/api/v4/windlocation/add`;
    let p = `?ID=${windDa.ID}&Name=${windDa.Name}&AirVolume=
    ${windDa.AirVolume}&Requiredairflow=${windDa.Requiredairflow}&Gas=${windDa.Gas}
    &EnterTemperature=${windDa.EnterTemperature}&OutTemperature=${windDa.OutTemperature}&Type=${windDa.Type}
    &Transect=${windDa.Transect}&PersonNum=${windDa.PersonNum}&WindSpeed=${windDa.WindSpeed}
    &LocalFanVolume=${windDa.LocalFanVolume}&TotolPower=${windDa.TotolPower}&Area=${windDa.Area}`
    return post({
        api: api,
        url: api + p
    })
}
//用风地点修改
export const getWindlocaUpdate = (windDa) => {
    let api = `/api/v4/windlocation/update`;
     let p = `?ID=${windDa.ID}&Name=${windDa.Name}&AirVolume=
    ${windDa.AirVolume}&Requiredairflow=${windDa.Requiredairflow}&Gas=${windDa.Gas}
    &EnterTemperature=${windDa.EnterTemperature}&OutTemperature=${windDa.OutTemperature}&Type=${windDa.Type}
    &Transect=${windDa.Transect}&PersonNum=${windDa.PersonNum}&WindSpeed=${windDa.WindSpeed}
    &LocalFanVolume=${windDa.LocalFanVolume}&TotolPower=${windDa.TotolPower}&Area=${windDa.Area}`
    return post({
        api: api,
        url: api + p
    })
 
}
//用风地点删除
export const getWindlocaDel = (ID) => {
    let api = `/api/v4/windlocation/del`;
    let p = `?ID=${ID}`
    return post({
        api: api,
        url: api + p
    })
}
// 安全监控
// 测点查询
export const getMeasuringPoint = (code) => {
    let api = `/api/v2/safety/code`;
    let p = `?code=${code}`
    return get({
        api: api,
        url: api + p
    })
}
// 安全监控类型查询
export const getSecurityMonitoringType = () => {
    let api = `/api/v2/safety/querrylx`;
    return get({
        api: api,
        url: api
    })
}
// 安全监控列表查询 type默认先传-1
export const getSecurityMonitoringList = (lx,adress) => {
    let api = `/api/v2/safety/list`;
    let p=''
    if(adress!==undefined&&adress!==''&&adress!==null){
        // let newadress=adress.value
 p = `?type=-1&lx=${lx}&adress=${adress}`
    }else{
         p = `?type=-1&lx=${lx}`
    }
   
    return get({
        api: api,
        url: api + p
    })
}

// 安全监控分页列表查询 type默认先传-1
export const getSecurityMonitoringPageList = (pageSize, pageIndex, lx) => {
    let api = `/api/v2/safety/pageList`;
    let p = `?pageSize=${pageSize}&pageIndex=${pageIndex}&lx=${lx}&type=-1`
    return get({
        api: api,
        url: api + p
    })
}
// 历史曲线
export const getHistoricalCurve = (startTime, endTime, code) => {
    let api = `/api/v2/safety/Hislist`;
    let p = `?startTime=${startTime}&endTime=${endTime}&code=${code}`
    return get({
        api: api,
        url: api + p
    })
}
// ——————————风门
// 风门列表
export const getWindDoorList = () => {
    let api = `/api/v3/winddoor/list`;
    let p = ``
    return get({
        api: api,
        url: api + p
    })
}
// 风门code查询
export const getWindDoorCode = (code) => {
    let api = `/api/v3/winddoor/code`;
    let p = `?code=${code}`
    return get({
        api: api,
        url: api + p
    })
}
export const controlWindDoor = (obj) => {

    const { id, doorId, controlType } = obj;
    let api = `/api/v3/winddoor/control`;
    let p = `?id=${id}&doorId=${doorId}&controlType=${controlType}`
    return get({
        api: api,
        url: api + p
    })
}
// ——————————风窗
// 风窗列表
export const getWindwindowList = () => {
    let api = `/api/v3/windwindow/list`;
    let p = ``
    return get({
        api: api,
        url: api + p
    })
}
// 风窗code查询
export const getWindwindowCode = (code) => {
    let api = `/api/v3/windwindow/code`;
    let p = `?code=${code}`
    return get({
        api: api,
        url: api + p
    })
}
export const controlWindWindow = ({ id, windowId, controlType }) => {
    let api = `/api/v3/windwindow/control`;
    let p = `?id=${id}&windowId=${windowId}&controlType=${controlType}`
    return get({
        api: api,
        url: api + p
    })
}
// —————————————局扇风机
// 局扇风机列表
export const getMainFanList = () => {
    let api = `/api/v3/mainfan/list`;
    let p = ``
    return get({
        api: api,
        url: api + p
    })
}
// 局扇风机code查询
export const getMainFanCode = (code) => {
    let api = `/api/v3/mainfan/code`;
    let p = `?code=${code}`
    return get({
        api: api,
        url: api + p
    })
}
// ——————————————主通
// 主通列表
export const getLocalFanList = () => {
    let api = `/api/v3/localfan/list`;
    let p = ``
    return get({
        api: api,
        url: api + p
    })
}
// 主通code查询
export const getLocalFanCode = (code) => {
    let api = `/api/v3/localfan/code`;
    let p = `?code=${code}`
    return get({
        api: api,
        url: api + p
    })
}
// 主通报警历史查询
export const getLocalFanHisalarm = (code) => {
    let api = `/api/v3/localfan/hisalarm `;
    let p = `?code=${code}`
    return get({
        api: api,
        url: api + p
    })
}
// ————————————智能抽采
export const getExtractList = () => {
    let api = `/api/v3/extrac/list`;
    return get({
        api: api,
        url: api
    })
}
// ————————————智能防火
// 在线防火基本信息与监测信息
export const getFireproof = () => {
    let api = `/api/v2/fireonline/list`;
    return get({
        api: api,
        url: api
    })
}
// 光纤测温基础信息
export const getFireprBase = () => {
    let api = `/api/v1/fire/fiber/base`;
    return get({
        api: api,
        url: api
    })
}
// 光纤测温列表查询
export const getFireList = () => {
    let api = `/api/v2/fibertemp/list`;
    return get({
        api: api,
        url: api
    })
}

export const getFireQx = (devAddress) =>{
    let api =`/api/v2/fibertemp/hislist`
    let p = `?devAddress=${devAddress}`
    return get({
        api: api,
        url: api + p
    })
}
// api/v2/fibertemp/hislist?devAddress
// 光纤测温实时数据
export const getFireprRealValue = (pageSize, pageIndex) => {
    let api = `/api/v1/fire/fiber/realValue`;
    let p = `?pageSize=${pageSize}&pageIndex=${pageIndex}`
    return get({
        api: api,
        url: api + p
    })
}
// 光纤测温预警报警
export const getFireprAlarm = (startTime, endTime) => {
    let api = `/api/v1/fire/fiber/alarm`;
    // 暂不传
    // let p = `?startTime=${startTime}&endTime=${endTime}`
    return get({
        api: api,
        url: api
        // url: api + p
    })
}
// ————————————角色访问模块
// 权限列表查询
export const getModuleList = () => {
    let api = `/api/v1/module/list`;
    return get({
        api: api,
        url: api
    })
}
// 权限分页加名称查询
export const getModulePageList = (name) => {
    let api = `/api/v1/module/pagelist`;
    let p = ''
    if (name != undefined) {
        p = `?name=${name}`;
    }
    return get({
        api: api,
        url: api + p
    })
}
// 角色id查询模块访问权限 
export const getModuleRoleId = (roleId) => {
    let api = `/api/v1/module/list/roleId`;
    let p = `?roleId=${roleId}`
    return get({
        api: api,
        url: api + p
    })
}
// 给角色分配页面访问权限
export const getsetModule = (data) => {
    let api = `/api/v1/role/setModule`;
    return postByFromForm({
        api: api,
        url: api
    }, { parm: JSON.stringify(data) })
}
// 权限添加
export const getModuleAdd = (formInline) => {
    let api = `/api/v1/module/add`;
    let p
    if (formInline.ModuleId == undefined || formInline.ModuleId == null) {
        p = `?Name=${formInline.Name}&Url=${formInline.Url}&SortCode=${formInline.SortCode}`

    } else {
        p = `?Name=${formInline.Name}&Url=${formInline.Url}&ModuleId=${formInline.ModuleId}&SortCode=${formInline.SortCode}`

    }
    return post({
        api: api,
        url: api + p
    })
}
// 权限更新
export const getModuleUndete = (formInline) => {
    let api = `/api/v1/module/update`;
    let p = `?ModuleId=${formInline.ModuleId}&Name=${formInline.Name}&Url=${formInline.Url}&ID=${formInline.ID}&CreatorTime=${formInline.CreatorTime}&SortCode=${formInline.SortCode}`

    return post({
        api: api,
        url: api + p
    })
}
// 权限删除
export const getModuleDel = (ModuleId) => {
    let api = `/api/v1/module/del`;
    let p = `?ModuleId=${ModuleId}`
    return del({
        api: api,
        url: api + p
    })
}
// ————————————用户授权
export const getRoleList = () => {
    let api = `/api/v1/role/list`;
    return get({
        api: api,
        url: api
    })
}
// 分页查询列表
export const getRolePageList = (pageIndex, pageSize, name, type) => {
    console.log(pageIndex, pageSize, name, type);
    let api = `/api/v1/role/pagelist`;
    let p
    if (name == "" || type == "") {
        p = `?pageSize=${pageSize}&pageIndex=${pageIndex}`
    } else {
        p = `?pageIndex=${pageIndex}&pageSize=${pageSize}&name=${name}&type=${type}`
    }
    return get({
        api: api,
        url: api + p
    })
}
// ————————————user用户
// 查询用户角色信息
export const getUserRole = () => {
    let api = `/api/v1/user/list/all`;
    let p = ``
    return get({
        api: api,
        url: api + p
    })
}
// 用户添加
export const getUserAdd = (formInline) => {
    console.log(formInline);
    let api = `/api/v1/user/add`;
    let p = `?Account=${formInline.Account}&UserName=${formInline.UserName}&MobilePhone=${formInline.MobilePhone}&Email=${formInline.Email}&Enable=${formInline.Enable}&Description=${formInline.Description}&UserLogin.UserPassword=${formInline.UserPassword}`
    // let p = `?UserId=${formInline.UserId}&Account=${formInline.Account}&UserName=${formInline.UserName}&MobilePhone=${formInline.MobilePhone}&Email=${formInline.Email}&Enable=${formInline.Enable}&Description=${formInline.Description}&UserLoginId=${formInline.UserLoginId}&UserPassword=${formInline.UserPassword}&LoginCount=${formInline.LoginCount}&LastLoginTime=${formInline.LastLoginTime}&LastLoginIp=${formInline.LastLoginIp}`
    return post({
        api: api,
        url: api + p
    })
}
// 用户编辑
export const getUserUpdate = (formInline) => {
    let api = `/api/v1/user/update`;
    let p = `?UserId=${formInline.UserId}&Account=${formInline.Account}&UserName=${formInline.UserName}&MobilePhone=${formInline.MobilePhone}&Email=${formInline.Email}&Enable=${formInline.Enable}&Description=${formInline.Description}&UserPassword=${formInline.UserPassword}`
    // let p = `?UserId=${formInline.UserId}&Account=${formInline.Account}&UserName=${formInline.UserName}&MobilePhone=${formInline.MobilePhone}&Email=${formInline.Email}&Enable=${formInline.Enable}&Description=${formInline.Description}&UserLoginId=${formInline.UserLoginId}&UserPassword=${formInline.UserPassword}&LoginCount=${formInline.LoginCount}&LastLoginTime=${formInline.LastLoginTime}&LastLoginIp=${formInline.LastLoginIp}`
    return post({
        api: api,
        url: api + p
    })
}
// 用户删除
export const getUserDel = (ids) => {
    let api = `/api/v1/user/del`;
    let p = `?ids=${ids}`
    return del({
        api: api,
        url: api + p
    })
}
// 用户名手机号模糊查询
export const getUserfuzzyquery = (pageSize, pageIndex, account, phone) => {
    let api = `/api/v1/user/fuzzyquery`;
    let p
    if (account == undefined || phone == undefined) {
        p = `?pageSize=${pageSize}&pageIndex=${pageIndex}`
    } else {
        p = `?pageSize=${pageSize}&pageIndex=${pageIndex}&account=${account}&phone=${phone}`
    }
    return get({
        api: api,
        url: api + p
    })
}
// 给用户分配角色 
export const distributionRole = (data) => {
    let api = `/api/v1/user/setRole`;
    return postByFromForm({
        api: api,
        url: api
    }, { parm: JSON.stringify(data) },)
}
// ——————角色管理
// 角色列表
export const getRoleDel = (roleId) => {
    let api = `/api/v1/role/del`;
    let p = `?roleId=${roleId}`
    return del({
        api: api,
        url: api + p
    })
}
// 角色更新 
export const getRoleUpdate = (formInline) => {
    let api = `/api/v1/role/update`;
    let p = `?ID=${formInline.ID}&Name=${formInline.Name}&Type=${formInline.Type}&CreatorTime=${formInline.CreatorTime}`
    return post({
        api: api,
        url: api + p
    })
}
// 角色添加
export const getRoleAdd = (formInline) => {
    let api = `/api/v1/role/add`;
    let p = `?Name=${formInline.Name}&Type=${formInline.Type}`
    return post({
        api: api,
        url: api + p
    })
}



// ———————————日报
// 日报添加
export const getDay = (Files) => {
    const form = new FormData();
    for (let i = 0; i < Files.length; i++) {
        form.append("Files", Files[i]);
    }
    let api = `/api/v1/ventilateReportData/model/day`;
    return post({
        api: api,
        url: api
    }, form)
}
// 日报PageList列表数据
export const getDayList = (pageSize, pageIndex) => {
    let api = `/api/v1/ventilateReportData/pageList/day`;
    let p = `?pageSize=${pageSize}&pageIndex=${pageIndex}`
    return get({
        api: api,
        url: api + p
    })
}
// ———————————周报
// 周报添加
export const getWeek = (Files) => {
    const form = new FormData();
    for (let i = 0; i < Files.length; i++) {
        form.append("Files", Files[i]);
    }
    let api = `/api/v1/ventilateReportData/model/week`;
    return post({
        api: api,
        url: api
    }, form)
}
// 周报PageList列表数据
export const getWeekList = (pageSize, pageIndex) => {
    let api = `/api/v1/ventilateReportData/pageList/week`;
    let p = `?pageSize=${pageSize}&pageIndex=${pageIndex}`
    return get({
        api: api,
        url: api + p
    })
}
// ———————————月报
// 月报添加
export const getMonth = (Files) => {
    const form = new FormData();
    for (let i = 0; i < Files.length; i++) {
        form.append("Files", Files[i]);
    }
    let api = `/api/v1/ventilateReportData/model/month`;
    return post({
        api: api,
        url: api
    }, form)
}
// 月报PageList列表数据
export const getMonthList = (pageSize, pageIndex) => {
    let api = `/api/v1/ventilateReportData/pageList/month`;
    let p = `?pageSize=${pageSize}&pageIndex=${pageIndex}`
    return get({
        api: api,
        url: api + p
    })
}
// ———————————年
// 年报添加
export const getYear = (Files) => {
    const form = new FormData();
    for (let i = 0; i < Files.length; i++) {
        form.append("Files", Files[i]);
    }
    let api = `/api/v1/ventilateReportData/model/year`;
    return post({
        api: api,
        url: api
    }, form)
}
// 年报PageList列表数据
export const getYearList = (pageSize, pageIndex) => {
    let api = `/api/v1/ventilateReportData/pageList/year`;
    let p = `?pageSize=${pageSize}&pageIndex=${pageIndex}`
    return get({
        api: api,
        url: api + p
    })
}
// 日报-月报-周报-年报删除
export const getDayDel = (id) => {
    let api = `/api/v1/ventilateReportData/del`;
    let p = `?id=${id}`
    return del({
        api: api,
        url: api + p
    })
}
// Node
// 添加巷道节点
export const getNodeAdd = (data) => {
    let api = `/api/v4/node/add`;
    let p = `?NodeName=${data.NodeName}&Number=${data.Num}&Vertex=${data.Vertex}&x=${data.X}&y=${data.Y}&z=${data.Z}`
    return post({
        api: api,
        url: api + p
    },)
}
// 修改巷道节点
export const getNodeUpdate = (data) => {
    let api = `/api/v4/node/update`;
    let p = `?NodeID=${data.NodeID}&NodeName=${data.NodeName}&Number=${data.Num}&Vertex=${data.Vertex}&x=${data.X}&y=${data.Y}&z=${data.Z}`
    return post({
        api: api,
        url: api + p
    },)
}
// 删除巷道节点
export const getNodeDel = (ID) => {
    let api = `/api/v4/node/del`;
    let p = `?NodeID=${ID}`
    return post({
        api: api,
        url: api + p
    },)
}
// 查询巷道节点
export const getNodeList = () => {
    let api = `/api/v4/node/querryList`;
    return get({
        api: api,
        url: api
    },)
}
// 查询重复点位
export const getNodeDistance = (data) => {
    let api = `/api/v4/node/distance`;
    let p = `?NodeID=${data.NodeID}&NodeName=${data.NodeName}&Number=${data.Num}&Vertex=${data.Vertex}&x=${data.x}&y=${data.y}&z=${data.z}&StaticForce=${data.StaticForce}&Temperature=${data.Temperature}&Humidity=${data.Humidity}&VaporForce=${data.VaporForce}&Density=${data.Density}&SchemeID=${data.SchemeID}&range=${data.range}`
    return get({
        api: api,
        url: api + p
    },)
}
// 查询重复点位 
export const getNodeCheckDouble = (data) => {
    let api = `/api/v4/node/checkDouble`;
    return postByFromForm({
        api: api,
        url: api
    }, { param: JSON.stringify(data) })
}
// Tun
// 添加巷道节点
export const getTunAdd = (data) => {
    let api = `/api/v4/tun/add`;
    let p = `?TunName=${data.TunName}&Place=${data.Place}&TunState=${data.TunState}&SnodeID=${data.SnodeID}&MnodeID=${data.MnodeID}&EnodeID=${data.EnodeID}&Ventflow=${data.Ventflow}&Lx=${data.Lx}&ShoringType=${data.ShoringType}&ShapeType=${data.ShapeType}&UseType=${data.UseType}&Length=${data.Length}&Width=${data.Width}&Height=${data.Height}&S=${data.S}&A=${data.Height}&R=${data.R}&NetLine=${data.NetLine}'`
    return post({
        api: api,
        url: api + p
    },)
}
// 修改巷道节点
export const getTunUpdate = (data) => {
    let api = `/api/v4/tun/update`;
    let p = `?TunID=${data.TunID}&TunName=${data.TunName}&Place=${data.Place}&TunState=${data.TunState}&SnodeID=${data.SnodeID}&MnodeID=${data.MnodeID}&EnodeID=${data.EnodeID}&Ventflow=${data.Ventflow}&Lx=${data.Lx}&ShoringType=${data.ShoringType}&ShapeType=${data.ShapeType}&UseType=${data.UseType}&Length=${data.Length}&Width=${data.Width}&Height=${data.Height}&S=${data.S}&A=${data.Height}&R=${data.R}&NetLine=${data.NetLine}&V=${data.V}`
    return post({
        api: api,
        url: api + p
    },)
}
export const getTunUpdate1 = (data) => {
    let api = `/api/v4/tun/update`;
    let p = `?TunID=${data.TunID}&TunName=${data.TunName}&SnodeID=${data.SnodeID}&MnodeID=${data.MnodeID}&EnodeID=${data.EnodeID}&Ventflow=${data.Ventflow}&Place=${data.Place}&Lx=${data.Lx}&ShoringType=${data.ShoringType}&ShapeType=${data.ShapeType}&Length=${data.Length}&Width=${data.Width}&Height=${data.Height}&S=${data.S}&A=${data.A}&R=${data.R}&NetLine=${data.NetLine}&QDemand=${data.QDemand}&QCalculate=${data.QCalculate}&QMonitor=${data.QMonitor}&V=${data.V}&H=${data.H}&Q2=${data.Q2}&H2=${data.H2}&V2=${data.V2}&Q3=${data.Q3}&H3=${data.H3}&V3=${data.V3}&HWay=${data.HWay}&HParte=${data.HParte}&HRelate=${data.HRelate}&HDynamic=${data.HDynamic}&HStatic=${data.HStatic}&HEnergy=${data.HEnergy}&H100m=${data.H100m}&Vssz=${data.Vssz}`
    return post({
        api: api,
        url: api + p
    },)
}
// 删除巷道节点
export const getTunDel = (ID) => {
    let api = `/api/v4/tun/del`;
    let p = `?TunID=${ID}`
    return post({
        api: api,
        url: api + p
    },)
}
// 保存巷道批量更新
export const saveBatchingUpdate = (data) => {
    let api = `/api/v4/gis/batchupdate`;
    return postByFromForm({
        api: api,
        url: api
    }, { parma: JSON.stringify(data) })
}
// 查询巷道
export const getTunList = () => {
    let api = `/api/v4/tun/querryList`;
    return get({
        api: api,
        url: api
    },)
}
// VentilateNet
// 点位查询
export const getVentilatenetNodes = () => {
    let api = `/api/v4/ventilatenet/nodes`;
    return get({
        api: api,
        url: api
    },)
}
// 路线查询
export const getVentilatenetBranch = () => {
    let api = `/api/v4/ventilatenet/branch`;
    return get({
        api: api,
        url: api
    },)
}
// 修改通风节点
export const updateVentilatenetNodes = (data) => {
    let api = `/api/v4/ventilatenet/update`;
    let p = `?NodeID=${data.NodeID}&NodeName=${data.NodeName}&X=${data.x}&Y=${data.y}&SchemeID=${data.SchemeID}`
    return post({
        api: api,
        url: api + p
    },)
}



// Configuration
// 查询组态配置页面列表
export const getConfigurationList = () => {
    let api = `/api/v3/configuration/list`;
    return get({
        api: api,
        url: api
    },)
}
// 添加组态配置页面
export const getConfigurationAdd = (zutInit) => {
    let api = `/api/v3/configuration/add`;
    let p = `?ID=${zutInit.ID}&Name=${zutInit.Name}&Url=${zutInit.Url}&Type=${zutInit.Type}`
    return post({
        api: api,
        url: api + p
    },)
}
// 更新组态配置页面
export const getConfigurationUpdate = (zutInit) => {
    let api = `/api/v3/configuration/update`;
    let p = `?ID=${zutInit.ID}&Name=${zutInit.Name}&Url=${zutInit.Url}&Type=${zutInit.Type}`
    return post({
        api: api,
        url: api + p
    },)
}
// 删除组态配置页面
export const getConfigurationDel = (ID) => {
    let api = `/api/v3/configuration/del`;
    let p = `?ID=${ID}`
    return del({
        api: api,
        url: api + p
    },)
}
// 查询详细组态配置
export const getConfigurationDetail = (WebUrl, Type) => {
    let api = `/api/v3/configuration/detail`;
    let p = `?WebUrl=${WebUrl}&Type=${Type}`
    return get({
        api: api,
        url: api + p
    },)
}


// 组态页面编辑-看板
//查询看板列表

export const getBoardList = () => {
    let api = `/api/v3/board/list`;
    return get({
        api: api,
        url: api
    },)
}

// 选择属性点
export const getPoint = (ID) => {
    let api = `/api/v1/point/list`;
    let p = `?deviceId=${ID}`
    return get({
        api: api,
        url: api + p
    },)

}

//更新组态页面详细信息

export const updateConfig = (data) => {
    // const form = new FormData();
    let api = `/api/v3/configuration/updatedetail`;
    return post({
        api: api,
        url: api
    }, data)
}


//报警告警

export const alarmList = () => {
    let api = `/api/v1/alarm/list`;
    return get({
        api: api,
        url: api
    })

}

//报警告警分页数据查询
export const alarmPageList = (pageSize, pageIndex) => {
    let api = `/api/v1/alarm/pageList`;
    let p = `?pageSize=${pageSize}&pageIndex=${pageIndex}`
    return get({
        api: api,
        url: api + p
    })
}

//报警告警删除查询

export const delalaList = (ID) => {
    let api = `/api/v1/alarm/del`;

    let p = `?id=${ID}`
    return del({
        api: api,
        url: api + p
    })
}

// 报警告警添加
export const getAlarmAdd = (formInline) => {
    let api = `/api/v1/alarm/add`;
    let p = `?Name=${formInline.Name}&Solve=${formInline.Solve}`
    return post({
        api: api,
        url: api + p
    })
}

//报警告警点位
export const getalarmPointList = (alarmDa) => {
    let api = `/api/v1/alarmconfig/listSys`;
    let p = `?type=${alarmDa.type}&Lx=${alarmDa.Lx}`
    return get({
        api: api,
        url: api + p
    }) 
}

//修改

export const updatealarmList = (alarmInit) => {

    let api = `/api/v1/alarm/update`;
    let p = `?ID=${alarmInit.Id}&Solve=${alarmInit.Solve}`
    return post({
        api: api,
        url: api + p
    })
}

//报警告警编辑页面
//列表查询
export const getalarmconfigList = () => {
    let api = `/api/v1/alarmconfig/list`;

    return get({
        api: api,
        url: api + p
    })
}



//分页查询
export const getalarmconfigPage = (pageSize, pageIndex) => {
    let api = `/api/v1/alarmconfig/pageList`;
    let p = `?pageSize=${pageSize}&pageIndex=${pageIndex}`
    return get({
        api: api,
        url: api + p
    })
}




// 报警告警添加
export const getalarmconfigAdd = (alarmInit) => {
    let api = `/api/v1/alarmconfig/add`;
    let p = `?ID=${alarmInit.ID}&Name=${alarmInit.Name}&AlarmType=${alarmInit.AlarmType}&Lx=${alarmInit.Lx}&MaxValue=${alarmInit.MaxValue}
      &MinValue=${alarmInit.MinValue}
      &EqualsValue=${alarmInit.EqualsValue}&AlarmLevel=${alarmInit.AlarmLevel}&AlarmTime=${alarmInit.AlarmTime}
      &AlarmPoint=${alarmInit.AlarmPoint}&Length=${alarmInit.Length}`
    return post({
        api: api,
        url: api + p
    })
}


//修改
export const getalarmconfigUpdate = (alarmInit) => {
    let api = `/api/v1/alarmconfig/update`;
    let p = `?ID=${alarmInit.ID}&Name=${alarmInit.Name}&AlarmType=${alarmInit.AlarmType}&Lx=${alarmInit.Lx}
      &MaxValue=${alarmInit.MaxValue}&MinValue=${alarmInit.MinValue}
      &EqualsValue=${alarmInit.EqualsValue}&AlarmLevel=${alarmInit.AlarmLevel}&AlarmTime=${alarmInit.AlarmTime}
      &AlarmPoint=${alarmInit.AlarmPoint}&Length=${alarmInit.Length}`
    return post({
        api: api,
        url: api + p
    })


}

//删除
export const getalarmconfigDel = (ID) => {
    let api = `/api/v1/alarmconfig/del`;
    let p = `?ID=${ID}`
    return del({
        api: api,
        url: api + p
    })
}

// 计算需风量
export const getVentflow = (ID) => {
    let api = `/api/v4/windlocation/decision`;
    let p = `?ID=${ID}`
    return get({
        api: api,
        url: api + p
    })  

}
//聊天信息
export const getChat = () => {
    let api = `/v1/chat-messages`;
    let p = `?api_key=${app-gd2E0gIAgTVRVR3DE7PVuRQy}`
    return post({
        api: api,
        url: api + p
    })
}

//视频转码
export const getVideo = (rtspUrl) => {
    let api = `/api/v1/video/start`;
    let p = `?rtspUrl=${rtspUrl}`
    return get({
        api: api,
        url: api + p
    })
}


//停止转码
export const stopVideo = () => {
    let api = `/api/v1/video/stop`;
    
    return get({
        api: api,
        url: api + p
    })
}

//播放
export const playVideo = () => {
    let api = `/api/v1/video/playlist`; 
    return get({
        api: api,
        url: api 
    })
}


export const playVideo1 =() =>{
    let api ='/api/v1/video'
     return get({
        api: api,
        url: api 
    })
}
