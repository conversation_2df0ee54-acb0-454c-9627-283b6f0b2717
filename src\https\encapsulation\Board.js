import { getBoardList } from "../api";
// 看板列表
export const ApBoardCode = async () => {
  const { data } = await getBoardList();
  return data;
  // return data
  // TODO: name,type需要前端提供,其他内容后端根据自己的需求来
  // DisplayUrl 是图片地址,最好将原始图片发给后端,让他给你一个可以访问的url

  // return {
  //   ...data,
  //   Result: [
  //     ...data.Result,
  //     {
  //       ID: "051b7e35cabc49888380a88cad07e112",
  //       Name: "基本信息",
  //       Type: 7,
  //       DisplayUrl: "src\\views\\businessCenter\\assets\\img\\gzzd_temp.png",
  //       Config: null,
  //     },
  //     {
  //       ID: "051b7e35cabc49888380a88cad07e111",
  //       Name: "环境监测-1",
  //       Type: 6,
  //       DisplayUrl: "src\\views\\businessCenter\\assets\\img\\gzzd_temp.png",
  //       Config: null,
  //     },
  //     {
  //       ID: "051b7e35cabc49888380a88cad07e123",
  //       Name: "环境监测-2",
  //       Type: 8,
  //       DisplayUrl: "src\\views\\businessCenter\\assets\\img\\gzzd_temp.png",
  //       Config: null,
  //     },
  //   ],
  // };
};
