import {
  getConfigurationList,
  getConfigurationAdd,
  getConfigurationDetail,
  getConfigurationUpdate,
  getConfigurationDel,
  getPoint,
  updateConfig,
  getalarmconfigList,   
  getalarmconfigPage,
  getalarmconfigAdd,
  getalarmconfigUpdate,
  getalarmconfigDel,
  getalarmPointList

} from "../api";
// 查询组态配置页面列表
export const ApiConfigurationList = async () => {
  const { data } = await getConfigurationList();
  return data;
};
// 添加组态配置页面
export const ApiConfigurationAdd = async (datas) => {
  const { data } = await getConfigurationAdd(datas);
  return data;
};
// 更新组态配置页面
export const ApiConfigurationUpdate = async (datas) => {
  const { data } = await getConfigurationUpdate(datas);
  return data;
};
// 删除组态配置页面
export const ApiConfigurationDel = async (ID) => {
  const { data } = await getConfigurationDel(ID);
  return data;
};
// 查询详细组态配置
export const ApiConfigurationDetail = async (WebUrl, Type) => {
  const { data } = await getConfigurationDetail(WebUrl, Type);
  return data;
};

//选择属性点
export const ApiPoint = async (deviceId) => {
  const { data } = await getPoint(deviceId);
  return data;
};

//修改组态配置详情
export const apiUpdataConf = async (datas) => {
 
  const data = updateConfig(datas);

  return data;

};



//报警配置列表
//列表查询
export const ApiAlarmconfigList = async () => {
  const { data } = await getalarmconfigList();
  return data;
};


//分页查询
export const ApiAlarmconfigPage = async (pageSize, pageIndex) => {
  const { data } = await getalarmconfigPage(pageSize,pageIndex);
  return data;
};

//添加报警配置
export const ApiAlarmconfigAdd = async (datas) => {
  const { data } = await getalarmconfigAdd(datas);
  return data;
};


//修改报警配置
export const ApiAlarmconfigUpdate = async (datas) => {
  const { data } = await getalarmconfigUpdate(datas);
  return data;
};

//删除报警配置
export const ApiAlarmconfigDel = async (datas) => {
  const { data } = await getalarmconfigDel(datas);
  return data;
};

// 选择属性点
export const ApiAlarmPointList = async (datas) => {
  const { data } = await getalarmPointList(datas);
  return data;
};