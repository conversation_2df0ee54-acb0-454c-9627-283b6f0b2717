import { getFireproof, getFireprBase, getFireprRealValue, getFireprAlarm,getFireQx,getFireList } from '../api'
// ————————————智能防火
// 在线防火基本信息与监测信息
export const ApiFireproof = async () => {
    const { data } = await getFireproof()
    return data
}

// 光纤测温列表
export const ApiFireList = async () => {
    const { data } = await getFireList()
    return data
}
// 光纤测温列表
export const ApiFireQx = async (devAddress) => {
    const { data } = await getFireQx(devAddress)
    return data
}

// 光纤测温基础信息
export const ApiFireprBase = async () => {
    const { data } = await getFireprBase()
    return data
}
// 光纤测温实时数据
export const ApiFireprRealValue = async (pageSize, pageIndex) => {
    const { data } = await getFireprRealValue(pageSize, pageIndex)
    return data
}

// 光纤测温预警报警
export const ApiFireprAlarm = async (startTime, endTime) => {
    const { data } = await getFireprAlarm(startTime, endTime)
    return data
}