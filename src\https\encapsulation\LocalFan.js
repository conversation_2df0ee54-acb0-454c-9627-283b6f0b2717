import { getLocalFanList, getLocalFanCode,getVideo,stopVideo,playVideo,playVideo1 } from '../api'
// 局扇列表
export const ApiLocalFanList = async () => {
    const { data } = await getLocalFanList()
    return data
}
export const ApLocalFanList = async () => {
    const { data } = await getLocalFanList()
    return data
}
// 局扇code查询
export const ApiLocalFanCode = async (code) => {
    const { data } = await getLocalFanCode(code)
    return data
}
export const ApLocalFanCode = async (code) => {
    const { data } = await getLocalFanCode(code)
    return data
}

export const ApiVideo = async (rtspUrl) => {
    const { data } = await getVideo(rtspUrl)
    return data
}

export const ApiStopVideo = async () => {
    const { data } = await stopVideo()
    return data
}
export const ApiPlayVideo = async () => {
    const { data } = await playVideo()
    return data
}

export const ApiPlayVideo1 = async () => {
    const { data } = await playVideo1()
    return data
}