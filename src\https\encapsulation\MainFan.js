import { getMainFanList, getMainFanCode, getLocalFanHisalarm } from '../api'
// 主通列表
export const ApiMainFanList = async () => {
    const { data } = await getMainFanList()
    return data
}
export const ApMainFanList = async () => {
    const { data } = await getMainFanList()
    return data
}
// 主通code查询
export const ApiMainFanCode = async (code) => {
    const { data } = await getMainFanCode(code)
    return data
}
export const ApMainFanCode = async (code) => {
    const { data } = await getMainFanCode(code)
    return data
}
//主通历史报警查询
export const ApiLocalFanHisalarm = async (code) => {
    const { data } = await getLocalFanHisalarm(code)
    return data
}