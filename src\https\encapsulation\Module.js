import { getModuleRoleId, getModuleList, getModulePageList, getModuleAdd, getModuleDel, getModuleUndete } from '../api'
export const ApiModuleRoleId = async (id) => {
    const { data } = await getModuleRoleId(id)
    return data
}
// 页面权限列表
export const ApiModuleList = async () => {
    const { data } = await getModuleList()
    return data
}
// 权限分页列表
export const ApiModulePageList = async (name) => {
    const { data } = await getModulePageList(name)
    return data
}
// 权限添加 
export const ApiModuleAdd = async (formInline) => {
    const { data } = await getModuleAdd(formInline)
    return data
}
// 权限删除 
export const ApiModuleDel = async (ModuleId) => {
    const { data } = await getModuleDel(ModuleId)
    return data
}
// 权限更改

export const ApiModuleUndete = async (ModuleId) => {
    const { data } = await getModuleUndete(ModuleId)
    return data
} 