// 用户授权 
import { getRoleList, getRolePageList, getRoleUpdate, getsetModule } from '../api'

export const ApiRoleList = async () => {
    const { data } = await getRoleList()
    return data
}

export const ApiRolePageList = async (pageIndex, pageSize, name, type) => {
    const { data } = await getRolePageList(pageIndex, pageSize, name, type)
    return data
}
// 角色更新
export const ApiRoleUpdate = async (formInline) => {
    const { data } = await getRoleUpdate(formInline)
    return data
}

// 给角色分配页面
export const ApisetModule = async (data) => {
    const res = await getsetModule(data)
    return res
}
