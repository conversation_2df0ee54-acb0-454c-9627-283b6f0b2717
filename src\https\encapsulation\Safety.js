import { getMeasuringPoint, getSecurityMonitoringType, getSecurityMonitoringList, getSecurityMonitoringPageList, getHistoricalCurve } from '../api'
//测点查询 code = 编码
export const ApiMeasuringPoint = async function (code) {
    const res = await getMeasuringPoint(code)
    return res
}
// 安全监控类型查询
export const ApiSecurityMonitoringType = async function () {
    const res = await getSecurityMonitoringType()
    return res
}
// 安全监控列表查询 lx=安全监控类型查询返回的类型
export const ApiSecurityMonitoringList = async function (lx,adress) {
    const res = await getSecurityMonitoringList(lx,adress)
    return res
}

// 安全监控分页列表查询 lx=安全监控类型查询返回的类型
export const ApiSecurityMonitoringPageList = async function (pageSize, pageIndex, lx) {
    const res = await getSecurityMonitoringPageList(pageSize, pageIndex, lx)
    return res
}
//  历史曲线 startTime=开始时间  endTime=结束时间  code = 编码
export const ApiHistoricalCurve = async function ({ startTime, endTime, code }) {
    const res = await getHistoricalCurve(startTime, endTime, code)
    return res
}