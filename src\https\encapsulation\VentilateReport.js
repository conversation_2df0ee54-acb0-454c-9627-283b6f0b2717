import { getDayList, getDay, getDayDel, getWeekList, getWeek, getMonth<PERSON>ist, getMonth, getYearList, getYear } from '../api'
// 日表列表
export const ApiDayList = async (pageSize, pageIndex) => {
    const res = await getDayList(pageSize, pageIndex)
    return res
}
// 日报添加
export const ApiDay = async (Files) => {
    const res = await getDay(Files)
    return res
}


// 周报列表
export const ApiWeekList = async (pageSize, pageIndex) => {
    const res = await getWeekList(pageSize, pageIndex)
    return res
}
// 周报添加
export const ApiWeek = async (Files) => {
    const res = await getWeek(Files)
    return res
}
// 月报列表
export const ApiMonthList = async (pageSize, pageIndex) => {
    const res = await getMonthList(pageSize, pageIndex)
    return res
}
// 月报添加
export const ApiMonth = async (Files) => {
    const res = await getMonth(Files)
    return res
}
// 年报列表
export const ApiYearList = async (pageSize, pageIndex) => {
    const res = await getYearList(pageSize, pageIndex)
    return res
}
// 年报添加
export const ApiYear = async (Files) => {
    const res = await getYear(Files)
    return res
}


// 日报删除 
export const ApiDayDel = async (Files) => {
    const res = await getDayDel(Files)
    return res
}