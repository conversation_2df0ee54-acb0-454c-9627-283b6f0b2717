import { getWindDoorList, getWindDoorCode } from '../api'
// 风门列表
export const ApiWindDoorList = async () => {
    const { data } = await getWindDoorList()
    return data
}
export const ApWindDoorList = async () => {
    const { data } = await getWindDoorList()
    return data
}
// 风门code查询
export const ApiWindDoorCode = async (code) => {
    const { data } = await getWindDoorCode(code)
    return data
}
export const ApWindDoorCode = async (code) => {
    const { data } = await getWindDoorCode(code)
    return data
}