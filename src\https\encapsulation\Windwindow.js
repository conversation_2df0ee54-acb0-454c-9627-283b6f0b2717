import { getWindwindowList, getWindwindowCode } from '../api'
// 风窗列表
export const ApitWindwindowList = async () => {
    const { data } = await getWindwindowList()
    return data
}
export const AptWindwindowList = async () => {
    const { data } = await getWindwindowList()
    return data
}
// 风窗code查询
export const ApiWindwindowCode = async (code) => {
    const { data } = await getWindwindowCode(code)
    return data
}
export const ApWindwindowCode = async (code) => {
    const { data } = await getWindwindowCode(code)
    return data
}