import { alarmList,alarmPageList,delalaList,updatealarmList } from '../api'

//报警列表
export const ApalarmList = async () => {
    const { data } = await alarmList()
    return data
}


//报警列表分页查询
export const ApialarmPageList = async (pageSize, pageIndex) => {
    const res = await alarmPageList(pageSize, pageIndex)
    return res
}

//删除

export const ApialarmDel = async (ID) => {
    const res = await delalaList(ID)
    return res
}

//修改
export const ApialarmUpdate = async (alarmInit) => {
    const { data } = await updatealarmList(alarmInit)
    console.log(data,'修改告警')
    return data
}

