// 登录接口
import {
    Log, roadway, getNodeAdd, getNodeCheckDouble,
     getNodeDistance, getNodeList, getabnormal, getupdate, 
     getTunAdd, getTunList, getTunDel, getTunUpdate1,
      getTunUpdate, getNodeUpdate, getNodeDel, 
      getRoadwayIDInquire, getRoadwayLis, getMiningSectionList,
       getUseList, getShapeList, getSupportTypeList, 
       getRoadwayTypeList, getInlineResistanceAnalyze,
        getInlineResistanceList, getInlineWindspeed,
         getInlineCO, getInlineGas, getThreezonesDistribution, getHardroute,
          getWindsite,getWindlocaList,getWindlocaAdd,getWindlocaUpdate,getWindlocaDel,
          getVentflow

} from '../api'
import { post } from '../request'
export const Login = async function (name, tiem, encipher) {
    const res = await Log(name, tiem, encipher)
    return res
}
// 添加巷道节点
export const ApiNodeDistance = async (List) => {
    const { data } = await getNodeDistance(List)
    return data
}
// 查询重复点位
export const ApiNodeCheckDouble = async (param) => {
    const { data } = await getNodeCheckDouble(param)
    return data
}

// 添加巷道节点
export const ApiNodeAdd = async (List) => {
    const { data } = await getNodeAdd(List)
    return data
}
// 修改巷道节点 
export const ApiNodeUpdate = async (List) => {
    const { data } = await getNodeUpdate(List)
    return data
}
// 删除巷道节点 
export const ApiNodeDel = async (ID) => {
    const { data } = await getNodeDel(ID)
    return data
}
// 查询巷道节点
export const ApiNodeList = async () => {
    const { data } = await getNodeList()
    return data
}
// tun
// 添加巷道节点
export const ApiTunAdd = async (List) => {
    const { data } = await getTunAdd(List)
    return data
}
// 修改巷道节点
export const ApiTunUpdate = async (List) => {
    const { data } = await getTunUpdate(List)
    return data
}
export const ApiTunUpdate1 = async (List) => {
    const { data } = await getTunUpdate1(List)
    return data
}
// 删除巷道节点
export const ApiTunDel = async (ID) => {
    const { data } = await getTunDel(ID)
    return data
}
// 查询巷道 
export const ApiTunList = async () => {
    const { data } = await getTunList()
    return data
}





// 巷道信息更新 
export const ApiUpdate = async (List) => {
    const { data } = await getupdate(List)
    return data
}
// 设备异常查询接口
export const ApiAbnormal = async () => {
    const { data } = await getabnormal()
    return data
}
// 巷道
export const roadwayList = async () => {
    const { data } = await roadway()
    return data
}
// 巷道id查询详细信息
// tunid ID
export const ApiRoadwayIDInquire = async (tunid) => {
    const { data } = await getRoadwayIDInquire(tunid)
    return data
}
// 三维巷道列表查询
export const ApiRoadwayLis = async () => {
    const { data } = await getRoadwayLis()
    return data
}
// 采取列表查询 
export const ApiMiningSectionList = async () => {
    const { data } = await getMiningSectionList()
    return data
}
// 用途列表查询
export const ApiUseList = async () => {
    const { data } = await getUseList()
    return data
}
// 形状列表查询 
export const ApiShapeList = async () => {
    const { data } = await getShapeList()
    return data
}
// 支护类型列表查询
export const ApiSupportTypeList = async () => {
    const { data } = await getSupportTypeList()
    return data
}
// 巷道类型列表查询
export const ApiRoadwayTypeList = async () => {
    const { data } = await getRoadwayTypeList()
    return data
}

// 在线阻力误差分析
export const ApiInlineResistanceAnalyze = async () => {
    const { data } = await getInlineResistanceAnalyze()
    return data
}
// 在线阻力列表 
export const ApiInlineResistanceList = async () => {
    const { data } = await getInlineResistanceList()
    return data
}
// 在线风速
export const ApiInlineWindspeed = async () => {
    const { data } = await getInlineWindspeed()
    return data
}
// 在线CO
export const ApiInlineCO = async () => {
    const { data } = await getInlineCO()
    return data
}
// 在线瓦斯
export const ApiInlineGas = async () => {
    const { data } = await getInlineGas()
    return data
}
// 三区分布
export const ApiThreezonesDistribution = async () => {
    const { data } = await getThreezonesDistribution()
    return data
}
// 困难路线
export const ApiHardroute = async () => {
    const { data } = await getHardroute()
    return data
}
// 用风地点查询
export const ApiWindsite = async () => {
    const { data } = await getWindsite()
    return data
}

//用风地点列表查询
export const ApiWindlocaList = async () => {
    const { data } = await getWindlocaList()
    return data 
}

//用风地点新增
export const ApiWindlocaAdd = async (windDa) => {
    const { data } = await getWindlocaAdd(windDa)
    return data 
}
//用风地点修改
export const ApiWindlocaUpdate = async (windDa) => {
    const { data } = await getWindlocaUpdate(windDa)
    return data
}
//用风地点删除
export const ApiWindlocaDel = async (ID) => {
    const { data } = await getWindlocaDel(ID)
    return data
}

//计算需风量和决策
export const ApiVentflow = async (windDa) => {
    const { data } = await getVentflow(windDa)
    return data
}

// 避灾路线
export const tobtainDisasterAvoidanceRoutes = (TunId) => {
    let api = `/api/v4/escape/route?`;
    let p = `TunId=${TunId}`
    return post({
        api: api,
        url: api + p
    })
}