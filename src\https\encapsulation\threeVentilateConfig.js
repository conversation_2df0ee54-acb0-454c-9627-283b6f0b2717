import { get, post, del, postByFromForm } from '../request'

const baseUrl = `/api/v4`
// 设备设施绑定巷道和节点
export const facilityBindingRoadway = (data) => {
    let api = `${baseUrl}/venilateConfig/devicebind`;
    let p = `?FacilityID=${data.FacilityID}&FacilityType=${data.FacilityType}&X=${data.x}&Y=${data.y}&Z=${data.z}&TunID=${data.TunID}&NodeID=${data.NodeID}&Remark=${data.Remark}&OperateInfo=${data.OperateInfo}&RealValue=${data.RealValue}&IOTime=${data.IOTime}&Upflag=${data.Upflag}`;
    return post({
        api: api,
        url: api + p
    })
}
// 修改设备设施绑定巷道和节点
export const facilityUpdateBindingRoadway = (data) => {
    let api = `${baseUrl}/venilateConfig/updatebind`;
    let p = `?ID=${data.ID}&FacilityID=${data.FacilityID}&FacilityType=${data.FacilityType}&X=${data.x}&Y=${data.y}&Z=${data.z}&TunID=${data.TunID}&NodeID=${data.NodeID}&Remark=${data.Remark}&OperateInfo=${data.OperateInfo}&RealValue=${data.RealValue}&IOTime=${data.IOTime}&Upflag=${data.Upflag}`;
    return post({
        api: api,
        url: api + p
    })
}
// 删除设备设施绑定巷道和节点
export const facilityDelBindingRoadway = (ID) => {
    let api = `${baseUrl}/venilateConfig/delbind`;
    let p = `?id=${ID}`;
    return del({
        api: api,
        url: api + p
    })
}
// 分页查询
export const findFacilityBindingRoadwayPageList = (data) => {
    let api = `${baseUrl}/venilateConfig/pageList`;
    let p = `?pageSize=${data.pageSize}&pageIndex=${data.pageIndex}&type=${data.type}&tunID=${data.tunID}&nodeID=${data.nodeID}`;
    return get({
        api: api,
        url: api + p
    })
}
// 查询巷道绑定的设备的list
export const findFacilityBindingRoadway = (data) => {
    let api = `${baseUrl}/venilateConfig/list`;
    let p = `?`;
    if (data.type) {
        p += `type=${data.type}`
    }
    if (data.tunID) {
        p += `&tunID=${data.tunID}`
    }
    if (data.nodeID) {
        p += `&nodeID=${data.nodeID}`
    }
    return get({
        api: api,
        url: api + p
    })
}

// 登录
export const Log = (accout, timeStr, token) => {
    let api = `/api/v1/client/login`;
    let p = `?accout=${accout}&timeStr=${timeStr}&token=${token}`
    return post({
        api: api,
        url: api + p
    })
}