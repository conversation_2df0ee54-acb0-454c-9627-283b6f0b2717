import { getUserR<PERSON>, getUserAdd, getUserDel, getUserfuzzyquery, getUserUpdate, getRoleDel, getRoleAdd, getRoleUpdate, distributionRole } from '../api'
// 查询用户角色信息 userId = 用户ID
export const ApiUserRole = async (userId) => {
    const { data } = await getUserRole(userId)
    return data
}
// 用户添加 UserId 用户ID, Account账号, UserName用户名, MobilePhone 手机号, Email 邮箱, Enable 是否启用 true启用false禁用, Description 描述, UserLoginId 登录ID, UserPassword 密码, LoginCount 登录次数, LastLoginTime 最后一次登录时间 最后一次登录ip, LastLoginIp
export const ApiUserAdd = async (formInline) => {
    const { data } = await getUserAdd(formInline)
    return data
}
// 
// 用户更新
export const ApiUserUpdate = async (formInline) => {
    const { data } = await getUserUpdate(formInline)
    return data
}
// 用户删除 
export const ApiUserDel = async (ids) => {
    const { data } = await getUserDel(ids)
    return data
}
// 用户名手机号模糊查询
export const ApiUserfuzzyquery = async (pageSize, pageIndex, account, phone) => {
    const { data } = await getUserfuzzyquery(pageSize, pageIndex, account, phone)
    return data
}
// ————————角色管理
// 角色添加 
export const ApiRoleAdd = async (roleId) => {
    const { data } = await getRoleAdd(roleId)
    return data
}
// 角色编辑
export const ApiRoleUpdate = async (roleId) => {
    const { data } = await getRoleUpdate(roleId)
    return data
}
// 角色删除 
export const ApiRoleDel = async (formInline) => {
    const { data } = await getRoleDel(formInline)
    return data
}
// 给用户分配角色

export const ApidistributionRole = async (data) => {
    const res = await distributionRole(data)
    return res
}