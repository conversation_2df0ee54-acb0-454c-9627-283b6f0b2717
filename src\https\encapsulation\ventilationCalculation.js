import { get, post, del, postByFromForm } from '../request'
import { useVentilationCalculation } from '@/store/ventilationCalculation';
import { watch } from 'vue'

const store = useVentilationCalculation();
let schemeId = null;
watch(() => store.schemeId, (val) => {
    if (val) {
        console.log(val, 'val');
        schemeId = val;
    }
})
const base = `/api/v4`
const baseUrl = base + `/scheme`
// 添加方案
export const addScheme = (data) => {
    let api = `${baseUrl}/add`;
    // let p = `?SchemeName=${data.SchemeName}&IsDel=${data.IsDel}&UpdateTime=${data.UpdateTime}`;
    let p = `?SchemeName=${data.SchemeName}`;
    return post({
        api: api,
        url: api + p
    })
}
// 修改方案
export const updateScheme = (data) => {
    let api = `${baseUrl}/update`;
    let p = `?ID=${data.ID}&SchemeName=${data.SchemeName}`;
    return post({
        api: api,
        url: api + p
    })
}
// 删除方案
export const delScheme = (id) => {
    let api = `${baseUrl}/del`;
    let p = `?id=${id}`;
    return del({
        api: api,
        url: api + p
    })
}
// 查询所有方案
export const getSchemeList = () => {
    let api = `${baseUrl}/list`;
    return get({
        api: api,
        url: api
    })
}

// 通风解算结构
export const getStructural = (schemeId) => {
    let api = `${base}/gis/structure`;
    let p = `?schemeId=${schemeId ?? ''}`;
    return get({
        api: api,
        url: api + p
    })
}

// 通风解算结果
export const geVentilationCalculation = (schemeId) => {
    let api = `${base}/gis/solution`;
    let p = `?schemeId=${schemeId ?? ''}`;
    return get({
        api: api,
        url: api + p
    })
}

//困难路线
export const getHardRoute = (schemeId) => {
    let api = `${base}/gis/ArithmeticsHMax`;
    let p = `?schemeId=${schemeId?? ''}`;  
    return get({
        api: api,
        url: api + p 
    })
}

/** SchemeNode */
// 添加巷道节点
export const addSchemeNode = async (data) => {
    let api = `${baseUrl}/node/add`;
    let p = `?NodeName=${data.NodeName}&Number=${data.Num}&Vertex=${data.Vertex}&x=${data.X}&y=${data.Y}&z=${data.Z}&SchemeID=${schemeId}`
    const { data: data_1 } = await post({
        api: api,
        url: api + p
    });
    return data_1;
}
// 修改巷道节点
export const updateSchemeNode = async (data) => {
    let api = `${baseUrl}/node/update`;
    let p = `?NodeID=${data.NodeID}&NodeName=${data.NodeName}&Number=${data.Num}&Vertex=${data.Vertex}&x=${data.X}&y=${data.Y}&z=${data.Z}&SchemeID=${schemeId}`
    const { data: data_1 } = await post({
        api: api,
        url: api + p
    });
    return data_1;
}
// 删除巷道节点
export const delSchemeNode = async (ID) => {
    let api = `${baseUrl}/node/del`;
    let p = `?NodeID=${ID}&schemeId=${schemeId}`
    const { data } = await del({
        api: api,
        url: api + p
    });
    return data;
}
// 查询巷道节点
export const getSchemeNodeList = (schemeId) => {
    let api = `${baseUrl}/node/list`;
    let p = `?schemeId=${schemeId}`
    return get({
        api: api,
        url: api + p
    },)
}
// 根据距离查询附近点
export const getSchemeNodeDistance = async (data) => {
    let api = `${baseUrl}/node/distance`;
    let p = `?NodeID=${data.NodeID}&NodeName=${data.NodeName}&Number=${data.Num}&Vertex=${data.Vertex}&x=${data.x}&y=${data.y}&z=${data.z}&StaticForce=${data.StaticForce}&Temperature=${data.Temperature}&Humidity=${data.Humidity}&VaporForce=${data.VaporForce}&Density=${data.Density}&SchemeID=${schemeId}&range=${data.range}`
    const { data: data_1 } = await get({
        api: api,
        url: api + p
    });
    return data_1;
}
// 查询重复点位 
export const getSchemeNodeCheckDouble = async (data) => {
    let api = `${baseUrl}/node/checkDouble`;
    const { data: data_1 } = await postByFromForm({
        api: api,
        url: api
    }, { param: JSON.stringify(data) });
    return data_1;
}
// Tun
// 添加巷道节点
export const addSchemeTun = async (data) => {
    let api = `${baseUrl}/tun/add`;
    let p = `?TunName=${data.TunName}&Place=${data.Place}&TunState=${data.TunState}&SnodeID=${data.SnodeID}&MnodeID=${data.MnodeID}&EnodeID=${data.EnodeID}&Ventflow=${data.Ventflow}&Lx=${data.Lx}&ShoringType=${data.ShoringType}&ShapeType=${data.ShapeType}&UseType=${data.UseType}&Length=${data.Length}&Width=${data.Width}&Height=${data.Height}&S=${data.S}&A=${data.Height}&R=${data.R}&NetLine=${data.NetLine}&SchemeID=${schemeId}`
    const { data: data_1 } = await post({
        api: api,
        url: api + p
    });
    return data_1;
}
// 修改巷道节点
export const updateSchemeTun = async (data) => {
    let api = `${baseUrl}/tun/update`;
    let p = `?TunID=${data.TunID}&TunName=${data.TunName}&Place=${data.Place}&TunState=${data.TunState}&SnodeID=${data.SnodeID}&MnodeID=${data.MnodeID}&EnodeID=${data.EnodeID}&Ventflow=${data.Ventflow}&Lx=${data.Lx}&ShoringType=${data.ShoringType}&ShapeType=${data.ShapeType}&UseType=${data.UseType}&Length=${data.Length}&Width=${data.Width}&Height=${data.Height}&S=${data.S}&A=${data.Height}&R=${data.R}&NetLine=${data.NetLine}&V=${data.V}&SchemeID=${schemeId}`
    const { data: data_1 } = await post({
        api: api,
        url: api + p
    });
    return data_1;
}
// 删除巷道节点
export const delSchemeTun = async (ID) => {
    let api = `${baseUrl}/tun/del`;
    let p = `?TunID=${ID}&schemeId=${schemeId}`
    const { data } = await del({
        api: api,
        url: api + p
    });
    return data;
}

// 查询巷道
export const getSchemeTunList = (schemeId) => {
    let api = `${baseUrl}/tun/querryList`;
    let p = `?schemeId=${schemeId}`
    return get({
        api: api,
        url: api + p
    },)
}
// 查询巷道
export const getSchemeTunGis = (schemeId) => {
    let api = `${baseUrl}/tun/gis`;
    let p = `?schemeId=${schemeId}`
    return get({
        api: api,
        url: api + p
    },)
}
// 批量更新
export const saveSchemeBatchingUpdate = async (schemeId, data) => {
    let api = `${baseUrl}/batchupdate`;
    let p = `?schemeId=${schemeId}`
    return postByFromForm({
        api: api,
        url: api + p
    }, { parma: JSON.stringify(data) })
}