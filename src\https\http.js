import axios from 'axios'
// 获取登录tokne

import { ref } from "vue";
const apiKey = ref('app-5GrrJ1nlmEQYndPppCWDOAbN');
const BASEURL = window.g.baseUrl
const $http = () => {
    // 请求 
    let http = axios.create({
        baseURL:'/api',
        timeout: 35000
    })

    // 请求拦截器
    http.interceptors.request.use((config) => {
        
  
    //    config.headers.Authorization = `${ticket.value}`;
    //    console.log(config.headers.Authorization,'!!!!!!!!!!!!!!!!!!登陆后');
        return config
       

    }, (error) => {
        return Promise.reject(error)
    })

    http.interceptors.response.use((res) => {
        return Promise.resolve(res)

    }, (error) => {
        return Promise.reject(error)
    })
    return http
}
// get请求
function get(params) {
    const http = $http()
    const { api, url } = params;
    return http.get(url, {
        headers: {
            'Authorization':apiKey.value,
        }
    })
}
// post请求
function post(parm, data) {
    const http = $http()
    const { api, url } = parm;
    return http.post(url, data, {
        headers: {
            'Authorization':`Bearer `+ apiKey.value,
            'Content-Type': 'application/json'
        }
    })
}
function postByFromForm(params, data) {
    const http = $http()
    const { url } = params;
    return http.postForm(url, data, {
        headers: {
            'Authorization':'Bearer'+' '+ apiKey.value,
        }
    })
}
// 删除接口
function del(params) {
    const http = $http()
    const { api, url } = params;
    return http.delete(url, {
        headers: {
      'Authorization':'Bearer'+' '+ apiKey.value,
        }
    })

}
// 抛出请求
export { get, post, del, postByFromForm }