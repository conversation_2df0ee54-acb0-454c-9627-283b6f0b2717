import axios from 'axios'
// 获取登录tokne
import { useCommonStore } from "@/store/common";
import { ref } from "vue";


const store = useCommonStore();

const BASEURL = window.g.baseUrl
const $http = () => {
	// 请求 
	let http = axios.create({
		baseURL: BASEURL,
		timeout: 35000
	})
	// 请求拦截器
	http.interceptors.request.use((config) => {
		
      // 全局添加Authorization头
      config.headers['Authorization'] = store.token.Token;
      // 全局添加X-Ca-Token头
      config.headers['X-Ca-Token'] = store.token.Token;
    return config;
		

	}, (error) => {
		return Promise.reject(error)
	})

	http.interceptors.response.use((res) => {
		return Promise.resolve(res)

	}, (error) => {
		return Promise.reject(error)
	})
	return http
}
// get请求
function get(params) {
	const http = $http()
	const { api, url } = params;
	return http.get(url, {
		headers: {
			'X-Ca-Token': store.token.Token,
			'Authorization': store.token.Token
				
		}
	
	})
}
// post请求
function post(parm, data) {
	const http = $http()
	const { api, url } = parm;
	return http.post(url, data, {
		headers: {
			'X-Ca-Token': store.token.Token,
			'Authorization': store.token.Token
		}
	})
}
function postByFromForm(params, data) {
	const http = $http()
	const { url } = params;
	return http.postForm(url, data, {
		headers: {
			'X-Ca-Token': store.token.Token,
			'Authorization': store.token.Token
		}
	})
}
// 删除接口
function del(params) {
	const http = $http()
	const { api, url } = params;
	return http.delete(url, {
		headers: {
			'X-Ca-Token': store.token.Token,
			'Authorization': store.token.Token
		}
	})

}
// 抛出请求
export { get, post, del, postByFromForm }