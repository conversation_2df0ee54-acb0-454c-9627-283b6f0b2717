// 首先是一个立即执行函数，执行时传入的参数是window和document
(function flexible(window, document) {
    var docEl = document.documentElement; // 返回文档的root元素
    var dpr = window.devicePixelRatio || 1; // 获取设备的dpr，即当前设置下物理像素与虚拟像素的比值
    // adjust body font size 设置默认字体大小，默认的字体大小继承自body
    function setBodyFontSize() {
        if (document.body) {
            // document.body.style.fontSize = 12 * dpr + "px";
        } else {
            document.addEventListener("DOMContentLoaded", setBodyFontSize);
        }
    }
    setBodyFontSize();
    function isFullscreen() {
        return !!(
            document.fullscreenElement ||      // 标准
            document.webkitFullscreenElement || // Safari/旧Chrome
            document.mozFullScreenElement ||   // Firefox
            document.msFullscreenElement       // IE/Edge
        );
    }



    function setRemUnit(e) {

        if (docEl.clientHeight >= 1080) {
            docEl.style.fontSize = (docEl.clientWidth / 1920) + 'px';
        } else {
            docEl.style.fontSize = (docEl.clientWidth / 1920) * 0.85 + 'px';
        }

        // docEl.style.fontSize = (docEl.clientWidth / 1920) + 'px';
    }

    setRemUnit();
    // document.addEventListener('fullscreenchange', setRemUnit);
    // reset rem unit on page resize
    window.addEventListener("resize", setRemUnit);
    window.addEventListener("pageshow", function (e) {
        if (e.persisted) {
            setRemUnit();
        }
    });

    // detect 0.5px supports  检测是否支持0.5像素，解决1px在高清屏多像素问题，需要css的配合。
    if (dpr >= 2) {
        var fakeBody = document.createElement("body");
        var testElement = document.createElement("div");
        testElement.style.border = ".5px solid transparent";
        fakeBody.appendChild(testElement);
        docEl.appendChild(fakeBody);
        if (testElement.offsetHeight === 1) {
            docEl.classList.add("hairlines");
        }
        docEl.removeChild(fakeBody);
    }
})(window, document);