import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
// 动态设置根元素html字体大小
import "./lib/flexible";
// 引入 echarts
import * as echarts from "echarts";

// 包
import './style/main.scss';
import 'uno.css'
import 'animate.css'


// 注册全局指令
import registerDirective from './hooks/useMyDirective.js'

// 组件库
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
// 日期组件转换成中文
import 'dayjs/locale/zh-cn';
import locale from 'element-plus/es/locale/lang/zh-cn';
const app = createApp(App).use(ElementPlus,{locale});


// const app = createApp(App)
app.use(store)
app.use(router)
app.use(ElementPlus)

registerDirective(app)
app.mount('#app')
// 全局挂载 echarts
app.config.globalProperties.$echarts = echarts;