import { createRouter, createWebHashHistory } from 'vue-router'
import layout from '../layout/layout.vue'
import Loading from '../components/common/Loading/globalLoading.vue'
const routes = [
  {
    path: '/demo1',
    name: 'demo1',
    component: () => import('../views/demo1/demo1.vue')
  },
  {
    path: '/',
    name: 'logIn',
    component: () => import('../views/logIn/logIn.vue')
  },
  {
    path: '/loading',
    name: 'loading',
    component: Loading,
  },
  {
    path: '/layout',
    name: 'layout',
    component: layout,
    children: [
      // 暂存
      {
        path: '/threeTest',
        name: 'threeTest',
        component: () => import('../views/threeTest/threeTest.vue')
      },
      // 暂存
      {
        path: '/test',
        name: 'test',
        component: () => import('../views/tunEdit/components/setNodeManually-test.vue')
      },
      // 免登录页面
      {
        path: '/noSignIn',
        name: 'noSignIn',
        component: () => import('../views/noSignIn/noSignIn.vue')
      },
      // 管控中心
      {
        path: '/controlCenter',
        name: 'controlCenter',
        component: () => import('../views/controlCenter/controlCenter.vue')
      },
      // 三维通风
      {
        path: '/threeVentilate',
        name: 'threeVentilate',
        component: () => import('../views/thereVentilation/threeVentilate.vue')
      },
      // 三维通风配置
      {
        path: '/threeVentilateConfig',
        name: 'threeVentilateConfig',
        component: () => import('../views/threeVentilateConfig/threeVentilateConfig.vue')
      },
      // 三维编辑
      {
        path: '/tunEdit',
        name: 'tunEdit',
        component: () => import('../views/tunEdit/windDemandForecast.vue')
      },
      // 需风预测
      {
        path: '/ventilationCalculation',
        name: 'ventilationCalculation',
        component: () => import('../views/tunEdit/windDemandForecast.vue')
        // component: () => import('../views/ventilationCalculation/ventilationCalculation.vue')
      },
      //用风地点
      {
        path: '/ventilationList',
        name: 'ventilationList',
       
        component: () => import('../views/ventilationCalculation/ventilationList.vue')
      },
      // 通风网络
      {
        path: '/ventilationNetwork',
        name: 'ventilationNetwork',
        component: () => import('../views/ventilationNetwork/ventilationNetwork.vue')
      },
      {
        path: '/ventilationNetworkEdit',
        name: 'ventilationNetworkEdit',
        component: () => import('../views/ventilationNetwork/ventilationNetworkEdit.vue')
      },
      // 智能测风
      {
        path: '/smartAnemometry',
        name: 'smartAnemometry',
        component: () => import('../views/smartAnemometry/smartAnemometry.vue')
      },
      // 智能防火
      {
        path: '/fireProof',
        name: 'fireProof',
        component: () => import('../views/ventilateFacility/fireProof.vue')
      },

      //防火在线
      {
        path: '/fireproofOnline',
        name: 'fireproofOnline',
        component: () => import('../views/ventilateFacility/fireproofOnline.vue')
      },
      // 皮带防火
      {
        path: '/Beltfireproof',
        name: 'Beltfireproof',
        component: () => import('../views/ventilateFacility/Beltfireproof.vue')
      },
      // 通风设施
      {
        path: '/ventilateFacility',
        name: 'ventilateFacility',
        component: () => import('../views/ventilateFacility/ventilateFacility.vue'),
        children: [
          {
            path: '/ventilateFacility/mainFan',
            name: 'mainFan', // 主要通风机
            component: () => import('../views/ventilateFacility/mainFan/mainFan.vue')
          },
          {
            path: '/ventilateFacility/localFan',
            name: 'localFan',//  局部通风机
            component: () => import('../views/ventilateFacility/localFan/localFan.vue')
          },
          {
            path: '/ventilateFacility/airDoor',
            name: 'airDoor',//  智能风门
            component: () => import('../views/ventilateFacility/airDoor/airDoor.vue')
          },
          {
            path: '/ventilateFacility/airWindow',
            name: 'airWindow',//  智能风窗
            component: () => import('../views/ventilateFacility/airWindow/airWindow.vue')
          }
        ]
      },
      // 智能抽采
      {
        path: '/smartExtraction',
        name: 'smartExtraction',
        component: () => import('../views/smartExtraction/smartExtraction.vue')
      },
      // 智能降尘-采煤机降尘
      {
        path: '/smartDust',
        name: 'smartDust',
        component: () => import('../views/smartDust/smartDust.vue')
        
      },
      // 智能降尘-巷道降尘
      {
        path: '/smartLane',
        name: 'smartLane',
        component: () => import('../views/smartDust/smartLane.vue')
        
      },
      // 智能降尘
      {
        path: '/smartDustly',
        name: 'smartDustly',
        component: () => import('../views/smartDust/smartDustly.vue')
        
      },
      // 智能监控
      {
        path: '/smartMonitoring',
        name: 'smartMonitoring',
        component: () => import('../views/smartMonitoring/smartMonitoring.vue')
      },
      // 角色管理
      {
        path: '/role',
        name: 'role', // 角色管理
        component: () => import('../views/businessCenter/roleManagement/role.vue')
      },
      // 用户管理
      {
        path: '/user',
        name: 'user',
        component: () => import('../views/businessCenter/user/user.vue')
      },

      //页面配置
      {
        path: '/editPage',
        name: 'editPage',
        component: () => import('../views/businessCenter/editPage/editPage.vue')
      },

      {
        path: '/editHome',
        name: 'editHome',
        component: () => import('../views/businessCenter/editPage/editHome.vue')
      },


      //报警告警
      {
        path: '/alarmList',
        name: 'alarmList',
        component: () => import('../views/businessCenter/editPage/alarm/alarmList.vue')
      },

      //报警配置
      {
        path: '/alarmEdit',
        name: 'alarmEdit',
        component: () => import('../views/businessCenter/editPage/alarm/alarmEdit.vue')
      },
      // 日报 
      {
        path: '/day',
        name: 'day',
        component: () => import('../views/businessCenter/day/day.vue')
      },
      // 周报 
      {
        path: '/weeks',
        name: 'weeks',
        component: () => import('../views/businessCenter/weeks/weeks.vue')
      },
      // 月报
      {
        path: '/month',
        name: 'month',
        component: () => import('../views/businessCenter/month/month.vue')
      },
      // 年报
      {
        path: '/years',
        name: 'years',
        component: () => import('../views/businessCenter/years/years.vue')
      },
      //权限管理 
      {
        path: '/privilegeManagement',
        name: 'privilegeManagement',
        component: () => import('../views/businessCenter/privilegeManagement/privilegeManagement.vue')
      },
      //权限管理 
      {
        path: '/texte',
        name: 'texte',
        component: () => import('../views/businessCenter/texte/texte.vue')
      },

    ],
    redirect: '/controlCenter'
  }
]



// 导航守卫
const router = createRouter({
  history: createWebHashHistory(),
  routes
})
import { useCommonStore } from "@/store/common";
router.beforeEach((to, from, next) => {
  const store = useCommonStore();
  if (to.path == '/noSignIn') {
    next();
  } else {
    if (store.name != '') {
      next();
    } else {
      if (to.path == "/") {
        next()
      } else {
        next("/")
      }
    }
  }
})
export default router
