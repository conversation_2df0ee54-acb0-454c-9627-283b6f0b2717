import { defineStore } from "pinia";

export const configStore = defineStore("data", {
  state: () => ({
    data: null,
    devicesMap: null,
    camerasMap: null,
  }),
  actions: {
    setData(newData) {
      this.data = newData;
    },
    setDevicesMap(map) {
      this.devicesMap = map;
     
      console.log(this.devicesMap,' setDevicesMap');
    },
    setCameraMap(map) {
      this.camerasMap = map;
    },
  },
  persist: true,
});
