import { defineStore } from "pinia";

export const useThereVentilation = defineStore('useThereVentilation', {
    state: () => ({
        bottomTabCurrentIndex: -1,
        loadingShow: false,
        tunList: [],
        nodeList: [],
        // 火灾模拟
        isFlag: false,   // 是否启用交互
        currentTun: null, // 当前选中巷道
        positioningNodes: null,
        lineData: [],
        is3D: true, // 初始为三维视图
        is2D: false, // 控制视图模式：false=三维，true=二维
        syncState: null // 用于同步两视图的状态（如选中项、相机位置）
    }),
     actions: {
    // 切换视图模式
     toggle3DMode() {
      this.is3D = !this.is3D
    },
      // 同步两视图状态（如选中的巷道/节点）
    setSyncState(state) {
      this.syncState = state
    }
    }
    

  
})