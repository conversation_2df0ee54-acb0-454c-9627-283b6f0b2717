import { tunStr } from '@/views/thereVentilation/js/tunInit';
import { toRaw } from 'vue';
import { defineStore } from 'pinia';

export const useTunEdit = defineStore('useTunEdit', {
    state: () => ({
        enterFormDiv: false,
        isThree3DFullScreen: false,
        isThree2DFullScreen: false,
        // 用于需风预测中对方案进行切换
        schemeId: null,
        // 交互模式
        interactionMode: 0, // 0:漫游, 1: 拾取点 ,2:拾取巷道
        // 当前操作模式
        operationMode: 0, // 新增节点: 1, 编辑节点: 2, 删除节点: 3, 新增管道: 4, 编辑管道: 5, 删除管道: 6 ,打断巷道：7
        // 操作记录
        operationRecord: [],
        // 还原记录
        restoreRecord: [],
        // 巷道节点列表
        nodeList: [],
        // 巷道列表
        tunList: [],
        // 巷道连接列表
        // roadwayList: [],
        // 执行刷新
        renderFlag: 0,
        // 更新节点表单
        updateNodeInfo: { NodeID: '', NodeName: '', x: null, y: null, z: null, Vertex: null },
        // 新增巷道节点列表
        addTunNodeList: [{ originPoint: { x: null, y: null, z: null }, uuid: '' }],
        // 更新巷道节点列表
        updateTunNodeList: [{ originPoint: { x: null, y: null, z: null }, uuid: '', NodeID: null, state: true }],
        updateTunOther: { TunID: '', TunName: '', Ventflow: null, Width: 0, Level: 0, Lx: 0 },
        // 更新指定位置的巷道节点
        updateTunChangIndex: null,
        updateTunIsFlag: null,
        // 打断巷道 拾取点位 所需
        interruptMovePosition: { x: -Infinity, y: -Infinity, z: -Infinity },
        interruptChangeMesh: {
            removeTun: null,
            addNode: null,
            addTunList: []
        },
        // 开启设置节点列表 控制操作模式
        setNodeListShow: false,

    }),
    // 计算属性
    getters: {
        //根据 tunList 生成 tunStr 其中存着巷道节点的连接关系
        tunStr() {
            const arr = []
            this.tunList.forEach((v, i) => {
                const { SnodeID, EnodeID, MnodeID } = toRaw(v);
                const list = MnodeID ? [SnodeID, ...MnodeID.split('-').map(v => Number(v)).filter(v => v != 0), EnodeID] : [SnodeID, EnodeID]
                arr.push(list);
            })
            return arr

        },
        roadwayList() {
            return this.tunList.map((v, i) => {
                const Nodes = this.tunStr[i].map(k => toRaw(this.nodeList.find(n => n.NodeID == k)));
                return {
                    ...toRaw(v),
                    Nodes
                }
            })
        },
        addTunNodeIDList() {
            return this.addTunNodeList.map(v => v.NodeID).filter(v => v);
        },
        updateTunNodeIDList() {
            // console.log(this.updateTunNodeList, 'updateTunNodeList');

            return this.updateTunNodeList.map(v => v.NodeID).filter(v => v);
        }

    },
    // 派发函数
    actions: {
        // 根据 tunList 和 tunStr 生成 roadwayList
        // updateRoadwayList() {
        //     this.roadwayList = this.tunList.map((v, i) => {
        //         const { SnodeID, EnodeID, MnodeID } = toRaw(v);
        //         const list = MnodeID ? [SnodeID, ...MnodeID.split('-').map(v => Number(v)), EnodeID] : [SnodeID, EnodeID]
        //         const Nodes = list.map(k => toRaw(this.nodeList.find(n => n.NodeID == k)));
        //         return {
        //             ...toRaw(v),
        //             Nodes
        //         }
        //     })
        // }
        // 重置变量信息
        resetVar(isFullRefresh) {
            if (isFullRefresh) {
                this.schemeId = null
                // 交互模式
                this.interactionMode = 0
                // 当前操作模式
                this.operationMode = 0
            }
            // 操作记录
            this.operationRecord = []
            this.isThree3DFullScreen = false
            this.isThree2DFullScreen = false
            // 还原记录
            this.restoreRecord = []
            // 更新节点表单
            this.updateNodeInfo = { NodeID: '', NodeName: '', x: null, y: null, z: null, Vertex: null }
            // 新增巷道节点列表
            this.addTunNodeList = [{ originPoint: { x: null, y: null, z: null }, uuid: '' }]
            // 更新巷道节点列表
            this.updateTunNodeList = [{ originPoint: { x: null, y: null, z: null }, uuid: '', NodeID: null, state: true }]
            this.updateTunOther = { TunID: '', TunName: '', Ventflow: 1, Width: 0 }
            // 更新指定位置的巷道节点
            this.updateTunChangIndex = null
            this.updateTunIsFlag = null
            // 打断巷道 拾取点位 所需
            this.interruptMovePosition = { x: -Infinity, y: -Infinity, z: -Infinity }
            this.interruptChangeMesh = {
                removeTun: null,
                addNode: null,
                addTunList: []
            }
        }
    }

});
