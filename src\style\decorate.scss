@use './loading.scss';
$success: var(--success); // #00db77;
$warning: var(--warning); // #ffb30f
$info: var(--info); //#fff
$danger: var(--danger); // #dd212f
$theme: var(--activeColor); // #fb8c00

// 文字阴影
.text-theme {
    color: $theme;
    text-shadow: 0 0 12px $theme, 0 0 1px $theme;
}

.text-success {
    color: $success;
    text-shadow: 0 0 12px $success, 0 0 1px $success;
}

.text-warning {
    color: $warning;
    text-shadow: 0 0 12px $warning, 0 0 1px $warning;
}

.text-info {
    color: $info;
    text-shadow: 0 0 12px $info, 0 0 1px $info;
}

.text-danger {
    color: $danger;
    text-shadow: 0 0 12px $danger, 0 0 1px $danger;
}

// 标题
.title {
    margin: 0 auto;
    background-color: var(--bg);
    opacity: 0.7;
    text-align: center;
    font-weight: 700;
    font-size: 20px;
    padding: 10px 0;
    position: relative;
    cursor: pointer;

    .bg-b.l-t {
        top: 0;
        left: 0;
        border-bottom-color: transparent;
        border-right-color: transparent;
    }

    .bg-b.l-b {
        bottom: 0;
        left: 0;
        border-top-color: transparent;
        border-right-color: transparent;
    }

    .bg-b.r-t {
        top: 0;
        right: 0;
        border-bottom-color: transparent;
        border-left-color: transparent;
    }

    .bg-b.r-b {
        bottom: 0;
        right: 0;
        border-top-color: transparent;
        border-left-color: transparent;
    }

    .bg-b {
        border: 1px solid var(--color);
        width: 5px;
        height: 5px;
        position: absolute;
        z-index: 1;
    }
}

.card-style {
    opacity: 0.8;
    border: 2px solid transparent;
    border-radius: 10px;
    cursor: pointer;
    transform-origin: right bottom;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.320, 1);
}


.card2 {
    cursor: pointer;
    transform-origin: right bottom;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.320, 1);
    color: white;
    // gap: 20px;
    border-radius: 10px;
    backdrop-filter: blur(5px);
    background-color: rgba(255, 255, 255, 0.1);
    box-shadow: rgba(255, 255, 255, 0.35) 0px 5px 15px;
}

// .card {
//     @extend .card-style;
//     backdrop-filter: blur(5px);
//     background: linear-gradient(var(--bg), var(--bg)) padding-box,
//         linear-gradient(-20deg, transparent 35%, #72edf2, #5151e5) border-box;
// }

.list {
    @extend .card-style;
    border-radius: 5px;
    color: var(--activeColor);
    background: linear-gradient(var(--bg), var(--bg)) padding-box,
        linear-gradient(45deg, #ddda21, #db5000) border-box;
    transition: all 0.6s;
}

.list-hover {
    @extend .list-active;
}

.list-active {
    color: $warning;
    background: rgba(0, 219, 119, .02);
    box-shadow: inset 0 0 5px $warning;
}

// 数据框渐变色
.data-border {
    background: linear-gradient(var(--bg), var(--bg)) padding-box,
        linear-gradient(-20deg, transparent 35%, #72edf2, #5151e5) border-box;
    border: 2px dashed transparent;
}

// 数据展示框 主题色
.faultDiagnosis-item.theme {
    padding-left: 10px;
    border-radius: 10px;
    color: var(--activeColor);
    @extend .data-border;

    &:hover {
        @extend .warning;

        .iconfont-turn {
            @extend .iconfont-turn-warning;
        }
    }


}

.theme.active {

    @extend .warning;
    background: linear-gradient(var(--bg), var(--bg)) padding-box,
        linear-gradient(45deg, var(--activeColor), var(--color)) border-box;
    border: 2px solid transparent;

    .iconfont-turn {
        @extend .iconfont-turn-warning;
    }

}

// 数据展示框 成功框
.faultDiagnosis-item.success {
    color: $success;
    background: rgba(0, 219, 119, .02);
    border: 1px solid $success;
    box-shadow: inset 0 0 15px $success;
}

// 数据展示框 警告框
.faultDiagnosis-item.warning {
    color: $warning;
    background: rgba(0, 219, 119, .02);
    border: 1px solid $warning;
    box-shadow: inset 0 0 15px $warning;
}

// 数据展示框 info
.faultDiagnosis-item.info {
    color: $info;
    background: rgba(0, 219, 119, .02);
    border: 1px solid $info;
    box-shadow: inset 0 0 15px $info;
}

// 数据展示框 错误 
.faultDiagnosis-item.danger {
    color: $danger;
    background: rgba(0, 219, 119, .02);
    border: 1px solid $danger;
    box-shadow: inset 0 0 15px $danger;
}


.faultDiagnosis-item {
    flex: 0 0 1;
    height: 80px;
    align-content: center;
    margin: 10px 0 0 0;
    position: relative;
}

.faultDiagnosis-item:after,
.faultDiagnosis-item:before {
    position: absolute;
    width: 2px;
    height: 2px;
    content: "";
    top: -1px;
}

.faultDiagnosis-item:before {
    left: 0;
}

.faultDiagnosis-item:after {
    right: -1px;
}

.faultDiagnosis-item .bias {
    position: relative;
}

.faultDiagnosis-item .bias:after,
.faultDiagnosis-item .bias:before {
    width: 10px;
    height: 10px;
    position: absolute;
    content: "";
    top: 7px;
}

.faultDiagnosis-item .bias:before {
    left: 0;
    transform: rotate(45deg);
}

.faultDiagnosis-item .bias:after {
    right: 0;
    transform: rotate(-45deg);
}

.faultDiagnosis-item .itemfoot {
    position: relative;
}

.faultDiagnosis-item .itemfoot:after,
.faultDiagnosis-item .itemfoot:before {
    position: absolute;
    width: 2px;
    height: 2px;
    content: "";
    bottom: -3px;
}

.faultDiagnosis-item .itemfoot:before {
    left: 0;
}

.faultDiagnosis-item .itemfoot:after {
    right: -1px;
}

.faultDiagnosis-item .itemfoot .bias {
    position: relative;
}

.faultDiagnosis-item .itemfoot .bias:after,
.faultDiagnosis-item .itemfoot .bias:before {
    width: 10px;
    height: 10px;
    position: absolute;
    content: "";
    top: -7px;
}

.faultDiagnosis-item .itemfoot .bias:before {
    left: 7px;
    transform: rotate(320deg);
}

.faultDiagnosis-item .itemfoot .bias:after {
    right: 7px;
    transform: rotate(-320deg);
}

.faultDiagnosis-item .name {
    font-size: 14px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.faultDiagnosis-item .name span {
    margin-left: 5px;
}

.faultDiagnosis-item .name .round {
    flex: 0 0 10px;
}

.faultDiagnosis-item .val {
    font-family: bah;
    font-size: 22px;
    display: flex;
    height: 60%;
    align-content: center;
    justify-content: center;
    align-items: center;
}

.faultDiagnosis-item .val .iconfont {
    font-size: 18px;
    margin-right: 7px;
    margin-bottom: 3px;
}

.faultDiagnosis-item:hover {
    cursor: pointer;
}

// 呼吸小球动画
.round-success {
    @extend .round;
    color: $success;
    background-image: -webkit-gradient(linear, left top, left bottom, from($success), to($success));
    box-shadow: 0 0 5px 5px $success;
}

.round-warning {
    @extend .round;
    color: $warning;
    background-image: -webkit-gradient(linear, left top, left bottom, from($warning), to($warning));
    box-shadow: 0 0 5px 5px $warning;
}

.round-info {
    @extend .round;
    color: $info;
    background-image: -webkit-gradient(linear, left top, left bottom, from($info), to($info));
    box-shadow: 0 0 5px 5px $info;
}

.round-danger {
    @extend .round;
    color: $danger;
    background-image: -webkit-gradient(linear, left top, left bottom, from($danger), to($danger));
    box-shadow: 0 0 5px 5px $danger;
}

.round-theme {
    @extend .round;
    color: var(--activeColor);
    ;
    background-image: -webkit-gradient(linear, left top, left bottom, from(var(--activeColor)), to(var(--activeColorDark)));
    box-shadow: 0 0 5px 5px var(--activeColor);
    ;
}


.round {
    position: relative;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    text-align: center;
    box-shadow: 0 1px 2px rgba(0, 0, 0, .3);
    overflow: hidden;
    -webkit-animation-timing-function: ease-in-out;
    -webkit-animation-name: breathe-81994adc;
    -webkit-animation-duration: 1s;
    -webkit-animation-iteration-count: infinite;
    -webkit-animation-direction: alternate;
}

// round呼吸动画
@keyframes breathe-81994adc {
    0% {
        opacity: 1;
    }

    100% {

        opacity: 0.4;
    }
}

// 发光小球
.circle:before {
    box-shadow: 0 0 5px 5px rgba(255, 179, 15, .3);
    background: #ffb30f;
}


.iconfont-turn-info {
    background-image: -webkit-linear-gradient(top, rgba(192, 192, 192), rgba(192, 192, 192, 0.747));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    @extend .turn;
}

.iconfont-turn-warning {
    background-image: -webkit-linear-gradient(top, #ffc600, #e29308);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    @extend .turn;
}

.iconfont-turn {
    background-image: -webkit-linear-gradient(top, var(--activeColor), var(--activeColorDark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    @extend .turn;
}

.turn {
    animation: turnAni 4s linear infinite;
}


// 图标旋转动画
@keyframes turnAni {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    25% {
        -webkit-transform: rotate(90deg);
        transform: rotate(90deg);
    }

    50% {
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg);
    }

    75% {
        -webkit-transform: rotate(270deg);
        transform: rotate(270deg);
    }

    100% {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn);
    }
}


// 浮动
.float_ani {
    animation: floatAni 1.5s linear infinite alternate;
}

// 扩散
.diffuse_ani {
    animation: diffuseAni 2.5s linear infinite alternate;
}

// 呼吸
.breathe_ani {
    animation: breatheAni 2.5s linear infinite alternate;
}

// 水母
.acaleph_ani {
    animation: acalephAni 3s linear infinite alternate;
}

.acaleph_ani2 {
    animation: acalephAni2 3s linear infinite alternate;
}

@keyframes acalephAni {
    from {
        opacity: 0.5;
        transform: translateY(-5px);
        transform: rotateY(-30deg)
    }

    to {
        opacity: 1;
        transform: translateY(5px);
        transform: rotateY(30deg)
    }
}

@keyframes acalephAni2 {
    from {
        opacity: 0.5;
        transform: translateX(-5px);
        transform: rotateX(-30deg)
    }

    to {
        opacity: 1;
        transform: translateX(5px);
        transform: rotateX(30deg)
    }
}

@keyframes diffuseAni {
    0% {
        scale: 1
    }

    100% {
        scale: 1.08
    }
}



@keyframes floatAni {
    0% {
        transform: translateY(3px);
    }

    100% {
        transform: translateY(-3px);
    }
}

@keyframes breatheAni {
    0% {
        opacity: 0.5;
    }

    100% {
        opacity: 0.9;
    }
}