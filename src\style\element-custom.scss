// 弹窗

.el-dialog {
    // width: 850px;
    padding: 20rem !important;
    // height: 350px;
    background: rgb(0, 0, 0) !important;
    box-shadow: rgb(255, 179, 15) 0px 0px 20px inset !important;
    border: 1px solid rgba(255, 179, 15, 0.3) !important;
    border-radius: 10px !important;

    .el-dialog__body {
        font-size: 18rem !important;
        font-family: Source Han Sans CN !important;
        color: rgba(227, 216, 183) !important;
    }

    // .el-table__body tr.current-row>td {
    //     background-color: #5e430a;
    //     /* 高亮背景色 */
    //   }
}

// .el-dialog {
//     background: none !important;
//     background-image: url(../assets/png/popup.png) !important;
//     background-size: 100% 100% !important;
//     padding: 20rem !important;

//     .el-dialog__body {
//         font-size: 18rem !important;
//         font-family: Source <PERSON>s <PERSON>N !important;
//         color: rgba(227, 216, 183) !important;
//     }
// }
.el-button+.el-button {
    background-color: rgba(255, 179, 15, 0.3) !important;
    border: 1px solid rgba(232, 157, 6, 0.8) !important;
}

.el-dialog__title {
    font-size: 20rem !important;
    font-family: Source Han Sans CN !important;
    font-weight: 400 !important;
    color: rgba(227, 216, 183) !important;
    margin-left: 10rem;

}

.el-dialog__headerbtn {
    top: 15rem !important;
    right: 20rem !important;
    font-size: 23rem !important;
}

.el-dialog__close {
    color: rgba(227, 216, 183) !important;
}

// 修改 el-popper 悬浮背景颜色
.el-popper.is-customized {
    /* Set padding to ensure the height is 32px */
    padding: 6px 12px;
    font-size: 16px;
    font-weight: bold;
    color: #262626;
    background: linear-gradient(90deg, rgb(159, 229, 151), rgb(204, 229, 129));
}

.el-popper.is-customized .el-popper__arrow::before {
    font-size: 16px;
    font-weight: bold;
    background: linear-gradient(45deg, #b2e68d, #bce689);
    right: 0;
}

.el-popper.is-light,
.el-popper.is-light .el-popper__arrow:before {
    background: #453F37 !important;
    border: 1rem solid #453F37 !important;
}

.el-select-dropdown__item {
    color: #eee !important;

    &:hover {
        background: #21242999 !important;
    }
}

.el-select-dropdown__item.is-hovering {
    background: #21242999 !important;
}

.el-select-dropdown__item.is-selected {
    background: #212429 !important;
    color: #ff9800 !important;
}