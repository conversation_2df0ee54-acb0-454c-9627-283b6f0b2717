@use './style.scss';
@use './reset.scss';
@use './theme.scss';
@use './decorate.scss';
@use './three.scss';
@use './element-custom.scss';
@use './messageBox.scss';

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*,
li,
ul {
    padding: 0;
    margin: 0;
    list-style: none;
}

body,
#app {
    height: 100vh;
    width: 100%;
    padding: 0;
    margin: 0;
    font-size: 16px;
    overflow: hidden;
}

#app {
    background-color: #191919;
    // background-image: url('@/assets/png/bg.png');
    background-size: 100vw 100vh;
}