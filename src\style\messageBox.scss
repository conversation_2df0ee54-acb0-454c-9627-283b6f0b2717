.el-message-box {
  height: 150px;
  background-color: rgb(0, 0, 0,0.1) !important;
  box-shadow: rgb(255, 179, 15) 0 0 10rem inset !important;
  border: 1rem solid rgba(255, 179, 15, 0.3) !important;
  color: #fff !important;
  // border-radius: 10rem !important;
  .el-message-box__title,.el-message-box__message,.el-icon .el-message-box__close{
    color: #fff !important;
  }
  .el-message-box__headerbtn .el-message-box__close{
    color: #fff !important;
  }
  .el-message-box__btns{
    .el-button--primary{
      --el-button-bg-color: rgba(255, 179, 15, 0.3) !important;
      --el-button-border-color:  rgba(232, 157, 6, 0.8) !important;
      // --el-button-active-bg-color:rgba(255, 179, 15, 0.3) !important;
      // --el-button-active-border-color:  rgba(232, 157, 6, 0.8) !important;
      --el-button-hover-bg-color:rgba(255, 179, 15, 0.3) !important;
      --el-button-hover-border-color:  rgba(232, 157, 6, 0.8) !important;
    }
  }
}