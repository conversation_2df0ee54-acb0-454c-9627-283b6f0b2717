.fill-color {
    opacity: 0.95;
    filter: invert(52%) sepia(96%) saturate(700%) hue-rotate(359deg) brightness(101%) contrast(106%);
}

.fontLabel {
    width: 1rem;
    height: 0.75rem;
    color: black;
    opacity: 1;
    padding: 0.15rem;
    font-size: 12px;
    text-align: center;
    line-height: 0.35rem;
    font-weight: bold;
    position: relative;

    .img {
        position: absolute;
        left: 0;
        top: 0;
        z-index: -1;
        width: 100%;
        height: 100%;
        // fill: currentColor;
        // color: #000;
    }
}

.svgLabel {
    @extend .fontLabel;
    width: 2rem;

    .svg {
        position: absolute;
        left: 0;
        top: 0;
        z-index: -1;
        width: 100%;
        height: 100%;
        scale: 1.9;
        // fill: currentColor;
        // color: #000;
    }
}

.fontLabel1 {
    opacity: 1;
    padding: 0.15rem;
    font-size: 12px;
    text-align: center;
    line-height: 0.35rem;
    font-weight: bold;
    position: relative;
    width: 20px;
    height: 20px;
    color: red;
}

// 三维通风页面数据标注框
.data_label_box {
    // 在标注框外部通过像素将左上角挤到三维原点上

    .data_label {
        position: relative;
        display: flex;
        justify-content: center;

        span {
            background-image: url('/textures/data_label.svg');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            position: absolute;
            top: 0;
            left: 0;
            display: block;
            text-align: center;
            line-height: 85px;
            white-space: nowrap;
            overflow: hidden;
            padding-left: 50px;
            padding-right: 20px;
            padding-top: 2px;
            width: fit-content;
            height: 70px;
            font-size: 20px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #FFFFFF;
        }
    }
}

.data_label_box1 {
    // 在标注框外部通过像素将左上角挤到三维原点上
    @extend .data_label_box;

    .data_label {
        filter: hue-rotate(90deg);

        span {
            color: #ffff66;
            font-size: 20px;
        }
    }
}

.svg_color {
    fill: red;
}

.canvasBox {
    width: 100vw;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
}