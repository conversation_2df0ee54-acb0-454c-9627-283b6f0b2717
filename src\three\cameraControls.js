import * as THREE from 'three';
import gsap from "gsap";
import { findMesh, getDataType } from './utils/utils';
import CameraControls from 'camera-controls';
CameraControls.install({ THREE: THREE });
export function initCameraControls(camera, renderer) {
    console.log('1111',camera)
    const cameraControls = new CameraControls(camera, renderer.domElement);
    // 修改鼠标原本键位
    cameraControls.mouseButtons = {
        //左键平移
        left: CameraControls.ACTION.TRUCK,
        //滚轮滑动
        wheel: CameraControls.ACTION.DOLLY,
        //右键旋转
        right: CameraControls.ACTION.ROTATE
    }
    return cameraControls;
}
// 移动到某点 并让相机朝向这个点 cameraAdd 是相对于这个点的偏移 point是去到的点位 target是相机指向的点位
export function moveToCamera(controls, cameraAdd, point, target) {
    const [x, y, z] = point ?? [0, 0, 0]
    const [x1, y1, z1] = target ?? [x, y, z]

    if (getDataType(cameraAdd) == 'Number') {
        return controls.setLookAt(x - cameraAdd, y + cameraAdd, z + cameraAdd, x1, y1, z1, true)
    } else {
        const [addX, addY, addZ] = cameraAdd;
        return controls.setLookAt(x - addX, y + addY, z + addZ, x1, y1, z1, true)
    }
}

// 相机路径过渡动画 采用gsap实现
export function cameraAni(cameraControls, path = [], lookPoint = [0, 0, 0], callBack = () => { }) {
    let curve;
    if (!(path instanceof THREE.CatmullRomCurve3)) {
        let path2 = path.map(v => {
            if (!(v instanceof Array)) {
                throw new Error("路径数组里的项必须是数组或者是THREE.CatmullRomCurve3的实例");
            }
            return new THREE.Vector3(...v)
        })
        curve = new THREE.CatmullRomCurve3(path2);
    }
    else {
        curve = path;
    }
    const [lookX, lookY, lookZ] = lookPoint
    const _tmp = new THREE.Vector3();
    const animationProgress = { value: 0 };
    const gsapAni = gsap.fromTo(
        animationProgress,
        {
            value: 0,
        },
        {
            value: 1,
            duration: 3,
            overwrite: true,
            paused: true,
            onUpdateParams: [animationProgress],
            onUpdate({ value }) {

                if (!this.isActive()) return;

                curve.getPoint(value, _tmp);
                const cameraX = _tmp.x ;
                const cameraY = _tmp.y ;
                const cameraZ = _tmp.z ;
                const lookAtX = lookX;
                const lookAtY = lookY;
                const lookAtZ = lookZ;

                cameraControls.setLookAt(
                    cameraX,
                    cameraY,
                    cameraZ,
                    lookAtX,
                    lookAtY,
                    lookAtZ,
                    false, // IMPORTANT! disable cameraControls's transition and leave it to gsap.
                );



            },
            onStart() {
                cameraControls.enabled = true;

            },
            onComplete() {
                cameraControls.enabled = true;
                callBack()
            },
        }
    );
    gsapAni.play()
}

// 限制相机垂直方向角度 参数: 相机控制器 最小 最大角度
export function restrictCameraVerticalDirection(cameraControls, min, max) {

    cameraControls.minPolarAngle = min; // 最小垂直旋转角度
    cameraControls.maxPolarAngle = max; // 最大垂直旋转角度
}

// 限制相机缩放比例
export function limitCameraVerticalAngle(cameraControls, minZoom, maxZoom) {
    cameraControls.minZoom = minZoom; // 最小缩放比例
    cameraControls.maxZoom = maxZoom; // 最大放大比例
}
// 让相机飞跃到某个物体跟前
export function cameraFlyToMesh(cameraControls, { mesh, scene, name, paddingTop = 0, paddingRight = 0, paddingBottom = 0, paddingLeft = 0 }) {
    const mesh1 = mesh ?? findMesh(scene, name)
    cameraControls.fitToBox(mesh1, true, { paddingTop, paddingRight, paddingBottom, paddingLeft })
}

// 相机回到初始位置
export function resetCamera(cameraControls, callback = () => { }) {
    cameraControls.reset(true)
    callback()
}
