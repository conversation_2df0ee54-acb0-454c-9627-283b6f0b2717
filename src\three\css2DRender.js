import { CSS2DRenderer } from 'three/examples/jsm/renderers/CSS2DRenderer.js';


export function initCSS2DRenderer() {
    const labelRenderer = new CSS2DRenderer();
    labelRenderer.setSize(window.innerWidth, window.innerHeight);
    labelRenderer.domElement.style.position = 'absolute';
    labelRenderer.domElement.style.top = '0px';
    return labelRenderer
}

//封装2d标签标注方法
import { CSS2DObject } from 'three/examples/jsm/renderers/CSS2DRenderer.js';
export function create2DText(x, y, z, text) {
    const earthDiv = document.createElement('div');
    earthDiv.className = 'label';
    earthDiv.textContent = text;
    earthDiv.style.backgroundColor = 'transparent';
    earthDiv.style.color = 'white';
    earthDiv.style.fontSize = '12px'

    const label = new CSS2DObject(earthDiv);
    label.position.set(x, y, z);
    // label.center.set(0, 1);
    return label
}
function parseDom(arg) {
    var objE = document.createElement("p");
    objE.innerHTML = arg;
    return objE.childNodes;
}
export function createIconText(x, y, z, text, path = '../assets/svg/dhk.svg') {
    // <img src="./textures/bz.png" style="width:100px;height:100px"/>

    const earthDiv = parseDom(
        `
            <div class="fontLabel">
                <img class="img" src="${require(path)}"></img> 
                <p>${text}</p>
            </div>
        `
    )[1]
    // console.log(earthDiv, 'earthDiv ');
    // earthDiv.textContent = text;
    // earthDiv.style.backgroundColor = 'transparent';

    const label = new CSS2DObject(earthDiv);
    label.position.set(x, y, z);
    // label.center.set(0, 1);
    return label
}
// 天蓝色

export function createSvgText(x, y, z, text, iconColor = '#fb8c00') {
    // <img src="./textures/bz.png" style="width:100px;height:100px"/>

    const earthDiv = parseDom(
        `
            <div class="svgLabel">
                <svg class="svg" t="1691738183796"  viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6493" width="200" height="200"><path d="M682.666667 789.802667l-140.501334 136.106666a42.026667 42.026667 0 0 1-60.330666 0L341.333333 789.802667H128c-23.573333 0-42.666667-19.498667-42.666667-43.562667V128.896C85.333333 104.832 104.426667 85.333333 128 85.333333h768c23.573333 0 42.666667 19.498667 42.666667 43.562667V746.24c0 24.064-19.093333 43.562667-42.666667 43.562667H682.666667zM256 469.333333h341.333333v85.333334H256v-85.333334z m0-170.666666h512v85.333333H256v-85.333333z" fill="${iconColor}" p-id="6494"></path></svg>
                <p>${text}</p>
            </div>
        `
    )[1]
    // earthDiv.textContent = text;
    // earthDiv.style.backgroundColor = 'transparent';

    const label = new CSS2DObject(earthDiv);
    label.position.set(x, y, z);
    // label.center.set(0, 1);
    return label
}