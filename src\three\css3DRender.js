// 引入CSS3渲染器CSS3DRenderer
import { CSS3DRenderer, CSS3DObject, CSS3DSprite } from 'three/examples/jsm/renderers/CSS3DRenderer.js';
import * as THREE from 'three'
export function initCSS3DRenderer() {
    const css3Renderer = new CSS3DRenderer();
    css3Renderer.setSize(window.innerWidth, window.innerHeight);
    // HTML标签<div id="tag"></div>外面父元素叠加到canvas画布上且重合
    css3Renderer.domElement.style.position = 'absolute';
    css3Renderer.domElement.style.top = '0px';
    //设置.pointerEvents=none，解决HTML元素标签对threejs canvas画布鼠标事件的遮挡
    css3Renderer.domElement.style.pointerEvents = 'none';
    return css3Renderer
}

function parseDom(arg) {
    var objE = document.createElement("p");
    objE.innerHTML = arg;
    return objE.childNodes;
}
// 引入CSS3模型对象 CSS3DSprite 用于创建精灵
export function createCss3DSprite(path = require('../assets/svg/dhk.svg'), text = '123456', scale = 1, dom) {
    const div = parseDom(
        dom ??
        `
            <div class="  flex justify-center items-center p-5px text-[12px]">
                <p>${text}</p>
            </div>
        `
    )[1]
    const tag = new CSS3DSprite(div);
    div.style.pointerEvents = 'none';//避免HTML标签遮挡三维场景的鼠标事件
    // 缩放
    tag.scale.set(scale, scale, scale)
    return tag
}
// 引入CSS3模型对象 CSS3DSprite 用于创建精灵
export function createCss3DText(point = [0, 0, 0], text = '123456', scale = 1, dom) {
    const [x, y, z] = point;
    const div = parseDom(
        dom ??
        `
            <div style="color:#eee;">
                ${text}
            </div>
        `
    )[1]
    const tag = new CSS3DSprite(div);
    div.style.pointerEvents = 'none';//避免HTML标签遮挡三维场景的鼠标事件
    // 缩放
    tag.scale.set(scale, scale, scale)
    tag.position.set(x, y, z)
    return tag
}


// 创建一个 CSS3DObj 模型 不会朝向相机 无法进行交互 纯静态展示
export function createCss3DObj({ imgPath, text, scale = 1, dom }) {
    const div = parseDom(
        dom ??
        `
            <div class="flex justify-center items-center p-5px text-[12px] ">
                <img  class="img w-227px h-50px" src="${imgPath}"></img> 
                <p>${text}</p>
            </div>
        `
    )[1]
    var label = new CSS3DObject(div);
    // const label = new THREE.Object3D();
    // label.userData.element = div;

    div.style.pointerEvents = 'none';//避免HTML标签遮挡三维场景的鼠标事件
    label.scale.set(scale, scale, scale);
    label.renderOrder = 1; // 设置渲染顺序，确保标签在最前面


    return label;
}

