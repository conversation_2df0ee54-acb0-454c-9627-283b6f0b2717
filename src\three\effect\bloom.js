import { initComposer } from "./shine";
import { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass.js';
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass.js';
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass.js';
import * as THREE from 'three'
import { textureLoader } from './../utils/loader';
export function initBloom(renderer, scene, camera) {

    // 创建 bloom
    function createBloom(renderer, scene, camera) {
        const { composer: bloomComposer, effectFXAA } = initComposer(renderer, scene, camera, false)
        effectFXAA.renderToScreen = true; // ***
        const bloomPass = new UnrealBloomPass(
            new THREE.Vector2(window.innerWidth, window.innerHeight),
            1,
            0,
            0
        );

        bloomComposer.renderToScreen = false; // ***
        bloomComposer.addPass(bloomPass);
        bloomComposer.addPass(effectFXAA);

        const { composer: finalComposer } = initComposer(renderer, scene, camera, false)
        const finalPass = new ShaderPass(
            new THREE.ShaderMaterial({
                uniforms: {
                    baseTexture: { value: textureLoader().load('./textures/xq.png') },
                    bloomTexture: { value: bloomComposer.renderTarget2.texture },
                    uColor: { value: new THREE.Vector3(0.8, 0.3, 1.0) }
                },
                vertexShader: `
                    varying vec2 vUv;
                    void main() {
                        vUv = uv;

                        gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );

                    }

            `,
                fragmentShader: `
                    uniform sampler2D baseTexture;
                    uniform sampler2D bloomTexture;
                    uniform vec3 uColor;
                    varying vec2 vUv;

                        void main() {

                            gl_FragColor = ( texture2D( baseTexture, vUv ) + vec4(uColor,1.0 ) * texture2D( bloomTexture, vUv ) );

                        }
            `,
                defines: {},
            }),
            "baseTexture"
        );
        finalPass.needsSwap = true;
        finalComposer.addPass(finalPass);
        finalComposer.addPass(effectFXAA); // 必须在 finalPass 后面

        return { finalComposer, bloomComposer, bloomPass, finalPass }
    }
    const { bloomComposer, bloomPass, finalComposer, finalPass } = createBloom(renderer, scene, camera);
    // 处理handleBloom
    function handleBloom() {
        // 备份的材质
        const materials = {};
        const ENTIRE_SCENE = 0,
            BLOOM_SCENE = 1;

        const bloomLayer = new THREE.Layers();
        bloomLayer.set(BLOOM_SCENE);
        const darkMaterial = new THREE.MeshBasicMaterial({ color: "black" });
        // 对需要保留的材质备份 设置成黑色 不受辉光影响
        scene.traverse((obj) => {
            if (obj instanceof THREE.Scene) {
                materials.scene = obj.background
                obj.background = null
            }
            if (obj instanceof THREE.AxesHelper) {

            }
            // 记录设置为0的物体 将他们材质保存下来 默认物体为0
            if (obj.isMesh && bloomLayer.test(obj.layers) == 0) {
                materials[obj.uuid] = obj.material;
                obj.material = darkMaterial;
            }
        });
        bloomComposer.render();
        scene.traverse((obj) => {
            // 还原 layer 为0的物体
            if (materials[obj.uuid]) {
                obj.material = materials[obj.uuid];
                delete materials[obj.uuid];
            }
            // 还原场景
            if (obj instanceof THREE.Scene) {
                obj.background = materials.scene;
                delete materials.scene
            }
        });
        finalComposer.render();
    }
    return { handleBloom, bloomPass, finalPass, bloomComposer, finalComposer }
}

