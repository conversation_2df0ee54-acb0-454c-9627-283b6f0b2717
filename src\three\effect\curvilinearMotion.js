import * as THREE from 'three';
import FlyLineShader from "@/three/mesh/FlyLineShader";
import { randomColor } from "@/three/utils/utils";
// 圆形曲线 二维数组
export function initCircleCurvilinearrMotion({ radius = 5, color = 0x00ffff }) {
    let curve = new THREE.ArcCurve(0, 0, radius, 0, 2 * Math.PI);
    const points = curve.getPoints(1000);
    console.log(points);
    const geometry = new THREE.BufferGeometry().setFromPoints(points);

    const material = new THREE.LineBasicMaterial({
        linewidth: 10,
        color,
        linecap: 'round',
        linejoin: 'round'
    });

    // 普通曲线
    const line = new THREE.Line(geometry, material);
    // 实现物体在曲线上进行运动
    function meshMotion(mesh, elapsed) {
        const time = elapsed / 10 % 1;
        const point = curve.getPoint(time);
        const { x, y } = point;
        const ve3 = new THREE.Vector3(x, y, 0)
        mesh.position.copy(ve3);
    }

    // 飞跃曲线
    const flyLine = new FlyLineShader({ pointsArr: points, color: randomColor(), useCenter: false }).mesh
    return { line, flyLine, meshMotion, points }
}


