
import * as THREE from 'three';
import { textureLoader } from './../utils/loader';


// 空气流动效果
export function initAirFlow(geo, scale, path) {
    // 创建一个平面
    const planeGeometry = geo ?? new THREE.PlaneGeometry(100, 5, 64, 64);
    // 添加贴图
    const texture = new THREE.TextureLoader().load(path ?? "./textures/airflow.png");
    // 拉伸贴图 铺满平面
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.RepeatWrapping;
    texture.repeat.set(1, 1);

    // 创建一个材质 
    const planeMaterial = new THREE.MeshStandardMaterial({
        map: texture,
        color: 0xffffff,
        side: THREE.DoubleSide,
        transparent: true,
        opacity: 0.9,
        emissive: 0xffffff,
        emissiveIntensity: 5
        // wireframe: true
    });
    const airMesh = new THREE.Mesh(planeGeometry, planeMaterial);
    // geo ? ' ' : airMesh.rotation.z = -Math.PI / 2;
    const scale1 = scale ?? 1
    airMesh.scale.set(scale1, scale1, scale1);

    // 材质偏移
    // gsap.to(planeMaterial.map.offset, {
    //     duration: 3,
    //     y: 1,
    //     repeat: -1,
    //     ease: "none",
    // })
    // gsap.to(planeMaterial.map.offset, {
    //     duration: 3,
    //     x: 0.3,
    //     repet: -1,
    //     yoyo: true,
    // })
    const clock = new THREE.Clock()
    function airAnimate() {
        const time = clock.getElapsedTime()
        // 一定要在此函数中调用
        if (planeMaterial.map) {
            planeMaterial.map.offset.y += Math.sin(time) * 0.002
            planeMaterial.map.offset.x -= 0.01

        }

    }

    // 优化偏移动画 用tween.js 实现无缝循环
    // const tween = new TWEEN.Tween(planeMaterial.map.offset)
    //     .to({ y: 1, x: 0.2 }, 4000)
    //     .easing(TWEEN.Easing.Linear.None)
    //     .repeat(Infinity)
    //     .start()
    return { airMesh, airAnimate };
}

// 道路流光效果
export function initRoadFlow(linePoints = [], { path = './textures/jt2-1.png', color = 0xff0000, radius = 1 }) {
    let texture = textureLoader().load(path);

    texture.wrapS = texture.wrapT = THREE.RepeatWrapping; //每个都重复
    texture.repeat.set(1, 1)
    texture.needsUpdate = true

    const material = new THREE.MeshStandardMaterial({
        map: texture,
        color: color,
        side: THREE.DoubleSide,
        transparent: true,
        opacity: 0.9,
        emissive: 0xffffff,
        emissiveIntensity: 5
        // wireframe: true
    });
    // 创建顶点数组
    let points = linePoints.length ? linePoints : [
        new THREE.Vector3(0, 0, 0),
        new THREE.Vector3(10, 0, 0),
        new THREE.Vector3(10, 0, 10),
        new THREE.Vector3(0, 0, 10),
        new THREE.Vector3(0, 0, 0),
    ]

    // CatmullRomCurve3创建一条平滑的三维样条曲线
    let curve = new THREE.CatmullRomCurve3(points) // 曲线路径

    // 创建管道
    let tubeGeometry = new THREE.TubeGeometry(curve, 80, radius)

    let roadMesh = new THREE.Mesh(tubeGeometry, material);


    function roadAnimate() {
        // 一定要在此函数中调用
        if (texture) texture.offset.x -= 0.01
    }

    return { roadMesh, roadAnimate }
}

