import * as THREE from 'three';
var vertexShader = [
    'varying vec3	vVertexWorldPosition;',
    'varying vec3	vVertexNormal;',

    'varying vec4	vFragColor;',

    'void main(){',
    '	vVertexNormal	= normalize(normalMatrix * normal);',//将法线转换到视图坐标系中

    '	vVertexWorldPosition	= (modelMatrix * vec4(position, 1.0)).xyz;',//将顶点转换到世界坐标系中

    '	// set gl_Position',
    '	gl_Position	= projectionMatrix * modelViewMatrix * vec4(position, 1.0);',
    '}'

].join('\n');

// 大气层效果
var fragmentShader1 = [
    'uniform vec3	glowColor;',
    'uniform float	coeficient;',
    'uniform float	power;',
    'uniform float uTime;',

    'varying vec3	vVertexNormal;',
    'varying vec3	vVertexWorldPosition;',

    'varying vec4	vFragColor;',

    'void main(){',
    '	vec3 worldCameraToVertex= vVertexWorldPosition - cameraPosition;',	//世界坐标系中从相机位置到顶点位置的距离
    '	vec3 viewCameraToVertex	= (viewMatrix * vec4(worldCameraToVertex, 0.0)).xyz;',//视图坐标系中从相机位置到顶点位置的距离
    '	viewCameraToVertex	= normalize(viewCameraToVertex);',//规一化
    '	float intensity		= pow(coeficient + dot(vVertexNormal, viewCameraToVertex), power);',
    '	gl_FragColor		= vec4(glowColor, intensity);',
    '}'//vVertexNormal视图坐标系中点的法向量
    //viewCameraToVertex视图坐标系中点到摄像机的距离向量
    //dot点乘得到它们的夹角的cos值
    //从中心向外面角度越来越小（从钝角到锐角）从cos函数也可以知道这个值由负变正，不透明度最终从低到高
].join('\n');

// 辉光效果
var fragmentShader2 = [
    'uniform vec3	glowColor;',
    'uniform float	coeficient;',
    'uniform float	power;',
    'uniform float uTime;',

    'varying vec3	vVertexNormal;',
    'varying vec3	vVertexWorldPosition;',

    'varying vec4	vFragColor;',

    'void main(){',
    '	vec3 worldVertexToCamera= cameraPosition - vVertexWorldPosition;',	//世界坐标系中顶点位置到相机位置到的距离
    '	vec3 viewCameraToVertex	= (viewMatrix * vec4(worldVertexToCamera, 0.0)).xyz;',//视图坐标系中从相机位置到顶点位置的距离
    '	viewCameraToVertex	= normalize(viewCameraToVertex);',//规一化
    '	float intensity		= coeficient + dot(vVertexNormal * ((cos(uTime) + 6.0) / 5.0), viewCameraToVertex);',
    '	if(intensity > 0.55){ intensity = 0.0;}',
    '	gl_FragColor		= vec4(glowColor, intensity);',
    '}'//vVertexNormal视图坐标系中点的法向量
    //viewCameraToVertex视图坐标系中点到摄像机的距离向量
    //dot点乘得到它们的夹角的cos值
    //从中心向外面角度越来越大（从锐角到钝角）从cos函数也可以知道这个值由负变正，不透明度最终从高到低
].join('\n');

const material_glow = new THREE.ShaderMaterial({
    uniforms: {
        coeficient: {
            type: "f",
            value: 0.0
        },
        power: {
            type: "f",
            value: 2
        },
        glowColor: {
            type: "c",
            value: new THREE.Color('blue')
        },
        uTime: {
            value: 0.0
        }
    },
    vertexShader: vertexShader,
    fragmentShader: fragmentShader1,
    blending: THREE.NormalBlending,
    transparent: true,
    side: THREE.DoubleSide
});

var customMaterial = new THREE.ShaderMaterial({
    uniforms: {
        c: { type: "f", value: 1.0 },
        p: { type: "f", value: 1.4 },
        glowColor: { type: "c", value: new THREE.Color('blue') },
        viewVector: { type: "v3", value: { x: 0, y: 100, z: 400 } },
        uTime: {
            value: 0.0
        }
    },
    // 顶点着色器
    vertexShader: `
            uniform vec3 viewVector;
            uniform float c;
            uniform float p;
            varying float intensity;
            void main() {
                vec3 vNormal = normalize( normalMatrix * normal);
            vec3 vNormel = normalize( normalMatrix * viewVector);
            intensity = pow( c - dot(vNormal, vNormel), p );
            gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0);
                }
            `,
    // 片段着色器
    fragmentShader: `
            uniform vec3 glowColor;
            varying float intensity;
            void main()
            {
                vec3 glow = glowColor * intensity;
            gl_FragColor = vec4( glow, 1.0 );
                }
            `,
    side: THREE.DoubleSide,
    transparent: true,
    blending: THREE.AdditiveBlending, //在使用此材质显示对象时要使用何种混合。加法
});

//
import { deepCloneGeometry } from '@/utils/utils'
export function createGlslShine(geo, color = 'blue') {
    const material = customMaterial.clone();
    material.uniforms.glowColor.value = new THREE.Color(color);
    // 放大geo
    const geo1 = geo.clone();
    const scaleNum = 1.001
    geo1.scale(scaleNum, scaleNum, scaleNum);
    const mesh = new THREE.Mesh(geo1, material);
    mesh.name = 'glslShine';
    // 放大
    // mesh.scale.multiplyScalar(1.2);
    return mesh;
}