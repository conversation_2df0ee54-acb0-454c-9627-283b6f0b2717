// 实现材质烘培效果 将光照信息烘培到贴图中
import * as THREE from 'three'
const vertexShader = `
varying highp vec3 vNormal;
uniform highp mat4 world;WS
varying highp mat4 view;
void main() {
    // 经过转换后的正确坐标
    view = viewMatrix;
    vNormal = normalize(vec3(world * vec4(normal, 0.0)));
    gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.0);
}
`
const fragmentShader = `
  varying highp vec3 vNormal;
  varying highp mat4 view;
  uniform sampler2D matcapTexture;

  void main(void) {
    highp vec2 muv = vec2(view * vec4(normalize(vNormal), 0))*0.5+vec2(0.5,0.5);
    gl_FragColor = texture2D(matcapTexture, vec2(muv.x, 1.0-muv.y));
  }
`

// 根据上述着色器代码创建着色器材质
export function initMatcap(url = './textures/555.png') {
    return new THREE.ShaderMaterial({
        uniforms: {
            matcapTexture: {
                value: new THREE.TextureLoader().load(url)
            },
            // 创建一个uniform变量，用于传递世界矩阵
            world: {
                value: new THREE.Matrix4()
            }
        },
        vertexShader,
        fragmentShader,
        side: THREE.DoubleSide
    })
}

// Realistic Materials in OpenGL ES 2.0 着色器代码 第二种写法 没有高光效果
// const vertexShader = `
// varying highp vec3 vNormal;
// varying highp vec3 vViewPosition;
// uniform highp mat4 world;
// void main() {
//     // 经过转换后的正确坐标
//     vViewPosition = vec3(world * vec4(position, 1.0));
//     vNormal = normalize(vec3(world * vec4(normal, 0.0))); // Normal in model space
//     gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.0);
// }
// `
// const fragmentShader = `
//   varying highp vec3 vNormal;
//   varying highp vec3 vViewPosition;
//   uniform sampler2D matcapTexture;

//   void main(void) {
//     highp vec3 viewDir = normalize(vViewPosition);
//     highp vec3 normal = normalize(vNormal);
//     highp vec3 reflectDir = reflect(viewDir, normal);
//     highp float m = 2.8284271247461903 * sqrt(reflectDir.z+1.0);
//     highp vec2 uv = reflectDir.xy / m + 0.5;
//     gl_FragColor = texture2D(matcapTexture, uv);
//   }
// `


// 使用 three 自带的 meshMatcapMaterial属性进行烘培材质的使用 效果更好
import { MeshMatcapMaterial, TextureLoader } from 'three'
import { findMesh } from '../utils/meshUtils'
export function initMatcapThree(options = { path: null, color: null, map: null }) {
    const { path, color, map } = options;
    const matcap = new TextureLoader().load(path ?? './textures/xk1.png')
    const mat = new MeshMatcapMaterial({
        // color: new THREE.Color().setHex(0xffffff).convertSRGBToLinear(),
        color: new THREE.Color(color ?? 0xffffff),
        matcap: matcap,
        map: map ?? null
    });
    return mat
}

// 贴图函数
export function matcapTexture(meshArr, meshNameArr = [], path = './textures/222.png') {
    const mat = initMatcapThree({ path })
    function addMat(mesh) {
        mesh.material = mat;
        mesh.material.transparent = true;
        mesh.material.opacity = 0.95;
    }
    meshNameArr.forEach(v => {
        const mesh = findMesh(meshArr, v);
        if (mesh instanceof THREE.Group) {
            mesh.children.forEach(groupMesh => {
                addMat(groupMesh)
            })
        } else {
            addMat(mesh)
        }
    })
}