import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer.js';
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass.js';
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass.js';
// 发光通道
import { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass.js';
// ShaderPass功能：使用后处理Shader创建后处理通道
import { FXAAShader } from 'three/examples/jsm/shaders/FXAAShader.js';
// SMAA抗锯齿通道
import { SMAAPass } from 'three/examples/jsm/postprocessing/SMAAPass.js';
// 引入OutlinePass通道
import { OutlinePass } from 'three/examples/jsm/postprocessing/OutlinePass.js';

import * as THREE from 'three'
import gsap from "gsap";

const params = {
    exposure: 0.1,
    bloomStrength: 2,
    bloomThreshold: 0,
    bloomRadius: 1,
}

// 根据后期处理效果实现发光
export function initShine(renderer, scene, camera) {
    const width = window.innerWidth;
    const height = window.innerHeight;
    const composer = new EffectComposer(renderer);
    const renderScene = new RenderPass(scene, camera);
    composer.addPass(renderScene);

    // 抗锯齿
    //获取.setPixelRatio()设置的设备像素比
    const pixelRatio = renderer.getPixelRatio();
    // smaaPass抗锯齿
    const smaaPass = new SMAAPass(width * pixelRatio, height * pixelRatio);
    composer.addPass(smaaPass);
    //FXAAPass抗锯齿
    const FXAAPass = new ShaderPass(FXAAShader);
    // width、height是canva画布的宽高度
    FXAAPass.uniforms.resolution.value.x = 1 / (width * pixelRatio);
    FXAAPass.uniforms.resolution.value.y = 1 / (height * pixelRatio);
    composer.addPass(FXAAPass);

    const effectFXAA = new ShaderPass(FXAAShader);
    effectFXAA.uniforms['resolution'].value.set(1 / window.innerWidth, 1 / window.innerHeight);
    composer.addPass(effectFXAA);


    // 物体发光轮廓
    const outlinePass = new OutlinePass(new THREE.Vector2(window.innerWidth, window.innerHeight), scene, camera);

    const textureLoader = new THREE.TextureLoader();
    textureLoader.load('textures/outline/tri_pattern.jpg', function (texture) {
        outlinePass.patternTexture = texture;
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;

    });

    outlinePass.edgeThickness = 4.0;//高亮发光描边厚度
    outlinePass.edgeGlow = 2.0;    // 边缘发光
    outlinePass.edgeStrength = 4.0;    //高亮描边发光强度
    outlinePass.pulsePeriod = 3.0;    //模型闪烁频率控制，默认0不闪烁
    // 添加轮廓通道
    function addOutlinePass(selectedObjects, color = 0x00ffff) {
        // 设置OutlinePass的参数
        //模型描边颜色，默认白色         
        outlinePass.visibleEdgeColor.set(color);
        outlinePass.selectedObjects = selectedObjects
    }
    composer.addPass(outlinePass);
    const outlinePass1 = new OutlinePass(new THREE.Vector2(window.innerWidth, window.innerHeight), scene, camera);
    outlinePass1.edgeThickness = 3.0;
    outlinePass1.edgeGlow = 1.0;
    outlinePass1.edgeStrength = 5.0;
    outlinePass1.pulsePeriod = 3.0;
    // 添加轮廓通道
    function addOutlinePass1(selectedObjects, color = 0xff0000) {
        // 设置OutlinePass的参数
        //模型描边颜色，默认白色         
        outlinePass1.visibleEdgeColor.set(color);
        outlinePass1.selectedObjects = selectedObjects
    }
    composer.addPass(outlinePass1);

    return { composer, addOutlinePass, addOutlinePass1, outlinePass, outlinePass1, effectFXAA }
}


export function initComposer(renderer, scene, camera, addEffectFXAA) {
    const width = window.innerWidth;
    const height = window.innerHeight;
    const composer = new EffectComposer(renderer);
    const renderScene = new RenderPass(scene, camera);

    // // 抗锯齿
    const pixelRatio = renderer.getPixelRatio();

    // smaaPass抗锯齿
    const smaaPass = new SMAAPass(width * pixelRatio, height * pixelRatio);

    //FXAAPass抗锯齿
    const FXAAPass = new ShaderPass(FXAAShader);
    FXAAPass.uniforms.resolution.value.x = 0.6 / (width * pixelRatio);
    FXAAPass.uniforms.resolution.value.y = 0.6 / (height * pixelRatio);

    // 抗锯齿
    const effectFXAA = new ShaderPass(FXAAShader);
    effectFXAA.uniforms['resolution'].value.set(0.6 / window.innerWidth, 0.6 / window.innerHeight);
    composer.addPass(FXAAPass);
    composer.addPass(smaaPass);
    composer.addPass(renderScene);
    addEffectFXAA && composer.addPass(effectFXAA);
    return { composer, effectFXAA }
}

import { deepCloneMaterial } from '@/three/utils/utils'


// 使用着色器实现发光效果
export function createShineMesh(meshGeo, meshMat, color = 0x00ffff) {
    const mat = deepCloneMaterial(meshMat)
    // 天蓝色
    mat.color = new THREE.Color(color)
    var meshShine = new THREE.Mesh(meshGeo.clone(), mat);
    meshShine.name = "meshShine";
    // 放大1.2倍
    // meshShine.scale.multiplyScalar(10)
    return meshShine
}

export function textureShine(mesh, color) {
    var material = new THREE.MeshLambertMaterial({ color });
    mesh.material = material;
    // 加载贴图
    var texture = textureLoader.load("/textures/shine/shine.png");
    // 点精灵材质
    var spriteMaterial = new THREE.SpriteMaterial({
        map: texture,//贴图
        color,
        blending: THREE.AdditiveBlending,//在使用此材质显示对象时要使用何种混合。加法
    });
    var sprite = new THREE.Sprite(spriteMaterial);
    // 发光范围
    sprite.scale.set(100, 100, 1.0);
    mesh.add(sprite);
}

export function glslShine(mesh, color) {
    // 着色器材质(Uniforms 是 GLSL 着色器中的全局变量。
    var customMaterial = new THREE.ShaderMaterial({
        uniforms: {
            c: { type: "f", value: 1.0 },
            p: { type: "f", value: 1.4 },
            glowColor: { type: "c", value: new THREE.Color(color) },
            viewVector: { type: "v3", value: new THREE.Vector3(0, 100, 400) },
        },
        vertexShader: `
        uniform vec3 viewVector;
        uniform float c;
        uniform float p;
        varying float intensity;

        void main() {
            vec3 vNormal = normalize(normalMatrix * normal);
            vec3 vNormel = normalize(normalMatrix * viewVector);
            intensity = pow((c - dot(vNormal, vNormel)*5.0), p);
            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
    `,
        fragmentShader: `
        uniform vec3 glowColor;
        varying float intensity;

        void main() {
            vec3 glow = glowColor * intensity;
            gl_FragColor = vec4(glow, 1.0);
        }
    `,
        side: THREE.FrontSide,
        transparent: true,
        blending: THREE.AdditiveBlending
    });

    var moonGlow = new THREE.Mesh(mesh.geometry.clone(), customMaterial.clone());
    // 放大1.2倍
    moonGlow.scale.multiplyScalar(1.2);
    mesh.add(moonGlow)
}

