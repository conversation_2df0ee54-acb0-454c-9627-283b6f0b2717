import * as THREE from 'three';
import { initRender } from "./renderer";
import { initScene } from "./scene";
import { initShine } from "./effect/shine";
import CameraControls from 'camera-controls';
import * as TWEEN from '@tweenjs/tween.js'

CameraControls.install({ THREE: THREE });
const width = window.innerWidth;
const height = window.innerHeight;
export function initThree(x, y, z) {
    const clock = new THREE.Clock();
    const camera = new THREE.PerspectiveCamera(75, width / height, 1, 40000);
    const renderer = initRender()
    const scene = initScene(renderer)
    // 后期发光效果
    const { composer, addOutlinePass, addOutlinePass1, effectFXAA, outlinePass, outlinePass1 } = initShine(renderer, scene, camera)
    const { cameraControls } = config(x, y, z)
    init()
    // 初始化
    function init() {

        // 监听屏幕大小改变的变化，设置渲染的尺寸
        window.addEventListener("resize", () => {
            // 更新摄像头
            camera.aspect = window.innerWidth / window.innerHeight;
            // 更新摄像机的投影矩阵
            camera.updateProjectionMatrix();
            // 更新渲染器
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setPixelRatio(window.devicePixelRatio);

            // effectFXAA.uniforms['resolution'].value.set(1 / window.innerWidth, 1 / window.innerHeight);
        });
    }

    function config(x, y, z) {
        // 设置相机位置
        camera.position.set(x, y, z);
        const cameraControls = new CameraControls(camera, renderer.domElement);
        scene.add(camera)
        composer.setPixelRatio(window.devicePixelRatio);
        return { cameraControls }
    }

    function render() {
        // renderer.clear();
        // camera.layers.set(0);
        composer.render();
        // renderer.clearDepth();
        // camera.layers.set(1);
        renderer.render(scene, camera);
    }
    function ani(callback = () => { }) {
        const delta = clock.getDelta();
        const time = clock.getElapsedTime();
        const updated = cameraControls.update(delta);
        composer.render();
        renderer.render(scene, camera);

        // 更新tween动画 
        TWEEN.update();
        requestAnimationFrame(() => {
            callback(time, camera, delta)
            ani(callback)
        });
        if (updated) {
            composer.render();
            renderer.render(scene, camera);
        }

    }
    // 后期效果合集
    const shineObj = { addOutlinePass, addOutlinePass1, outlinePass, outlinePass1 }
    const renderObj = { renderer }
    return {
        TWEEN, scene, camera, ...renderObj, THREE, ani, ...shineObj
    }
}


// 移除物体
export function meshRemove(mesh) {
    mesh.remove();
    mesh.removeFromParent();
    mesh.geometry.dispose();
    mesh.material.dispose();
}
