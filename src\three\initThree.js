import * as THREE from "three";
import { initScene } from "./scene";
import { initRender } from "./renderer";
import { initCSS2DRenderer } from "./css2DRender";
import { initShine } from "./effect/shine";
import { initCSS3DRenderer } from "./css3DRender";
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";
import * as TWEEN from "@tweenjs/tween.js";
const width = window.innerWidth;
const height = window.innerHeight;
export function initThree(x = 0, y = 10, z = 10) {
  const renderer = initRender();
  const labelRenderer = initCSS2DRenderer(); // css2D渲染器
  const css3Renderer = initCSS3DRenderer(); // css3D渲染器
  const scene = initScene(renderer);
  // 相机
  const camera = new THREE.PerspectiveCamera(75, width / height, 1, 40000);

  //控制器
  const controls = new OrbitControls(camera, renderer.domElement);
  
  const labelControls = new OrbitControls(camera, labelRenderer.domElement);
  // 后期发光效果
  const { composer, addOutlinePass, addOutlinePass1, effectFXAA, outlinePass } = initShine(renderer, scene, camera);
  config();
  init();

  function config() {
    // 设置相机位置
    camera.position.set(x, y, z);
    scene.add(camera);
  }

  // 初始化
  function init() {
    // 更新摄像头
    camera.aspect = window.innerWidth / window.innerHeight;
    //   更新摄像机的投影矩阵
    camera.updateProjectionMatrix();
    // 监听屏幕大小改变的变化，设置渲染的尺寸
    window.addEventListener("resize", () => {
      // 更新摄像头
      camera.aspect = window.innerWidth / window.innerHeight;
      // 更新摄像机的投影矩阵
      camera.updateProjectionMatrix();
      // 更新渲染器
      renderer.setSize(window.innerWidth, window.innerHeight);
      labelRenderer.setSize(window.innerWidth, window.innerHeight);
      css3Renderer.setSize(window.innerWidth, window.innerHeight);
      composer.setSize(window.innerWidth, window.innerHeight);

      // 设置渲染器的像素比例
      renderer.setPixelRatio(window.devicePixelRatio);
      effectFXAA.uniforms["resolution"].value.set(1 / window.innerWidth, 1 / window.innerHeight);
      ani.initRender();
    });
  }

  // 渲染类
  const clock = new THREE.Clock();
  class Animate {
    constructor() {
      this.myAni = null;
      this.init();
    }
    // 再一次渲染
    initRender() {
      controls.update();
      labelControls.update();
      // css2D渲染器
      labelRenderer.render(scene, camera);
      // css3D渲染器
      css3Renderer.render(scene, camera);
      renderer.render(scene, camera);
      composer.render();
      renderer.setPixelRatio(window.devicePixelRatio);
      // 更新tween动画
      TWEEN.update();
    }
    init() {
      controls.update();
      labelControls.update();
      const time = clock.getElapsedTime();

      this.myAni && this.myAni(time, camera);

      labelRenderer.render(scene, camera);
      // css3D渲染器
      css3Renderer.render(scene, camera);
      renderer.render(scene, camera);
      composer.render();
      // 设置设备像素比，避免canvas画布输出模糊
      renderer.setPixelRatio(window.devicePixelRatio);
      // 更新tween动画
      TWEEN.update();
      requestAnimationFrame((time) => {
        this.init(time);
      });
    }
  }

  // 挂载函数
  function mountRender(el) {
    el.appendChild(css3Renderer.domElement);
    el.appendChild(renderer.domElement);
  }

  // 统一导出 规范导出
  const effectObj = { addOutlinePass, addOutlinePass1 };
  const controlsObj = { controls, labelControls };
  const utils = { THREE };
  var ani = new Animate();
  return {
    scene,
    camera,
    renderer,
    labelRenderer,
    css3Renderer,
    ani,
    outlinePass,
    mountRender,
    ...effectObj,
    ...controlsObj,
    ...utils,
  };
}

// 移除物体
export function meshRemove(mesh) {
  mesh.remove();
  mesh.removeFromParent();
  mesh.geometry.dispose();
  mesh.material.dispose();
}
