import * as THREE from 'three';
import { initRender } from "./renderer";
import { initCSS2DRenderer } from "./css2DRender";
import { initCSS3DRenderer } from "./css3DRender";
import { initScene } from "./scene";
import { initShine } from "./effect/shine";
import { initCameraControls } from './cameraControls';
import * as TWEEN from '@tweenjs/tween.js'
import gsap from 'gsap';
import { GUI } from 'three/examples/jsm/libs/lil-gui.module.min'
import { initSvgRenderer } from './svgRender';
import { addEvent, removeEvent } from '@/utils/window';


let width = window.innerWidth;
let height = window.innerHeight;


export function initThree(x, y, z, options = { isControl, isDepthBuffer, myCamera: null, isComposer }) {

    const isControl = options.isControl ?? true;
    const isDepthBuffer = options.isDepthBuffer ?? false;
    const isComposer = options.isComposer ?? true;
    let camera = options.myCamera ?? new THREE.PerspectiveCamera(75, width / height, 0.01, 40000);

    const clock = new THREE.Clock();
    let renderer = initRender(isDepthBuffer)
    let scene = initScene(renderer)
    // let css2Renderer = initCSS2DRenderer() // css2D渲染器
    let css3Renderer = initCSS3DRenderer() // css3D渲染器
    // 后期发光效果
    const { composer, addOutlinePass, addOutlinePass1, effectFXAA, outlinePass, outlinePass1 } = initShine(renderer, scene, camera)
    config(x, y, z)
    let cameraControls = isControl && initCameraControls(camera, renderer);
    const resizeObserver = new ResizeObserver(() => {
        resizeFn();
    });
    init()
    // 初始化
    function init() {
        // 监听屏幕大小改变的变化，设置渲染的尺寸
        addEvent(window, 'resize', resizeFn)
    }
    function resizeFn() {
        if (sceneDomElement) {
            width = sceneDomElement.clientWidth;
            height = sceneDomElement.clientHeight;
        } else {
            width = window.innerWidth;
            height = window.innerHeight;
        }

        if (camera) {
            // 更新摄像头
            camera.aspect = width / height;
            // 更新摄像机的投影矩阵
            camera.updateProjectionMatrix();
        }

        // 设置渲染器的像素比例 
        isComposer && renderer && renderer.setPixelRatio(window.devicePixelRatio);
        isComposer && composer.setPixelRatio(window.devicePixelRatio);
        // css2Renderer.setSize(width, height);
        // 更新渲染器
        renderer && renderer.setSize(width ?? window.innerWidth, height ?? window.innerHeight);
        isComposer && composer.setSize(width ?? window.innerWidth, height ?? window.innerHeight);
        css3Renderer.setSize(width ?? window.innerWidth, height ?? window.innerHeight);
        effectFXAA.uniforms['resolution'].value.set(0.6 / width, 0.6 / height);
    }

    function config(x, y, z) {
        // 设置相机位置
        camera.position.set(x, y, z);
        scene.add(camera)
    }
    let requestAnimationId = null;
    function ani(callback = () => { }) {
        const delta = clock.getDelta();
        const time = clock.getElapsedTime();
        isControl && cameraControls.update(delta);

        // 更新tween动画 
        TWEEN.update();
        requestAnimationId = requestAnimationFrame(() => {
            callback(time, camera, delta, requestAnimationId)
            ani(callback)
        });

        if (scene && camera && renderer) {
            css3Renderer.render(scene, camera);
            isComposer && composer.render(scene, camera); // 可以代替 renderer
        };
    }

    let sceneDomElement = null;
    // 停止渲染函数
    function stopRender() {
        // 取消动画帧请求
        cancelAnimationFrame(requestAnimationId);
        // 清除网格
        clearMesh();

        renderer && renderer.forceContextLoss();
        renderer && renderer.dispose();
        scene && scene.clear();
        sceneDomElement && (sceneDomElement.innerHTML = '');

        scene = null;
        camera = null;
        cameraControls = null;
        renderer = null;
        sceneDomElement = null;
        // css2Renderer = null;
        css3Renderer = null;
        // 移除事件
        removeEvent(window, 'resize', resizeFn);

    }
    function clearMesh() {
        try {
            scene.traverse && scene.traverse(child => {
                scene.remove(child);
                if (child.material) {
                    child.material.dispose();
                }
                if (child.geometry) {
                    child.geometry.dispose();
                }

                if (child instanceof THREE.Group) {
                    clearGroup(child);
                }
                child = null;
            })

        } catch (error) {
            scene && scene instanceof Object && scene.children && scene.children.forEach(child => {
                scene.remove(child);
                if (child.material) {
                    child.material.dispose();
                }
                if (child.geometry) {
                    child.geometry.dispose();
                }
                if (child instanceof THREE.Group) {
                    clearGroup(child);
                }
                child = null;
            })
        }
    }
    // 清理group组
    function clearGroup(group) {
        const clearCache = (item) => {
            item.geometry && item.geometry.dispose();
            item.material && item.material.dispose();
        };
        const removeObj = (obj) => {
            let arr = obj.children.filter((x) => !!x);
            arr.forEach((item) => {
                if (item.children.length) {
                    removeObj(item);
                } else {
                    clearCache(item);
                    item.clear();
                }
            });
            obj.clear();
            arr = null;
        };
        removeObj(group);
    }


    // 挂载函数
    function mountRender(el) {
        sceneDomElement = el;
        // width = sceneDomElement.clientWidth;
        // height = sceneDomElement.clientHeight;
        // renderer.setSize(width, height);
        // css3Renderer.setSize(width, height);
        // composer.setSize(width, height);
        // css2Renderer.setSize(width, height);
        resizeFn();
        el.appendChild(renderer.domElement);
        el.appendChild(css3Renderer.domElement);
        resizeObserver.observe(el);
    }

    const shineObj = { composer, addOutlinePass, addOutlinePass1, outlinePass, outlinePass1 }// 后期效果
    const renderObj = { renderer, css3Renderer }
    const utilsObj = { gsap, TWEEN } // 工具库
    return {
        scene, camera, ...renderObj, cameraControls, THREE, ani, clearMesh, stopRender, mountRender,
        ...utilsObj, ...shineObj,
    }

}


// 移除物体
export function meshRemove(mesh, flag = false) {
    if (flag || !(mesh instanceof THREE.Mesh)) {
        return
    }
    mesh.remove();
    // 释放内存
    mesh.geometry && mesh.geometry.dispose();
    mesh.material && mesh.material.dispose();
    mesh.texture && mesh.texture.dispose();
}



