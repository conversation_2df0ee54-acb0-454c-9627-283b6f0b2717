// 用法
// var { texture } = makeTextSprite(THREE, text,
//     {
//         fontsize: 110,
//         borderColor: { r: 255, g: 0, b: 0, a: 1.0 },/* 边框黑色 */
//         color: color ?? { r: 255, g: 255, b: 255, a: 1.0 },
//         backgroundColor: bgColor ?? { r: 0, g: 151, b: 164, a: 1.0 }/* 背景颜色 */
//     });
// circle.material.map = texture;
import { Texture } from 'three';

export function canvasTexture(THREE, message, parameters) {

    if (parameters === undefined) parameters = {};

    var fontface = parameters.hasOwnProperty("fontface") ?
        parameters["fontface"] : "Arial";

    /* 字体大小 */
    var fontsize = parameters.hasOwnProperty("fontsize") ?
        parameters["fontsize"] : 18;

    /* 边框厚度 */
    var borderThickness = parameters.hasOwnProperty("borderThickness") ?
        parameters["borderThickness"] : 1;

    /* 边框颜色 */
    var borderColor = parameters.hasOwnProperty("borderColor") ?
        parameters["borderColor"] : { r: 0, g: 0, b: 0, a: 1.0 };

    /* 背景颜色 */
    var backgroundColor = parameters.hasOwnProperty("backgroundColor") ?
        parameters["backgroundColor"] : { r: 255, g: 255, b: 255, a: 1.0 };

    /* 字体颜色*/
    var fontColor = parameters.hasOwnProperty('color') ?
        parameters["color"] : { r: 0, g: 0, b: 0, a: 1.0 };

    /* 创建画布 */
    var canvas = document.createElement('canvas');
    var context = canvas.getContext('2d');

    /* 字体加粗 */
    context.font = "Bold " + fontsize + "px " + fontface;

    /* 获取文字的大小数据，高度取决于文字的大小 */
    var metrics = context.measureText(message);
    var textWidth = metrics.width;
    var textHeight = metrics.height;

    /* 背景颜色 */
    context.fillStyle = "rgba(" + backgroundColor.r + "," + backgroundColor.g + ","
        + backgroundColor.b + "," + backgroundColor.a + ")";

    /* 边框的颜色 */
    context.strokeStyle = "rgba(" + borderColor.r + "," + borderColor.g + ","
        + borderColor.b + "," + borderColor.a + ")";
    context.lineWidth = borderThickness;

    /* 绘制圆角矩形 */

    drawCicle(context, textWidth + borderThickness, textWidth + borderThickness, (textWidth + borderThickness) * 5);
    /* 字体颜色 */
    context.fillStyle = "rgba(" + fontColor.r + "," + fontColor.g + ","
        + fontColor.b + "," + fontColor.a + ")";
    const x = (canvas.width - textWidth) / 2;
    const y = (canvas.height - textHeight) / 2 + textHeight;
    context.fillText(message, x, fontsize + borderThickness);

    /* 画布内容用于纹理贴图 */
    var texture = new Texture(canvas);
    texture.needsUpdate = true;

    return { texture };

}
/* 绘制圆形*/
function drawCicle(ctx, x, y, radius = 50) {
    ctx.beginPath();
    ctx.arc(x, y, radius, 0, 2 * Math.PI);
    ctx.fill(); // 填充圆形
    ctx.closePath();
}
/* 绘制圆角矩形 */
function roundRect(ctx, x, y, w, h, r) {

    ctx.beginPath();
    ctx.moveTo(x + r, y);
    ctx.lineTo(x + w - r, y);
    ctx.quadraticCurveTo(x + w, y, x + w, y + r);
    ctx.lineTo(x + w, y + h - r);
    ctx.quadraticCurveTo(x + w, y + h, x + w - r, y + h);
    ctx.lineTo(x + r, y + h);
    ctx.quadraticCurveTo(x, y + h, x, y + h - r);
    ctx.lineTo(x, y + r);
    ctx.quadraticCurveTo(x, y, x + r, y);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();

}



export function canvasTexture2(text, options = { bgColor: 'rgba(0,0,0,0.6)', color: "#ffffff" }) {
    const canvas = document.createElement("canvas");
    const arr = text.split(""); //分割为单独字符串
    let num = 0;
    const reg = /[\u4e00-\u9fa5]/;
    for (let i = 0; i < arr.length; i++) {
        if (reg.test(arr[i])) { //判断是不是汉字
            num += 1;
        } else {
            num += 0.5; //英文字母或数字累加0.5
        }
    }
    // 根据字符串符号类型和数量、文字font-size大小来设置canvas画布宽高度
    const h = 120; //根据渲染像素大小设置，过大性能差，过小不清晰
    const w = h + num * 32;
    canvas.width = w;
    canvas.height = h;
    const c = canvas.getContext('2d');
    // 定义轮廓颜色，黑色半透明
    c.fillStyle = options.bgColor;
    // 绘制矩形
    c.fillRect(0, 0, w, h); // 填充矩形
    c.fill();

    // 文字
    c.beginPath();
    c.translate(w / 2, h / 2);
    c.fillStyle = options.color; //文本填充颜色
    c.font = "bold 100px 宋体"; //字体样式设置
    c.textBaseline = "middle"; //文本与fillText定义的纵坐标
    c.textAlign = "center"; //文本居中(以fillText定义的横坐标)
    c.fillText(text, 0, 0);
    /* 画布内容用于纹理贴图 */
    var texture = new Texture(canvas);
    texture.needsUpdate = true;

    return { texture };
}
// 对话框
export function canvasTexture3(text) {
    const canvas = document.createElement("canvas");
    const arr = text.split(""); //分割为单独字符串
    let num = 0;
    const reg = /[\u4e00-\u9fa5]/;
    for (let i = 0; i < arr.length; i++) {
        if (reg.test(arr[i])) { //判断是不是汉字
            num += 1;
        } else {
            num += 0.5; //英文字母或数字累加0.5
        }
    }
    // 根据字符串符号类型和数量、文字font-size大小来设置canvas画布宽高度
    const h = 120; //根据渲染像素大小设置，过大性能差，过小不清晰
    const w = h + num * 32;
    canvas.width = w;
    canvas.height = h;
    const h1 = h * 0.8;
    const c = canvas.getContext('2d');
    // 定义轮廓颜色，黑色半透明
    c.fillStyle = "rgba(0,0,0,0.6)";
    // 绘制矩形
    c.fillRect(0, 0, w, h1); // 填充矩形
    c.fill();
    // 绘制箭头
    c.beginPath();
    const h2 = h - h1;
    c.moveTo(w / 2 - h2, h1);
    c.lineTo(w / 2 + h2, h1);
    c.lineTo(w / 2, h * 0.9);
    c.fill();
    // 文字
    c.beginPath();
    c.translate(w / 2, h1 / 2);
    c.fillStyle = "#ffffff"; //文本填充颜色
    c.font = "bold 32px 宋体"; //字体样式设置
    c.textBaseline = "middle"; //文本与fillText定义的纵坐标
    c.textAlign = "center"; //文本居中(以fillText定义的横坐标)
    c.fillText(text, 0, 0);
    /* 画布内容用于纹理贴图 */
    var texture = new Texture(canvas);
    texture.needsUpdate = true;

    return { texture };
}