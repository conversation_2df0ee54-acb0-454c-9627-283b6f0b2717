import { MeshPhysicalMaterial, TextureLoader, Color, DoubleSide } from 'three';
import { textureLoader } from '../utils/loader';

const marbleTexture = textureLoader().load('/textures/stone/marble-01.jpg')
const stripeTexture = textureLoader().load('/textures/ball/stripe.jpg')
const ballBumpTexture = textureLoader().load('/textures/ball/bump.jpg')
const ballDisplacementTexture = textureLoader().load(
    '/textures/ball/displacement.jpg'
)
const ballNormalTexture = textureLoader().load('/textures/ball/normal.jpg')
const alphaTexture = textureLoader().load('/textures/ball/alpha2.jpg')
const alphaTexture3 = textureLoader().load('/textures/ball/alpha3.png')

export function initGlass(opacity = 0.1, color = '#ffffff') {
    /* 材质 */
    const mat = new MeshPhysicalMaterial({
        transparent: true,
        side: DoubleSide,
        roughness: 0,
        opacity,
        clearcoat: 1,
        transmission: 0.1,
        color: new Color(color)
    })
    mat.bumpMap = marbleTexture
    mat.bumpScale = 0.02
    return mat;
}
export function initGlass2() {
    /* 材质 */
    const mat = new MeshPhysicalMaterial()
    // 透明涂层的强度
    mat.clearcoat = 1
    // 透射度
    mat.transmission = 1
    mat.opacity = 0
    return mat;
}



/* 材质 */
// const mat = new MeshPhysicalMaterial()
// 透明涂层的强度
// mat.clearcoat = 0.5
// 凹凸
// mat.bumpMap = marbleTexture
// mat.bumpScale = 0.02
// 透明涂层强度贴图
// mat.clearcoatMap = stripeTexture
// 透明图层法线贴图
// mat.clearcoatNormalMap = ballNormalTexture
// 透明图层法线的强度
// mat.clearcoatNormalScale = new Vector2(0.5, 0.5)

// 透明涂层的粗糙度
// mat.clearcoatRoughness = 1
// 颜色
// mat.color = new Color('red')
// 辉光颜色
// mat.sheenColor = new Color('white')
// 辉光强度
// mat.sheen = 1
// 辉光粗糙度
// mat.sheenRoughness = 0
// 透射度
// mat.transmission = 1
// mat.opacity = 0

// 使用透明度贴图
// mat.transparent = true
// mat.side = DoubleSide
// mat.alphaMap = alphaTexture

// 透射率贴图
// mat.transmissionMap = stripeTexture

// 非金属材质折射率
// 玻璃
// mat.ior = 1.5
// 冰
// mat.ior = 1.3

//反射率
// mat.reflectivity = 0.3

// 高光强度
// mat.specularIntensity = 10

// 高光贴图
// mat.specularIntensityMap = alphaTexture3

// 高光颜色
// mat.specularColor = new Color('red')

