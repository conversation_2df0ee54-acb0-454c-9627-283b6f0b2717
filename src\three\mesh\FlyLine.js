import * as THREE from 'three';
import { initMatcapThree } from '../effect/matcap'
import gsap from "gsap";
const mat = new THREE.MeshBasicMaterial({ transparent: true, side: THREE.DoubleSide })
export default class FlyLine {
  constructor(options = { duration: 8, linePoints: [] }) {
    const { duration, linePoints } = options;

    // 1/创建曲线
    this.lineCurve = new THREE.CatmullRomCurve3(linePoints);
    // 2/根据曲线生成管道几何体
    this.geometry = new THREE.TubeGeometry(
      this.lineCurve,
      20,
      3, //半径
      2, // 两面
      false
    );
    // 3/设置飞线材质
    // 创建纹理
    const textloader = new THREE.TextureLoader();
    this.texture = textloader.load("./textures/z1.png");
    this.texture.repeat.set(1, 2);
    this.texture.wrapS = THREE.RepeatWrapping;
    this.texture.wrapT = THREE.MirroredRepeatWrapping;

    this.material = mat;
    this.material.map = this.texture;

    // 4/创建飞线物体
    this.mesh = new THREE.Mesh(this.geometry, this.material);

    // 5/创建飞线的动画
    // 通过偏移纹理贴图位置 来实现动画效果
    gsap.to(this.texture.offset, {
      x: -1,
      duration,
      repeat: -1,
      ease: "none",
    });
  }
}
