import * as THREE from 'three';
import gsap from "gsap";
import vertex from "@/three/shader/flyLine/vertex.glsl";
import fragment from "@/three/shader/flyLine/fragment.glsl";

export default class FlyLineShader {
  constructor({ pointsArr = [], endPosition = [0, 0, 0], color = 0x00ffff, startPosition = [0, 0, 0], useCenter = true, count = 750
  }) {
    // const { endPosition, color, useCenter, startPosition, count } = options;

    let linePoints;
    // 1/根据点生成曲线
    if (!pointsArr.length) {
      const [x, y, z] = startPosition;
      const [x1, y1, z1] = endPosition;
      if (useCenter) {
        linePoints = [
          new THREE.Vector3(x, y, z),
          new THREE.Vector3(x1 / 2, y1 / 2, z1 / 2),
          new THREE.Vector3(x1, y1, z1),
        ];
      } else {
        linePoints = [
          new THREE.Vector3(x, y, z),
          new THREE.Vector3(x1, y1, z1),
        ];
      }
    } else {
      if (pointsArr && pointsArr[0] instanceof THREE.Vector2) {
        linePoints = pointsArr.map((v) => {
          return new THREE.Vector3(v.x, v.y, 0)
        });
      } else {
        linePoints = pointsArr;
      }

    }


    // 创建曲线
    this.lineCurve = new THREE.CatmullRomCurve3(linePoints);
    const points = this.lineCurve.getPoints(count);
    // 2/创建几何顶点
    this.geometry = new THREE.BufferGeometry().setFromPoints(points);

    // 给每一个顶点设置属性
    const aSizeArray = new Float32Array(points.length);
    for (let i = 0; i < aSizeArray.length; i++) {
      aSizeArray[i] = i;
    }
    // console.log(aSizeArray, 'aSizeArry');
    // 设置几何体顶点属性
    this.geometry.setAttribute(
      "aSize",
      new THREE.BufferAttribute(aSizeArray, 1)
    );
    // 3/设置着色器材质
    this.shaderMaterial = new THREE.ShaderMaterial({
      color,
      uniforms: {
        uTime: {
          value: 0,
        },
        uColor: {
          value: new THREE.Color(color),
        },
        uLength: {
          value: points.length,
        },
      },
      vertexShader: vertex,
      fragmentShader: fragment,
      transparent: true,
      depthWrite: false,
      blending: THREE.AdditiveBlending,
    });

    this.mesh = new THREE.Points(this.geometry, this.shaderMaterial);

    // 改变uTime来控制动画
    gsap.to(this.shaderMaterial.uniforms.uTime, {
      value: 1000,
      duration: 6,
      repeat: -1,
      ease: "none",
    });
  }
  remove() {
    this.mesh.remove();
    this.mesh.removeFromParent();
    this.mesh.geometry.dispose();
    this.mesh.material.dispose();
  }
}
