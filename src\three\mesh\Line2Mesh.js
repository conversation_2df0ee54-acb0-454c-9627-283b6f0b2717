
import { Line2 } from 'three/examples/jsm/lines/Line2';
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry';
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial';

const lineMat = new LineMaterial({
    color: 0xfff000,
    linewidth: 2.5
});
export function createLine2(arr, mat) {
    const geometry = new LineGeometry();
    geometry.setPositions(arr);
    // 创建线条
    const lineMesh = new Line2(geometry, mat ?? lineMat);
    return lineMesh
}
export function createLine2Mat(color = 0xfff000, lineWidth = 2.5) {
    const mat = new LineMaterial({
        color,
        linewidth: lineWidth
    });
    mat.resolution.set(window.innerWidth, window.innerHeight);
    function setResolution(el) {
        el && mat.resolution.set(el.clientWidth, el.clientHeight);
    }
    mat.setResolution = setResolution
    return mat;
}