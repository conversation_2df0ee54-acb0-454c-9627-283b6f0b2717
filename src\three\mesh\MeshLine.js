import * as THREE from 'three';
// 为物体添加线框
export default class MeshLine {
  constructor(geometry, color = '#fff') {
    const edges = new THREE.EdgesGeometry(geometry);
    this.material = new THREE.LineBasicMaterial({ color });
    const line = new THREE.LineSegments(edges, this.material);
    this.geometry = edges;
    this.mesh = line;
  }
}
// 创建一条流动的线,可以在物体内部展示
export function createFlowLine(linePoints, color = '#fff') {
  const geometry = new THREE.BufferGeometry().setFromPoints(linePoints);
  const material = new THREE.LineBasicMaterial({ color });
  const line = new THREE.Line(geometry, material);
  return line;
}