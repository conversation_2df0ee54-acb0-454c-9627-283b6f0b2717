import * as THREE from 'three'
import vertex from '../shader/fireflies/vertex.vs'
import fragment from '../shader/fireflies/fragment.fs'
const textureLoader = new THREE.TextureLoader()
export function initFireflies(color = '#00d5ff', { count = 80, rangeX = 5, rangeY = 5, rangeZ = 5 }) {
  const firefliesGeometry = new THREE.BufferGeometry();
  const firefliesCount = count;
  const positionArray = new Float32Array(firefliesCount * 3);
  const scaleArray = new Float32Array(firefliesCount);

  for (let i = 0; i < firefliesCount; i++) {
    new THREE.Vector3(
      (Math.random() - 0.5) * rangeX,
      Math.random() * 1.5 * rangeY,
      (Math.random() - 0.5) * rangeZ).
      toArray(positionArray, i * 3);
    scaleArray[i] = Math.random();
    scaleArray[i] = Math.random();

  }
  firefliesGeometry.setAttribute(
    "position",
    new THREE.BufferAttribute(positionArray, 3));

  firefliesGeometry.setAttribute(
    "aScale",
    new THREE.BufferAttribute(scaleArray, 1));

  const params = {
    color,
    size: 150
  };

  const texture = textureLoader.load('textures/particles/9.png');
  const firefliesMaterial = new THREE.ShaderMaterial({
    vertexShader: vertex,
    fragmentShader: fragment,
    // transparent: true,
    uniforms: {
      uTexture: {
        value: texture
      },
      uTime: { value: 0 },
      uPixelRatio: { value: Math.min(window.devicePixelRatio, 2) },
      uSize: { value: params.size },
      uColor: { value: new THREE.Color(params.color) }
    },

    blending: THREE.AdditiveBlending,
    depthWrite: false
  });

  const fireflies = new THREE.Points(firefliesGeometry, firefliesMaterial);
  return fireflies
}


import vertex1 from '../shader/test/vertex.vs'
import fragment1 from '../shader/test/fragment.fs'
export function initShinePoint({ pointsArr = [new THREE.Vector3(0, 0, 0)], color = '#00d5ff', size = 150 }) {
  const firefliesGeometry = new THREE.BufferGeometry();
  const firefliesCount = pointsArr.length;
  const positionArray = new Float32Array(firefliesCount * 3);
  const scaleArray = new Float32Array(firefliesCount);
  const lines = [];
  pointsArr.forEach((v, i) => {
    const { x, y, z } = v
    lines.push([x, y, z])
    scaleArray[i] = 2
  })
  positionArray.set(lines.flat())
  firefliesGeometry.setAttribute(
    "position",
    new THREE.BufferAttribute(positionArray, 3));

  firefliesGeometry.setAttribute(
    "aScale",
    new THREE.BufferAttribute(scaleArray, 1));

  const params = {
    color,
    size,
    uFrequency: 10,
    uScale: 0.1,
  };

  const firefliesMaterial = new THREE.ShaderMaterial({
    vertexShader: vertex1,
    fragmentShader: fragment1,
    transparent: true,
    uniforms: {
      uTime: { value: 0 },
      uPixelRatio: { value: Math.min(window.devicePixelRatio, 2) },
      uSize: { value: params.size },
      uColor: { value: new THREE.Color(params.color) }
    },

    blending: THREE.AdditiveBlending,
    depthWrite: false
  });
  const fireflies = new THREE.Points(firefliesGeometry, firefliesMaterial);
  return fireflies
}

import vertex2 from '../shader/test/vertex.vert'
import fragment2 from '../shader/test/fragment.frag'
export function initShinePlane({ position = [], color = '#00d5ff', size = 1 }) {
  // 创建纹理加载器对象
  const textureLoader = new THREE.TextureLoader();
  // const texture = textureLoader.load("./texture/da.jpeg");
  const params = {
    uFrequency: 10,
    uScale: 0.1,
    color,
  };

  // 创建着色器材质;
  const shaderMaterial = new THREE.ShaderMaterial({
    vertexShader: vertex2,
    fragmentShader: fragment2,
    uniforms: {
      uColor: {
        value: new THREE.Color(color),
      },
      // 波浪的频率
      uFrequency: {
        value: params.uFrequency,
      },
      // 波浪的幅度
      uScale: {
        value: params.uScale,
      },
      // 动画时间
      uTime: {
        value: 0,
      },
      // uTexture: {
      //   value: texture,
      // },
    },
    side: THREE.DoubleSide,
    transparent: true,
  });

  // new THREE.MeshBasicMaterial({ color: "#00ff00" })
  // 创建平面
  const floor = new THREE.Mesh(
    new THREE.PlaneGeometry(size, size, 64, 64),
    // new THREE.SphereGeometry(size, 64, 64),
    shaderMaterial
  );
  position && floor.position.set(...position)
  return floor;
}
