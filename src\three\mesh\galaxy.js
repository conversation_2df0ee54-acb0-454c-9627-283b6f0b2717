import * as THREE from 'three'
import vertexShader from '@/three/shader/galaxy/vertex.vert'
import fragmentShader from '@/three/shader/galaxy/fragment.frag'

const textureLoader = new THREE.TextureLoader()
const texture = textureLoader.load('textures/particles/9.png');
const texture1 = textureLoader.load('textures/particles/9.png');
const texture2 = textureLoader.load('textures/particles/9.png');
let material;
let geometry = null;
let galaxy = null;
export function initGalaxy({ color1 = 0xfadb14, color2 = 0x00ffff, scene }) {
    // 设置星系的参数
    const params = {
        count: 500,
        size: 0.1,
        radius: 20,
        branches: 4,
        spin: 0.5,
        color: color1,
        outColor: color2,
    };
    // GalaxyColor
    let galaxyColor = new THREE.Color(params.color);
    let outGalaxyColor = new THREE.Color(params.outColor);
    // 如果已经存在这些顶点，那么先释放内存，在删除顶点数据
    if (galaxy !== null) {
        geometry.dispose();
        material.dispose();
        scene && scene.remove(galaxy);
    }
    // 生成顶点几何
    geometry = new THREE.BufferGeometry();
    //   随机生成位置
    const positions = new Float32Array(params.count * 3);
    const colors = new Float32Array(params.count * 3);

    const scales = new Float32Array(params.count);

    //图案属性
    const imgIndex = new Float32Array(params.count)

    //   循环生成点
    for (let i = 0; i < params.count; i++) {
        const current = i * 3;

        // 计算分支的角度 = (计算当前的点在第几个分支)*(2*Math.PI/多少个分支)
        const branchAngel =
            (i % params.branches) * ((2 * Math.PI) / params.branches);

        const radius = Math.random() * params.radius;
        // 距离圆心越远，旋转的度数就越大
        // const spinAngle = radius * params.spin;

        // 随机设置x/y/z偏移值
        const randomX =
            Math.pow(Math.random() * 2 - 1, 3) * 0.5 * (params.radius - radius) * 0.3;
        const randomY =
            Math.pow(Math.random() * 2 - 1, 3) * 0.5 * (params.radius - radius) * 0.3;
        const randomZ =
            Math.pow(Math.random() * 2 - 1, 3) * 0.5 * (params.radius - radius) * 0.3;

        // 设置当前点x值坐标
        positions[current] = Math.cos(branchAngel) * radius + randomX;
        // 设置当前点y值坐标
        positions[current + 1] = randomY;
        // 设置当前点z值坐标
        positions[current + 2] = Math.sin(branchAngel) * radius + randomZ;

        const mixColor = galaxyColor.clone();
        mixColor.lerp(outGalaxyColor, radius / params.radius);

        //   设置颜色
        colors[current] = mixColor.r;
        colors[current + 1] = mixColor.g;
        colors[current + 2] = mixColor.b;

        // 顶点的大小
        scales[current] = Math.random();

        // 根据索引值设置不同的图案；
        imgIndex[current] = i % 3;
    }
    geometry.setAttribute("position", new THREE.BufferAttribute(positions, 3));
    geometry.setAttribute("color", new THREE.BufferAttribute(colors, 3));
    geometry.setAttribute("aScale", new THREE.BufferAttribute(scales, 1));
    geometry.setAttribute("imgIndex", new THREE.BufferAttribute(imgIndex, 1));


    //   设置点的着色器材质
    material = new THREE.ShaderMaterial({
        vertexShader: vertexShader,
        fragmentShader: fragmentShader,
        transparent: true,
        vertexColors: true,
        blending: THREE.AdditiveBlending,
        depthWrite: false,
        uniforms: {
            uTime: {
                value: 0,
            },
            uTexture: {
                value: texture
            },
            uTexture1: {
                value: texture1
            },
            uTexture2: {
                value: texture2
            },
            uTime: {
                value: 0
            },
            uColor: {
                value: galaxyColor
            }

        },
    });

    //   生成点
    galaxy = new THREE.Points(geometry, material);

    function animate(time) {
        material.uniforms.uTime.value = time;
    }
    return { galaxy, animate }
};


