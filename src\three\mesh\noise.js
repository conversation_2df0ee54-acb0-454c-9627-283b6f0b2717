
import * as THREE from 'three'
import vertex from '../shader/noise/vertex.glsl'
import fragment from '../shader/noise/fragment.glsl'
export function initNoise(width, color1 = '#0000ff', color2 = '#fb8c00') {
    const debugObject = {
        portalColorStart: color1,
        portalColorEnd: color2
    };
    // PortalLightMaterial
    const portalLightMaterial = new THREE.ShaderMaterial({
        vertexShader: vertex,
        fragmentShader: fragment,
        transparent: false,
        blending: THREE.AdditiveBlending,
        side: THREE.DoubleSide,
        uniforms: {
            uTime: { value: 0 },
            uColorStart: { value: new THREE.Color(debugObject.portalColorStart) },
            uColorEnd: { value: new THREE.Color(debugObject.portalColorEnd) }
        }
    });
    const planeMesh = new THREE.PlaneGeometry(width, width, 36, 36)
    const mesh = new THREE.Mesh(planeMesh, portalLightMaterial);
    mesh.rotateX(-Math.PI * 0.5)
    return mesh
}