import * as THREE from 'three'

//创建鼠标投射光线
const raycaster = new THREE.Raycaster()


// 物体检测方法
export function meshPickup(event, cubMeshArr, camera, el) {
    // 鼠标的位置 二维向量
    const mouse = new THREE.Vector2()
    if (el) {
        const rect = el.getBoundingClientRect();
        const mouseX = event.clientX;
        const mouseY = event.clientY;

        // 判断鼠标是否在 DOM 元素内
        if (!(mouseX >= rect.left &&
            mouseX <= rect.left + rect.width &&
            mouseY >= rect.top &&
            mouseY <= rect.top + rect.height)
        ) {
            return {
                currentMesh: null,
                allMesh: [],
                raycaster,
                mouse
            };
        }
    }
    mouse.x = el ? ((event.clientX - el.getBoundingClientRect().left) / el.offsetWidth) * 2 - 1 : (event.clientX / window.innerWidth) * 2 - 1
    mouse.y = el ? -((event.clientY - el.getBoundingClientRect().top) / el.offsetHeight) * 2 + 1 : -((event.clientY / window.innerHeight) * 2 - 1)

    raycaster.setFromCamera(mouse, camera);
    //intersectObjects 用来检测物体是否被选中
    let result = cubMeshArr.length ? raycaster.intersectObjects(cubMeshArr) : []
    // result[0].object.material = rayMat
    return {
        currentMesh: result[0] ?? null,
        allMesh: result,
        raycaster,
        mouse
    }
}

// 物体检测方法
export function meshPickup2(event, cubMeshArr, camera) {

    // 鼠标的位置 二维向量
    const mouse = new THREE.Vector2()
    mouse.x = (event.clientX / window.innerWidth) * 2 - 1
    mouse.y = -((event.clientY / window.innerHeight) * 2 - 1)

    raycaster.setFromCamera(mouse, camera);
    //intersectObjects 用来检测物体是否被选中
    let result = cubMeshArr.length ? raycaster.intersectObjects(cubMeshArr) : []
    // result[0].object.material = rayMat
    return {
        currentMesh: result[0] ?? null,
        allMesh: result,
        raycaster,
        mouse
    }
}



const intersectionPoint = new THREE.Vector3();
const planeNormal = new THREE.Vector3();
const plane = new THREE.Plane();
export function getAccuratePosition(e, camera, scene) {
    // 获取鼠标在三维坐标系中的精确位置 通过平面相交
    const mouse = new THREE.Vector2();
    mouse.x = (e.clientX / window.innerWidth) * 2 - 1;
    mouse.y = -(e.clientY / window.innerHeight) * 2 + 1;
    planeNormal.copy(camera.position).normalize();
    plane.setFromNormalAndCoplanarPoint(planeNormal, scene.position);
    raycaster.setFromCamera(mouse, camera);
    raycaster.ray.intersectPlane(plane, intersectionPoint);

    return intersectionPoint
}
