import * as THREE from 'three';
export function initRender(isDepthBuffer = false) {
  const renderer = new THREE.WebGLRenderer({
    // 启用透明渲染
    alpha: true,
    // 抗锯齿
    antialias: true,
    sortObjects: false,
    autoClear: true, //启用硬件加速
    // 设置对数深度缓冲区，优化深度冲突问题 两个模型距离较近时会产生闪烁问题
    logarithmicDepthBuffer: isDepthBuffer
  });
  // 开启裁剪
  renderer.setPixelRatio(window.devicePixelRatio);
  // 设置渲染尺寸大小
  renderer.setSize(window.innerWidth, window.innerHeight);
  renderer.outputEncoding = THREE.sRGBEncoding; // 设置正确颜色空间
  renderer.toneMapping = THREE.ACESFilmicToneMapping;
  renderer.toneMappingExposure = 1.8; // 调整曝光度
  return renderer
}