import * as THREE from 'three';

// 引入 rgbe 加载器
import { RGBELoader } from 'three/examples/jsm/loaders/RGBELoader';
import { EXRLoader } from 'three/examples/jsm/loaders/EXRLoader.js';

export function initScene(renderer) {
    // 初始化场景
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x595959);
    // 同于优化环境贴图效果
    // const pmremGenerator = new THREE.PMREMGenerator(renderer);
    // pmremGenerator.compileCubemapShader();
    // 使用 hdr 环境贴图
    const rgbeLoader = new RGBELoader()
    // rgbeLoader.loadAsync('textures/hdr/bg4.jpg').then((texture) => {
    //     texture.mapping = THREE.EquirectangularReflectionMapping;
    //     // const envMap = pmremGenerator.fromEquirectangular(texture).texture;

    //     // 将HDR环境贴图设置为场景的背景
    //     // scene.background = envMap;
    //     // scene.environment = envMap;
    //     scene.background = texture;
    //     scene.environment = texture;
    // })

    // 使用exr图片
    // const exrLoader = new EXRLoader();
    // exrLoader.load('textures/exr/bg1_2K.exr', (texture) => {
    //     texture.mapping = THREE.EquirectangularReflectionMapping;
    //     // 将 EXR 纹理应用于材质的环境贴图
    //     scene.background = texture;
    //     scene.environment = texture;
    // });



    // new THREE.TextureLoader().load('textures/hdr/bg5.jpg', (texture) => {
    //     texture.mapping = THREE.EquirectangularReflectionMapping;
    //     scene.background = texture;
    //     scene.environment = texture;
    // })

    // 场景天空盒
    // const textureCubeLoader = new THREE.CubeTextureLoader().setPath("./textures/");
    // const textureCube = textureCubeLoader.load([
    //     "1.jpg",
    //     "2.jpg",
    //     "3.jpg",
    //     "4.jpg",
    //     "5.jpg",
    //     "6.jpg",
    // ])
    // scene.background = textureCube;
    // scene.environment = textureCube;


    // const loader = new RGBELoader();
    // loader.load("./textures/hdr/Dosch-Space_0026_4k.hdr", function (texture) {
    //     texture.mapping = THREE.EquirectangularReflectionMapping;
    //     scene.background = texture;
    //     scene.environment = texture;
    // });

    // const textureCubeLoader = new THREE.CubeTextureLoader().setPath("./textures/cubeBg/1/");
    // const cubeMap = textureCubeLoader.load([
    //     'px.png', 'nx.png',
    //     'py.png', 'ny.png',
    //     'pz.png', 'nz.png',
    // ]);
    // scene.background = cubeMap;
    // scene.environment = cubeMap;


    // 使用 hdr 环境贴图
    // rgbeLoader.loadAsync('./textures/hdr/012.hdr').then((texture) => {
    //     texture.mapping = THREE.EquirectangularReflectionMapping;
    //     scene.background = texture;
    //     scene.environment = texture;
    // })

    /**视频贴图 */
    // const video = document.createElement('video');
    // video.src = './video/bg1.mp4'; // 替换为你的视频文件路径
    // video.load();
    // // 静音
    // video.muted = true;
    // // 循环播放
    // video.loop = true;
    // const videoTexture = new THREE.VideoTexture(video);
    // setTimeout(() => {
    //     video.play();
    // }, 300);
    // // 将视频作为场景的背景
    // scene.background = videoTexture


    return scene
}

