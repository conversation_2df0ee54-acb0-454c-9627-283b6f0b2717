uniform float uPixelRatio;
uniform float uSize;
uniform float uTime;
attribute float aScale;
varying vec2 vUv;
float easeOutCirc(float x){
  return sqrt(1.-pow(x-1.,2.));
}
void main(){
  vec4 modelPosition=modelMatrix*vec4(position,1.);
  modelPosition.y+=sin(uTime+modelPosition.x*100.)*aScale*.2;
  modelPosition.z+=cos(uTime+modelPosition.x*100.)*aScale*.2;
  modelPosition.x+=cos(uTime+modelPosition.x*100.)*aScale*.2;
  vec4 viewPosition=viewMatrix*modelPosition;
  vec4 projectionPostion=projectionMatrix*viewPosition;
  
  gl_Position=projectionPostion;
  gl_PointSize=uSize*aScale*uPixelRatio;
  gl_PointSize*=(1./-viewPosition.z);
  vUv=uv;
  
}