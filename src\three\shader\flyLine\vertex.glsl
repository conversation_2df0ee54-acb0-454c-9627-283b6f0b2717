attribute float aSize;

uniform float uTime;
uniform vec3 uColor;
uniform float uLength;

varying float vSize;

void main() {
    vec4 viewPosition = viewMatrix * modelMatrix * vec4(position, 1);
    gl_Position = projectionMatrix * viewPosition;
    vSize = (aSize - uTime);
    if(vSize < 0.0f) {
        vSize = vSize + uLength;
    }
    // 控制飞线粗细 和 长度
    vSize = (vSize - 500.0f) * 0.1f;

    gl_PointSize = -vSize / viewPosition.z;
}