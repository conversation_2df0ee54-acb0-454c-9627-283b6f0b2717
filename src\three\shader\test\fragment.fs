uniform vec3 uColor;
void main(){
   float distanceToCenter=distance(gl_PointCoord,vec2(.5));
   float strength=.05/distanceToCenter-.1;
   gl_FragColor=vec4(uColor,strength);
   
   // 计算点在正方形中心的距离
   // vec2 distanceToCenter=abs(gl_PointCoord-vec2(.5));
   // // 如果点在正方形内，强度生效；否则，强度为零
   // float strength=0.;
   // if(distanceToCenter.x<=.5&&distanceToCenter.y<=.5){
      
      //   float x=.05/distanceToCenter.x-.1;
      //   float y=.05/distanceToCenter.y-.1;
      //   strength=x;
   // }
   
   // 设置片段的颜色
   // gl_FragColor=vec4(uColor,strength);
}