uniform float uPixelRatio;
uniform float uSize;
uniform float uTime;
attribute float aScale;
varying vec2 vUv;
float easeOutCirc(float x){
    return sqrt(1.-pow(x-1.,2.));
}
void main(){
    vec4 modelPosition=modelMatrix*vec4(position,1.);
    gl_Position=projectionMatrix*viewMatrix*modelPosition;
    
    gl_PointSize=uSize*aScale*uPixelRatio;
    // gl_PointSize*=(1./-viewPosition.z);
    vUv=uv;
    
}