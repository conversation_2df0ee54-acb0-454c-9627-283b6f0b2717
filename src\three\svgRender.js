
import { SVGRenderer, SVGObject } from 'three/examples/jsm/renderers/SVGRenderer.js';
import * as THREE from 'three'
export function initSvgRenderer() {
    const svgRenderer = new SVGRenderer();
    svgRenderer.setSize(window.innerWidth, window.innerHeight);
    // svgRenderer.setQuality('low');
    return svgRenderer
}

function parseDom(arg) {
    var objE = document.createElement("p");
    objE.innerHTML = arg;
    return objE.childNodes;
}

