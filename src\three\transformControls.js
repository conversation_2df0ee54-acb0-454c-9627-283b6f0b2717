// 变换控制器
import { TransformControls } from 'three/examples/jsm/controls/TransformControls.js';
import { addEvent, removeEvent } from '@/utils/window'
// 变换控制器

export function initTransformControls(camera, renderer, controls) {
    let tControl = new TransformControls(camera, renderer.domElement);

    //  操作变换控制器时暂时禁用其他控制器
    function tControlDraggingChanged(event) {
        if (tControl.enabled) {
            controls.enabled = !event.value;
        } else {
            controls.enabled = !event.value;
        }
    }
    function tControlChange(callback) {
        callback(tControl);
    }

    function tControlAddEvent(callback = () => { }) {
        addEvent(tControl, 'change', tControlChange(callback));
        addEvent(tControl, 'dragging-changed', tControlDraggingChanged);
    }
    function tControlRemoveEvent(callback = () => { }) {
        removeEvent(tControl, 'change', tControlChange(callback));
        removeEvent(tControl, 'dragging-changed', tControlDraggingChanged);
    }
    return { tControl, tControlAddEvent, tControlRemoveEvent };
}



