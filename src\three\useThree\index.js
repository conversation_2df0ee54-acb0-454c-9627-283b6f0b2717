// 导入辅助坐标轴
import axesHelper from "../helper";
import { initThree, meshRemove } from '../initThree1'
// 获取物体世界坐标并修改数据弹窗位置
import { getMeshWorld, worldToScreen } from "../utils/coordinate";
import { cameraAni, cameraFlyToMesh, restrictCameraVerticalDirection, limitCameraVerticalAngle } from '../cameraControls';
// 烘培材质 自带光感
import { initMatcapThree, matcapTexture } from "../effect/matcap";
import { findMesh, setMeshScale, setMeshRotation, setPosition } from "../utils/utils";
// 执行动画
import { executeAnimation, executeMultipleAnimation } from '../utils/animat'
// 导入glb模型
import { fbxLoader, gltfLoader, textureLoader } from "../utils/loader";
import { addAmbientLight, addPointLight, addDirectionalLight } from "../utils/light";
// 双击模型事件
import { dbModelClick, modelClick, modelHover } from '../utils/event'

let fn = null;
export function useThree({ startCamera, endCamera, isCameraAni = true, isAxes = false, isControl = true, myCamera = null, isDepthBuffer = false, isComposer = true }) {
    if (fn) { fn = null; };
    const startCamera1 = startCamera ?? [1, 100, 8]
    const endCamera1 = endCamera ?? [10, 12, 3]
    fn = initThree(...(isCameraAni ? startCamera1 : endCamera1), { myCamera, isControl, isDepthBuffer, isComposer });
    // 初始化函数
    let { scene, THREE, renderer, outlinePass, composer, outlinePass1,
        cameraControls, camera, addOutlinePass, addOutlinePass1, mountRender, bloomAni, ani, stopRender, clearMesh, css3Renderer, gsap, TWEEN, GUI } = fn;

    initScene()
    // 模型初始相机飞跃动画
    if (isCameraAni) {
    
        cameraAni(cameraControls, [startCamera1, endCamera1])
    }

    // 添加辅助坐标轴
    isAxes && scene.add(axesHelper);
    function initScene() {
        // 配置 outlinePass 后期效果
        outlinePass.edgeStrength = 6.0;
        outlinePass.edgeGlow = 2.0;
        outlinePass.edgeThickness = 3.0;
        // 添加光源
        scene.add(addAmbientLight(0xffffff, 3));
        const light2 = addPointLight(0xffffff, 2);
        scene.add(light2);
        // 为相机添加光源
        camera.add(light2);
        camera.lookAt(0, 0, 0)
        camera.position.set(10, 101, 10)
    }

    // 切换选中物体
    function changeCurrentMesh(mesh, options = { isFlyMesh: true, isLabel: false, callback: () => { }, addT: 0, addB: 0, addL: 0, addR: 0 }) {
        const { isFlyMesh, isLabel, callback, addT, addB, addL, addR } = options;
        addOutlinePass([mesh])

        if (isFlyMesh) {
            cameraFlyToMesh(cameraControls, { mesh, paddingBottom: addB, paddingLeft: addL, paddingTop: addT, paddingRight: addR })
        }
        if (isLabel) {
            // 等待相机动画完成后 计算标注框位置
            let time = setTimeout(() => {
                const obj = changePosition(mesh)
                callback(obj)
                time = null;
            }, 1000)

        }
    }
    // 切换选中物体按名字查找
    function changeCurrentMeshByName(name, scene, options = { field: 'name', isFlyMesh: true, isLabel: false, callback: () => { }, addT: 0, addB: 0, addL: 0, addR: 0 }) {
        const { isFlyMesh, isLabel, callback, addT, addB, addL, addR } = options;
        const mesh = findMesh(scene, name)
        changeCurrentMesh(mesh, { isFlyMesh: isFlyMesh ?? true, isLabel, callback, addT, addB, addL, addR })
    }



    // 该变弹窗位置
    function changePosition(mesh) {
        const { x, y, z } = getMeshWorld(mesh);
        const { left, top } = worldToScreen(renderer, camera, x, y, z)
        return {
            left,
            top: top - 50,
            show: true
        }
    }

    // 模型初始化
    async function initModel(modelPath = './model/tfj.glb', point = [3, 0, 0], callback = () => { }, options = { position: [0, 0, 0], scale: 1, rotation: { x: 0, y: 0, z: 0 }, addModel: true }) {
        const { addModel, scale, rotation } = options;
        const gltf = await gltfLoader().loadAsync(modelPath)
        const model = gltf.scene;
        setPosition(model, ...point)
        setMeshRotation(model, rotation);
        setMeshScale(model, scale);
        callback(gltf)
        addModel && scene.add(gltf.scene)
        return gltf
    }

    // 加载多个模型
    // 加载多个模型
    async function initModelArr(pathArr = [], callback = () => { }, options = [{ position: [0, 0, 0], scale: 1, rotation: { x: 0, y: 0, z: 0 }, addModel: true }], loaderName = 'gltf',) {
        const retults = [];

        for (let i = 0; i < pathArr.length; i++) {
            const modelPath = pathArr[i];
            const result = loaderName == 'gltf' ? await gltfLoader().loadAsync(modelPath) : await fbxLoader().loadAsync(modelPath);
            const model = loaderName == 'gltf' ? result.scene : result;
            if (options.length >= i) {
                setPosition(model, ...options[i].position);
                setMeshRotation(model, options[i].rotation);
                setMeshScale(model, options[i].scale);
                options[i].addModel && scene.add(model);
            } else {
                scene.add(model);
            }
            retults.push(result);
        }

        callback(retults);
        return retults;
    }



    // 重置相机
    function restCamera(callback = () => { }, isAni = true) {
        cameraControls.reset(true)
        if (startCamera) {
            isAni && cameraAni(cameraControls, [startCamera, endCamera])
        }
        callback()
    }

    // 删除物体按名称查找
    function removeMeshByName(scene, name) {
        const mesh = findMesh(scene, name)
        meshRemove(mesh)
    }

    const animation = { executeAnimation, executeMultipleAnimation }
    const cameraFn = { restrictCameraVerticalDirection, cameraFlyToMesh, cameraAni, restCamera, limitCameraVerticalAngle }
    const textureFn = { initMatcapThree, matcapTexture } // 烘培贴图
    const meshAni = { findMesh, meshRemove, changeCurrentMeshByName, changeCurrentMesh, removeMeshByName, setMeshScale, setMeshRotation, setPosition }
    // 射线事件
    const event = { dbModelClick, modelClick, modelHover }
    const light = { addAmbientLight, addPointLight, addDirectionalLight }
    const effect = { addOutlinePass, addOutlinePass1, outlinePass, outlinePass1, composer }
    const loader = { textureLoader }
    const utils = { gsap, THREE, TWEEN, GUI }
    return {
        mountRender, initModel, initModelArr, ani, bloomAni, stopRender, clearMesh, renderer, cameraControls, scene, camera, css3Renderer,
        ...textureFn, ...cameraFn, ...animation, ...meshAni, ...event, ...light, ...effect,
        ...utils, ...loader
    }
}









