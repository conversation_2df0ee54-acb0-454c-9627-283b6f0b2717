import { AnimationMixer, LoopOnce, LoopRepeat } from 'three'
// 执行单个动画
export function executeAnimation(mesh, animation, options = { isLoop: true }) {
    const { isLoop } = options;
    const mixer = new AnimationMixer(mesh);
    const action = mixer.clipAction(animation)//把该物体需要的动画拿出来
    function startAnimation() {
        if (isLoop) {
            action.setLoop(LoopRepeat);//设置循环播放
        } else {
            action.setLoop(LoopOnce);//设置只播放一次
        }
        action.clampWhenFinished = true; // 在动画结束时保持最后一帧状态
        // action1.startAt(1); // 从最后一帧开始播放
        action.play();
    }

    function updateAnimation(delta) {
        if (mixer) {
            mixer.update(delta * 4.5);
        }
    }
    function stopAnimation() {
        action.stop();
    }
    return { startAnimation, updateAnimation, stopAnimation }
}

// 执行多个动画 参数：mesh 模型 animation 模型带的动画数组 options 配置项 {isLoop：是否循环播放动画}
export function executeMultipleAnimation(mesh, animation = [], options = { isLoop: true }) {
    const { isLoop } = options;
    const mixer = new AnimationMixer(mesh);
    const actions = []
    function startMultipleAnimation() {
        animation.forEach((v) => {
            const action = mixer.clipAction(v)//把该物体需要的动画拿出来
            if (isLoop) {
                action.setLoop(LoopRepeat);//设置循环播放
            } else {
                action.setLoop(LoopOnce);//设置只播放一次
            }
            action.clampWhenFinished = true; // 在动画结束时保持最后一帧状态
            // action1.startAt(1); // 从最后一帧开始播放
            action.play();

            actions.push(action)
        })

    }

    function updateMultipleAnimation(delta) {
        if (mixer) {
            mixer.update(delta);
        }
    }
    function stopMultipleAnimation() {
        if (!actions.length) return;
        actions.forEach((action) => {
            action.stop()
        })
    }
    return { startMultipleAnimation, updateMultipleAnimation, stopMultipleAnimation }
}


