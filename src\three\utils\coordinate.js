import * as THREE from 'three';
// 屏幕转世界坐标系
export function screenToWorld(camera, x, y) {
    const worldPos = new THREE.Vector3();
    worldPos.set((x / window.innerWidth) * 2 - 1, -(y / window.innerHeight) * 2 + 1, 0.5);
    // 使用投影矩阵和逆矩阵将屏幕坐标转换为Three.js中的世界坐标
    worldPos.unproject(camera);
    return worldPos;
}
// 世界坐标系转屏幕
export function worldToScreen(renderer, camera, x, y, z) {
    const screenPos = new THREE.Vector3();
    screenPos.set(x, y, z);
    screenPos.project(camera);
    const { x: x1, y: y1 } = screenPos
    // const { width, height } = renderer.domElement
    const { innerWidth: width, innerHeight: height } = window

    const left = (x1 + 1) * width / 2
    const top = (-y1 + 1) * height / 2
    return {
        left,
        top
    };
}

// 获取物体的世界坐标系
export function getMeshWorld(mesh) {
    const worldPosition = new THREE.Vector3();
    mesh.getWorldPosition(worldPosition);
    return worldPosition;
}