
import { meshPickup } from '@/three/raycaster'
// 双击模型事件
export function dbModelClick(interactionMeshArr, camera, callBack = () => { }, el) {
    window.addEventListener('dblclick', fn, false)
    function dblRemove() {
        window.removeEventListener('dblclick', fn, false)
    }
    function fn(event) {
        const { currentMesh, allMesh } = meshPickup(event, interactionMeshArr, camera, el)
        if (currentMesh) {
            callBack(currentMesh, event)
        }
    }
    return { dblRemove }
}
// 单击模型事件
export function modelClick(interactionMeshArr, camera, callBack = () => { }, el) {
    // 创建八叉树
    window.addEventListener('mousedown', fn)
    function clickRemove() {
        window.removeEventListener('mousedown', fn)
    }
    function fn(event) {
        const { currentMesh, allMesh } = meshPickup(event, interactionMeshArr, camera, el)
        if (event.button === 0) {
            if (currentMesh) {
                callBack(currentMesh, event)
            }
        }
    }
    return { clickRemove }
}

// 悬浮模型事件
export function modelHover(interactionMeshArr, camera, callBack = () => { }, el) {
    window.addEventListener('mousemove', fn, false)
    function hoverRemove() {
        window.removeEventListener('mousemove', fn, false)
    }
    function fn(event) {
        const { currentMesh, allMesh } = meshPickup(event, interactionMeshArr, camera, el)
        callBack(currentMesh, event)
    }
    return { hoverRemove }
}

