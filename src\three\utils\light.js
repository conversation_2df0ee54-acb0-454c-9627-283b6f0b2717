import * as THREE from 'three'
import { setPosition } from './meshUtils';

// 添加环境光源
export function addAmbientLight(color = 0xC0C0C0, intensity = 0.5, position = [0, 0, 0]) {
    const light = new THREE.AmbientLight(color, intensity)
    setPosition(light, ...position);
    return light
}
// 添加点光源
export function addPointLight(color = 0xffffff, intensity = 0.5, position = [0, 0, 0]) {
    const light = new THREE.PointLight(color, intensity);
    setPosition(light, ...position);
    return light
}
// 添加平行光
export function addDirectionalLight(color = 0xffffff, intensity = 0.8, position = [0, 0, 0]) {
    const light = new THREE.DirectionalLight(color, intensity);
    setPosition(light, ...position);
    return light
}
// 添加聚光灯
export function addSpotLight(color = 0xffffff, intensity = 0.8, position = [0, 0, 0]) {
    const light = new THREE.SpotLight(color, intensity);
    setPosition(light, ...position);
    return light
}
// 添加半球灯
export function addHemisphereLight(skyColor = 0x00ffff, groundColor = 0xffffff, intensity = 1) {
    const light = new THREE.HemisphereLight(skyColor, groundColor, intensity);
    return light
}

