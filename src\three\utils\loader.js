// gltf模型加载器
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import { DRACOLoader } from "three/examples/jsm/loaders/DRACOLoader";
//DRACOLoader 解压缩技术
const dLoader = new DRACOLoader();
dLoader.setDecoderPath('https://www.gstatic.com/draco/versioned/decoders/1.5.7/');
dLoader.setDecoderConfig({ type: 'js' })


export function gltfLoader(options = { useDRACO: false }) {
    const { useDRACO } = options;
    const loader = new GLTFLoader()
    useDRACO && loader.setDRACOLoader(dLoader);
    return loader
}

import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js'
export function fbxLoader(options = { useDRACO: false }) {
    const { useDRACO } = options;
    const loader = new FBXLoader()
    useDRACO && loader.setDRACOLoader(dLoader);
    return loader
}
import { TextureLoader } from 'three'
export function textureLoader() {
    const loader = new TextureLoader()
    return loader
}
import { RGBELoader } from 'three/examples/jsm/loaders/RGBELoader';
export function rgbeLoader() {
    const loader = new RGBELoader()
    return loader
}

import { FontLoader } from 'three/examples/jsm/loaders/FontLoader.js';

export function fontLoader() {
    const loader = new FontLoader()
    return loader
}