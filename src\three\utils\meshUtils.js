import * as THREE from 'three'
// 根据名称查找指定物体函数
export function findMesh(meshScene, meshInfo, filed = 'name') {
    let mesh;
    if (Array.isArray(meshInfo)) {
        mesh = []
        for (let i = 0; i < meshInfo.length; i++) {
            const obj = meshScene.children.find((v) => {
                return v[filed] === meshInfo[i]
            })
            mesh.push(obj)
        }
    } else {
        mesh = meshScene.children.find((v) => {
            return v[filed] === meshInfo
        })
    }
    return mesh
}

//

// material拷贝
export function deepCloneMaterial(material) {
    const newMaterial = material.clone();
    newMaterial.uniforms = { ...material.uniforms };
    return newMaterial;
}
// geometry拷贝
export function deepCloneGeometry(geometry) {
    const newGeometry = geometry.clone();
    newGeometry.attributes = { ...geometry.attributes };
    return newGeometry;
}

// 选中根据名称选中物体的方法
export function selectMesh(gltfScene, name, addOutlinePass, callback = () => { }) {
    let mesh = findMesh(gltfScene, name)
    addOutlinePass([mesh])
    callback()
}

// 模型初始化
export async function initGlbModel(scene, modelPath = './model/tfj.glb', point = [3, 0, 0], callback = () => { }) {
    const gltf = await gltfLoader().loadAsync(modelPath)
    setPosition(...point, gltf.scene)
    callback(gltf)
    scene.add(gltf.scene)
    return gltf
}

/**
 * 模型缩放
 * @param {THREE.Mesh | THREE.Group} mesh 
 * @param {Number | {x:Number,y:Number,z:Number}} scale 
 */
export function setMeshScale(mesh, scale = 1) {
    if (scale instanceof Object) {
        const { x, y, z } = scale;
        mesh.scale.set(x, y, z)
    } else {
        mesh.scale.set(scale, scale, scale)
    }
}
// 模型旋转角度 Math.PI
export function setMeshRotation(mesh, options = { x: 0, y: 0, z: 0 }) {
    const { x, y, z } = options;
    x && (mesh.rotation.x = x);
    y && (mesh.rotation.y = y);
    z && (mesh.rotation.z = z);
}

// 设置三维对象坐标
export function setPosition(mesh, x, y, z) {
    if (typeof mesh == 'number') {
        z.position.set(mesh, x, y)
    } else {
        mesh.position.set(x, y, z)
    }
}
/**
 * 修改模型的默认朝向
 * @param {THREE.Mesh} mesh 
 * @param {THREE.Vector3} targetPosition 
 * @param {THREE.Vector3} directionVector 
 */
export function changeMeshOrientation(mesh, targetPosition, directionVector) {
    // 计算指向目标点的方向向量
    const direction = new THREE.Vector3();
    direction.subVectors(targetPosition, mesh.position).normalize();

    // 计算从默认方向向量 (0, 1, 0) 到目标方向向量的四元数
    const quaternion = new THREE.Quaternion();
    quaternion.setFromUnitVectors(directionVector, direction);
    // 应用四元数旋转到圆锥体的几何体
    mesh.applyQuaternion(quaternion);

    // 标记顶点和法线已更新
    mesh.verticesNeedUpdate = true;
    mesh.normalsNeedUpdate = true;
}