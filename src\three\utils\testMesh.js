import * as THREE from 'three'
// 在该点位置添加一个小球
export function testMesh(x, y, z, scale = 1) {
    const geometry = new THREE.SphereGeometry(0.1, 32, 32);
    const material = new THREE.MeshBasicMaterial({ color: 0xffff00 });
    const sphere = new THREE.Mesh(geometry, material);
    sphere.scale.multiplyScalar(scale)
    sphere.position.set(x, y, z)
    return sphere
}
export function randomMesh({ count = 5, randomRange = { x: 1, y: 1, z: 1 }, color = 0xffff00, scale = 1 }) {
    const group = new THREE.Group()
    for (var i = 0; i < count; i++) {
        const sphere = new THREE.Mesh(new THREE.SphereGeometry(0.1, 32, 32), new THREE.MeshBasicMaterial({ color }))
        sphere.scale.multiplyScalar(scale)
        const { x, y, z } = randomRange;
        const [x1, y1, z1] = [Math.random() * x, Math.random() * y, Math.random() * z]
        sphere.position.set(x1, y1, z1)
        group.add(sphere)
    }
    return group
}