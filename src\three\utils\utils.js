import * as THREE from 'three';
export * from './meshUtils'
// 在两个三维坐标点之间生成指定的插值点, 返回一个数组
export function interpolateBetweenTwoPoints(linePoints, count) {
    const threeLinePoints = []
    linePoints.forEach((v) => {
        threeLinePoints.push(new THREE.Vector3(...v))
    })
    // 创建Catmull-Rom曲线
    const curve = new THREE.CatmullRomCurve3(threeLinePoints);
    // 生成插值点
    const points = curve.getPoints(count); // 生成所选点位之间的插值点
    return points;
}

// three随机颜色方法
export function randomColor() {

    return new THREE.Color(Math.random(), Math.random(), Math.random())

}
// 16进制随机颜色方法
export function getRandomColor() {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
        color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
}



// 将 16 进制颜色转换为 RGB 数组
export function hexToRgb(hexColor) {
    const hex = hexColor.replace(/^#/, '');
    const r = parseInt(hex.substring(0, 2), 16) / 255;
    const g = parseInt(hex.substring(2, 4), 16) / 255;
    const b = parseInt(hex.substring(4, 6), 16) / 255;
    return [r, g, b];
}
// 随机取出数组中的一项
export function getArrayRandomItem(arr) {
    var randomIndex = Math.floor(Math.random() * arr.length);
    return arr[randomIndex];
}


// 判断数据类型
export function getDataType(value) {
    const typeString = Object.prototype.toString.call(value);
    return typeString.substring(8, typeString.length - 1); // 格式 "Array"
}

