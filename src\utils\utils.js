// 判断数据类型
export function getType(val) {
    let str = Object.prototype.toString.call(val);

    return firstLetterToLowerCase(str.split(" ")[1].slice(0, str.split(" ")[1].length - 1))
    function firstLetterToLowerCase(str) {
        return str.charAt(0).toLowerCase() + str.slice(1); // number string array 
    }
}

// 从数组中堆积获取一项
export function getRandomFromArray(array) {
    const randomIndex = Math.floor(Math.random() * array.length);
    return array[randomIndex];
}
// 对象深拷贝1
export function deepClone(obj) {
    return JSON.parse(JSON.stringify(obj));
}
// 对象深拷贝2
export function deepClone2(obj, hash = new WeakMap()) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }

    if (hash.has(obj)) {
        return hash.get(obj);
    }

    let clone = Array.isArray(obj) ? [] : {};

    hash.set(obj, clone);

    for (let key in obj) {
        // eslint-disable-next-line no-prototype-builtins
        if (obj.hasOwnProperty(key)) {
            clone[key] = deepClone2(obj[key], hash);
        }
    }

    return clone;
}

// 利用闭包特性 让方法只执行一次
export const onceFn = (fn) => {
    let done = false;
    return function (flag) { // 可以手动设置执行 false
        if (typeof flag != 'undefined')
            done = flag;
        if (!done) {
            fn.apply(this);
        } else {
            console.log("该函数已经执行");
        }
        done = true;
    }
}
// 示例
// function fn() {
//     console.log('执行');
// }
// const handleFn = once(fn)
// handleFn();
// handleFn();
// handleFn(false);
// handleFn();


// 节流函数
export function throttleFn(callback = () => { }, delay, resultCallback = () => { }) {
    let time = null;
    return function (...args) {
        if (time) return;
        time = setTimeout(() => {
            const result = callback(...args);
            resultCallback(result);
            clearTimeout(time);
            time = null;
        }, delay)
    }
}

// 提取对象中的某些项转为数组
export function objectPropertyToArray(obj = {}, keys = []) {

    let entries = Object.entries(obj);
    let result = entries.filter(([key, value]) => keys.includes(key)).map(([key, value]) => value);

    return result;
}

// 生成随机uuid
export function getUuid() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        let r = Math.random() * 16 | 0,
            v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

