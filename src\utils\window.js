// 添加事件
export var addEvent = function (obj, type, fn) {
    if (obj.addEventListener)
        obj.addEventListener(type, fn, false);
    else if (obj.attachEvent) {
        obj["e" + type + fn] = fn;
        obj.attachEvent("on" + type, function () {
            obj["e" + type + fn].call(obj, window.event);
        });
    }
};

// 移除事件
export var removeEvent = function (obj, type, fn) {
    if (obj.removeEventListener)
        obj.removeEventListener(type, fn, false);
    else if (obj.detachEvent) {
        obj.detachEvent("on" + type, obj["e" + type + fn]);
        obj["e" + type + fn] = null;
    }
};


// 阻止事件(包括冒泡和默认行为)
export var stopEvent = function (e) {
    e = e || window.event;
    if (e.preventDefault) {
        e.preventDefault();
        e.stopPropagation();
    } else {
        e.returnValue = false;
        e.cancelBubble = true;
    }
}

// 仅阻止事件冒泡
export var stopPropagation = function (e) {
    e = e || window.event;
    if (!+"\v1") {
        e.cancelBubble = true;
    } else {
        e.stopPropagation();
    }
}


// 仅阻止浏览器默认行为

export var preventDefault = function (e) {
    e = e || window.event;
    if (e.preventDefault) {
        e.preventDefault();
    } else {
        e.returnValue = false;
    }
}

// 取得事件源对象

export var getEventTarget = function (e) {
    e = e || window.event;
    var target = event.srcElement ? event.srcElement : event.target;
    return target;
}