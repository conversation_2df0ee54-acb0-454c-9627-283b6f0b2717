// .page {
//     background-color: rgba(255, 255, 255, 1);
//     position: relative;
//     width: 100vw;
//     height: 100vh;
//     overflow: hidden;
// }

.flex_center {
  display: flex;
  justify-content: center;
  align-items: center;
}

// .section_1 {
//     width: 100vw;
//     height: 100vh;
// }

// https://lanhu.oss-cn-beijing.aliyuncs.com/pss2hrw9pujtgp63h0bvslgmafuo4a464rif6513621-08dd-44ee-8dcc-76a97925ed69
.fan_state_error {
  width: 220px;
  height: 30px;
  font-size: 18px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #e83535;
  line-height: 30px;
}

.box1 {
  position: fixed;
  width: 488px;
  height: 463px;
  background-image: url("../assets/box_bg.gif");
  background-size: 488px 463px;

  .fan_time {
    width: 220px;
    height: 36px;
    background: rgba(30, 160, 193, 0);
    border: 1px solid #1ea0c1;
    box-shadow: inset 0 0 15px #1ea0c1;
    text-align: center;
    line-height: 36px;

    span {
      width: 182px;
      height: 36px;
      font-size: 20px;
      font-family: DIN;
      font-weight: 500;
      color: #ffffff;
    }
  }

  .box1_value {
    width: auto;
    height: 17px;
    font-size: 22px;
    font-family: DIN;
    font-weight: 500;
    color: #ffffff;
    line-height: 17px;
    text-align: center;
  }

  .box1_name {
    text-align: center;
    width: auto;
    height: 16px;
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #76f7ff;
    line-height: 30px;
  }
}
.boxs {
  @extend .box1;
  width: 488px;
  height: 400px;
  background-image: url("./img/box1_bg.png");
  background-size: 488px 400px;
}
.box2 {
  @extend .box1;

  .box2_value {
    text-align: center;
    width: 150px;
    height: 18px;
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    line-height: 30px;
  }
}

.boxzj {
  @extend .boxs;

  width: 230px;
  height: 210px;
  background-image: url("./img/box1_bg.png");
  background-size: 230px 210px;
  position: fixed;
}
.box3 {
  @extend .box1;
  width: 488px;
  height: 236px;
  background-image: url("./img/control_panel.png");
  background-size: 488px 236px;

  .box3_btn_bg_common {
    width: 96px;
    height: 110px;
    background-size: 96px 110px;

    span {
      display: block;
      width: 96px;
      text-align: center;
      height: 30px;
      font-size: 14px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #3ff1a4;
      line-height: 30px;
    }
  }

  .box3_btn_bg1 {
    @extend .box3_btn_bg_common;
    background-image: url("./img/control_panel_btn_bg1.png");
  }

  .box3_btn_bg2 {
    @extend .box3_btn_bg_common;
    background-image: url("./img/control_panel_btn_bg2.png");

    span {
      color: #f1c53f;
    }
  }

  .box3_btn_bg3 {
    @extend .box3_btn_bg_common;
    background-image: url("./img/control_panel_btn_bg3.png");

    span {
      color: #f13f5c;
    }
  }

  .box3_btn2_bg_common {
    width: 116px;
    height: 38px;
    background-size: 116px 38px;

    span {
      width: 56px;
      height: 14px;
      font-size: 14px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #f13f5c;
      line-height: 30px;
    }
  }

  .box3_btn2_bg1 {
    @extend .box3_btn2_bg_common;
    background-image: url("./img/control_panel_btn_bg4.png");

    span {
      color: #ffffff;
    }
  }

  .box3_btn2_bg2 {
    @extend .box3_btn2_bg_common;
    background-image: url("./img/control_panel_btn_bg5.png");
  }
}

.box4 {
  @extend .box3;

  .box4_camera {
    width: 85px;

    span {
      width: 85px;
      height: 32px;
      font-size: 14px;
      text-align: justify;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
      line-height: 18px;
    }
  }
}

//故障诊断
.box5 {
  @extend .box1;
  width: 488px;
  height: 439px;
  background-image: url("./img/box5_bg.png");
  background-size: 488px 439px;

  .box5_tab {
    text-align: center;
    width: 96px;
    height: 16px;
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #ffffff;
    line-height: 36px;
  }

  .box5_tab_active {
    width: 65px;
    height: 3px;
    background: #41f5f1;
    border-radius: 2px;
    margin: 0 auto;
  }

  .box5_data_name {
    width:0 auto;
    height: 36px;
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #61d5de;
    line-height: 36px;
  }

  .box5_data_value_bg {
    width: 126px;
    height: 36px;
    background-image: url("./img/box5_data_bg.png");
    background-size: 126px 36px;
    // text-align: center;
  }

  .box5_data_value {
    margin-left: 20px;
    width: 96px;
    height: 36px;
    font-size: 20px;
    font-family: DIN;
    font-weight: 500;
    color: #feffff;
    line-height: 36px;
  }
}

//预警警报
.box6 {
  @extend .box1;
  width: 488px;
  height: 576px;
  background-image: url("./img/box6_bg.png");
  background-size: 488px 576px;
}

//实时监控
.box7 {
  @extend .box1;
  width: 488px;
  height: 940px;
  background-image: url("./img/box7_bg.png");
  background-size: 488px 940px;
}

//智能测风-压差传感器
.boxSense {
  @extend .box1;
  width: 508px;
  height: 940px;
  background-image: url("./img/boxSen_bg1.png");
  background-size: 508px 940px;
}

.boxUltrasonic {
  @extend .box1;
  width: 1354px;
  height: 368px;
  background-image: url("./img/boxSen_bg2.png");
  background-size: 1654px 368px;
}

// 多参数传感器
.boxSensor {
    @extend .box1;
    width: 1354px;
    height: 557px;
    background-image: url("./img/boxSen_bg3.png");
    background-size: 1654px 557px;

}

//智能监控左边
.boxMonitor_left {
  @extend .box1;
  width: 170px;
  height: 939px;
  background-image: url('../../../smartMonitoring/assest/img/minotor_aside.png');
  background-size: 170px 939px;
 
 
}

// 智能监控右边
.boxMonitor {
  @extend .box1;
  width: 830px;
  height: 454px;
  position: relative;
  background-image: url('./img/minotorBoxs.png');
  background-size: 830px 454px;

}

// 局部-控制面板
.boxControl {
  @extend .box1;
  width: 488px;
  height: 310px;
  background-image: url('./img/control_panel.png');
  background-size: 488px 310px;
}

// 局部-故障诊断

.boxError {
  @extend .box1;
  width: 488px;
  height: 367px;
  background-image: url('./img/group_boxError.png');
  background-size: 488px 367px;

  .box5_tab {
    text-align: center;
    width: 96px;
    height: 16px;
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #ffffff;
    line-height: 36px;
  }

  .box5_tab_active {
    width: 65px;
    height: 3px;
    background: #41f5f1;
    border-radius: 2px;
    margin: 0 auto;
  }

  .box5_data_name {
    width: 100px;
    height: 36px;
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #61d5de;
    line-height: 36px;
  }

  .box5_data_value_bg {
    width: 126px;
    height: 36px;
    background-image: url("./img/box5_data_bg.png");
    background-size: 126px 36px;
    // text-align: center;
  }

  .box5_data_value {
    margin-left: 20px;
    width: 96px;
    height: 36px;
    font-size: 20px;
    font-family: DIN;
    font-weight: 500;
    color: #feffff;
    line-height: 36px;
  }
}

//智能风门

.boxDoor{
  @extend .box1;
  background-image: url(../assets/img/basic_background.png);
  width: 488px;
  height: 387px;
  background-size: 488px 387px;

}

 //环境监测
 .boxDoor_box2 {
  @extend .box1;
  background-image: url(../assets/img/basic_background.png);
  width: 488px;
  height: 331px;
  background-size: 488px 331px;

 }

 //故障诊断
 .boxDoor_box3 {
  @extend .boxDoor_box2;
  background-image: url(../assets/img/door_box3_background.png);
  height: 194px;
  background-size: 488px 194px;

  .box5_tab {
    text-align: center;
    width: 96px;
    height: 16px;
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #ffffff;
    line-height: 36px;
  }

  .box5_tab_active {
    width: 65px;
    height: 3px;
    background: #41f5f1;
    border-radius: 2px;
    margin: 0 auto;
  }

  .box5_data_name {
    width: 100px;
    height: 36px;
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #61d5de;
    line-height: 36px;
  }

  .box5_data_value_bg {
    width: 166px;
    height: 43px;
    background-image: url("../../airDoor/assest/img/airDoor_backg.png");
    background-size: 166px 36px;
    // text-align: center;
  }

  .box5_data_value {
    margin-left: 20px;
    width: 96px;
    height: 36px;
    font-size: 20px;
    font-family: DIN;
    font-weight: 500;
    color: #feffff;
    line-height: 36px;
  }
  
 }

 //控制面板
 .boxDoor_box4 {
  @extend .boxDoor;
  width: 488px;
  height: 354px;
  background-size: 488px 354px;

 }


 //视频监控

 .boxDoor_box5 {
  @extend .boxDoor;
  width: 488px;
  height: 573px;
  background-image: url(../assets/img/video_background_door.png);
  background-size: 488px 573px;

 }

//  智能防火-防火在线
.boxOnline_box1 {

  @extend .box1;
  width: 488px;
  height: 238px;
  background-image: url(../assets/img/fireOnline_box1.png);
  background-size: 488px 238px;

}

.boxOnline_box2 {
  @extend .box1;
  width: 488px;
  height: 259px;
  background-image: url(../assets/img/fireOnline_box2.png);
  background-size: 488px 259px;

}

.boxOnline_box3 {
  @extend .box1;
  width: 488px;
  height: 415px;
  background-image: url(../assets/img/fireOnline_box3.png);
  background-size: 488px 415px;
}

.boxOnline_box4 {
  @extend .box1;
  width: 488px;
  height: 941px;
  background-image: url(../assets/img/fireOnline_box4.png);
  background-size: 488px 941px;
}

.boxDust_box {
  @extend .box1;
  width: 599px;
  height: 323px;
  background-image: url(../assets/img/work_basic.png);
  background-size: 599px 323px;
 
}

.boxDrainage{
  @extend .box1;
  width: 488px;
  height: 359px;
  background-image: url(../assets/img/drainage_Box.png);
  background-size: 488px 359px;
  
}

.boxDrainage_box{
  @extend .box1;
  width: 1090px;
  height: 300px;
  background-image: url(../../../smartExtraction/assest/img/drainage_ox2.png);
  background-size: 1090px 300px;
  
}

