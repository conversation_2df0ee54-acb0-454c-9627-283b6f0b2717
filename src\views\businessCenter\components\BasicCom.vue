<template>
  <div class="basic_com">
    <div class="header">{{ title }}</div>
    <div class="tab">
      <div
        class="tab_item"
        :class="{ active: current === 1 }"
        @click="changeCurrent(1)"
      >
        风机1
      </div>
      <div
        class="tab_item"
        :class="{ active: current === 2 }"
        style="margin-right: 10px"
        @click="changeCurrent(2)"
      >
        风机2
      </div>
    </div>

    <slot></slot>
  </div>
</template>
<script setup>
import { ref } from "vue";
const emits = defineEmits(["changeCurrent"]);
const current = ref(1);
const props = defineProps({
  title: String,
  com1: Object,
  com2: Object,
});
const changeCurrent = (val) => {
  // console.log(emits);
  emits("changeCurrent", +val);
  current.value = +val;
  // emits.changeCurrent(current)
};
</script>


<style scoped lang="scss">
.basic_com {
  width: 100%;
  height: 100%;
  background: url("../assets/img/basic_background.png") left top;
  background-size: 100% 100%;
}
.header {
  margin-left: 20px;
  margin-top: 6px;
}
.tab {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  margin-top: 17px;
}
.tab_item {
  margin-top: 18px;
  width: 54px;
  height: 24px;
  line-height: 24px;
  font-size: 8px;
  text-align: center;
  border: 1px solid #0d3d58;
  background-color: #0f364e;
  color: #9dbdca;
}
.active {
  background-color: #fff;
  background: linear-gradient(to top, #735a4f, #735a4f00);
  color: #98694f;
}
</style>