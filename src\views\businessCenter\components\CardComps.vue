<template>
  <div v-for="(card, index) in cards" :key="card.id">
    <!-- <div style="fontSize:50px"> {{card.x}}</div> -->
    <component
      v-if="card.show"
      class="card"
      :class="{ selected_com: card.selected }"
      :is="card.type"
      :width="card.width"
      :height="card.height"
      :x="card.x"
      :y="card.y"
      :rem="true"
      :com1="card.com1"
      :com2="card.com2"
      :status="card.status"
      :cameras="card.cameras"
      @mousedown="(e) => handleMousedown(e, index, card)"
    >
      <!-- <div> -->
      <!-- <div class="hover-element" @dblclick="handleDelete">
      悬浮元素
    </div>
    <p v-if="showText">{{ text }}</p>
  </div> -->

      <div
        v-if="card.selected"
        class="delete-text"
        @click="() => deleteCom(card.id)"
      >
        删除
      </div>
    </component>
  </div>
</template>

<script setup>
import { watchEffect, ref, toRaw, watch } from "vue";
import useVModel from "@/hooks/useVModel";
const props = defineProps({
  components: Array,
});
//  定义:       向父组件传递 mousedown       更新父组件传过来的 components
const emits = defineEmits(["mousedown", "update:components"]);

const cards = ref(toRaw(props.components)); // 接收附件传递的components

console.log("props.components", cards.value);

// 更新 数据
watch(cards, (newVal) => emits("update:components", newVal), { deep: true });

// 暂时无用
const backupComps = [...props.components].map((item) => ({
  ...item,
  startX: 0,
  startY: 0,
}));

// 暂时无用
let startX = 0,
  startY = 0;
function handleMousedown(e, index, card) {
  emits("mousedown", e, index, card.id); // 触发 mousedown ，向父组件 mousedown 传递参数
  // 先将所有选择状态改为 false
  cards.value = cards.value.map((c) => {
    c.selected = false; // 代替父组件修改数据， 显示删除按钮
    return c;
  });
  cards.value[index].selected = true; // 再将选中的组件状态改为  true

  // cards.value[index].selected = true;

  //   backupComps[index].startX = e.clientX;
  //   backupComps[index].startY = e.clientY;
  //   startX = backupComps[index].x;
  //   startY = backupComps[index].y;
  //   console.log("cards", cards.value);
}

// 暂时无用
function handleMouseMove(e, index) {
  const { clientX, clientY } = e;
  const backup = backupComps[index];
  const offsetX = clientX - backup.startX;
  const offsetY = clientY - backup.startY;
  cards.value[index].x = startX + offsetX;
  cards.value[index].y = startY + offsetY;
}

function deleteCom(id) {
  // 删除组件
  cards.value = cards.value.filter((row) => row.id !== id);
  console.log(" [...toRaw(cards.value)]", [...toRaw(cards.value)]);

  emits("update:components", [...toRaw(cards.value)]);
}
</script>