<template>
   <div class="title">1#风机</div>
          <div class="floor-top">
            <div class="floor-left">
              <div>基础</div>
            </div>
            <div class="floor-right">
              <div>
                <el-input
                  value="1#风机"
                  style="width: 100px; height: 30px"
                  disabled
                />
                <div style="color: #807a6f">标题</div>
              </div>
              <div>
                <el-date-picker
                  v-if="selectCom.com1"
                  v-model="selectCom.com1.time"
                  value-format="YYYY-MM-DD"
                  type="date"
                  placeholder="选择时间"
                  style="width: 100px; height: 30px"
                />
                <div style="color: #807a6f">时间</div>
              </div>
              <div>
                <el-select
                  v-if="selectCom.com1"
                  v-model="selectCom.com1.status"
                  placeholder="选择状态"
                  style="width: 100px; height: 30px"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <div style="color: #807a6f">状态</div>
              </div>
              <div>
                <!-- <el-input value="1#风机" style="width: 120px; height: 30px;"/> -->
                <el-button
                  style="width: 100px; height: 30px"
                  @click="chooseStats(1)"
                >
                  选择属性
                </el-button>
                <div style="color: #807a6f">属性</div>
              </div>
            </div>
          </div>
          <div class="floor-bottom">
            <div class="floor-left">
              <div>属性</div>
            </div>
            <div class="floor-right" v-if="selectCom.com1">
              <div v-for="item in selectCom.com1.stats" :key="item.id">
                <el-button
                  style="margin: 4px; width: 90%"
                  type="warning"
                  @click="openUpdateStats(1, item.id)"
                >
                  修改点位
                </el-button>
                <div style="color: #807a6f">{{ item.text }}</div>
              </div>
            </div>
          </div>
</template>

<script setup>

</script>
<script>
export default {
  name: "CustomSize",
};
</script>


<style scoped>
.floor-right{
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}
</style>