<template>
  <div
    class="donglijiance"
    :style="{
      position: 'absolute',
      width: width + (rem ? 'rem' : 'px'),
      height: height + (rem ? 'rem' : 'px'),
      left: x + (rem ? 'rem' : 'px'),
      top: y + (rem ? 'rem' : 'px'),
    }"
  >
    <basic-com
      :com1="com1"
      :com2="com2"
      @changeCurrent="handleChangeCurrent"
      title="动力监测"
    >
      <div>
        <div class="top">
          <div class="left">
            <img src="../assets/img/fan.png" alt="" class="fan" />
          </div>
          <div class="right">
            <div class="status" v-if="typeof com1.status === 'number'">
              {{ current.value
              }}
              {{ ["1#风机正常运行", "1#风机停止运行"][com1.status] }}
            </div>

            <div class="status" v-else-if="typeof com2.status === 'number'">
              {{ current.value
              }}{{ ["2#风机正常运行", "2#风机停止运行"][com2.status] }}
            </div>
            <!-- <div class="time">
              {{
                current === 1
                  ? com1?.time || "23天5时28分46秒"
                  : com2?.time || "23天5时28分46秒"
              }}
            </div> -->
          </div>
        </div>
        <div class="content" v-if="current === 1">
          <template v-if="com1?.stats">
            <div class="item" v-for="item in com1.stats" :key="item.id">
              <div class="icon">
                <img
                  src="../assets/img/air_quantity.png"
                  alt=""
                  style="width: 20px; height: 20px; margin-left: 11px"
                />
              </div>
              <div class="data">
                <div style="width: 80px">
                  {{ firstDevices.find((d) => d.ID === item)?.Value || 0.0 }}
                </div>
                <div style="color: #378395; width: 80px">
                  {{ firstDevices.find((d) => d.ID === item)?.Label }}
                </div>
              </div>
            </div>
          </template>
        </div>
        <div class="content" v-else>
          <template v-if="com2.stats">
            <div class="item" v-for="item in com2.stats" :key="item.id">
              <div class="icon">
                <img
                  src="../assets/img/air_quantity.png"
                  alt=""
                  style="width: 20px; height: 20px; margin-left: 11px"
                />
              </div>
              <div class="data">
                <div style="width: 80px">
                  {{ secondDevices.find((d) => d.ID === item)?.Value || 0.0 }}
                </div>
                <div style="color: #378395; width: 80px">
                  {{ secondDevices.find((d) => d.ID === item)?.Label }}
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </basic-com>
    <slot></slot>
  </div>
</template>

<script>
import { ref, computed } from "vue";
import BasicCom from "../components/BasicCom.vue";
import { configStore } from "@/store/config";
import { storeToRefs } from "pinia";
export default {
  name: "DongLiJIanCe",
  components: { BasicCom },
  props: {
    x: Number,
    y: Number,
    width: Number,
    height: Number,
    com1: Object,
    com2: Object,
    rem: Boolean,
  },
  setup(props) {

    

    const current = ref(1);
    function handleChangeCurrent(c) {
      current.value = c;

      console.log(c,'动力监测')
    }
    
    const store = configStore();
    // console.log(store,'获取存储信息')
    const { devicesMap = [] } = storeToRefs(store);
    // 风机1  风机2 数组数据
    const [firstDevices, secondDevices] = devicesMap.value;
  
    console.log('firstDevices',firstDevices);
    
   
    return {
      current,
      handleChangeCurrent,
      firstDevices,
      secondDevices,
    };
  },
};
</script>



<style lang="scss" scoped>
.top {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  .left {
    position: relative;
    width: 90px;
    height: 70px;
    background: url("../assets/img/fan_bg.png") left top;
    background-size: 100% 100%;
    .fan {
      position: absolute;
      width: 30px;
      left: 30px;
      top: 8px;
    }
  }
  .right {
    width: 120px;
    text-align: center;
    font-size: 10px;
    .status {
      color: #86353c;
      margin-bottom: 4px;
    }
    .time {
      width: 100%;
      height: 20px;
      line-height: 20px;
      box-shadow: inset 0px 0px 10px #1c7199;
    }
  }
}
.content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  padding: 20px;
  .item {
    width: 100px;
    height: 40px;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;
    .icon {
      width: 60px;
      height: 30px;
      background: url("../assets/img/bule_decorate.png") left top;
      background-size: 100% 100%;
    }
    .data {
      font-size: 12px;
      width: 60px;
      text-align: center;
    }
  }
}
</style>
