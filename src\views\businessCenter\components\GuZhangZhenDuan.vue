<template>
  <div
    class="card-wrapper"
    :style="{
      position: 'absolute',
      width: width + (rem ? 'rem' : 'px'),
      height: height + (rem ? 'rem' : 'px'),
      left: x + (rem ? 'rem' : 'px'),
      top: y + (rem ? 'rem' : 'px'),
    }"
  >
    <basic-com
      :com1="com1"
      :com2="com2"
      @changeCurrent="handleChangeCurrent"
      title="故障诊断"
    >
      <div class="content">
        <div class="header">
          <div
            class="header-item"
            :class="currentIndex === 1 ? 'active' : ''"
            @click="currentIndex = 1"
          >
            实时数据
          </div>
          <div
            class="header-item"
            :class="currentIndex === 2 ? 'active' : ''"
            @click="currentIndex = 2"
          >
            历史报警数据
          </div>
        </div>
        <div class="bottom" v-if="current === 1 && com1">
          <div v-for="item in com1.stats" :key="item.id">
            <div class="data-item">
              <div class="data-text">
                {{ firstDevices.find((d) => d.ID === item)?.Label }}
              </div>
              <div class="data-icon">
                {{ firstDevices.find((d) => d.ID === item)?.Value || 0.0 }}
              </div>
            </div>
          </div>
        </div>
        <div class="bottom" v-else-if="current === 2 && com2">
          <div v-for="item in com2.stats" :key="item.id">
            <div class="data-item">
              <div class="data-text">
                {{ secondDevices.find((d) => d.ID === item)?.Label }}
              </div>
              <div class="data-icon">
                {{ secondDevices.find((d) => d.ID === item)?.Value || 0.0 }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </basic-com>
    <slot></slot>
  </div>
</template>

<script setup>
import BasicCom from "./BasicCom.vue";
import useCardMoveSize from "@/hooks/useCardMove.js";
import { ref, toRaw, watch, watchEffect } from "vue";
import { configStore } from "@/store/config";
import { storeToRefs } from "pinia";
const props = defineProps({
  x: Number,
  y: Number,
  width: Number,
  height: Number,
  com1: Object,
  com2: Object,
  rem: Boolean,
});

// const initialState = {
//   width: props.width,
//   height: props.height,
//   top: props.y,
//   left: props.x,
// };
// 初始化 hooks
// transform 为 transform: tanslate(left px, top px)
// size 为 { width: 111111 , height: 2222222 }
// const { width, height, transform, size } = useCardMoveSize(initialState);
const current = ref(1);
const currentIndex = ref(1);
const store = configStore();
const { devicesMap = [] } = storeToRefs(store);
const [firstDevices, secondDevices] = devicesMap.value;
console.log("keys firstDevices", firstDevices);
function handleChangeCurrent(c) {
  current.value = c;
}

// watch(props, (newVal) => {
//   // 更新坐标
//   size.left = newVal.x;
//   size.top = newVal.y;
// });
</script>

<script>
// export default {
//   name: "GuZhangZhenDuan",
// };
</script>

<style scoped lang="scss">
// .card-wrapper {
//   width: v-bind(width);
//   height: v-bind(height);
//   transform: v-bind(transform);
//   //   position: absolute;
//   //   top: 0;
//   //   left: 0;
//   user-select: none;
// }
.content {
  padding: 6px 10px;
  .header {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    width: 100%;
    height: 30px;
    line-height: 30px;
    border-bottom: 2px solid #125376;
    .header-item {
      display: inline-block;
    }
    .active {
      border-bottom: 2px solid #2bc4d8;
    }
  }
  .bottom {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    padding: 0 20px;
    .data-item {
      margin-top: 10px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .data-text {
        font-size: 10px;
        margin-bottom: 4px;
        color: #4aa7b7;
      }
      .data-icon {
        width: 70px;
        height: 24px;
        background: url("../assets/img/box5_data_bg.png");
      }
    }
  }
}
</style>
