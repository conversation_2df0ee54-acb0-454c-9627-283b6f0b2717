<template>
  <div
    class="hjjiance"
    :style="{
      position: 'absolute',
      width: width + (rem ? 'rem' : 'px'),
      height: height + (rem ? 'rem' : 'px'),
      left: x + (rem ? 'rem' : 'px'),
      top: y + (rem ? 'rem' : 'px'),
    }"
  >
    <basic-com
      :com1="com1"
      :com2="com2"
      @changeCurrent="handleChangeCurrent"
      title="环境监测"
    >
      <div>
        <div class="content" v-if="current === 1">
          <div class="item" v-for="item in com1.stats" :key="item.id">
            <div
              class="tep-parent"
              style="position: relative; overflow: hidden"
            >
              <div
                class="tep-wrapper el-carousel__item is-active is-animating tem-onews"
                style="height: 400px"
              >
                <div class="environmental ml-74px mt-25px">
                  <div class="envir_one diffuse_ani">
                    <img src="../assets/img/icon_hjjc.png" />
                  </div>
                  <div class="envir_one_ul">
                    <div class="envir_one_co float_ani">
                      <p>{{ item.text || 0.0 }}</p>
                    </div>
                    <!-- <div class="envir_one_ws float_ani">
                  <p>{{item.text || 0.00}}</p>
                </div>
                <div class="envir_one_gd float_ani">
                  <p>{{item.text || 0.00}}</p>
                </div>
                <div class="envir_one_yt float_ani">
                  <p>{{item.text || 0.00}}</p>
                </div>
                <div class="envir_one_hui float_ani">
                  <p>{{item.text || 0.00}}</p>
                </div> -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="content" v-else>
          <div class="item" v-for="item in com2.stats" :key="item.id">
            <div
              class="tep-parent"
              style="position: relative; overflow: hidden"
            >
              <div
                class="tep-wrapper el-carousel__item is-active is-animating tem-onews"
                style="height: 400px"
              >
                <div class="environmental ml-74px mt-25px">
                  <div class="envir_one diffuse_ani">
                    <img src="../assets/img/icon_hjjc.png" />
                  </div>
                  <div class="envir_one_ul">
                    <div class="envir_one_co float_ani">
                      <p>迎头co</p>
                    </div>
                    <div class="envir_one_ws float_ani">
                      <p>迎头瓦斯</p>
                    </div>
                    <div class="envir_one_gd float_ani">
                      <p>供电点瓦斯</p>
                    </div>
                    <div class="envir_one_yt float_ani">
                      <p>迎头温度</p>
                    </div>
                    <div class="envir_one_hui float_ani">
                      <p>回风巷风速</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </basic-com>
    <slot></slot>
  </div>
</template>

<script>
import BasicCom from "./BasicCom.vue";
export default {
  name: "HjingJIanCe",
  components: { BasicCom },
  data() {
    return {
      current: 1,
    };
  },
  props: {
    x: Number,
    y: Number,
    width: Number,
    height: Number,
    com1: Object,
    com2: Object,
    rem: Boolean,
  },
  methods: {
    handleChangeCurrent(current) {
      this.current = current;
    },
  },
};
</script>



<style lang="scss" scoped>
.content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  padding: 20px;
  .item {
    width: 100px;
    height: 40px;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;
    .icon {
      width: 60px;
      height: 30px;
      background-size: 100% 100%;
    }
    .data {
      font-size: 12px;
      width: 60px;
      text-align: center;
    }
  }
}
.tep-parent {
  padding: 6px 10px;
}

.environmental {
  position: relative;
  color: #fff;
}

.envir_one {
  background-image: url(../assets/img/hjjc_background.png);
  background-size: 321px 321px;
  width: 321px;
  height: 321px;
  // position: relative;

  img {
    position: absolute;
    width: 145px;
    height: 145px;
    top: 88px;
    left: 88px;
  }
}

.envir_one_ul {
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #ffffff;

  .envir_one_co {
    position: absolute;
    background-image: url(../assets/img/envir_yellow_back.png);
    background-size: 115px 40px;
    width: 115px;
    height: 40px;
    top: -5px;
    left: 145px;

    p {
      text-align: center;
      padding-top: 10px;
    }
  }

  .envir_one_ws {
    @extend .envir_one_co;
    top: 67px;
    left: -15px;

    p {
      text-align: center;
      padding-top: 10px;
    }
  }

  .envir_one_gd {
    @extend .envir_one_co;
    top: 115px;
    left: 238px;

    p {
      text-align: center;
      padding-top: 10px;
    }
  }

  .envir_one_yt {
    @extend .envir_one_co;
    left: -42px;
    top: 189px;

    p {
      text-align: center;
      padding-top: 10px;
    }
  }

  .envir_one_hui {
    @extend .envir_one_co;
    left: 123px;
    top: 244px;

    p {
      text-align: center;
      padding-top: 10px;
    }
  }
}
</style>
