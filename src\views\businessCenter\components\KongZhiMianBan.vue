<template>
  <div
    :style="{
      position: 'absolute',
      width: width + (rem ? 'rem' : 'px'),
      height: height + (rem ? 'rem' : 'px'),
      left: x + (rem ? 'rem' : 'px'),
      top: y + (rem ? 'rem' : 'px'),
    }"
  >
    <BasicCom :com1="com1" :com2="com2" title="控制面板" />
    <slot></slot>
  </div>
</template>

<script setup>
import BasicCom from "./BasicCom.vue";

defineProps({
  x: Number,
  y: Number,
  width: Number,
  height: Number,
  com1: Object,
  com2: Object,
  rem: Boolean,
});
</script>

<script>
export default {
  name: "KongZhiMianBan",
};
</script>


<!-- <script lang="ts" setup>

    import {defineProps} from 'vue'
    const props = defineProps({
        x: Number,
        y: Number,
        width: Number,
        height: Number,
    })
</script> -->
