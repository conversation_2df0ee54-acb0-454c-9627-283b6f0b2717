<template>
  <div
  :style="{
      position: 'absolute',
      width: width + (rem ? 'rem' : 'px'),
      height: height + (rem ? 'rem' : 'px'),
      left: x + (rem ? 'rem' : 'px'),
      top: y + (rem ? 'rem' : 'px'),
    }"
  >
    <BasicCom :com1="com1" :com2="com2" title="视频监控">
      <ul>
        <li v-for="camera in cameras" :key="camera">
          {{ camerasMap.find((c) => c.ID === camera)?.Name }}
        </li>
      </ul>
    </BasicCom>
    <slot></slot>
  </div>
</template>

<script setup>
import { configStore } from "@/store/config";
import { storeToRefs } from "pinia";
import BasicCom from "./BasicCom.vue";

defineProps({
  x: Number,
  y: Number,
  width: Number,
  height: Number,
  com1: Object,
  com2: Object,
  cameras: Array,
  rem: Boolean,
});
const store = configStore();
const { camerasMap } = storeToRefs(store);
console.log("camerasMap", camerasMap.value);
</script>



<script>
//   export default{
//   name:"ShiPinJianKong"
// }
</script>
