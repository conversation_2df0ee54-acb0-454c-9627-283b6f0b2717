<template>
  <div>
    <h3>一号主通风机房</h3>
    <el-select multiple v-model="cameras">
      <el-option
        v-for="t in options"
        :key="t.ID"
        :label="t.Name"
        :value="t.ID"
      />
    </el-select>

    <br />
    <div>
      相机绑点：
      <br />
      <ul>
        <li v-for="c in cameras" :key="c">
          <!-- c 为相机ID （上面绑定了  :value="t.ID" ） -->
          <!-- 根据ID找对应的文字 -->
          {{ cameraKeysMap[c] }} 
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch, ref } from "vue";

const props = defineProps({
  cameras: Array,
  options: { // 相机下拉框数据 （相机列表）
    type: Array,
    default: () => [],
  },
});
// 定义 更新选中的相机
const emits = defineEmits(["update:cameras"]);

const cameraKeysMap = {}; 
// 重组相机列表
for (const { ID, Name } of props.options) {
  cameraKeysMap[ID] = Name;
}

const cameras = ref(props.cameras);
watch(cameras, (newVal) => emits("update:cameras", newVal)); // 更新相机数据
</script>

<script>
export default {
  name: "VideoMonitor",
};
</script>
