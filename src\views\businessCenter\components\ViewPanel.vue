<template>
  <div class="ele_left">
    <div class="h-40px show1 w-full">
      组件
      <!-- <img class="w-20px h-20px ml-20px" src="../assets/tableimg.png"> -->
    </div>
    <!-- 看板开始 -->
    <div class="left_kb">
      <div class="kanban">看板</div>
      <!-- 组件看板动态 -->
      <div class="zuj_edit">
        <div class="zuj_edit_ul">
          <!--  -->
          <div v-for="(item, index) in pannels" :key="index" class="zuj_list">
            <!-- 看板标题 -->
            <div class="kb_title">
              <div class="circle"></div>
              <div class="circle1"></div>
              <div class="circle2"></div>
              <div class="mt-7px ml-20px">{{ item.Name }}</div>
            </div>
            <!-- 看板标题end -->
            <!-- 组件图片start -->
            <div
              class="zuj-box"
              @mousedown="emits('ohChoose', item)"
              @dragstart="(e) => handleDragStart(e, item)"
            >
              <img :src="item.DisplayUrl" alt="" style="width: 100%" />
            </div>
            <!-- 组件图片end -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  pannels: Array,
});

const emits = defineEmits(["ohChoose"]);

function handleDragStart(e, item) {
  e.dataTransfer.setData("text", item.ID);
}
</script>

<script>
export default {
  name: "ViewPanel",
};
</script>


<style scoped lang="scss">
.ele_left {
  width: 10%;
  min-width: 200px;
  height: 100%;
}
.left_kb {
  display: flex;
  width: 100%;
  height: calc(100% - 62px);

  .kanban {
    width: 60px;
    height: 50px;
    line-height: 50px;
    margin-top: 2px;
    background-color: #232324;
    text-align: center;
  }
}

.show1 {
  background-color: #232324;
  padding-left: 10px;
  line-height: 40px;

  img {
    margin-left: 2px;
    padding-top: 5px;
  }
}

.zuj_edit {
    display: flex;
    flex-direction: column;
    width: 95%;
    height: 100%;
    margin-top: 1px;
    margin-left: 5px;
    overflow-y: scroll;
    // 滚动条的颜色的大小
    &::-webkit-scrollbar {
      width: 1px;
    }

    &::-webkit-scrollbar-track {
      background-color: #232324;
    }

    &::-webkit-scrollbar-thumb {
      background: #232324;
      border-radius: 1px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background-color: #555;
    }

    .zuj_edit_ul {
      width: 95%;
      height: 100%;
      margin-left: 5px;
      overflow-y: scroll;
      // 滚动条的颜色的大小
      &::-webkit-scrollbar {
        width: 1px;
      }

      &::-webkit-scrollbar-track {
        background-color: #232324;
      }

      &::-webkit-scrollbar-thumb {
        background: #232324;
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background-color: #555;
      }

      .zuj_list {
        width: 100%;
        height: auto;
        background-color: #232324;
        margin-top: 10px;
      }
      .zuj-box {
        width: 95%;
        padding: 6px 10px;
        // background-color: #232324;
        border-radius: 10px;
        margin-top: 5px;
      }
      .kb_title {
        display: flex;
        flex-direction: row;
      }
      .circle {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #fc625d;
        margin-left: 4px;
        margin-top: 10px;
      }

      .circle1 {
        @extend .circle;
        background-color: #fcbc40;
      }
      .circle2 {
        @extend .circle;
        background-color: #34c749;
      }
    }
  }

</style>
