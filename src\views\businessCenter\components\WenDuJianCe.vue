<template>
  <div
    class="donglijiance"
    :style="{
      position: 'absolute',
      width: width + (rem ? 'rem' : 'px'),
      height: height + (rem ? 'rem' : 'px'),
      left: x + (rem ? 'rem' : 'px'),
      top: y + (rem ? 'rem' : 'px'),
    }"
  >
    <basic-com
      :com1="com1"
      :com2="com2"
      @changeCurrent="handleChangeCurrent"
      title="温度监测"
    >
      <div>
        <div class="content" v-if="current === 1 && com1?.stats">
          <div class="item" v-for="item in com1.stats" :key="item.id">
            <div class="relative icon">
              <img src="../assets/img/temp_bg.png" class="turn w-28px h-28px" />
              <img
                src="../assets/img/temp.png"
                class="breathe_ani z-2 absolute w-10px h-13px top-8px left-10px"
              />
            </div>
            <div class="data">
              <div class="box2_value cursor-pointer">
                <span>{{
                  firstDevices.find((d) => d.ID === item)?.Label
                }}</span>
                <span
                  >({{
                    firstDevices.find((d) => d.ID === item)?.Value || 0.0
                  }}°)
                </span>
              </div>
              <div>
                <img
                  src="../assets/img/temp_decorate.png"
                  class="w-100% h-7px"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="content" v-else-if="com2?.stats">
          <div class="item" v-for="item in com2.stats" :key="item.id">
            <div class="relative icon">
              <img src="../assets/img/temp_bg.png" class="turn w-28px h-28px" />
              <img
                src="../assets/img/temp.png"
                class="breathe_ani z-2 absolute w-10px h-13px top-8px left-10px"
              />
            </div>
            <div class="data">
              <div class="box2_value cursor-pointer">
                <span>
                  {{ secondDevices.find((d) => d.ID === item)?.Label }}</span
                >
                <span>
                  ({{
                    secondDevices.find((d) => d.ID === item)?.Value || 0.0
                  }}°)
                </span>
              </div>
              <div>
                <img
                  src="../assets/img/temp_decorate.png"
                  class="w-100% h-7px"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </basic-com>
    <slot></slot>
  </div>
</template>

<script setup>
import BasicCom from "../components/BasicCom.vue";
import { ref, computed } from "vue";
import { configStore } from "@/store/config";
import { storeToRefs } from "pinia";

defineProps({
  x: Number,
  y: Number,
  width: Number,
  height: Number,
  com1: Object,
  com2: Object,
  rem: Boolean,
});
const current = ref(1);
const store = configStore();
const { devicesMap = [] } = storeToRefs(store);
const [firstDevices, secondDevices] = devicesMap.value;

function handleChangeCurrent(c) {
  current.value = c;
}
</script>

<script>
export default {
  name: "WenDuJIanCe",
};
</script>



<style lang="scss" scoped>
.content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  padding: 20px;
  .item {
    width: auto;
    height: 40px;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;
    .icon {
      width: auto;
      height: 30px;
      background-size: 100% 100%;
    }
    .data {
      font-size: 12px;
      width: auto;
      text-align: center;
    }
  }
}
</style>
