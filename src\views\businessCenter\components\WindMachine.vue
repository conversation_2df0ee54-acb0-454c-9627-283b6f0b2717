<template>
  <div>
    <div class="titleFj">{{ options.title }}</div>
    <div class="floor-top">
      <div class="floor-leftdl">基础</div>
      <div class="floor-right">
       <div class="floor-right-title">

        <div class="">
          <el-input
            :value="options.title"
            style="width: 78px; height: 25px;margin-left: -2px;"
            disabled
          />
          <div style="color: #807a6f; margin-top: 8px; margin-left: 5px;">
            标题
          </div>
        </div>
         <!-- 动力监测的状态 -->
         <div class="" v-if="hasStatus">
          <el-select
            v-model="statusVar"
            placeholder="选择状态"
            style="width: 78px; height: 25px;margin-left: 3px;"
          >
            <el-option label="停机" :value="0" />
            <el-option label="运行" :value="1" />
          </el-select>
          <div style="color: #807a6f; margin-top: 8px; margin-left: 6px">
            状态
          </div>
        </div>
          <!-- 动力监测的状态 -->
          <div class="">
          <el-button
            style="width: 78px; height: 25px;margin-left: 2px; "
            @click="show = true"
          >
            选择属性
          </el-button>
          <div style="color: #807a6f; margin-top: 10px; margin-left: 5px">
            属性
          </div>
        </div>
       </div>
       
       
       
      </div>
    </div>

    <div class="line"></div>
    <div class="floor-bottom">
      <div class="floor-sx">
        属性
        <ul class="shux-ul">
          <!-- 已选择的设备列表 -->
          <div
            class="ul_list"
            v-for="stat in statsVar"
            :key="stat"
            @click="show = true"
          >
            <el-button class="m-20 cursor-pointer p-5" style="width: 78px; height: 25px; margin-top: 10px"> 修改属性 </el-button>
            <p class="el_button_li"> {{ devices.filter((d) => d.ID === stat)[0]?.Label }}</p>
           
          </div>
        </ul>
      </div>
    </div>

    <!-- 弹窗 -->

    <el-dialog v-model="show" title="选择属性" width="800">
      <div style="display: flex; flex-direction: row; flex-wrap: wrap">
        <!-- 可供选择的设备列表 -->
        <!-- statsVar 是 devices.ID 数组（:value="device.ID"） -->
        <el-checkbox-group v-model="statsVar" size="small">
          <el-checkbox
            v-for="device in devices"
            style="margin: 10px"
            :key="device.KeyID"
            :value="device.ID"
            :label="device.Label"
            border
          />
        </el-checkbox-group>
        <div
          style="
            width: 100%;
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
          "
        >
          <el-button type="primary" @click="submitStats">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watchPostEffect, toRaw, watch, computed } from "vue";
import useVModel from "@/hooks/useVModel";

const props = defineProps({
  options: Object, // 杂项 ex: 标题 文字。。。。
  stats: {
    // 选中的设备；列表
    type: Array,
    default: [],
  },
  status: Number, // 动力监测专属字段
  devices: Array, // 可供选择的设备列表
  hasStatus: Boolean, // 动力监测专属字段 true代表有状态可供选择
});
// 定义向父组件传递的事件
const emits = defineEmits(["update:stats", "update:status", "submit"]);

// 接收选中的设备
const statsVar = ref(props.stats || []);
// 接收 动力监测 的状态
const statusVar = ref(props.status);

watch(statusVar, (newVal) => emits("update:status", newVal)); // 更新

const show = ref(false); // 弹框

function submitStats() {
  emits("update:stats", [...toRaw(statsVar.value)]); // 提交数据
  console.log([...toRaw(statsVar.value)], "属性");
  show.value = false; // 关闭弹框
}
</script>

<script>
export default {
  name: "WindMachine",
};
</script>

<style scoped  lang="scss">
.titleFj {
  font-size: 16px;
  color: #79bbff;
  padding-bottom: 10px;
  padding: 15px;
  margin-top: 10px;
}

.floor-top {
  display: flex;
  flex-direction: row;
  margin-left: 30px;
}
.line {
  margin-top: 20px;
  margin-bottom: 10px;
  margin-left: 30px;
  margin-right: 30px;
  // width: 400px;
  border-bottom: 2px solid #fff;
}
.floor-bottom {
  margin-top: 30px;
  display: flex;
  flex-direction: row;
  margin-left: 30px;

  // .floor-sx{
  //   display: flex;
  //   flex-direction: row;

  // }
  .shux-ul {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
  }

  .ul_list {
    display: flex;
    flex-direction: column;
    margin-left: 7px;
    margin-top: 5px;

    .el-button{
      background-color: #1E72AE;
      width: 70%;
      height: 70%;
      border: 0;
      color: #fff;
      font-size: 14px;
      margin-bottom: 20px;
     

    }
  }
}
.floor-leftdl {
  width: 50px;
}
.floor-right{
  display: flex;
  width: 100%;
  height: 100%;
  
  .floor-right-title {
    display: flex;
    flex-wrap: wrap;
    width: 98%;
 
  }
}


.power-wrapper {
  transition: transform 0.6s ease-in-out;
}

.tep-wrapper {
  transition: transform 0.6s ease-in-out;
}

.fault_wrapper {
  transition: transform 0.6s ease-in-out;
}
</style>