<template>
  <div
    :style="{
      position: 'absolute',
      width: width + (rem ? 'rem' : 'px'),
      height: height + (rem ? 'rem' : 'px'),
      left: x + (rem ? 'rem' : 'px'),
      top: y + (rem ? 'rem' : 'px'),
    }"
  >
    <basic-com
      :com1="com1"
      :com2="com2"
      @changeCurrent="handleChangeCurrent"
      title="视频监控"
    >
      <div></div>
    </basic-com>
  </div>
</template>

<script>
import BasicCom from "../components/BasicCom.vue";

export default {
  name: "basicTem",
  components: { BasicCom },
  props: {
    x: Number,
    y: Number,
    width: Number,
    rem: Boolean,
    height: Number,
  },
};
</script>



<!-- <script lang="ts" setup>

    import {defineProps} from 'vue'
    const props = defineProps({
        x: Number,
        y: Number,
        width: Number,
        height: Number,
    })
</script> -->
