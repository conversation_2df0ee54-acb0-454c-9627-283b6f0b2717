// <!-- 组件开始 -->
// <div class="ele_left">
//   <div class="h-40px show1 w-full">
//     组件
//     <!-- <img class="w-20px h-20px ml-20px" src="../assets/tableimg.png"> -->
//   </div>
//   <!-- 看板开始 -->
//   <div class="left_kb">
//     <div class="kanban">看板</div>
//     <!-- 组件看板动态 -->
//     <div class="zuj_edit">
//       <div class="zuj_edit_ul">
//         <!--  -->
//         <div
//           v-for="(item, index) in comsData"
//           :key="index"
//           class="zuj_list"
//         >
//           <!-- 看板标题 -->
//           <div class="kb_title">
//             <div class="circle"></div>
//             <div class="circle1"></div>
//             <div class="circle2"></div>
//             <div class="mt-7px ml-65px">{{ item.Name }}</div>
//           </div>
//           <!-- 看板标题end -->
//           <!-- 组件图片start -->
//           <div class="zuj-box" @mousedown="handleMousedown(item)">
//             <img :src="item.DisplayUrl" alt="" style="width: 100%" />
//           </div>
//           <!-- 组件图片end -->
//         </div>
//       </div>
//     </div>
//   </div>
// </div>
// <!-- 
// components: { MyComponent },组件结束 -->
