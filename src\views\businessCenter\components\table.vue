<template>
  <div class="box">
    <!-- 头部 -->
    <div class="top"></div>
    <!-- 中间 -->
    <div class="characters">
      <div class="characters_top">
        <slot name="title"> </slot>
      </div>
      <div class="characters_center">
        <div class="characters_center_text">
          <!-- 左边 -->
          <div class="characters_center_left">
            <!-- 左边三按钮 -->
            <div class="characters_center_left_title" mr-20px>
              <slot name="top-left"> </slot>
            </div>
            <div class="characters_center_left_title" mr-20px>
              <slot name="top-center"> </slot>
            </div>
            <div class="characters_center_left_titleRed">
              <slot name="top-right"></slot>
            </div>
          </div>
          <!-- 右边 -->
          <div class="characters_center_right">
            <div mr-20px>
              <slot name="right-01"></slot>
            </div>
            <div mr-20px>
              <slot name="right-02"></slot>
            </div>
            <div class="characters_center_right_title" mr-20px>
              <slot name="right-03"></slot>
            </div>
            <div class="characters_center_right_title" mr-20px>
              <slot name="right-04"></slot>
            </div>
          </div>
        </div>
      </div>
      <!-- 底部 -->
      <div class="bottom">
        <slot
          style="width: 100%; height: 10%; overflow: auto"
          name="Download"
        ></slot>
        <slot
          style="width: 100%; height: 100%; overflow: auto"
          name="table"
        ></slot>
      </div>
      <div class="foot">
        <slot name="pages"></slot>
      </div>
    </div>
  </div>
</template> 
<style  lang="scss" scoped>
.box {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  color: #fff;
}

.top {
  height: 120px;
  width: 100%;
}

.img {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

.characters {
  height: 88%;
  // background-image: url("../../businessCenter/img/backgroundg.gif"); //xmm修改
  background-color: rgba(0, 0, 0, 0.5);//xmm修改
  background-size: 100% 100%;
  width: 100%;
  margin: auto;
}

.characters_top {
  width: 100%;
  height: 5%;
  display: flex;
  align-items: center;
  font-size: 18px;
  margin-left: 30px;
  font-family: AlimamaShuHeiTi;
  font-weight: bold;
  // line-height: 25px;
  /* background-color: red; */
  // xmm修改
  border-bottom: 1px solid rgba(255,255,255,0.2);

}

.characters_center {
  width: 100%;
  height: 10%;
  display: flex;
  align-items: center;
  justify-content: center;
  // background-color: red;

  .characters_center_text {
    width: 97%;
    height: 50%;
    display: flex;
    justify-content: space-between;
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    // background-color: pink;

    .characters_center_left {
      width: 20%;
      height: 100%;
      display: flex;
      align-items: center;
      // background-color: #000;

      .characters_center_left_title {
        cursor: pointer;
        width: 86px;
        height: 32px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 32px;
        background: rgba(43, 155, 216, 0);
        // background-image: url("../img/blue_border.png");
        background-size: 100% 100%;
      }

      .characters_center_left_titleRed {
        cursor: pointer;
        width: 86px;
        height: 32px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 32px;
        background: rgba(43, 155, 216, 0);
        // background-image: url("../img/red_border.png");
        background-size: 100% 100%;
      }
    }

    .characters_center_right {
      width: 60%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: right;

      .characters_center_right_title {
        cursor: pointer;
        width: 86px;
        height: 32px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        align-items: center;
        background: rgba(43, 155, 216, 0);
        // background-image: url("../img/blue_border.png");
        background-size: 100% 100%;
      }
    }
  }
}
.foot {
  width: 100%;
  height: 45px;
  margin-left: 10px;
  display: flex;
  align-items: center;
  // background-color: red;
}
.bottom {
  width: 98%;
  height: 80%;
  margin: auto;
  border: 1px solid #5c471f;
}
</style>