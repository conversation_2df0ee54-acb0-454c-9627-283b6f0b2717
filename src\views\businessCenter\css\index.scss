.statementBox {

    //  修改表格
    .el-table {
        --el-table-border-color: transparent !important;
        --el-table-border: none !important;
        --el-table-text-color: #fff !important;
        --el-table-header-text-color: #fff !important;
        --el-table-row-hover-bg-color: transparent !important;
        --el-table-current-row-bg-color: transparent !important;
        --el-table-header-bg-color: transparent !important;
        --el-table-bg-color: transparent !important;
        --el-table-tr-bg-color: transparent !important;
        --el-table-expanded-cell-bg-color: transparent !important;
    }


    .el-table thead {
        // background: rgba(10, 162, 238, 0.25);
        background-color: rgba(255, 255, 255, 0.02) !important;
    }

    .el-table tr {
        height: 40px;
        background: rgba(255, 179, 15, 0.08);
        // box-shadow: 0px 1px 0px 0px rgba(12, 164, 238, 0.7);
        border-radius: 0px 6px 6px 6px;
    }

    .el-table tbody tr:hover td {
        background-color: #2c2819 !important;
    }

    .el-table__body {
        border-collapse: separate;
        border-spacing: 0 16px;
    }

    // 修改element分页选页数样式
    .el-select-dropdown__item.is-hovering {
        background-color: rgba(10, 162, 238, 0.25);
    }

    .el-select-dropdown__list {
        background-color: rgba(10, 162, 238, 0.25) !important;
    }

    .el-select-dropdown__wrap {
        background-color: rgba(10, 162, 238, 0.25) !important;
    }

    .el-pagination__classifier {
        color: #fff;
    }

    // 修改输入框
    .el-input__wrapper,
    .el-input__wrapper:focus {
        background-color: transparent !important;
        // border: 1px solid rgba(232, 157, 6, 0.8) !important; //xmm修改 边框颜色
    }

    .el-input__inner {
        color: #fff !important;
        border: none !important;
    }



    // 多选框样式
    .el-checkbox__input.is-disabled+span.el-checkbox__label {
        color: #fff !important;

    }

    .el-select__placeholder {
        color: #fff !important;

        // xmm修改 2025-02-25
        --un-text-opacity: 1 !important;
        color: rgba(227, 216, 183, var(--un-text-opacity)) !important;

    }

    .el-select__wrapper {
        background-color: transparent;
        color: #fff !important;
    }

    .el-checkbox__input.is-checked+.el-checkbox__label {
        color: #0BDFF2 !important;

    }

    .el-checkbox__inner {
        background-color: rgba(169, 167, 168, 0.5) !important;
        border: 1px solid rgb(40, 41, 43) !important;
    }

    .el-checkbox__input.is-checked .el-checkbox__inner {
        background-color: rgba(140, 91, 19, 0.8) !important;
        border-color: 1px solid rgb(182, 100, 3) !important;
    }


    .el-checkbox__input.is-checked .el-checkbox__inner::after {
        border-color: 1px solid rgb(182, 100, 3) !important;
    }

    .top {
        height: 120px;
        // background-color: red !important;
        width: 100%;
    }

    .userBG-Rde {
        width: 67px;
        height: 28px;
        color: #f13f5c;
        background: rgba(241, 63, 92, 0.17);
        border: 1px solid #f13f5c;
        border-radius: 6px;
        margin: auto;
    }

    .userBG-Green {
        width: 67px;
        height: 28px;
        background: rgba(63, 241, 180, 0.17);
        border: 1px solid #3ff1b4;
        color: #3ff1b4;
        border-radius: 6px;
        margin: auto;
    }

    .userBlue {
        width: 67px;
        height: 28px;
        border: 1px solid #40f4f1;
        color: #40f4f1;
        cursor: pointer;
        border-radius: 6px;
        margin: auto;
    }

    .userRde {
        width: 80px;
        height: 32px;
        margin-left: 3px;
        color: #f13f5c;
        border: 1px solid #f13f5c;
        border-radius: 6px;
        cursor: pointer;
        margin: auto;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .img {
        width: 16px;
        height: 16px;
        margin-right: 6px;
    }

    // xmm修改
    .bullue,
    .bullue1 {
        cursor: pointer;
        width: 80px;
        height: 32px;
        margin-left: 3px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
    }

    .bullue {
        // background-image: url("../img/blue_border.png");
        // background-size: 100% 100%;
        // background: rgba(43, 155, 216, 0);
        background-color: rgba(81, 81, 81, 0.5);
        border: 1px solid rgba(81, 81, 81, 0.5);
    }

    .bullue1 {
        background-color: rgba(255, 179, 15, 0.3);
        border: 1px solid rgba(232, 157, 6, 0.8);
    }


    .red {
        cursor: pointer;
        width: 80px;
        height: 32px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        align-items: center;
        background: rgba(43, 155, 216, 0);
        background-image: url("../img/red_border.png");
        background-size: 100% 100%;
    }

    .el-pager li {
        background-color: transparent;
        color: white;

        &:hover {
            color: #76F7FF;
        }

        &.is-active {
            color: #11AEEE;
        }

    }

    .el-pagination>.is-last {
        background-color: transparent;

    }

    .el-pagination__total {
        color: #fff;

        // xmm修改 2025-02-25
        --un-text-opacity: 1;
        color: rgba(227, 216, 183, var(--un-text-opacity));

    }

    .btn-prev {
        color: #fff;

        // xmm修改 2025-02-25
        --un-text-opacity: 1;
        color: rgba(227, 216, 183, var(--un-text-opacity));

        .el-icon {
            color: #fff;

            // xmm修改 2025-02-25
            --un-text-opacity: 1;
            color: rgba(227, 216, 183, var(--un-text-opacity));
        }

    }

    .el-pagination__goto,
    .el-pagination__classifier {
        color: #fff;

        // xmm修改 2025-02-25
        --un-text-opacity: 1;
        color: rgba(227, 216, 183, var(--un-text-opacity));
    }

    .btn-next {
        @extend .btn-prev;
    }

    .el-pagination button[class*="prev"] {
        background: none;
    }

    .el-pagination button[class*="next"] {
        background: none;
    }

    ::v-deep .el-select__placeholder {
        // xmm修改 2025-02-25
        --un-text-opacity: 1 !important;
        color: rgba(227, 216, 183, var(--un-text-opacity)) !important;
    }
}