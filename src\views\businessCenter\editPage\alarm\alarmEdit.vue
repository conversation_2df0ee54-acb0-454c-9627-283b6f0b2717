<template>
  <div class="box statementBox">
    <!-- 头部分装 -->
    <encapsulationTable>
      <template #title> 报警配置</template>


      <!-- 左边 -->
      <template #top-left>
        <p class="bullue1" @click="add(formRef)">
          <img class="img" src="../../img/plusNew.png" alt="" />新增
        </p>
      </template>
      

      
      <template #table>
        <el-table ref="multipleTableRef" :data="tableData" highlight-current-row :cell-style="{ textAlign: 'center' }"
          :header-cell-style="{ 'text-align': 'center' }" @selection-change="handleSelectionChange"
          style="overflow: auto; height: 90%">
          <!-- <el-table-column type="selection" width="55" /> -->
          <el-table-column type="index" width="80" label="序号" />
          <el-table-column prop="Name" label="名称" />
          <el-table-column prop="AlarmLevel" label="报警等级" />
          


          <el-table-column label="报警类型" align="center" min-width="120">
            <template #default="scope">

              <span v-if="scope.row.AlarmType == '1'">主通</span>
              <span v-if="scope.row.AlarmType == '2'">局扇</span>
              <span v-if="scope.row.AlarmType == '3'">风门</span>
              <span v-if="scope.row.AlarmType == '4'">风窗</span>
              <span v-if="scope.row.AlarmType == '5'">安全监控</span>

            </template>
          </el-table-column>
          <el-table-column prop="AlarmPoint" label="报警点位" />
          <!-- <el-table-column prop="EqualsValue" label="相同值" /> -->
          <el-table-column prop="Lx" label="安全监控类型" />
          <el-table-column prop="MaxValue" label="最大值" />
          <el-table-column prop="MinValue" label="最小值" />
          <el-table-column prop="AlarmTime" label="时间" />


          <el-table-column label="操作" width="200px">
            <template #default="scope">
              <div style="display: flex">
                <div class="bullue1" @click="updateC(scope.row)">修改</div>
                <div class="userRde" @click="del(scope.row)">删除</div>
              </div>
            </template>
          </el-table-column>


        </el-table>
      </template>
      <template #pages>
        <el-pagination :page-sizes="[5, 10, 15, 20, 25]" layout="total, sizes, prev, pager, next, jumper" :total="total"
          @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </template>
    </encapsulationTable>


    <!-- 弹框 -->
    <el-dialog v-model="dialogShow" :title="dialogTitle">

      <div v-if="dialogTitle == '新增' || dialogTitle == '编辑'" style="width: 80%">


        <el-form :label-position="right" :model="alarmInit" ref="formRef" style="margin-left: -55px;">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="名称" prop="Name">
                <el-input v-model="alarmInit.Name" placeholder="请输入名称" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="报警等级" prop="AlarmLevel">
                <el-input v-model="alarmInit.AlarmLevel" placeholder="请输入等级" clearable />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">

              <el-form-item label="最大值" prop="MaxValue">
                <el-input v-model="alarmInit.MaxValue" placeholder="请输入" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">

              <el-form-item label="报警类型" prop="AlarmType">
                <el-select style="width: 100%" v-model="alarmInit.AlarmType" placeholder="请选择" size="large">
                  <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>

            </el-col>
            
          </el-row>
          <el-row :gutter="24">
           
              <el-col :span="12">

<el-form-item label="最小值" prop="MinValue">
  <el-input v-model="alarmInit.MinValue" placeholder="请输入" clearable />
</el-form-item>
</el-col>
             
           
            <el-col :span="12">

              <el-form-item label="监控类型" prop="Lx">
                 <el-select style="width: 100%" v-model="alarmInit.Lx" placeholder="请选择" size="large">
                  <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <!-- <el-input v-model="alarmInit.Lx" placeholder="请输入" clearable /> -->
              </el-form-item>
            </el-col>
          </el-row>


        
          <el-row :gutter="24">

            <el-col :span="12">

              <el-form-item label="间隔时间" prop="AlarmTime">
                <el-input v-model="alarmInit.AlarmTime" placeholder="请输入间隔时间" clearable />
              </el-form-item>

            </el-col>

             <el-col :span="12">

              <el-form-item label="系统点位" prop="AlarmPoint">
                 <el-select style="width: 100%" v-model="alarmInit.AlarmPoint" placeholder="请选择" size="large">
                  <el-option v-for="item in pointList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <!-- <el-input v-model="alarmInit.Lx" placeholder="请输入" clearable /> -->
              </el-form-item>
            </el-col>

          </el-row>
        </el-form>


      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogShow = false">取消</el-button>
          <el-button v-if="dialogTitle == '新增'" type="primary" @click="addList">
            添加
          </el-button>
          <el-button v-if="dialogTitle == '编辑'" type="primary" @click="editEdit">
            提交
          </el-button>

        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>

import encapsulationTable from "../../components/table.vue";
import {

  ApiSecurityMonitoringType,
 
} from "@/https/encapsulation/Safety";

import {
  ApiAlarmconfigPage,
  ApiAlarmconfigAdd,
  ApiAlarmconfigUpdate,
  ApiAlarmconfigDel,
  ApiAlarmconfigList,
  ApiAlarmPointList
  
} from "../../../../https/encapsulation/Configuration";
import { ElMessage, ElMessageBox } from 'element-plus';
// 引入样式
import "../../css/index.scss";
import { onMounted, ref, reactive,watch } from 'vue';
import "../../css/el-pagination.scss" //xmm


// 弹框名字
const dialogTitle = ref("");
const dialogShow = ref(false);
onMounted(() => {
  alarmList()
 

})

const value1 = ref('')
const alarmInit = reactive({
  ID: "",
  Name: '',
  AlarmLevel: '',
  AlarmType: '',
  EqualsValue: '',
  Lx: '',
  MaxValue: '',
  MinValue: '',
  AlarmTime: '',
  AlarmPoint: '',

})
const options = [
  {
    value: "1",
    label: "主通",
  },
  {
    value: "2",
    label: "局扇",
  },
  {
    value: "3",
    label: "风门",
  },
  {
    value: "4",
    label: "风窗",
  },
  {
    value: "5",
    label: "安全监控",
  },
]

const tableData = ref([])
const pageIndex = ref(1);
const pageSize = ref(5);
const total = ref();
const typeList = ref([]);
const pointList = ref([]);
// 列表数据
const alarmList = () => {

  ApiAlarmconfigPage(pageSize.value, pageIndex.value).then((res) => {
    tableData.value = res.Result;
    total.value = res.PageInfo.Total;
    
  })
}

// 报警类型
const alarmType = async () => {
  ApiSecurityMonitoringType().then((res) => {
    console.log(res, '类型')
     const newTypeList = res.data.Result.map(item => ({ value: item, label: item }));
    typeList.value = newTypeList;
    
  })
}
// 获取选中的
const handleSelectionChange = (val) => {
  SelectList.value = val;
};


const add = (formEl) => {
  dialogTitle.value = "新增";

  alarmInit.ID = "";
  alarmInit.Name = "";
  alarmInit.AlarmLevel = "";
  alarmInit.AlarmType = "";
  // alarmInit.EqualsValue = "";
  // alarmInit.Enable = true;
  alarmInit.Lx = "";
  alarmInit.MaxValue = "";
  alarmInit.MinValue = "";
  alarmInit.AlarmTime = "";
  alarmInit.AlarmPoint = "";
  dialogShow.value = true;
  alarmType();

};
//获取属性点
const getPointList = async () => {
  const res = await ApiAlarmPointList({
        type: alarmInit.AlarmType,
        Lx: alarmInit.Lx
    });
    return res;
  
 
}

//监听数据
watch([
    () => alarmInit.AlarmType,
    () => alarmInit.Lx
], async ([newAlarmType, newLx]) => {
    const res = await getPointList();
     if (Array.isArray(res.Result)) {
        pointList.value = res.Result.map(({ Id, Name }) => ({
            value: Id,
            label: Name
        }));
    } else {
        // 如果res.Result不是数组，可根据实际情况处理，比如清空pointList.value
        pointList.value = []; 
    }
  
   
  console.log(pointList.value, '属性点11111111111')
});
// 新增
const addList = async () => {
  console.log(alarmInit, '添加')
  ApiAlarmconfigAdd(alarmInit).then((res) => {
    console.log(res, '添加')
    if (res.Status == 0) {
      ElMessage({
        type: "success",
        grouping: true,
        message: "成功",
      });
      dialogShow.value = false;
      alarmList();
    }

  
    else if (res.Status == 3) {
      ElMessage({
        message: "添加失败",
        type: "warning",

      });
      dialogShow.value = false;

    }
  })
}

const formRef = ref(null);
//修改
const updateC = async (row) => {
  console.log(11111, row)
  dialogTitle.value = "编辑";
  dialogShow.value = true;
  alarmInit.ID = row.ID;
  alarmInit.Name = row.Name;
  alarmInit.AlarmLevel = row.AlarmLevel;
  alarmInit.Lx = row.Lx;
  alarmInit.MaxValue = row.MaxValue;
  alarmInit.MinValue = row.MinValue;
  alarmInit.AlarmTime = row.AlarmTime;
  alarmInit.AlarmPoint = row.AlarmPoint;
  alarmInit.AlarmType = row.AlarmType;

  alarmType();


  let type;
  if (row.AlarmType == "1") {
    type = "主通";
  } else if (row.AlarmType == "2") {
    type = "局扇";
  } else if (row.AlarmType == "3") {
    type = "风门";
  } else if (row.AlarmType == "4") {
    type = "风窗";
  } else {
    type = "安全监控";

  }

  alarmInit.AlarmType = type;


}

const editEdit = async () => {
  ApiAlarmconfigUpdate(alarmInit).then((res) => {
    console.log(alarmInit, '$$$$$$$$$$$')
    // console.log(res,'@@@@修改')
    if (res.Status == 0) {
      ElMessage({
        type: "success",
        grouping: true,
        message: "成功",
      });
      dialogShow.value = false;

      alarmList();

    } else if (res.Status == 3) {
      ElMessage({
        message: "修改失败",
        type: "warning",

      });
      dialogShow.value = false;
    }
  });

}
// 删除
const del = (row) => {
  console.log(row, '删除警告')
  ElMessageBox.confirm("此操作将永久删除, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      ApiAlarmconfigDel(row.ID).then((res) => {
        if (res.Status == 0) {
          alarmList();
          Tips();
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "已取消",
      });
    });
};

// 成功提示
const Tips = () => {
  ElMessage({
    type: "success",
    message: "删除成功",
  });
};
// 分页
const handleSizeChange = (val) => {
  pageSize.value = val;
  alarmList();
};
const handleCurrentChange = (val) => {
  pageIndex.value = val;
  alarmList();
};
</script>
<style lang="scss" scoped>
.box {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  color: #fff;
}

.underline {
  cursor: pointer;
}

.underline:hover {
  font-size: 15px;
  color: #315efb;
}

.el-upload__tip {
  color: #fff;
}

// .userBlue {
//   width: 67px;
//   height: 28px;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   border: 1px solid #40f4f1;
//   color: #40f4f1;
//   cursor: pointer;
//   margin: auto;
// }



.editB {

  width: 100px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #40f4f1;
  color: #fff;
  cursor: pointer;
  border-radius: 16px;
  margin: auto;
}

// .userBlue {

//   width: 67px;
//   height: 28px;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   border: 1px solid #40f4f1;
//   color: #fff;
//   cursor: pointer;
//   border-radius: 16px;
//   margin: auto;

//   img {
//     width: 20px;
//     height: 15px;
//   }
// }

.userRde {
  width: 86px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
}

::v-deep .el-form-item__label {
  color: #fff !important;
}

::v-deep .el-form {
  margin-top: 15px;
}

// ::v-deep .el-dialog{
//   --el-dialog-width: 64%;
//     height: 47vh;
// }
// 修改输入框
::v-deep .el-input__wrapper {
  background-color: transparent !important;
}

::v-deep .el-input__inner {
  color: #fff !important;
}

::v-deep .el-textarea__inner {
  background-color: transparent !important;
  color: #fff;
}

// 修改扇形
::v-deep .el-tree-node__content:active {
  background-color: transparent !important;
}

::v-deep .el-tree {
  background-color: transparent !important;
  color: #fff;

  :hover {
    background: rgba(10, 162, 238, 0.25);
  }
}

::v-deep .el-form-item__label {
  width: 120px;
}

::v-deep .el-tabs__item.is-active {
  color: #409eff;
}

::v-deep .el-tabs__item {
  color: #fff;
}

.scroll-container {
  height: 350px;
  /* 设置容器高度 */
  overflow: auto;
  /* 显示滚动条 */

  /* 自定义滚动条样式 */
  scrollbar-color: #999 transparent;
  /* 滚动条颜色 */
  scrollbar-width: thin;
  /* 滚动条宽度 */
}

.scroll-container::-webkit-scrollbar {
  width: 6px;
  /* 设置滚动条宽度 */
  height: 6px;
  /* 设置滚动条高度 */
}

.scroll-container::-webkit-scrollbar-thumb {
  background-color: #4d89c9;
  /* 设置滚动条 thumb 颜色 */
}

::v-deep .el-select--large .el-select__wrapper {
  min-height: 32rem;
}

// xmm修改 2025 02 12  修改表格样式
::v-deep .el-table th {
  color: #ffb30f;
  /* 修改表头字体颜色 */
  font-weight: bold;
  /* 可选：加粗字体 */
}

/* 自定义高亮样式 */
::v-deep .el-table__body tr.current-row>td {
  background-color: #5e430a;
  /* 高亮背景色 */
}

// xmm修改 2025-02-13
::v-deep .el-dialog {
  // width: 850px;
  height: 350px;
  background: rgb(0, 0, 0) !important;
  box-shadow: rgb(255, 179, 15) 0px 0px 20px inset !important;
  border: 1px solid rgba(255, 179, 15, 0.3) !important;
  border-radius: 10px !important;
}

::v-deep .el-button+.el-button {
  background-color: rgba(255, 179, 15, 0.3) !important;
  border: 1px solid rgba(232, 157, 6, 0.8) !important;
}

// xmm修改 2025 02 24  修改表格样式
::v-deep .el-table td.el-table__cell div {
  --un-text-opacity: 1;
  color: rgba(227, 216, 183, var(--un-text-opacity));

}

// 弹窗
::v-deep .el-dialog__body {
  width: 100%;
  height: 70%;
  display: flex;
  justify-content: center;
  align-items: center;
}

// xmm 修改input边框颜色 2025-03-04
::v-deep .el-input__wrapper {
  --el-input-focus-border-color: rgba(232, 157, 6, 0.8) !important;
}

// xmm 修改select边框颜色 2025-03-05
::v-deep .el-select__wrapper.is-focused {
  box-shadow: 0 0 0 1px rgba(232, 157, 6, 0.8) !important;
}
</style>