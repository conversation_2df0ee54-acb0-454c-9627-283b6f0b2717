<template>
  <div class="box statementBox">
    <!-- 头部分装 -->
    <encapsulationTable>
      <template #title> 报警告警 </template>


      


      <!-- 右边 -->
      <!-- <template #right-01>
      <span mr-10px>组态名称</span>
      <el-input
        v-model="roleName"
        placeholder="请输入"
        style="width: 250rem"
      ></el-input>
    </template>

    <template #right-03>
      <p class="bullue" @click="search">
        <img class="img" src="../../img/search.png" alt="" /> 搜索
      </p>
    </template>
    <template #right-04>
      <p class="bullue" @click="reset">
        <img class="img" src="../../img/reset.png" alt="" />重置
      </p>
    </template> -->
      <template #table>
        <el-table ref="multipleTableRef" :data="tableData" highlight-current-row :cell-style="{ textAlign: 'center' }"
          :header-cell-style="{ 'text-align': 'center' }" @selection-change="handleSelectionChange"
          style="overflow: auto; height: 90%">
          <!-- <el-table-column type="selection" width="55" /> -->
          <el-table-column type="index" width="80" label="序号" />
          <el-table-column prop="Name" label="名称" />
          <el-table-column prop="Msg" label="报警内容" />
           <el-table-column prop="AlarmValue" label="报警内容" />
          <el-table-column prop="Solve" label="处理内容" />
          <el-table-column prop="StartTime" label="时间" />

          <el-table-column label="操作" width="200px">
            <template #default="scope">
              <div style="display: flex">
                <div class="bullue1" @click="updateC(scope.row)">处理</div>
                <div class="userRde" @click="del(scope.row)">删除</div>
              </div>
            </template>
          </el-table-column>


        </el-table>
      </template>
      <template #pages>
        <el-pagination :page-sizes="[5, 10, 15, 20, 25]" layout="total, sizes, prev, pager, next, jumper" :total="total"
          @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </template>
    </encapsulationTable>


    <!-- 弹框 -->
    <el-dialog v-model="dialogShow" :title="dialogTitle">

      <div v-if="dialogTitle == '新增' || dialogTitle == '编辑'" style="width: 84%">
        <el-form :label-position="right" :model="alarmInit" ref="formRef">

          <el-form-item label="处理内容" prop="Solve">
            <el-input v-model="alarmInit.Solve" placeholder="请输入" clearable />
          </el-form-item>

        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogShow = false">取消</el-button>
          <el-button v-if="dialogTitle == '新增'" type="primary" @click="addList">
            添加
          </el-button>
          <el-button v-if="dialogTitle == '编辑'" type="primary" @click="editEdit">
            提交
          </el-button>

        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>

import encapsulationTable from "../../components/table.vue";
import {
  ApalarmList, ApialarmPageList, ApialarmDel, ApialarmUpdate
} from "../../../../https/encapsulation/alarm";
import { ElMessage, ElMessageBox } from 'element-plus';
// 引入样式
import "../../css/index.scss";
import { onMounted, ref, reactive } from 'vue';
import "../../css/el-pagination.scss" //xmm

// 弹框名字
const dialogTitle = ref("");
const dialogShow = ref(false);
onMounted(() => {
  alarmList()

})


const alarmInit = reactive({
  Solve: '',
  Id: "",

})



const tableData = ref([])
const pageIndex = ref(1);
const pageSize = ref(10);
const total = ref();

//告警列表数据
const alarmList = () => {

  ApialarmPageList(pageSize.value, pageIndex.value).then((res) => {
    tableData.value = res.data.Result;
    total.value = res.data.PageInfo.Total;
  })
}

const formRef = ref(null);
//修改
const updateC = async (row) => {
  dialogTitle.value = "编辑";
  dialogShow.value = true;
  alarmInit.Id = row.ID;
  alarmInit.Solve = row.Solve;

}

const editEdit = async () => {
  ApialarmUpdate(alarmInit).then((res) => {
    if (res.Status == 0) {
      ElMessage({
        type: "success",
        grouping: true,
        message: "成功",
      });
      dialogShow.value = false;

      alarmList();

    } else if (res.Status == 3) {
      ElMessage({
        message: "修改失败",
        type: "warning",

      });

    }
  });

}
// 删除
const del = (row) => {
  ElMessageBox.confirm("此操作将永久删除, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      ApialarmDel(row.ID).then((res) => {
        if (res.data.Status == 0) {
          ApalarmList();
          Tips();
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "已取消",
      });
    });
};

// 成功提示
const Tips = () => {
  ElMessage({
    type: "success",
    message: "删除成功",
  });
};
// 分页
const handleSizeChange = (val) => {
  pageSize.value = val;
  alarmList();
};
const handleCurrentChange = (val) => {
  pageIndex.value = val;
  alarmList();
};
</script>
<style lang="scss" scoped>
.box {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  color: #fff;
}

.underline {
  cursor: pointer;
}

.underline:hover {
  font-size: 15px;
  color: #315efb;
}

.el-upload__tip {
  color: #fff;
}

// .userBlue{
//   width: 67px;
//   height: 28px;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   border: 1px solid #40f4f1;
//   color: #40f4f1;
//   cursor: pointer;
//   margin: auto;
// }
.userRde {
  width: 86px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.editB {

  width: 100px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #40f4f1;
  color: #fff;
  cursor: pointer;
  border-radius: 16px;
  margin: auto;
}

// .userBlue {

// width: 67px;
// height: 28px;
// display: flex;
// align-items: center;
// justify-content: center;
// border: 1px solid #40f4f1;
// color: #fff;
// cursor: pointer;
// border-radius: 16px;
// margin: auto;
// img{
//   width: 20px;
//   height: 15px;
//  }
// }

// xmm修改 2025 02 12  修改表格样式
::v-deep .el-table th {
  color: #ffb30f;
  /* 修改表头字体颜色 */
  font-weight: bold;
  /* 可选：加粗字体 */
}

/* 自定义高亮样式 */
::v-deep .el-table__body tr.current-row>td {
  background-color: #5e430a;
  /* 高亮背景色 */
}

// xmm修改 2025-02-13
::v-deep .el-dialog {
  width: 28vw;
  height: 22vh;
  background: rgb(0, 0, 0) !important;
  box-shadow: rgb(255, 179, 15) 0px 0px 20px inset !important;
  border: 1px solid rgba(255, 179, 15, 0.3) !important;
  border-radius: 10px !important;
  top: 12%;
}

::v-deep .el-button+.el-button {
  background-color: rgba(255, 179, 15, 0.3) !important;
  border: 1px solid rgba(232, 157, 6, 0.8) !important;
}

// xmm修改2025-02-14
::v-deep .el-form-item__label {
  color: #fff !important;
}

// xmm修改 2025 02 24  修改表格样式
::v-deep .el-table td.el-table__cell div {
  --un-text-opacity: 1;
  color: rgba(227, 216, 183, var(--un-text-opacity));

}

// 弹窗
::v-deep .el-dialog__body {
  width: 100%;
  height: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

// xmm 修改input边框颜色 2025-03-04
::v-deep .el-input__wrapper {
  --el-input-focus-border-color: rgba(232, 157, 6, 0.8) !important;
}
</style>