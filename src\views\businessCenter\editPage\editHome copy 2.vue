<template>
  <!-- 三维模型 -->
  <!-- <div ref="sceneDiv" class="canvasBox"></div> -->

  <div class="edit_Home" @mouseup="handleMouseup">
    <!-- 顶部 -->
    <div class="Home_top top-110px">
      <div style="height: 100%;display: flex; flex-direction: row; align-items: center;">
        <div class="top_left">
        <p class="main-icon">
          <img class="w-25px h-20px" src="../assets/edit_main.png" alt="" />
        </p>
        <p class="back-icon">
          <img class="w-25px h-20px" src="../assets/back_icon.png" alt="" />
        </p>
        <p class="next-icon">
          <img class="w-25px h-20px" src="../assets/next_icon.png" alt="" />
        </p>
        <p class="save-icon">
          <img
            class="w-30px h-30px"
            src="../assets/img/save-icon.png"
            alt=""
          />保存
        </p>
      </div>
      <div class="left-700px position-absolute" style="color: #fff">
        组件配置-{{ maincenter }}
      </div>
      </div>
      <div class="right-35px">
        <p class="save-icon">
          <img
            class="w-30px h-30px"
            src="../assets/img/save-icon.png"
            alt=""
          />预览
        </p>
      </div>
    </div>
    <!-- 顶部结束 -->
    <!-- 编辑布局开始 -->
    <div class="edit_Content">
      <!-- 组件开始 -->
      <div class="ele_left">
        <div class="h-40px show1 w-full">
          组件
          <!-- <img class="w-20px h-20px ml-20px" src="../assets/tableimg.png"> -->
        </div>
        <!-- 看板开始 -->
        <div class="left_kb">
          <div class="kanban">看板</div>
          <!-- 组件看板动态 -->
          <div class="zuj_edit">
            <div class="zuj_edit_ul">
              <!--  -->
              <div v-for="item, index in comsData" :key="index" class="zuj_list">
                 <!-- 看板标题 -->
              <div class="kb_title" >
                <div class="circle"></div>
                <div class="circle1"></div>
                <div class="circle2"></div>
                <div class="mt-7px ml-65px">{{item.label}}</div>
              </div>
               <!-- 看板标题end -->
                <!-- 组件图片start -->
               <div class="zuj-box" @mousedown="handleMousedown(item)">
                  <img :src="item.img" alt="" style="width: 100%;">
                </div>
             <!-- 组件图片end -->
              </div>
             
            </div>
          </div>
        </div>
      </div>
      <!-- 
  components: { MyComponent },组件结束 -->

      <!-- 图层开始 -->
      <div class="ele_drag">
        <div class="drag_tc w-full h-40px">图层</div>
        <div class="tc_img_box" :class="item.selected ? 'selected' : ''" v-for="(item, index) in components" :key="index" @click="selectLayer(index)">
          <img :src="item.img" alt="" style="width: 80px;">
          <span style="margin-left: 10px">{{item.name}}</span>
          <img :src="item.show ? require('@/assets/preview.png') : require('@/assets/no_preview.png')" alt="" style="width: 18px;margin-left: 10px" @click="changeShow(index)">
        </div>
      </div>
      <!-- 图层结束 -->

      <!-- 中间操作开始 -->
      <div class="edit_cz" id="editCz" @dragover.prevent @drop="handleDrop">
        <div v-for="(component, index) in components" :key="index">
          <component :is="component.type" :width="component.width" :height="component.height" :x="component.x" :y="component.y" />
        </div>
      </div>
      <!-- 中间操作结束 -->

      <!-- 右边数据操作开始 -->
      <div class="edit_board">
        <div v-if="selectCom.type">
          <div class="header">
            <div class="dingzhi">定制</div>
          </div>
          <div class="size_box">
            <div style="width: 50px;">尺寸</div>
            <div style="flex: 1; display: grid; grid-template-columns: 1fr 1fr;">
              <el-input type="number" v-model="selectCom.width" style="width: 120px; height: 30px;" />
            <el-input type="number" v-model="selectCom.height" style="width: 120px; height: 30px;" />
            </div>
          </div>
        </div>
        <div class="floor floor-1" v-if="selectCom.type">
          <div class="title">1#风机</div>
          <div class="floor-top">
            <div class="floor-left">
              <div>基础</div>
            </div>
            <div class="floor-right">
              <div>
                <el-input value="1#风机" style="width: 120px; height: 30px;" disabled/>
                <div style="color: #807a6f">标题</div>
              </div>
              <div>
                <!-- <el-input value="1#风机" style="width: 120px; height: 30px;"/> -->
                <el-date-picker
                v-model="selectCom.com1.time"
                  value-format="YYYY-MM-DD"
                  type="date"
                  placeholder="选择时间"
                  style="width: 120px; height: 30px;"
                />
                <div style="color: #807a6f">时间</div>
              </div>
              <div>
                <el-select
                  v-model="selectCom.com1.status"
                  placeholder="选择状态"
                  style="width: 120px; height: 30px"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <div style="color: #807a6f">状态</div>
              </div>
              <div>
                <!-- <el-input value="1#风机" style="width: 120px; height: 30px;"/> -->
                <el-button  style="width: 120px; height: 30px;" @click="chooseStats(1)">选择属性</el-button>
                <div style="color: #807a6f">属性</div>
              </div>
            </div>
          </div>
          <div class="floor-bottom">
            <div class="floor-left">
              <div>属性</div>
            </div>
            <div class="floor-right">
              <div v-for="item in selectCom.com1.stats" :key="item.id">
                <el-button style="margin: 4px; width: 90%;" type="warning" @click="openUpdateStats(1, item.id)">修改点位</el-button>
                <div style="color: #807a6f">{{item.text}}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="floor" v-if="selectCom.type">
          <div class="title">2#风机</div>
          <div class="floor-top">
            <div class="floor-left">
              <div>基础</div>
            </div>
            <div class="floor-right">
              <div>
                <el-input value="2#风机" style="width: 120px; height: 30px;" disabled/>
                <div style="color: #807a6f">标题</div>
              </div>
              <div>
                <!-- <el-input value="1#风机" style="width: 120px; height: 30px;"/> -->
                <el-date-picker
                v-model="selectCom.com2.time"
                value-format="YYYY-MM-DD"
                  type="date"
                  placeholder="选择时间"
                  style="width: 120px; height: 30px;"
                />
                <div style="color: #807a6f">时间</div>
              </div>
              <div>
                <el-select
                v-model="selectCom.com2.status"
                  placeholder="选择状态"
                  style="width: 120px; height: 30px"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <div style="color: #807a6f">状态</div>
              </div>
              <div>
                <!-- <el-input value="1#风机" style="width: 120px; height: 30px;"/> -->
                <el-button  style="width: 120px; height: 30px;" @click="chooseStats(2)">选择属性</el-button>
                <div style="color: #807a6f">属性</div>
              </div>
            </div>
          </div>
          <div class="floor-bottom">
            <div class="floor-left">
              <div>属性</div>
            </div>
            <div class="floor-right">
              <div v-for="item in selectCom.com2.stats" :key="item.id">
                <el-button style="margin: 4px; width: 90%;" type="warning" @click="openUpdateStats(2, item.id)">修改点位</el-button>
                <div style="color: #807a6f">{{item.text}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 右边数据操作结束 -->
    </div>
    <!-- 编辑布局结束 -->

    <!-- 修改属性弹窗 -->
    <el-dialog v-model="dialogTableVisible" title="选择属性" width="800">
      <div style="display: flex; flex-direction: row;flex-wrap: wrap;">
        <el-checkbox-group v-model="currentStats" size="small">
          <el-checkbox v-for="item in statsList" :key="item.id" style="margin: 10px" :value="item.id" :label="item.text" border />
        </el-checkbox-group>
        <div style="width: 100%;display: flex; flex-direction: row; justify-content: flex-end">
          <el-button type="primary" @click="submitStats">确定</el-button>
        </div>
      </div>
    </el-dialog>


    <el-dialog v-model="dialogInputVisible" title="选择属性" width="300">
      <div style="display: flex; flex-direction: row;flex-wrap: wrap;">
        <el-input type="number" v-model="xxx" @input="changeStatsValue" />
        <div style="width: 100%;display: flex; flex-direction: row; justify-content: flex-end;margin-top:10px">
          <el-button type="primary" @click="dialogInputVisible = fase">确定</el-button>
        </div>
      </div>
    </el-dialog>
     <!-- 修改属性弹窗end -->
  </div>
</template>


<script  setup>
  import DongLiJianCe from '../components/DongLiJIanCe.vue';
  import GuZhangZhenDuan from '../components/GuZhangZhenDuan.vue';
  import WenDuJianCe from '../components/WenDuJianCe.vue';
  import KongZhiMianBan from '../components/KongZhiMianBan.vue';
  import ShiPinJianKong from '../components/ShiPinJianKong.vue';
  // import dljc_temp from "../assets/img/dljc_temp.png";
  import dljcTemp from '../assets/img/dljc_temp.png';
  import gzzdTemp from '../assets/img/gzzd_temp.png';
  import  wdjcTemp   from '../assets/img/wdjc_temp.png';
  import kzmbTemp  from '../assets/img/kzmb_temp.png';
  import spTemp   from '../assets/img/sp_temp.png'
 
  import { defineComponent, ref } from 'vue';
 
  
  const maincenter = 111;
  const components = ref([])
  const currentComponent = ref(null)

  const selectCom = ref({})

const dialogTableVisible = ref(false) //对话框状态
const dialogInputVisible = ref(false) //输入框状态

const dragging = ref(false) //拖动状态

const currentStats = ref([]) //当前选中的属性
//拖动起始状态
const startX = ref(0) 
const startY = ref(0)
const comStartX = ref(0)
const comStartY = ref(0)
const currentIndex = ref(0) //当前拖动的组件序号
const currentStatsId = ref(0) //当前属性id
const currentStatsNo = ref(1) //当前属性编号

//属性数据（后端获取）
const statsList = [
  {id: 1, text: '检修'},
{id: 2, text: '手动'},
{id: 3, text: '液压站油频'},
{id: 4, text: '上风门开运行'},
{id: 5, text: '下风门开运行'},
{id: 6, text: '中风门开运行'},
{id: 7, text: '单控选择'},
{id: 8, text: '复位'}]

const statsForm = ref({}) //修改后的属性

const facilityNo = ref(1) //当前选择属性的风机编号

  const xxx = ref(0)
  //状态
  const options = [
    {label: '停运', value: 0},
    {label: '正常', value: 1}
  ]
// const chooseStatsList = ref([]) //已选择属性



  const comsData = [
    {
      id:1,
      label:'动力监测',
      img: dljcTemp,
      width: 100, 
      height: 140, 
      type: DongLiJianCe
    },
    {
      id:2,
      label:'故障诊断',
      img: gzzdTemp,
       width: 100, 
       height: 140, 
       type: GuZhangZhenDuan
    },
    {
      id:3,
      label:'温度检测',
      img: wdjcTemp,
       width: 100, 
       height: 140,
        type: WenDuJianCe
    },
    {
      id:4,
      label:'控制面板',
      img: kzmbTemp,
       width: 100, 
       height: 140,
        type: KongZhiMianBan
    },
    {
      id:5,
      label:'视频监控',
      img: spTemp,
       width: 100, 
       height: 140,
       type: ShiPinJianKong
    }
  ]

    /**
     * 深度复制
     */
     const deepCopy = (obj) => {
    if (typeof obj !== 'object' || obj === null) {
        return obj; // 如果不是对象，则直接返回
    }

    let copy;
    if (Array.isArray(obj)) {
        copy = []; // 复制数组
        for (let i = 0; i < obj.length; i++) {
            copy[i] = deepCopy(obj[i]); // 递归复制每个元素
        }
    } else {
        copy = {}; // 复制对象
        for (let key in obj) {
            if (obj.hasOwnProperty(key)) {
                copy[key] = deepCopy(obj[key]); // 递归复制每个属性
            }
        }
    }
    return copy;
}

  /**
   * 点击组件时获取组件信息
   */
  const handleMousedown = (item) => {
    currentComponent.value = item
  }

  /**
   * 拖动后创建组件
   */
   const handleDrop = (e) => {
    if(currentComponent.value == null) {
      return

    }
    console.log(currentComponent.value);
    
    components.value = components.value.filter((row) => {
      return row.type != currentComponent.value.type
    })
    
    let data = {
      img: currentComponent.value.img,
      x: e.offsetX,
      y: e.offsetY,
      width: currentComponent.value.width,
      height: currentComponent.value.height,
      type: currentComponent.value.type,
      name: currentComponent.value.name,
      show: true,
      com1: {stats: []},
      com2: {stats: []}
    }
    components.value.push(data)
    selectLayer(components.value.length - 1)
    currentComponent.value = null

  }


  /**
   * 选中图层
   */
   const selectLayer = (index) => {
    components.value.map((row, i) => {
      if(i === index) {
        row.selected = true
        selectCom.value = row
      }else {
        row.selected = false
      }
    })
  }

    /**
   * 显示或隐藏图层
   */
   const changeShow = (index) => {
    components.value[index].show = !components.value[index].show
  }

    /**
   * 在组件上按下鼠标
   */
   const hanldleMouseDown = (e, index) => {
    selectLayer(index)
    dragging.value = true
    currentIndex.value = index
    startX.value = e.clientX
    startY.value = e.clientY
    comStartX.value = components.value[index].x
    comStartY.value = components.value[index].y
    
  }

   /**
   * 拖动组件
   */
   const handleMousemove = (e) => {    
    if(dragging.value) {
    console.log(dragging.value);

      const offsetX = e.clientX - startX.value
      const offsetY = e.clientY - startY.value
      components.value[currentIndex.value].x = comStartX.value + offsetX
      components.value[currentIndex.value].y = comStartY.value + offsetY
    }
  }

    /**
   * 抬起鼠标
   */
   const handleMouseup = () => {
    dragging.value = false
  }

  const handleChange = (value) => {
  console.log(value)
}

/**
 * 点击选择属性
 */
 const chooseStats = (no) => {
  dialogTableVisible.value = true
  facilityNo.value = no
  currentStats.value = no === 1 ? selectCom.value.com1.stats.map((row) => row.id) : selectCom.value.com2.stats.map((row) => row.id)
}

/**
 * 选择属性
 */
 const submitStats = () => {
  dialogTableVisible.value = false
  let data = statsList.filter((row) => {
    return currentStats.value.includes(row.id)
  })
  
  if(facilityNo.value === 1) {
    selectCom.value.com1.stats = deepCopy(data)
  } else {
    selectCom.value.com2.stats = deepCopy(data)
  }
}

const openUpdateStats = (statsNo, id) => {
  currentStatsNo.value = statsNo
  currentStatsId.value = id
  dialogInputVisible.value = true
  console.log(selectCom.value);
  if(statsNo == 1) {
    selectCom.value.com1.stats.map(row => {
      console.log('row', row);
      if(row.id == id) {
        xxx.value = row.value || 0
      }
    })
  }else {
    selectCom.value.com2.stats.map(row => {
      if(row.id == id) {
        console.log('row', row);
        xxx.value = row.value || 0
      }
    })
  }
}

const changeStatsValue = (e) => {
  if(currentStatsNo.value == 1) {
    console.log(selectCom.value);
    selectCom.value.com1.stats.map(row => {
      if(row.id == currentStatsId.value) {
        row.value = e
      }
    })
  }else {
    selectCom.value.com2.stats.map(row => {
      if(row.id == currentStatsId.value) {
        row.value = e
      }
    })
  }
}
</script>

<style scoped lang="scss">
body,
div,
html {
  font-size: 14px;
}

.edit_Home {
  // width: 100vw;
  min-width: 1000px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.Home_top {
  height: 70px;
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  position: relative;
  align-items: center;
  background-color: #031627;
  .top_left {
    display: flex;
    flex-direction: row;
    margin-left: 50px;

    img {
      padding-left: 8px;
      padding-top: 4px;
    }
  }
  .main-icon {
    background-color: #ffffff;
    border-radius: 4px;
    width: 35px;
    height: 27px;
  }

  .back-icon {
    width: 40px;
    height: 30px;
    background-color: #79bbff;
    margin-left: 20px;
    border-radius: 4px;
  }
  .next-icon {
    width: 40px;
    height: 30px;
    background-color: #409eff;
    margin-left: 10px;
    border-radius: 4px;
  }
  .save-icon {
    width: 70px;
    height: 30px;
    background-color: #fff;
    margin-left: 20px;
    border-radius: 4px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

// 编辑内容
.edit_Content {
  background-color: #070606;
  width: 100%;
  //  height: 95%;
  flex: 1;
  padding-left: 1%;
  padding-right: 1%;
  margin-top: 120px;
  display: flex;
  flex-direction: row;
  color: #ffffff;

  .ele_left {
    width: 20%;
    min-width: 200px;
    height: 100%;
  }
  .left_kb {
    display: flex;
    width: 100%;
    height: calc(100% - 62px);
    

    .kanban {
      width: 70px;
      height: 50px;
      line-height: 50px;
      margin-top: 2px;
      background-color: #232324;
      text-align: center;
    }
  }

  .show1 {
    background-color: #232324;
    padding-left: 10px;
    line-height: 40px;

    img {
      margin-left: 2px;
      padding-top: 5px;
    }
  }

  .zuj_edit {
    display: flex;
    flex-direction: column;
    width: 95%;
    height: 100%;
    margin-top: 1px;
    margin-left: 5px;
   
    

    .zuj_edit_ul {
      width: 95%;
      height: 100%;
      margin-left: 5px;
      overflow-y: scroll;
  // 滚动条的颜色的大小
  &::-webkit-scrollbar {
    width: 2px;
  }

  &::-webkit-scrollbar-track {
    background-color: #232324;
  }

  &::-webkit-scrollbar-thumb {
    background: #232324;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }
 
      .zuj_list{
        width: 100%;
        height: auto;
        background-color: #232324;
        margin-top: 10px;
      }
      .zuj-box {
        width: 95%;
        padding: 6px 10px;
        // background-color: #232324;
        border-radius: 10px;
        margin-top: 5px;
      }
.kb_title{
  display: flex;
  flex-direction: row;
}
      .circle {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color:#FC625D;
        margin-left: 5px;
        margin-top: 10px;
      }

      .circle1 {
        @extend .circle;
        background-color: #FCBC40;
      }
      .circle2 {
        @extend .circle;
        background-color: #34C749;
      }
    }
  }

  // 图层
  .ele_drag {
    width: 250px;
    height: 98%;
    margin-left: 5px;
    background-color: #232324;
    .drag_tc {
      background-color: #2a2a2b;
      width: 100%;
      padding-left: 10px;
      line-height: 40px;
      // padding-top: 15px;
    }
    .tc_img_box {
        width: 100%;
        padding: 5px 10px;
        display: flex;
        flex-direction: row;
        align-items: center;
        cursor: default;
      }
    .selected {
      border: 1px solid #3c846c;
      background-color: #30584c;
    }
  }
}
.edit_cz {
  // width: 52%;
  flex: 1;
  height: 98%;
 // border: 1px solid #79bbff;
  margin-left: 10px;
  position: relative;
  overflow: hidden;
  .component {
    cursor: default;
  }
  .selected_com {
    background-color: #3c846c3a;
    border: 1px solid #3c846c;
  }
}

.edit_board {
  width: 18%;
  height: 98%;
  border: 1px solid #fff;
  margin-left: 10px;
  .header {
    width: 100%;
    height: 40px;
    line-height: 40px;
    border-bottom: 2px solid #fff;
    padding-left: 10px;
    .dingzhi {
      width: 50px;
      height: 100%;
      text-align: center;
      // border-bottom: 2px solid #409eff;
    }
  }

  .size_box {
    display: flex;
    height: 60px;
    margin: 10px 6px;
    flex-direction: row;
    align-items: center;
    border-bottom: 2px solid #606165;
  }
  .floor {
    width: 100%;
    padding: 10px 6px 20px;
    .title {
      margin-bottom: 20px;
    }
    .floor-top, .floor-bottom {
      display: flex;
      flex-direction: row;
    }
    .floor-left {
      width: 50px;
    }
    .floor-right {
      flex: 1;
      display: grid;
      grid-template-rows: auto;
      grid-template-columns: 1fr 1fr;
      & > div {
        margin-bottom: 10px;
      }
    }
  }
  .floor-1 {
    border-bottom: 2px solid #606165;
  }
}

.box5-tab1 {
  display: flex;
  flex-direction: column;
  height: 300px;
  overflow-y: auto;

  // 滚动条的颜色的大小
  &::-webkit-scrollbar {
    width: 2px;
  }

  &::-webkit-scrollbar-track {
    background-color: #0f3450;
  }

  &::-webkit-scrollbar-thumb {
    background: #0f3450;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }

  .item-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #fff;
    padding: 10px 0;
  }
}

.power-wrapper {
  transition: transform 0.6s ease-in-out;
}

.tep-wrapper {
  transition: transform 0.6s ease-in-out;
}

.fault_wrapper {
  transition: transform 0.6s ease-in-out;
}
</style>