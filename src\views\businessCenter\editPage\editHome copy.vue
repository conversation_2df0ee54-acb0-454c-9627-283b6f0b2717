<template>
  <!-- 三维模型 -->
  <!-- <div ref="sceneDiv" class="canvasBox"></div> -->

  <div class="edit_Home">
    <!-- 顶部 -->
    <div class="Home_top">
      <div style="height: 100%;display: flex; flex-direction: row; align-items: center;">
        <div class="top_left">
        <p class="main-icon">
          <img class="w-25px h-20px" src="../assets/edit_main.png" alt="" />
        </p>
        <p class="back-icon">
          <img class="w-25px h-20px" src="../assets/back_icon.png" alt="" />
        </p>
        <p class="next-icon">
          <img class="w-25px h-20px" src="../assets/next_icon.png" alt="" />
        </p>
        <p class="save-icon">
          <img
            class="w-30px h-30px"
            src="../assets/img/save-icon.png"
            alt=""
          />保存
        </p>
      </div>
      <div class="left-300px" style="color: #fff">
        组件配置-{{ maincenter }}
      </div>
      </div>
      <div class="right-25px">
        <p class="save-icon">
          <img
            class="w-30px h-30px"
            src="../assets/img/save-icon.png"
            alt=""
          />预览
        </p>
      </div>
    </div>
    <!-- 顶部结束 -->
    <!-- 编辑布局开始 -->
    <div class="edit_Content">
      <!-- 组件开始 -->
      <div class="ele_left">
        <div class="h-40px show1 w-full">
          组件
          <!-- <img class="w-20px h-20px ml-20px" src="../assets/tableimg.png"> -->
        </div>
        <!-- 看板开始 -->
        <div class="left_kb">
          <div class="kanban">看板</div>
          <!-- 组件看板动态 -->
          <div class="zuj_edit">
            <div class="zuj_edit_ul">
              <div v-for="item, index in comsData" :key="index">
                <div class="zuj-box" @mousedown="handleMousedown(item)">
                  <img :src="item.img" alt="" style="width: 100%;">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 
  components: { MyComponent },组件结束 -->

      <!-- 图层开始 -->
      <div class="ele_drag">
        <div class="drag_tc w-full h-40px">图层</div>
      </div>
      <!-- 图层结束 -->

      <!-- 中间操作开始 -->
      <div class="edit_cz" id="editCz" @dragover.prevent @drop="handleDrop">
        <div v-for="(component, index) in components" :key="index">
          <component :is="component.type" :width="component.width" :height="component.height" :x="component.x" :y="component.y" />
        </div>
      </div>
      <!-- 中间操作结束 -->

      <!-- 右边数据操作开始 -->
      <div class="edit_board"></div>
      <!-- 右边数据操作结束 -->
    </div>
    <!-- 编辑布局结束 -->
  </div>
</template>


<script  setup>
  import DongLiJianCe from '../components/DongLiJIanCe.vue';
  import GuZhangZhenDuan from '../components/GuZhangZhenDuan.vue';
  import WenDuJianCe from '../components/WenDuJianCe.vue';
  import KongZhiMianBan from '../components/KongZhiMianBan.vue';
  import ShiPinJianKong from '../components/ShiPinJianKong.vue';
  // import dljc_temp from "../assets/img/dljc_temp.png";
  import dljcTemp from '../assets/img/dljc_temp.png';
  import gzzdTemp from '../assets/img/gzzd_temp.png';
  import  wdjcTemp   from '../assets/img/wdjc_temp.png';
  import kzmbTemp  from '../assets/img/kzmb_temp.png';
  import spTemp   from '../assets/img/sp_temp.png'
 
  import { defineComponent, ref } from 'vue';
 
  
  const maincenter = 111;
  // const components = ref<any>([])
  const currentComponent = ref({})

  const comsData = [
    {
      img: dljcTemp,
      width: 100, 
      height: 200, 
      type: DongLiJianCe
    },
    {
      img: gzzdTemp,
       width: 100, 
       height: 200, 
       type: GuZhangZhenDuan
    },
    {
      img: wdjcTemp,
       width: 100, 
       height: 200,
        type: WenDuJianCe
    },
    {
      img: kzmbTemp,
       width: 100, 
       height: 200,
        type: KongZhiMianBan
    },
    {
      img: spTemp,
       width: 100, 
       height: 200,
       type: ShiPinJianKong
    }
  ]

  /**
   * 点击组件时获取组件信息
   */
  const handleMousedown = (item) => {
    currentComponent.value = item
  }

  /**
   * 拖动后创建组件
   */
  // const handleDrop = (e: DragEvent) => {
  //   components.value = components.value.filter((row: any) => {
  //     return row.type != currentComponent.value.type
  //   })
  //   let data: any = currentComponent.value
    
  //   console.log(components.value);
    
  //   data.x = e.offsetX
  //   data.y = e.offsetY
  //   components.value.push(data)
    
  // }
</script>

<style scoped lang="scss">
body,
div,
html {
  font-size: 14px;
}

.edit_Home {
  // width: 100vw;
  min-width: 1000px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.Home_top {
  height: 70px;
  width: 100%;

  display: flex;
  flex-direction: row;
  justify-content: space-between;
  position: relative;
  align-items: center;
  background-color: #031627;
  .top_left {
    display: flex;
    flex-direction: row;
    margin-left: 50px;

    img {
      padding-left: 8px;
      padding-top: 4px;
    }
  }
  .main-icon {
    background-color: #ffffff;
    border-radius: 4px;
    width: 35px;
    height: 27px;
  }

  .back-icon {
    width: 40px;
    height: 30px;
    background-color: #79bbff;
    margin-left: 20px;
    border-radius: 4px;
  }
  .next-icon {
    width: 40px;
    height: 30px;
    background-color: #409eff;
    margin-left: 10px;
    border-radius: 4px;
  }
  .save-icon {
    width: 70px;
    height: 30px;
    background-color: #fff;
    margin-left: 20px;
    border-radius: 4px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

// 编辑内容
.edit_Content {
  background-color: #070606;
  width: 100%;
  // height: 80%;
  flex: 1;
  padding-left: 1%;
  padding-right: 1%;
  // margin-top: 120px;
  // margin-left: 30px;
  // opacity: 0.7;
  display: flex;
  flex-direction: row;
  color: #ffffff;

  .ele_left {
    width: 15%;
    min-width: 200px;
    height: 100%;
  }
  .left_kb {
    display: flex;
    width: 100%;
    height: calc(100% - 62px);
    // overflow-y: auto;

    .kanban {
      width: 70px;
      height: 50px;
      line-height: 50px;
      margin-top: 2px;
      background-color: #232324;
      text-align: center;
    }
  }

  .show1 {
    background-color: #232324;
    padding-left: 10px;
    line-height: 40px;

    img {
      margin-left: 2px;
      padding-top: 5px;
    }
  }

  .zuj_edit {
    display: flex;
    flex-direction: column;
    width: 95%;
    height: 100%;
    margin-top: 5px;
    margin-left: 5px;
    border: 1px solid red;
    

    .zuj_edit_ul {
      width: 95%;
      height: 100%;
      margin-left: 5px;
      overflow-y: scroll;
      .zuj-box {
        width: 100%;
        padding: 6px 10px;
        background-color: #232324;
        border-radius: 10px;
        margin-top: 10px;
      }
    }
  }

  // 图层
  .ele_drag {
    width: 250px;
    height: 98%;
    border: 1px solid pink;
    margin-left: 5px;

    .drag_tc {
      background-color: #2a2a2b;
      width: 100%;
      padding-left: 10px;
      line-height: 40px;
      // padding-top: 15px;
    }
  }
}
.edit_cz {
  // width: 52%;
  flex: 1;
  height: 98%;
  border: 1px solid #79bbff;
  margin-left: 10px;
  position: relative;
}

.edit_board {
  width: 18%;
  height: 98%;
  border: 1px solid #fff;
  margin-left: 10px;
}
.box5-tab1 {
  display: flex;
  flex-direction: column;
  height: 300px;
  overflow-y: auto;

  // 滚动条的颜色的大小
  &::-webkit-scrollbar {
    width: 2px;
  }

  &::-webkit-scrollbar-track {
    background-color: #0f3450;
  }

  &::-webkit-scrollbar-thumb {
    background: #0f3450;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }

  .item-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #fff;
    padding: 10px 0;
  }
}

.power-wrapper {
  transition: transform 0.6s ease-in-out;
}

.tep-wrapper {
  transition: transform 0.6s ease-in-out;
}

.fault_wrapper {
  transition: transform 0.6s ease-in-out;
}
</style>