<template>
  <div class="edit_Home " @mouseup="handleMouseup">
    <!-- 顶部 -->
    <div class="Home_top top-110px">
      <div style="
          height: 100%;
          display: flex;
          flex-direction: row;
          align-items: center;
        ">
        <div class="top_left">
          <p class="main-icon"  @click="fahHome">
            <img class="w-25px h-20px" src="../assets/edit_main.png" alt="" />
          </p>
          
          <p class="save-icon" @click.prevent="saveCom">
            <img class="w-30px h-30px" src="../assets/img/save-icon.png" alt="" />保存
          </p>
        </div>
        <div class="left-900px position-absolute" style="color: #fff">
          组件配置
        </div>
      </div>
      <div class="right-35px">
        <!-- <p class="save-icon">
          <img
            class="w-30px h-30px"
            src="../assets/img/save-icon.png"
            alt=""
          />预览
        </p> -->
      </div>
    </div>
    <!-- 顶部结束 -->

    <!-- 编辑布局开始 -->
    <div class="edit_Content">
      <!--  -->
      <!-- 看板 -->
      <ViewPanel :pannels="comsData" @choose="(e) => handleMousedown(e)" />
      <!-- 图层开始 -->
      <div class="ele_drag">
        <div class="drag_tc w-full h-40px">图层</div>
        <div v-for="(item, index) in components1" :key="item.id" :class="{ selected: item.selected }" class="tc_img_box"
          @click="selectLayer(index)">
          <img :src="item.img" alt="" style="width: 80px; height: 60px" />
          <span style="margin-left: 10px">{{ item.name }}</span>
          <!-- 隐藏or显示 -->
          <img :src="item.show ? preview : noPreview" alt="" style="width: 18px; margin-left: 10px"
            @click="changeShow(index)" />
        </div>
      </div>
      <!-- 图层结束 -->

      <!-- 中间操作开始 -->
      <!-- 刻度尺 -->
      <!-- <div id="app"> -->

      <!-- <canvas ref="canvaskd" width="2000" height="1000"></canvas> -->
      <!-- </div> -->
      <Ruler>
        <div class="edit_cz" id="editCz" ref="editCz">
          <div class="referSize" ref="referSizeRef"></div>
          <!-- 三维模型 -->
          <!-- <div
          class="resize"
          :style="{
            transform: `scale(${mainSize.cs})`,
          }"
        >
          <div ref="sceneDiv" class="canvasBox1" />
        </div> -->
          <div :style="{
            transform: `scale(${mainSize.cs})`,
          }" class="resize resizeDown" @dragover.prevent @drop="handleDrop" @mouseup.stop="handleMouseup"
            @mousemove.stop="handleMousemove">
            <div ref="sceneDiv" class="canvasBox1" />

            <!-- 三维模型结束 -->
            <!-- 画布开始 -->
            <CardComps v-model:components="components1" @mousedown="hanldleMouseDown" />
            <!-- 渲染config -->
            <!-- <div>
          <div v-for="item in config" :key="item.ID" class="config-item" :style="{left: item.X + 'px', top: item.Y + 'px', width: item.W + 'px', height: item.H + 'px'}">
            {{item.BoardName}}
          </div>
        </div> -->
          </div>
        </div>
      </Ruler>

      <!-- 右边数据操作开始 -->
      <div class="edit_board">
        <div v-if="selectCom.type">
          <div class="header">
            <div class="dingzhi">定制</div>
          </div>
          <div class="size_box">
            <div style="width: 50px">尺寸</div>
            <div style="flex: 1; display: grid; grid-template-columns: 1fr 1fr">
              <el-input type="number" v-model="selectCom.width" style="width: 100px; height: 30px; padding-left: 2px" />
              <el-input type="number" v-model="selectCom.height"
                style="width: 100px; height: 30px; padding-left: 2px" />
            </div>
          </div>
        </div>
        <!--定制尺寸结束  -->
        <!-- 配置 -->


        <template v-for="(state, index) in components1" :key="index">
          <template v-if="currentIndex === index">
            <!-- 视频页面配置 -->

            <VideoMonitor :cameras="selectCom.cameras" :options="cameraOptions" :config="selectCom.option"
              v-if="currentIndex === 3" />
            <template v-else>
              <!-- 其他页面配置 -->
              <WindMachine v-if="selectCom.com1" :options="{ title: '1#风机' }" :devices="devices[0]"
                v-model:stats="selectCom.com1.stats" v-model:status="selectCom.com1.status"
                :hasStatus="currentIndex === 0" />

              <WindMachine v-if="selectCom.com2" :options="{ title: '2#风机' }" :devices="devices[1]"
                v-model:stats="selectCom.com2.stats" v-model:status="selectCom.com2.status"
                :hasStatus="currentIndex === 0" />
            </template>
          </template>
        </template>
      </div>

    </div>
    <!-- 右边数据操作结束 -->
  </div>
  <!-- 编辑布局结束 -->

</template>


<script setup>
import DongLiJianCe from "../components/DongLiJIanCe.vue";
import GuZhangZhenDuan from "../components/GuZhangZhenDuan.vue";
import WenDuJianCe from "../components/WenDuJianCe.vue";
import KongZhiMianBan from "../components/KongZhiMianBan.vue";
import ShiPinJianKong from "../components/ShiPinJianKong.vue";
import HjingJIanCe from "../components/HjingJianCe.vue";
import ViewPanel from "../components/ViewPanel.vue";
import CardComps from "../components/CardComps.vue";
import WindMachine from "../components/WindMachine.vue";
import VideoMonitor from "../components/VideoMonitor.vue";
import basicTem from "../components/basicTem.vue";
import BasicTemp from "../components/BasicTemp.vue";

import dljcTemp from "../assets/img/dljc_temp.png?url";
import gzzdTemp from "../assets/img/gzzd_temp.png?url";
import wdjcTemp from "../assets/img/wdjc_temp.png?url";
import kzmbTemp from "../assets/img/kzmb_temp.png?url";
import spTemp from "../assets/img/sp_temp.png?url";
import hjTemp from "../assets/img/hjjc_temp.png?url";
import basicTemp from "../assets/img/basic_temp.png?url";
import noPreview from "../assets/no_preview.png?url";
import preview from "../assets/preview.png?url";
import { useThree } from "@/three/useThree";

// 刻度组件

import {
  defineComponent,
  ref,
  onMounted,
  watch,
  onBeforeUnmount,
  shallowRef,
  defineAsyncComponent,
  toRaw,
  reactive,
  markRaw,
} from "vue";
import { useRoute } from "vue-router";
import router from "@/router";
// 看板
import { ApBoardCode } from "../../../https/encapsulation/Board";
import {
  ApiPoint,
  apiUpdataConf,
  ApiConfigurationList,
} from "../../../https/encapsulation/Configuration";
import Ruler from "./ruler.vue";
// 接口
import { ApiMainFanList } from "@/https/encapsulation/MainFan";
import { configStore } from "@/store/config";
import { ElMessage } from "element-plus";
import { has } from "lodash";

// 绑定组件类
const components1 = ref([]);
const currentComponent = ref(null);

const selectCom = reactive({});

const dialogTableVisible = ref(false); //对话框状态
const dialogInputVisible = ref(false); //输入框状态

const dragging = ref(false); //拖动状态

const currentStats = ref([]); //当前选中的属性
//拖动起始状态
const startX = ref(0);
const startY = ref(0);
const comStartX = shallowRef(0);
const comStartY = shallowRef(0);
const currentIndex = ref(0); //当前拖动的组件序号
const currentStatsId = ref(0); //当前属性id
const currentStatsNo = ref(1); //当前属性编号

// //属性数据（后端获取）
// const statsList = [
//   { id: 1, text: "检修" },
//   { id: 2, text: "手动" },
//   { id: 3, text: "液压站油频" },
//   { id: 4, text: "上风门开运行" },
//   { id: 5, text: "下风门开运行" },
//   { id: 6, text: "中风门开运行" },
//   { id: 7, text: "单控选择" },
//   { id: 8, text: "复位" },
// ];

const statsForm = ref({}); //修改后的属性

const facilityNo = ref(1); //当前选择属性的风机编号

const store = configStore(); //初始化store
const config = ref([]);
const xxx = ref(0);

// 模型挂载
onMounted(() => {
  mountRender(sceneDiv.value);
  boList();
});

const { query } = useRoute();

watch(
  () => store.data,
  (newVal) => {
    // console.log('newValnewValnewValnewValnewVal',newVal);

    if (!newVal?.length) {
      return;
    }
    // console.log("newVal", newVal);
    const compsMap = [
      // 组件使用 shallowRef / markRaw 包裹
      { img: dljcTemp, type: shallowRef(DongLiJianCe), name: "动力监测" },
      { img: wdjcTemp, type: shallowRef(WenDuJianCe), name: "温度监测" },
      { img: kzmbTemp, type: shallowRef(KongZhiMianBan), name: "控制面板" },
      { img: spTemp, type: shallowRef(ShiPinJianKong), name: "视频监控" },
      { img: gzzdTemp, type: shallowRef(GuZhangZhenDuan), name: "故障诊断" },
      { img: hjTemp, type: shallowRef(HjingJIanCe), name: "环境监测-1" },
      { img: basicTemp, type: shallowRef(BasicTemp), name: "基本信息" },
    ];
    components1.value = [];

    console.log(components1.value, "222组件传值！！！！！");

    for (let k in newVal) {
      console.log(newVal, "#################");
      if (newVal[k].Type == query.type) {
        let data = newVal[k].Config;

        // 后端数据转换成我的格式
        components1.value = changeToMe(data, compsMap);
        console.log(components1.value, "666组件传值！！！！！");
        components1.value.forEach((ddd) => {
          let option = ddd.option;
          ddd.com1.stats = option.Fans[0]?.Keys || [];
          ddd.com2.stats = option.Fans[1]?.Keys || [];
        });
        break;
      }
    }
  },
  { deep: true, immediate: true }
);

watch(selectCom, (newVal) => {
  // 每次点击都会运行这个函数
  const idx = currentIndex.value;
  const comp = components1.value[idx];
  // 当定制尺寸变化时改变组件的尺寸
  comp.width = +newVal.width;
  comp.height = +newVal.height;
});
// 刻度尺
// function initCanvas() {
//       const canvas = this.$refs.canvaskd;
//       const ctx = canvas.getContext('2d');
//       this.drawChart(ctx);
//       this.drawRuler(ctx);
//     }
//    function drawChart(ctx) {
//       // 绘制示例图表代码
//     }
//    function drawRuler(ctx) {
//       // 绘制标尺代码
//     }

// 模型开始
const startCamera = [1, 100, 8];
const endCamera = [3, 12, 10];
const gltfGlobal = ref();
const restMark = ref(false); // 重置标识
const zntf = ref(false);
let modelInit = ref();
modelInit.value = useThree({ startCamera, endCamera });
const {
  mountRender,
  ani,
  stopRender,
  scene,
  camera,
  cameraControls,
  initModel,
  changeCurrentMesh,
  changeCurrentMeshByName,
  findMesh,
  restrictCameraVerticalDirection,
  matcapTexture,
  dbModelClick,
  executeMultipleAnimation,
  setMeshScale,
  executeAnimation,
  cameraAni,
  addAmbientLight,
} = modelInit.value;

scene.background = null;
const initModels = (res) => {
  //判断你要哪一个模型
  let glbUrl = "./model/tfj.glb";
  // 和加载后的操作
  // 加载模型
  initModel(glbUrl, [0, 0, 0], (gltf) => {
    if (gltf) {
      gltfGlobal.value = gltf;
      setMeshScale(gltf.scene, 55);
      // 需要进行贴图的物体名称
      const matName = ["TFJ_ZJS_02", "TFJ_ZJS_03", "TFJ_ZJS_01"];
      // 贴图函数 根据模型上物体的名称选择需要进行贴图的物体
      matcapTexture(gltf.scene, matName, "./textures/222.png");

      // if(){}  风机
      // 风机01风扇动画
      const {
        startMultipleAnimation,
        updateMultipleAnimation,
        stopMultipleAnimation,
      } = executeMultipleAnimation(gltf.scene, [
        gltf.animations[0],
        gltf.animations[1],
      ]);
      // 风机02风扇动画
      const {
        startMultipleAnimation: startAni2,
        updateMultipleAnimation: updateAni2,
        stopMultipleAnimation: stopAni2,
      } = executeMultipleAnimation(gltf.scene, [
        gltf.animations[2],
        gltf.animations[3],
      ]);

      // 初始化风机选中
      const mesh1 = findMesh(gltf.scene, "TFJ_ZJS_02");
      const mesh2 = findMesh(gltf.scene, "TFJ_ZJS_03");
      changeCurrentMesh(mesh1, { isFlyMesh: true });
      startMultipleAnimation();
      // 模型交互事件 双击模型 参数1:需要监测那些物体 就将哪些物体传进来 可以直接传gltf.scene 则所有物体都可被点击
      // dbModelClick([mesh1, mesh2], camera, (mesh) => {
      //   const { name } = mesh.object;
      //   fanActive.value = name == "TFJ_ZJS_02" ? 0 : 1;
      //   isShow.value = false;
      //   executeFanSelect();
      // });

      // 监听复位方法
      watch(restMark, (val) => {
        if (val) {
          executeFanSelect();
        }
        setTimeout(() => {
          restMark.value = false;
        }, 200);
      });

      // 渲染函数
      ani(function (...args) {
        const [time, camera, delta] = args;
        // updateAnimation(delta)
        // 更新动画
        updateMultipleAnimation(0.15);
        updateAni2(0.15);
      });
    }
  }).then((gltf) => {
    zntf.value = true;
  });
};

initModels();
//定时刷新系统通知，每个1分钟
const intervalId = ref(null);
//清除定时器，防止内存溢出
onBeforeUnmount(() => {
  clearInterval(intervalId.value);
});
const sceneDiv = shallowRef(null);
let comsData = ref({});
//获取看板列表
const boList = () => {
  ApBoardCode().then((res) => {
    if (res.Status === 0) {
      comsData.value = [
        {
          Name: "",
          DisplayUrl: "",
          width: "",
          height: "",
          type: "",
        },
      ];
      let obj = {};

      // for (let i = 0; i < comsData.value.length; i++) {
      for (let j = 0; j < res.Result.length; j++) {
        const target = res.Result[j];
        obj = {};

        obj.Name = target.Name;
        obj.width = 480;
        obj.height = 460;
        obj.DisplayUrl = `${res.Result[j].DisplayUrl}`;
        obj.ID = target.ID;
        obj.cameras = [];
        obj.BoardName = target.BoardName;
        console.log(obj, "obj");

        switch (target.Type) {
          case 1:
            obj.type = DongLiJianCe;
            break;
          case 2:
            obj.type = WenDuJianCe;
            break;
          case 3:
            obj.type = KongZhiMianBan;
            break;
          case 4:
            obj.type = ShiPinJianKong;
            break;
          case 5:
            obj.type = GuZhangZhenDuan;
            break;
          case 6:
            obj.type = BasicTemp;
            break;
          case 7:
            obj.type = HjingJIanCe;
            break;
        }

        // console.log( obj,'--');
        comsData.value.push(obj);
        // console.log(comsData.value, '#####看板列表')
      }
      // }
      // comsData.value = res.Result
    }
    console.log(res, "获取看板列表");
    console.log(comsData.value, "#####看板列表");
    comsData.value.forEach((v) => {
      const imgName = v.DisplayUrl.split("\\").at(-1);
      console.log(imgName);

      v.DisplayUrl = new URL(`../assets/img/${imgName}`, import.meta.url).href;
    });
  });
};
const fahHome = () => {
 
  router.push({ path: "/editPage" });
}
const devices = ref([]);

const cameraOptions = ref([]);

// 点击选择页面里的属性
ApiMainFanList().then((res) => {
  console.log("ApMainFanCode", res);

  const [No1, No2] = [[], []];
  for (const key in res.Result[0].Pointrtd) {
    let id = res.Result[0].Pointrtd[key].ID;
    res.Result[0].Pointrtd[key]["ID"] = res.Result[0].Pointrtd[key].KeyID;
    res.Result[0].Pointrtd[key]["KeyID"] = id;

    if (Object.prototype.hasOwnProperty.call(res.Result[0].Pointrtd, key)) {
      const element = res.Result[0].Pointrtd[key];
      // console.log('获取弹窗页面属性',element)

      if (key.endsWith("_1")) {
        No1.push(element);
      } else if (key.endsWith("_2")) {
        No2.push(element);
      }
    }
  }
  devices.value = [No1, No2];
  console.log(devices.value, "###########33");
  store.setDevicesMap([No1, No2]); // 存储设备给组件使用
  cameraOptions.value = res.Result[0].Fannerinfo.Cameras;
  store.setCameraMap(res.Result[0].Fannerinfo.Cameras); // 存储摄像机给 视频监控 使用
});

/**
 * 深度复制
 */
const deepCopy = (obj) => {
  if (typeof obj !== "object" || obj === null) {
    return obj; // 如果不是对象，则直接返回
  }

  let copy;
  if (Array.isArray(obj)) {
    copy = []; // 复制数组
    for (let i = 0; i < obj.length; i++) {
      copy[i] = deepCopy(obj[i]); // 递归复制每个元素
    }
  } else {
    copy = {}; // 复制对象
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        copy[key] = deepCopy(obj[key]); // 递归复制每个属性
      }
    }
  }
  return copy;
};

/**
 * 点击组件时获取组件信息
 */
const handleMousedown = (item) => {
  currentComponent.value = item;
};

/**
 * 拖动后创建组件
 */
const handleDrop = (e) => {
  const id = e.dataTransfer.getData("text");
  const item = { ...comsData.value.find((c) => c.ID === id) };

  // const ids = components1.value.map((row) => row.id);
  // if (ids.includes(id)) {
  //   return;
  // }

  components1.value.push(
    reactive({
      img: item.DisplayUrl,
      x: e.offsetX,
      y: e.offsetY,
      width: item.width,
      height: item.height,
      type: shallowRef(item.type),
      name: item.Name,
      show: true,
      com1: reactive({ stats: [], status: item.Status }),
      com2: reactive({ stats: [], status: item.Status }),
      selected: false,
      option: item.Option,
      cameras: item.Option?.Fans[0]?.Cameras,
      id: item.ID,
    })
  );
  console.log(components1.value, "@@@@@@@@@@@@@@@@@测试1111111");
  selectLayer(components1.value.length - 1);
  currentComponent.value = null;
};

/**
 * 选中图层
 */
const selectLayer = (index) => {
  components1.value.forEach((row, i) => {
    if (i === index) {
      row.selected = true;
      for (const key in row) {
        if (Object.prototype.hasOwnProperty.call(row, key)) {
          selectCom[key] = row[key]; // 选中组件将组件状态给 selectCom
        }
      }
    } else {
      row.selected = false;
    }
  });
  console.log("s", selectCom);
  setTimeout(() => {
    getMainSize();
  }, 10);
};

/**
 * 显示或隐藏图层
 */
const changeShow = (index) => {
  components1.value[index].show = !components1.value[index].show;
};

const rootFontSize = parseFloat(
  getComputedStyle(document.documentElement).fontSize
);

const pxToRem = (px) => {
  return px / rootFontSize;
};
console.log("根元素字体大小:", rootFontSize);
/**
 * 在组件上按下鼠标
 */
const hanldleMouseDown = (e, index, id) => {
  // 根据ID查找组件索引
  const idx = components1.value.findIndex((com) => com.id === id);
  selectLayer(idx);

  dragging.value = true;
  currentIndex.value = index;
  startX.value = e.clientX
  startY.value = e.clientY
  comStartX.value = components1.value[index].x;
  comStartY.value = components1.value[index].y;
};
/**
 * 拖动组件
 */
const handleMousemove = (e) => {
  if (dragging.value) {
    let offsetX = e.clientX - startX.value;
    let offsetY = e.clientY - startY.value;
    //适应缩放
    offsetX = Math.trunc(offsetX / mainSize.value.cs);
    offsetY = Math.trunc(offsetY / mainSize.value.cs);
    components1.value[currentIndex.value].x = comStartX.value + offsetX;
    components1.value[currentIndex.value].y = comStartY.value + offsetY;
    console.log("components1.value", components1.value[0]);
  }
};

/**
 * 抬起鼠标
 */
const handleMouseup = (e) => {
  dragging.value = false;
};
//
/**
 * 点击选择属性
 */
// const chooseStats = (no) => {
//   dialogTableVisible.value = true;
//   facilityNo.value = no;
//   // currentStats.value = selectCom["com" + no].stats.map(({ id }) => id);
// };

//保存调取更改组态配置详情接口
//将自己的转换成后端所需格式
import { testData, changeToApi, changeToMe } from "./test";

// 保存
const saveCom = () => {
  let d = deepCopy(components1.value);

  //更改接口

  let formdata = new FormData();
  //将自己的api换成数据所需格式
  d = changeToApi(
    d
    // , 1.2, 1.1
  );

  console.log("dddd", d);
  formdata.append("ID", query.id);
  formdata.append("Config", JSON.stringify(d));

  apiUpdataConf(formdata).then((res) => {
    if (res.data.Status == 0) {
      ElMessage({
        type: "success",
        message: "保存成功",
      });
    } else {
      ElMessage({
        type: "error",
        message: "保存失败",
      });
    }
  });
};

const mainSize = ref({
  w: 0,
  rw: 0,
  s: 1,
  cs: 1,
});

// 中间画布自适应宽高
const getMainSize = () => {
  if (editCz.value?.clientWidth) {
    mainSize.value.w = editCz.value.clientWidth;
    //实际宽度  因为用了rem适配 所以不是1920
    mainSize.value.rw = referSizeRef.value.clientWidth;
    mainSize.value.s = mainSize.value.rw / 1920;
    mainSize.value.cs = mainSize.value.w / mainSize.value.rw;
    console.log("mainSize", mainSize.value);
  }
};
const editCz = ref(null);
const referSizeRef = ref(null);
window.addEventListener("resize", (e) => {
  // console.log(e);
  getMainSize();
});
onMounted(() => {
  getMainSize();
  // initCanvas()
});
</script>

<style scoped lang="scss">
body,
div,
html {
  font-size: 14px;
}

.edit_Home {
  // width: 100vw;
  min-width: 1000px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// #app {
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   height: 100vh;
//   background-color: #2c3e50;
// }
// canvas {
//   background-color: #1f1f1f;
// }

.Home_top {
  height: 70px;
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  position: relative;
  align-items: center;
  background-color: #031627;

  .top_left {
    display: flex;
    flex-direction: row;
    margin-left: 50px;

    img {
      padding-left: 8px;
      padding-top: 4px;
    }
  }

  .main-icon {
    background-color: #ffffff;
    border-radius: 4px;
    width: 35px;
    height: 27px;
  }

  .back-icon {
    width: 40px;
    height: 30px;
    background-color: #79bbff;
    margin-left: 20px;
    border-radius: 4px;
  }

  .next-icon {
    width: 40px;
    height: 30px;
    background-color: #409eff;
    margin-left: 10px;
    border-radius: 4px;
  }

  .save-icon {
    width: 70px;
    height: 30px;
    background-color: #fff;
    margin-left: 20px;
    border-radius: 4px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

// 编辑内容
.edit_Content {
  background-color: #070606;
  width: 100%;
  height: 70vh;
  flex: 1;
  padding-left: 1%;
  padding-right: 1%;
  margin-top: 120px;
  display: flex;
  flex-direction: row;
  color: #ffffff;

  .ele_left {
    width: 12%;
    min-width: 200px;
    height: 100%;
  }

  .left_kb {
    display: flex;
    width: 100%;
    height: calc(100% - 62px);

    .kanban {
      width: 40px;
      height: 50px;
      line-height: 50px;
      margin-top: 2px;
      background-color: #232324;
      text-align: center;
    }
  }

  .show1 {
    background-color: #232324;
    padding-left: 10px;
    line-height: 40px;

    img {
      margin-left: 2px;
      padding-top: 5px;
    }
  }



  // 图层
  .ele_drag {
    width: 293px;
    height: 98%;
    margin-left: 2px;
    font-size: 12px;
    background-color: #232324;

    .drag_tc {
      background-color: #2a2a2b;
      width: 100%;
      padding-left: 10px;
      line-height: 40px;
      // padding-top: 15px;
    }

    .tc_img_box {
      width: 100%;
      padding: 5px 2px;
      display: flex;
      flex-direction: row;
      align-items: center;
      cursor: default;
    }

    .selected {
      border: 1px solid #3c846c;
      background-color: #30584c;
      border-radius: 10px;
    }
  }
}

.edit_cz {
  // width: 52%;
  flex: 1;
  height: 98%;
  border: 1px solid #79bbff;
  margin-left: 10px;
  position: relative;
  overflow: hidden;

  .component {
    cursor: default;
  }

  .selected_com {
    background-color: #3c846c3a;
    border: 1px solid #3c846c;
  }

  .delete-text {
    position: absolute;
    left: 40px;
    bottom: 100px;
  }
}

.edit_board {
  width: 17%;
  height: 100%;
  background: #232324;
  margin-left: 5px;
  overflow-y: auto;

  // 滚动条的颜色的大小
  &::-webkit-scrollbar {
    width: 0.01px;
  }

  &::-webkit-scrollbar-track {
    background-color: #0f3450;
  }

  &::-webkit-scrollbar-thumb {
    background: #0f3450;
    border-radius: 1px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }

  .header {
    width: 100%;
    height: 40px;
    line-height: 40px;
    border-bottom: 2px solid #fff;
    padding-left: 10px;

    .dingzhi {
      width: 50px;
      height: 100%;
      text-align: center;
      // border-bottom: 2px solid #409eff;
    }
  }

  .size_box {
    display: flex;
    height: 60px;
    margin: 10px 6px;
    flex-direction: row;
    align-items: center;
    border-bottom: 2px solid #606165;
  }
}

//看板样式滚动条
.box5-tab1 {
  display: flex;
  flex-direction: column;
  height: 300px;
  overflow-y: auto;

  // 滚动条的颜色的大小
  &::-webkit-scrollbar {
    width: 2px;
  }

  &::-webkit-scrollbar-track {
    background-color: #0f3450;
  }

  &::-webkit-scrollbar-thumb {
    background: #0f3450;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }

  .item-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #fff;
    padding: 10px 0;
  }
}

.power-wrapper {
  transition: transform 0.6s ease-in-out;
}

.tep-wrapper {
  transition: transform 0.6s ease-in-out;
}

.fault_wrapper {
  transition: transform 0.6s ease-in-out;
}

.canvasBox1 {
  width: 100%;
  height: 98%;
  position: absolute;
  top: 10px;
  // left: 10%;
}

.config-item {
  position: absolute;
  background-color: red;
}

.referSize {
  width: 1920px;
  height: 0px;
  z-index: 99999;
  pointer-events: none;
  // opacity: 0;
  // background: #34c749;
  // position: fixed;
  // left: 0;
  // top: 0;
}

.resize {
  width: 1920px;
  height: 1080px;
  transform-origin: top left;
}

.resizeDown {
  position: absolute;
  left: 0;
  top: 0;
  border: 1px solid #fff;
}
</style>