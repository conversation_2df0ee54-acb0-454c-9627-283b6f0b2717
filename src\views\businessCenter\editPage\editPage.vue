<template>
  <div class="box statementBox">
    <!-- 头部分装 -->
    <encapsulationTable>
      <template #title> 页面配置 </template>

      <!-- 左边 -->
      <template #top-left>
        <p class="bullue1" @click="addEdit(formRef)">
          <img class="img" src="../img/plusNew.png" alt="" />新增
        </p>
      </template>
      

      <!-- 右边 -->
      
      <template #table>
        <el-table ref="multipleTableRef" :data="tableData" highlight-current-row :cell-style="{ textAlign: 'center' }"
          :header-cell-style="{ 'text-align': 'center' }" @selection-change="handleSelectionChange"
          style="overflow: auto; height: 90%">
          <!-- <el-table-column type="selection" width="55" /> -->
          <el-table-column type="index" width="80" label="序号" />
          <el-table-column prop="Name" label="组态名称" />
          <el-table-column prop="Url" label="url" />
          <!-- <el-table-column prop="Type"  label="类型"/> -->

          <el-table-column label="类型" align="center" min-width="120">
            <template #default="scope">
              <span v-if="scope.row.Type == '0'">未知</span>
              <span v-if="scope.row.Type == '1'">主通</span>
              <span v-if="scope.row.Type == '2'">局扇</span>
              <span v-if="scope.row.Type == '3'">风门</span>
              <span v-if="scope.row.Type == '4'">风窗</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="350px">
            <template #default="{ row }">
              <div style="display: flex">
                <div class="bullue1"  @click="update(row)">编辑</div>
                <edit-home class="bullue1 ml-40px" :zjList="zjList" @click="edit(row)">
                  <img src="../img/editNew.png" style="width: 16px;height: 16px;" />配置组态
                </edit-home>
                <!-- <div class="userBlue"><img src="../img/yulan.png" />预览</div> -->
                <div class="userRde" @click="delZu(row)">
                  <img src="../img/del.png" />删除
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </encapsulationTable>

    <!-- 页面配置编辑弹窗 -->
    <!-- 弹窗 -->

    <!-- 弹框 -->
    <el-dialog v-model="dialogShow" :title="dialogTitle">

      <div v-if="dialogTitle == '新增' || dialogTitle == '编辑'" style="width: 70%">


        <el-form :label-position="right" :model="zutInit" ref="formRef" style="color: #fff;margin-left: -40px;">

        
            <el-form-item label="组态名称" prop="Name" label-width="100px">
              <el-input v-model="zutInit.Name" placeholder="请输入名称" clearable />
            </el-form-item>
         
            <el-form-item label="Url" prop="Url" label-width="100px">
              <el-input v-model="zutInit.Url" placeholder="请输入" clearable />
            </el-form-item>
        

              <el-form-item label="类型" prop="Type" label-width="100px">
                <el-select style="width: 100%" v-model="zutInit.Type" placeholder="请选择" size="large">
                  <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
          

        </el-form>


      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogShow = false">取消</el-button>
          <el-button v-if="dialogTitle == '新增'" type="primary" @click="addList">
            添加
          </el-button>
          <el-button v-if="dialogTitle == '编辑'" type="primary" @click="editList">
            提交
          </el-button>

        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import router from "@/router";
import encapsulationTable from "../components/table.vue";
import edithome from "../editPage/editHome.vue";
import { ElMessage, ElMessageBox } from 'element-plus';
import { configStore } from "@/store/config";
// 引入样式
import "../css/index.scss";

// 接口
import { ApiConfigurationList, ApiConfigurationAdd,ApiConfigurationDel,ApiConfigurationUpdate} from "@/https/encapsulation/Configuration";
import { ref } from "vue";
import { reactive } from "vue";
import { onMounted, nextTick } from "vue";
// 弹框
const dialogTitle = ref("");
const dialogShow = ref(false);
onMounted(() => {
  getList();

});
const formRef = ref(null);
const tableData = ref([]);
const zutInit = reactive({
  Name: "",
  Url: "",
  Type: "",
  ID: "",
});

const options = [
  {
    value: "1",
    label: "主通",
  },
  {
    value: "2",
    label: "局扇",
  },
  {
    value: "3",
    label: "风门",
  },
  {
    value: "4",
    label: "风窗",
  },
  {
    value: "0",
    label: "未知",
  },
]
const zutData = ref([]);
const zjList = ref([]);
const dataStore = configStore();
// 获取页面配置数据

const getList = () => {
  ApiConfigurationList().then(({ Status, Result }) => {

    if (Status !== 0) {
      tableData.value = [];
      return;
    }
    tableData.value = Result;
    zjList.value = Result[0].Config;


    dataStore.setData(Result);
  });
};
//新增
const addEdit = () => {
  dialogTitle.value = "新增";
  zutInit.ID ="";
  zutInit.Name = "";
  zutInit.Url = "";
  zutInit.Type = "";
  dialogShow.value = true;

};
// 新增
const addList = async () => {
 
  ApiConfigurationAdd(zutInit).then((res) => {
    if (res.Status == 0) {
     
      ElMessage({
        type: "success",
        message: "成功",
      });
     
    } else if (res.Status == 3) {
      ElMessage({
        message: "添加失败",
        type: "warning",

      });
      
    }
  })
  dialogShow.value = false;
  getList();
};

// 编辑
const update = (row) => {
  dialogTitle.value = "编辑";
  zutInit.ID = row.ID;
  zutInit.Name = row.Name; 
  zutInit.Url = row.Url;
 
  let type;
  if (row.Type == "1") {
    type = "主通";
  } else if (row.Type == "2") {
    type = "局扇";
  } else if (row.Type == "3") {
    type = "风门";
  } else if (row.Type == "4") {
    type = "风窗";
  } else {
    type = "未知";


  }
  zutInit.Type = type;

  dialogShow.value = true;

}
const editList = async () => {
  ApiConfigurationUpdate(zutInit).then((res) => {
    if (res.Status == 0) {
     
     ElMessage({
       type: "success",
       message: "修改成功",
     });
    
   } else if (res.Status == 3) {
     ElMessage({
       message: "修改失败",
       type: "warning",

     });
     
   }
 })
 dialogShow.value = false;
 getList();
    
  
}
const delZu = (row) => {
  console.log(row, '删除警告')
  ElMessageBox.confirm("此操作将永久删除, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      ApiConfigurationDel(row.ID).then((res) => {
        if (res.Status == 0) {
          getList();
          Tips();
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "已取消",
      });
    });
};

// 成功提示
const Tips = () => {
  ElMessage({
    type: "success",
    message: "删除成功",
  });
};
// 编辑

//页面配置
function edit(row) {
  router.push({
    path: "/editHome",
    query: {
      type: row.Type,
      id: row.ID,
    },
  });
}
</script>
<style lang="scss" scoped>
.box {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  color: #fff;
}

.underline {
  cursor: pointer;
}

.underline:hover {
  font-size: 15px;
  color: #315efb;
}

.el-upload__tip {
  color: #fff;
}

.editB {
  width: 100px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #40f4f1;
  color: #fff;
  cursor: pointer;
  border-radius: 16px;
  margin: auto;
}

.userBlue {
  width: 67px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #40f4f1;
  color: #fff;
  cursor: pointer;
  border-radius: 16px;
  margin: auto;

  img {
    width: 20px;
    height: 15px;
  }
}

// xmm修改 2025 02 12  修改表格样式
::v-deep .el-table th {
  color: #ffb30f !important;
  /* 修改表头字体颜色 */
  font-weight: bold;
  /* 可选：加粗字体 */
}

/* 自定义高亮样式 */
::v-deep .el-table__body tr.current-row>td {
  background-color: #5e430a;
  /* 高亮背景色 */
}

// xmm修改 2025 02 24  修改表格样式
::v-deep .el-table td.el-table__cell div {
  --un-text-opacity: 1;
  color: rgba(227, 216, 183, var(--un-text-opacity));

}

// 弹窗
::v-deep .el-dialog__body {
  width: 100%;
  height: 70%;
  display: flex;
  justify-content: center;
  align-items: center;
}

::v-deep .el-form-item__label{
  color: #fff;
}
</style>