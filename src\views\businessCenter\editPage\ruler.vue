<script setup>
import Ruler from "@scena/ruler";
import {onMounted} from "vue";

onMounted(() => {
  const ruler = new Ruler(document.querySelector('.Ruler-horizontal'), {
    type: "horizontal",
    unit:100,
    zoom:0.7,
    direction:'start',
    mainLineSize:'70%'
  });
  const ruler2 = new Ruler(document.querySelector('.Ruler-vertical'), {
    type: "vertical",
     unit:100,
    zoom:0.5,
    direction:'start',
    mainLineSize:'10%'

  });
  window.addEventListener("resize", () => {
    ruler.resize();
    ruler2.resize();
  });
})
</script>

<template>
  <div class="Ruler-container">
    <div class="Ruler-box"></div>
    <div class="Ruler-horizontal"></div>
    <div class="Ruler-vertical"></div>
    <div class="Ruler-other">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped>
 .Ruler-container{
   width: 100%;
   height: 100%;
   position: relative;
   --size:20px
 }
 .Ruler-box{
   width: var(--size);
   height: var(--size);
   background: #444;
   box-sizing: border-box;
 }
 .Ruler-horizontal{
   width: calc(100% - var(--size));
   height: var(--size);
   position: absolute;
   left:var(--size);
   top: 0;
 }
 .Ruler-vertical{
   width: var(--size);
   height: calc(100% - var(--size));
   position: absolute;
   top:var(--size);
   left: 0;
 }
 .Ruler-other{
   width: calc(100% - var(--size));
   height: calc(100% - var(--size));
   position: absolute;
   top:var(--size);
   left: var(--size);
 }
</style>
