import { reactive } from "vue";

export const testData = [
  {
    BoardId: "9449e5d540e54fcf988e96ed361a7160",
    BoardName: "动力监测",
    BoardType: 1,
    Image: "src\\views\\businessCenter\\assets\\img\\dljc_temp.png",
    X: 90,
    Y: 30,
    W: 488,
    H: 463,
    Option: {
      Title: "动力监测",
      Fans: [
        {
          Title: "1号风机",
          Keys: ["wind_flow_1", "pressure_blade_1"],
          Statue: "",
        },
        {
          Title: "2号风机",
          Keys: ["wind_flow_2", "pressure_blade_2"],
          Statue: "",
        },
      ],
    },
  },
  {
    BoardId: "bb64db238cf4402fb054dc422aa9060d",
    BoardName: "温度监测",
    BoardType: 2,
    Image: "src\\views\\businessCenter\\assets\\img\\wdjc_temp.png",
    X: 590,
    Y: 30,
    W: 488,
    H: 463,
    Option: {
      Title: "温度监测",
      Fans: [
        {
          Title: "1号风机",
          Keys: ["wind_flow_1", "pressure_blade_1"],
        },
        {
          Title: "2号风机",
          Keys: ["wind_flow_2", "pressure_blade_2"],
        },
      ],
    },
  },
  {
    BoardId: "0dce2f4174dd4ee8a7a625ac7f3e3946",
    BoardName: "控制面板",
    BoardType: 3,
    Image: "src\\views\\businessCenter\\assets\\img\\kzmb_temp.png",
    X: 90,
    Y: 400,
    W: 488,
    H: 236,
    Option: {
      Title: "控制面板",
      Fans: [
        {
          Title: "1号风机",
          Btns: [
            {
              Url: null,
              Name: "一键启停",
              Param: null,
              Type: "success",
            },
            {
              Url: null,
              Name: "一键反向",
              Param: null,
              Type: "warning",
            },
            {
              Url: null,
              Name: "一键倒台",
              Param: null,
              Type: "danger",
            },
            {
              Url: null,
              Name: "远程控制",
              Param: null,
              Type: "suspend",
            },
            {
              Url: null,
              Name: "叶片角度",
              Param: null,
              Type: "suspend",
            },
          ],
        },
        {
          Title: "2号风机",
          Btns: [
            {
              Url: null,
              Name: "一键启停",
              Param: null,
              Type: "success",
            },
            {
              Url: null,
              Name: "一键反向",
              Param: null,
              Type: "warning",
            },
            {
              Url: null,
              Name: "一键倒台",
              Param: null,
              Type: "danger",
            },
            {
              Url: null,
              Name: "远程控制",
              Param: null,
              Type: "suspend",
            },
            {
              Url: null,
              Name: "叶片角度",
              Param: null,
              Type: "suspend",
            },
          ],
        },
      ],
    },
  },
  {
    BoardId: "75f3e5aecb014754856900fad3902dab",
    BoardName: "视频监控",
    BoardType: 4,
    Image: "src\\views\\businessCenter\\assets\\img\\sp_temp.png",
    X: 436,
    Y: 400,
    W: 488,
    H: 236,
    Option: {
      Title: "视频监控",
      Fans: [
        {
          Id: "da295b6ab24f421fb778b13ff998cd00",
          Cameras: ["c8a8d14764be470b88b4b4b6d8a3c4ed"],
        },
      ],
    },
  },
  {
    BoardId: "051b7e35cabc49888380a88cad07e064",
    BoardName: "故障诊断",
    BoardType: 5,
    Image: "src\\views\\businessCenter\\assets\\img\\gzzd_temp.png",
    X: 695,
    Y: 400,
    W: 488,
    H: 439,
    Option: {
      Title: "故障诊断",
      Fans: [
        {
          Title: "1号风机",
          Tables: [
            {
              Title: "",
              Status: true,
              Type: "list",
              Url: "",
              Columns: [],
              Keys: [],
            },
          ],
        },
        {
          Title: "2号风机",
          Tables: [
            {
              Title: "",
              Status: true,
              Type: "list",
              Url: "",
              Columns: [],
              Keys: [],
            },
          ],
        },
      ],
    },
  },
];

const obj = {
  5: "故障诊断",
  4: "视频监控",
  3: "控制面板",
  2: "温度监测",
  1: "动力监测",
};
export const changeToApi = (d, xx = 1, yy = 1) => {
  console.log(d, "$$$$$$$$$hhhhh");
  for (let k in d) {
    //如果说 type 是主通风机  2 个 option
    //if
    let Option = {
      Title: d[k].name,
      Fans: [
        {
          Title: "1号风机",
          Keys: d[k].com1.stats,
          Statue: "",
        },
        {
          Title: "2号风机",
          Keys: d[k].com2.stats,
          Statue: "",
        },
      ],
    };
    d[k] = {
      BoardId: d[k].boardId,
      BoardName: d[k].name,
      BoardType: k, //改
      Image: d[k].img,
      X: d[k].x * xx,
      Y: d[k].y * yy,
      W: d[k].width * xx,
      H: d[k].height * yy,
      Option,
    };
  }
  return d;
};

export const changeToMe = (d, compsMap) => {
  let config = [];
  for (let k in d) {
    if (!d[k]?.BoardName) continue;
    let row = d[k];
    console.log("row", row, compsMap);
    let index = compsMap.findIndex((it) => it.name === row.BoardName);

    // img: compsMap[row.BoardType - 1].img,
    // //       name: row.BoardName,
    // //       show: true,
    // //       type: compsMap[row.BoardType - 1].type,
    // console.log(index,'!!!!!!!!!!!!!!!!!!!!!1')
    config.push({
      x: row.X,
      y: row.Y,
      width: row.W,
      height: row.H,
      com1: reactive({ stats: [], status: row.Status }),
      com2: reactive({ stats: [], status: row.Status }),
      selected: false,
      img: compsMap[index].img,
      name: row.BoardName,
      show: true,
      type: compsMap[index].type,
      option: row.Option,
      cameras: row.Option.Fans[0].Cameras,
      boardId: row.BoardId,
      id: Math.random(),
    });
  }
  return config;
};

// data = console.log("newVal data", data);
// for (let c in data) {
//   if (data[c]?.BoardName) {
//     let row = data[c];
//     // console.log("newVal data  c", data[c]);
//     components1.value.push({
//       x: row.X,
//       y: row.Y,
//       width: row.W,
//       height: row.H,
//       com1: reactive({ stats: [], status: row.Status }),
//       com2: reactive({ stats: [], status: row.Status }),
//       selected: false,
//       img: compsMap[row.BoardType - 1].img,
//       name: row.BoardName,
//       show: true,
//       type: compsMap[row.BoardType - 1].type,
//       option: row.Option,
//       cameras: row.Option.Fans[0].Cameras,
//       id: row.BoardId,
//     });
//   }
// }

// const data = newVal[query.type].Config;
// console.log('newVal data',data)

// components1.value = data.map((row, index) => {
//   let component = {};
//   console.log('newVal row.X',row.X)
//   component.x = row.X;
//   component.y = row.Y;
//   component.width = row.W;
//   component.height = row.H;
//   component.com1 = reactive({ stats: [], status: row.Status });
//   component.com2 = reactive({ stats: [], status: row.Status });
//   component.selected = false;

//   component.img = compsMap[row.BoardType - 1].img;
//   component.name = row.BoardName;
//   component.show = true;
//   component.type = compsMap[row.BoardType - 1].type;
//   component.option = row.Option;
//   component.cameras = row.Option.Fans[0].Cameras;
//   component.id = row.BoardId;
//   return component;
// });
