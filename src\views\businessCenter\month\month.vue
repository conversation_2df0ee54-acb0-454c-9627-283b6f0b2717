<template>
  <div class="box statementBox">
    <!-- 头部分装 -->
    <encapsulationTable>
      <template #title> 月报 </template>
      <!-- 标头 -->
      <!-- 左边 -->
      <template #top-left>
        <el-upload class="bullue" action="#" ref="uploadRef" :auto-upload="false" :on-preview="handlePreview"
          :on-change="handleAvatarChangeIcon" :on-remove="handleRemove" :file-list="fileList" multiple :limit="1">
          <template #trigger>
            <p class="bullue1" @click="add">
              <img class="img" src="../img/plusNew.png" alt="" /> 新增
            </p>
          </template>
        </el-upload>
      </template>
      <!-- <template #top-center>
          <p class="bullue" @click="checEdit">
            <img class="img" src="../img/edltor.png" alt="" />q编辑
          </p>
        </template> -->
      <template #top-center>
        <p class="red" @click="checkbox">
          <img class="img" src="../img/del.png" alt="" />删除
        </p>
      </template>
      <!-- 右边 -->
      <!-- <template #right-01>
          <span mr-10px>日报名称</span>
          <el-input
            v-model="roleName"
            placeholder="请输入角色名称"
            style="width: 250rem"
          ></el-input>
        </template> -->
      <!-- <template #right-03>
          <p class="bullue" @click="search">
            <img class="img" src="../img/search.png" alt="" /> 搜索
          </p>
        </template>
        <template #right-04>
          <p class="bullue" @click="reset">
            <img class="img" src="../img/reset.png" alt="" />重置
          </p>
        </template> -->
      <!-- Download -->
      <!-- <template #Download>
          <el-upload
            class="upload-demo"
            action="#"
            ref="uploadRef"
            :auto-upload="false"
            :on-preview="handlePreview"
            :on-change="handleAvatarChangeIcon"
            :on-remove="handleRemove"
            :file-list="fileList"
            multiple
            :limit="1"
          >
            <template #trigger>
              <div>点击上传文件</div>
            </template>
            <template #tip>
              <div class="el-upload__tip"> 
              </div>
            </template>
          </el-upload>
        </template> -->
      <template #table>
        <el-table ref="multipleTableRef" :data="List" highlight-current-row :cell-style="{ textAlign: 'center' }"
          :header-cell-style="{ 'text-align': 'center' }" @selection-change="handleSelectionChange"
          style="overflow: auto; height: 90%">
          <el-table-column type="selection" width="55" />
          <el-table-column type="index" width="80" label="序号" />
          <el-table-column property="Title" label="日报名称" />
          <el-table-column property="CreatorTime" label="创建时间">
            <template #default="scope">
              {{
                scope.row.CreatorTime != null
                  ? scope.row.CreatorTime.split(".")[0]
                  : ""
              }}
            </template>
          </el-table-column>
          <el-table-column property="FileUrl" label="月报链接">
            <template #default="scope">
              <p class="underline" @click="openSystem(scope.row.FileUrl)">
                {{ scope.row.FileUrl }}
              </p>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200px">
            <template #default="scope">
              <div style="display: flex">
                <!-- <div class="userBlue" @click="edit(scope.row)">编辑</div> -->
                <div class="userRde" @click="del(scope.row)">删除</div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pages>
        <el-pagination :page-sizes="[50, 100, 200, 400]" layout="total, sizes, prev, pager, next, jumper" :total="total"
          @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </template>
    </encapsulationTable>
  </div>
</template>
<script setup>
// 接口引入
import {
  ApiMonthList,
  ApiMonth,
  ApiDayDel,
} from "@/https/encapsulation/VentilateReport";
// 引入样式
import "../css/index.scss";
import "../css/el-pagination.scss";
import { ElMessage, ElMessageBox } from "element-plus";
import { ref } from "vue";
import encapsulationTable from "../components/table.vue";
import { reactive } from "vue";
import { onMounted, nextTick } from "vue";
onMounted(() => {
  getList();
});
const pageIndex = ref(1);
const pageSize = ref(50);
const total = ref(0);
const List = ref([]);
// 获取日报数据
const getList = () => {
  ApiMonthList(pageSize.value, pageIndex.value).then((res) => {
    if (res.data.Status == 0) {
      List.value = res.data.Result;
      total.value = res.data.Pagination.Total;
    } else {
      List.value = [];
      total.value = 0;
    }
  });
};
// 跳转查看上传文件
const openSystem = (item) => {
  window.open(item, "_blank");
};
// 添加
let yearFileList = ref([]);
let yearFile = ref([]);
const uploadRef = ref();
const handleAvatarChangeIcon = (file, fileList) => {
  const isLt2M = file.raw.size / 1024 / 1024 < 5;
  if (file.raw.type != "application/pdf") {
    uploadRef.value.submit();
    uploadRef.value.clearFiles();
    yearFileList.value = [];
  }
  if (!isLt2M) {
    ElMessage({
      message: "上传图片大小不能超过 2MB!",
      type: "warning",
      messageBox: true,
    });
    yearFileList.value = [];
    uploadRef.value.clearFiles();
    return false;
  } else if (isLt2M) {
  }
  yearFile = fileList.map((val) => {
    return val.raw;
  });
  nextTick(() => {
    if (yearFile.value != 0) {
      ApiMonth(yearFile).then((res) => {
        getList();
        yearFileList.value = [];
        yearFile.value = [];
        uploadRef.value.submit();
      });
    }
  });
};
// // 添加
const add = () => { };
const SelectList = ref([]);
// 删除
const del = (row) => {
  ElMessageBox.confirm("此操作将永久删除该文件, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      ApiDayDel(row.ID).then((res) => {
        if (res.data.Status == 0) {
          getList();
          Tips();
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "已取消",
      });
    });
};
// 多选删除
const checkbox = () => {
  if (SelectList.value.length == 0) {
    ElMessage({
      message: "请至少选中一行",
      type: "warning",
      messageBox: true,
    });
  } else {
    ElMessageBox.confirm("此操作将永久删除该文件, 是否继续?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        SelectList.value.forEach((item) => {
          ApiDayDel(item.ID).then((res) => {
            if (res.data.Status == 0) {
              getList();
              Tips();
            }
          });
        });
      })
      .catch(() => {
        ElMessage({
          type: "info",
          message: "已取消",
        });
      });
  }
};
// 成功提示
const Tips = () => {
  ElMessage({
    type: "success",
    message: "成功",
  });
};
// 分页
const handleSizeChange = (val) => {
  pageSize.value = val;
  getList();
};
const handleCurrentChange = (val) => {
  pageIndex.value = val;
  getList();
};
// 获取选中的
const handleSelectionChange = (val) => {
  SelectList.value = val;
};
</script>
<style lang="scss" scoped>
.box {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  color: #fff;
}

.underline {
  cursor: pointer;
}

.underline:hover {
  font-size: 15px;
  color: #315efb;
}

.el-upload__tip {
  color: #fff;
}

// xmm修改 2025 02 12  修改表格样式
::v-deep .el-table th {
  color: #ffb30f;
  /* 修改表头字体颜色 */
  font-weight: bold;
  /* 可选：加粗字体 */
}

/* 自定义高亮样式 */
::v-deep .el-table__body tr.current-row>td {
  background-color: #5e430a;
  /* 高亮背景色 */
}

// xmm修改 2025 02 24  修改表格样式
::v-deep .el-table td.el-table__cell div {
  --un-text-opacity: 1;
  color: rgba(227, 216, 183, var(--un-text-opacity));

}

// 弹窗
::v-deep .el-dialog__body {
  width: 100%;
  height: 70%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>