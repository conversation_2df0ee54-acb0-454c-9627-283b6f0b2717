<template>
  <div class="box statementBox">
    <!-- 头部分装 -->
    <encapsulationTable>
      <template #title> 权限管理 </template>
      <!-- 标头 -->
      <!-- 左边 -->
      <template #top-left>
        <p class="bullue1" @click="add(formRef)">
          <img class="img" src="../img/plusNew.png" alt="" />新增
        </p>
      </template>
      <template #top-center>
        <p class="bullue" @click="checEdit">
          <img class="img" src="../img/editNew.png" alt="" />编辑
        </p>
      </template>
      <template #top-right>
        <p class="red" @click="checkbox">
          <img class="img" src="../img/del.png" alt="" />删除
        </p>
      </template>
      <!-- 右边 -->
      <template #right-01>
        <span mr-10px>权限名称</span>
        <el-input v-model="name" placeholder="请输入" style="width: 250rem;"></el-input>
      </template>
      <template #right-03>
        <p class="bullue1" @click="getList">
          <img class="img" src="../img/searchNew.png" alt="" /> 搜索
        </p>
      </template>
      <template #right-04>
        <p class="bullue" @click="reset">
          <img class="img" src="../img/resetNew.png" alt="" />重置
        </p>
      </template>
      <template #table>
        <el-table :data="List" highlight-current-row style="overflow: auto; height: 100%" row-key="ID" ref="tableRef"
          @selection-change="handleSelectionChange" default-expand-all:false>
          <el-table-column type="selection" width="55" />
          <el-table-column property="Name" label="权限名称" />
          <el-table-column property="Url" label="路径" />
          <el-table-column property="SortCode" label="顺序" />
          <el-table-column property="CreatorTime" label="创建时间">
            <template #default="scope">
              {{
                scope.row.CreatorTime != null
                  ? scope.row.CreatorTime.split(".")[0]
                  : ""
              }}
            </template>
          </el-table-column>
          <el-table-column width="200" label="操作">
            <template #default="scope">
              <div style="display: flex">
                <div class="bullue1" @click="edit(scope.row)">编辑</div>
                <div class="userRde" @click="del(scope.row)">删除</div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </encapsulationTable>
    <!-- 弹框 -->
    <el-dialog v-model="dialogShow" :title="dialogTitle">
      <!-- 编辑信息 -->
      <div style="width: 88%">
        <el-form :label-position="right" :model="formInline" :rules="rules" ref="formRef">
          <el-form-item label="权限名称" prop="Name">
            <el-input v-model="formInline.Name" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="页面路径" style="margin-left: 10px">
            <el-input v-model="formInline.Url" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="页面顺序" style="margin-left: 10px">
            <el-input v-model="formInline.SortCode" type="number" placeholder="请输入" clearable />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogShow = false" type="info" style="color: #6a6c70; background: #fff">取消</el-button>
          <el-button v-if="dialogTitle == '新增'" type="primary" @click="addList">
            添加
          </el-button>
          <el-button v-if="dialogTitle == '编辑'" type="primary" @click="addEdit">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
// 引入样式
import "../css/index.scss";
import { ElMessage, ElMessageBox } from "element-plus";
// api接口引入
import {
  ApiModulePageList,
  ApiModuleAdd,
  ApiModuleDel,
  ApiModuleUndete,
} from "@/https/encapsulation/Module";
import { ref } from "vue";
import encapsulationTable from "../components/table.vue";
import { reactive } from "vue";
import { onMounted } from "vue";

// 弹框
const dialogShow = ref(false);
const dialogTitle = ref("");
// 分页
const pageIndex = ref(1);
const pageSize = ref(15);
const total = ref(0);
// 权限名称
const name = ref("");
// 添加数组
const formInline = reactive({
  Name: "",
  Url: "",
  ModuleId: "",
  ID: "",
  CreatorTime: "",
  SortCode: 0,
});
// 表格选中的数组
const SelectList = ref([]);
onMounted(() => {
  getList();
});
const Tips = () => {
  ElMessage({
    type: "success",
    grouping: true,
    message: "成功",
  });
};
// 获取数据
const List = ref([]);
const getList = () => {
  let arr = [];
  ApiModulePageList(name.value).then((res) => {
    if (res.Status == 0) {
      arr = res.Result;
      for (let i = arr.length - 1; i >= 0; i--) {
        const obj = arr[i];
        if (obj.ModuleId) {
          const index = arr.findIndex((item) => item.ID === obj.ModuleId);
          if (index !== -1) {
            const matchedObj = arr[index];
            if (!matchedObj.children) {
              matchedObj.children = [];
            }
            matchedObj.children.push(obj);
            arr.splice(i, 1);
          }
        }
      }
      arr.forEach((item) => {
        if (item.children) {
          item.children.sort((a, b) => a.SortCode - b.SortCode);
        }
      });

      List.value = arr;
    }

    // console.log(arr);
    // console.log(List.value);
  });
};
// 添加
const add = async (formEl) => {
  formInline.SortCode = null;
  dialogTitle.value = "新增";
  formInline.Name = "";
  formInline.Url = "";
  formInline.ModuleId = "";
  try {
    await formRef.value.validate((valid) => {
      if (valid) {
      } else {
        return false;
      }
    });
  } catch (err) { }
  if (SelectList.value.length > 1) {
    ElMessage({
      message: "最多选择一个设备",
      grouping: true,
      type: "warning",
      messageBox: true,
    });
  } else {
    dialogShow.value = true;
    if (!formEl) return;
    formEl.resetFields();
    SelectList.value.forEach((item) => {
      formInline.ModuleId = item.ID;
    });
  }
};
const addList = async () => {
  if (SelectList.value.length == 0 || SelectList.value.length == 1) {
    if (SelectList.value.length == 1) {
      formInline.ModuleId = SelectList.value[0].ID;
    } else {
      formInline.ModuleId = null;
    }
    try {
      await formRef.value.validate((valid) => {
        if (valid) {
          ApiModuleAdd(formInline).then((res) => {
            if (res.Status == 0) {
              getList();
              Tips();
              dialogShow.value = false;
            }
          });
        } else {
          ElMessage({
            message: "必填参数为空",
            grouping: true,
            type: "warning",
            messageBox: true,
          });
          return false;
        }
      });
    } catch (err) {
      console.error("表单校验出错：", err);
    }
  } else {
    ElMessage({
      message: "最多选择一个设备",
      grouping: true,
      type: "warning",
      messageBox: true,
    });
  }
};
// 编辑
const edit = async (row) => {
  console.log(row, "rowrowrow");
  dialogTitle.value = "编辑";
  dialogShow.value = true;
  formInline.ID = row.ID;
  formInline.Name = row.Name;
  if (row.Url == null) {
    formInline.Url = "";
  } else {
    formInline.Url = row.Url;
  }

  formInline.ModuleId = row.ModuleId;
  formInline.CreatorTime = row.CreatorTime;
  formInline.SortCode = row.SortCode;
  try {
    await formRef.value.validate((valid) => {
      if (valid) {
      } else {
        return false;
      }
    });
  } catch (err) {
    console.error("表单校验出错：", err);
  }
};
const rules = {
  Name: [{ required: true, message: "请输入权限名称", trigger: "blur" }],
};
const formRef = ref();
const addEdit = () => {
  console.log(formRef.value.validate);
  // formRef.value.validate((valid) => {
  //   if (valid) {
  //     // 表单验证通过，可以进行提交操作
  //     console.log("表单验证通过");
  //   } else {
  //     // 表单验证失败，可以进行错误处理
  //     console.log("表单验证失败");
  //   }
  // });

  ApiModuleUndete(formInline).then((res) => {
    if (res.Status == 0) {
      dialogShow.value = false;
      Tips();
      getList();
    }
  });
};
// 删除
const del = (row) => {
  ElMessageBox.confirm("此操作将永久删除该文件, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      ApiModuleDel(row.ID).then((res) => {
        if (res.Status == 0) {
          getList();
          Tips();
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        grouping: true,
        message: "已取消",
      });
    });
};
// 多选删除
const checkbox = () => {
  if (SelectList.value.length == 0) {
    ElMessage({
      message: "请至少选中一行",
      grouping: true,
      type: "warning",
      messageBox: true,
    });
  } else {
    ElMessageBox.confirm("此操作将永久删除该文件, 是否继续?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        SelectList.value.forEach((item) => {
          ApiModuleDel(item.ID).then((res) => {
            if (res.Status == 0) {
              Tips();
              getList();
            }
          });
        });
      })
      .catch(() => {
        ElMessage({
          type: "info",
          grouping: true,
          message: "已取消",
        });
      });
  }
};
const checEdit = async () => {
  try {
    await formRef.value.validate((valid) => {
      if (valid) {
      } else {
        return false;
      }
    });
  } catch (err) { }
  if (SelectList.value.length == 0) {
    ElMessage({
      message: "请至少选中一行",
      type: "warning",
      grouping: true,
      messageBox: true,
    });
  } else if (SelectList.value.length > 1) {
    ElMessage({
      message: "最多选择一个设备",
      grouping: true,
      type: "warning",
      messageBox: true,
    });
  } else {
    SelectList.value.forEach((item) => {
      edit(item);
    });
  }
};
const tableRef = ref();
// 获取选中的
const handleSelectionChange = (val) => {
  console.log(val, "val");
  val.forEach((item, i) => {
    const index = SelectList.value.findIndex((v) => v.ID == item.ID);
    if (index != -1) return;
    if (item.children) {
      item.children.forEach((m) => {
        const index = SelectList.value.findIndex((v) => v.ID == m.ID);
        if (index != -1) return;
        tableRef.value.toggleRowSelection(m, false);
      });
    }
  });
  SelectList.value = tableRef.value.getSelectionRows();
  // console.log(SelectList.value, "我是选中的");
};

// 重置
const reset = () => {
  name.value = "";
  getList();
};
</script>
<style lang="scss" scoped>
.box {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  color: #fff;
}

.top {
  height: 120px;
  background-color: red !important;
  width: 100%;
}

.userBG-Rde {
  width: 67px;
  height: 28px;
  color: #f13f5c;
  background: rgba(241, 63, 92, 0.17);
  border: 1px solid #f13f5c;
  border-radius: 6px;
  margin: auto;
}

.userBG-Green {
  width: 67px;
  height: 28px;
  background: rgba(63, 241, 180, 0.17);
  border: 1px solid #3ff1b4;
  color: #3ff1b4;
  border-radius: 6px;
  margin: auto;
}

// .userBlue {
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   width: 67px;
//   height: 28px;
//   border: 1px solid #40f4f1;
//   color: #40f4f1;
//   cursor: pointer;
//   border-radius: 6px;
//   margin: auto;
// }
.userwhite {
  width: 88px;
  height: 28px;
  border: 1px solid #fff;
  color: #fff;
  cursor: pointer;
  border-radius: 6px;
  margin: auto;
}

.userRde {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 86px;
  height: 32px;
  color: #f13f5c;
  border: 1px solid #f13f5c;
  border-radius: 6px;
  cursor: pointer;
  margin: auto;
}

.img {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

.characters_center_left_title {
  cursor: pointer;
  width: 86px;
  height: 32px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 32px;
  background: rgba(43, 155, 216, 0);
  background-image: url("../img/blue_border.png");
  background-size: 100% 100%;
}

::v-deep .el-form-item__label {
  color: #fff !important;
}

// 修改输入框
::v-deep .el-input__wrapper {
  background-color: transparent !important;
}

::v-deep .el-input__inner {
  color: #fff !important;
}

::v-deep .el-textarea__inner {
  background-color: transparent !important;
  color: #fff;
}

// xmm修改 2025-02-13
::v-deep .el-dialog {
  width: 30vw;
  height: 32vh;
  background: rgb(0, 0, 0) !important;
  box-shadow: rgb(255, 179, 15) 0px 0px 20px inset !important;
  border: 1px solid rgba(255, 179, 15, 0.3) !important;
  border-radius: 10px !important;
}

::v-deep .el-button+.el-button {
  background-color: rgba(255, 179, 15, 0.3) !important;
  border: 1px solid rgba(232, 157, 6, 0.8) !important;
}

// xmm修改 2025 02 22  修改表格样式
::v-deep .el-table th {
  color: #ffb30f;
  /* 修改表头字体颜色 */
  font-weight: bold;
  /* 可选：加粗字体 */
}

/* 自定义高亮样式 */
::v-deep .el-table__body tr.current-row>td {
  background-color: #5e430a;
  /* 高亮背景色 */
}

// xmm修改 2025 02 24  修改表格样式
::v-deep .el-table td.el-table__cell div {
  --un-text-opacity: 1;
  color: rgba(227, 216, 183, var(--un-text-opacity));
}

// 弹窗
::v-deep .el-dialog__body {
  width: 100%;
  height: 70%;
  display: flex;
  justify-content: center;
  align-items: center;
}

// xmm 修改input边框颜色 2025-03-04
::v-deep .el-input__wrapper {
  --el-input-focus-border-color: rgba(232, 157, 6, 0.8);
}
</style>
