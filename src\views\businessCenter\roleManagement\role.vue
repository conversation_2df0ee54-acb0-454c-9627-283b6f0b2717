<template>
  <div class="box statementBox">
    <!-- 头部分装 -->
    <encapsulationTable>
      <template #title> 角色管理 </template>
      <!-- 标头 -->
      <!-- 左边 -->
      <template #top-left>
        <p class="bullue1" @click="add(formRef)">
          <img class="img" src="../img/plusNew.png" alt="" /> 新增
        </p>
      </template>
      <template #top-center>
        <p class="bullue" @click="checEdit">
          <img class="img" src="../img/editNew.png" alt="" />编辑
        </p>
      </template>
      <template #top-right>
        <p class="red" @click="checkbox">
          <img class="img" src="../img/del.png" alt="" />删除
        </p>
      </template>
      <!-- 右边 -->
      <template #right-01>
        <span mr-10px>角色名称</span>
        <el-input v-model="roleName" placeholder="请输入角色名称" style="width: 250rem"></el-input>
      </template>
      <template #right-02>
        <span mr-10>角色权限 </span>
        <el-select style="width: 250rem" v-model="permissionCharacter" placeholder="请选择" size="large">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <!-- <el-input
          v-model="permissionCharacter"
          placeholder="请输入内容"
          style="width: 250rem"
        ></el-input> -->
      </template>
      <template #right-03>
        <p class="bullue1" @click="search">
          <img class="img" src="../img/searchNew.png" alt="" /> 搜索
        </p>
      </template>
      <template #right-04>
        <p class="bullue" @click="reset">
          <img class="img" src="../img/resetNew.png" alt="" />重置
        </p>
      </template>

      <template #table>
        <el-table ref="multipleTableRef" :data="List" highlight-current-row :cell-style="{ textAlign: 'center' }"
          :header-cell-style="{ 'text-align': 'center' }" @selection-change="handleSelectionChange"
          style="overflow: auto; height: 100%">
          <el-table-column type="selection" width="55" />
          <el-table-column type="index" width="80" label="序号" />
          <el-table-column property="Name" label="角色名称" />
          <el-table-column property="Type" label="角色权限">
            <template #default="scope">
              <p v-if="scope.row.Type == 0">管理员</p>
              <p v-if="scope.row.Type == 1">运维人员</p>
              <p v-if="scope.row.Type == 2">普通用户</p>
            </template>
          </el-table-column>
          <el-table-column property="CreatorTime" label="创建时间">
            <template #default="scope">
              {{
                scope.row.CreatorTime != null
                  ? scope.row.CreatorTime.split(".")[0]
                  : ""
              }}
            </template>
          </el-table-column>
          <el-table-column width="300" label="操作">
            <template #default="scope">
              <div style="display: flex;">
                <div class="bullue1" @click="edit(scope.row)">编辑</div>
                <div
                  class="bullue1"
                  style="margin-left: 8%;"
                  @click="editPermissions(scope.row)"
                >
                  编辑权限
                </div>
                <div class="userRde" @click="del(scope.row)">删除</div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pages>
        <el-pagination :page-sizes="[50, 100, 200, 400]" layout="total, sizes, prev, pager, next, jumper" :total="total"
          @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </template>
    </encapsulationTable>
    <!-- 弹框 -->
    <el-dialog
      v-model="dialogShow"
      :title="dialogTitle"
      :class="dialogTitle == '编辑权限' ? 'addDialog' : 'editDialog'"
    >
      <!-- 编辑信息 -->
      <div v-if="dialogTitle == '新增' || dialogTitle == '编辑'" style="width: 88%">
        <el-form :label-position="right" :model="formInline" :rules="rules" ref="formRef">
          <el-form-item label="角色名称" prop="Name">
            <el-input v-model="formInline.Name" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="用户权限" prop="Type">
            <el-select style="width: 100%" v-model="formInline.Type" placeholder="请选择" size="large">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <!-- 编辑权限 -->
      <div v-if="dialogTitle == '编辑权限'">
        <el-tabs :tab-position="tabPosition" style="height: 300px" class="demo-tabs">
          <el-tab-pane label="页面授权">
            <el-tree ref="treeRef" class="scroll-container" :data="DetailData" show-checkbox node-key="ID"
              :props="defaultProps" default-expand-all @check="handleCheckChange">
              <template #default="{ data }">
                <span class="custom-label">{{ data.Name }}</span>
              </template>
            </el-tree>
          </el-tab-pane>
          <!-- <el-tab-pane label="按钮授权">Config</el-tab-pane> -->
        </el-tabs>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogShow = false" type="info" style="color: #6a6c70;background: #fff;">取消</el-button>
          <el-button v-if="dialogTitle == '新增'" type="primary" @click="addList">
            添加
          </el-button>
          <el-button v-if="dialogTitle == '编辑'" type="primary" @click="addEdit">
            确定
          </el-button>
          <el-button v-if="dialogTitle == '编辑权限'" type="primary" @click="accredit">
            授权
          </el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 授权弹框 -->
  </div>
</template>
<script setup>
// 引入样式
import "../css/index.scss";
import "../css/el-pagination.scss";
// 用户接口
import { ApiRoleDel, ApiRoleAdd } from "@/https/encapsulation/user";
import {
  ApiRolePageList,
  ApiRoleUpdate,
  ApisetModule,
} from "@/https/encapsulation/Role";
import {
  ApiModuleRoleId,
  ApiModulePageList,
} from "@/https/encapsulation/Module";
// import { stringify } from "JSON";
// elemenet
import { ElMessage, ElMessageBox } from "element-plus";
import { ref, nextTick } from "vue";
import axios from "axios";
import encapsulationTable from "../components/table.vue";
import { reactive } from "vue";
import { onMounted } from "vue";
const rules = {
  Name: [
    { required: true, message: "请输入角色名称", trigger: "blur" },
    // 你可以添加其他验证规则，例如长度限制等
  ],
  Type: [
    { required: true, message: "请输入角色名称", trigger: "change" },
    // 你可以添加其他验证规则，例如长度限制等
  ],
};
// 弹框名字
const dialogTitle = ref("");
const dialogShow = ref(false);
// 角色权限
const options = [
  {
    value: "0",
    label: "管理员",
  },
  {
    value: "1",
    label: "运维人员",
  },
  {
    value: "2",
    label: "普通用户",
  },
];
const defaultProps = {
  children: "children",
  label: "label",
};
// 获取选中的
const SelectList = ref([]);
// 分页
const pageSize = ref(50);
const pageIndex = ref(1);
const total = ref(0);
const roleName = ref("");
const permissionCharacter = ref("");
// 进入页面执行
const List = ref([]);
onMounted(() => {
  getList();
  detailsList();
});
// 进入页面数据
// 获取页面数据
const DetailData = ref([]);
const detailsList = () => {
  let arr = [];
  ApiModulePageList().then((res) => {
    if (res.Status == 0) {
      arr = res.Result;
      for (let i = arr.length - 1; i >= 0; i--) {
        const obj = arr[i];
        if (obj.ModuleId) {
          const index = arr.findIndex((item) => item.ID === obj.ModuleId);
          if (index !== -1) {
            const matchedObj = arr[index];
            if (!matchedObj.children) {
              matchedObj.children = [];
            }
            matchedObj.children.push(obj);
            arr.splice(i, 1);
          }
        }
      }
      arr.forEach((item) => {
        if (item.children) {
          item.children.sort((a, b) => a.SortCode - b.SortCode);
        }
      });
      DetailData.value = arr;
    }
  });
};

const getList = async () => {
  ApiRolePageList(pageIndex.value, pageSize.value, "", "").then((res) => {
    if (res.Status == 0) {
      List.value = res.Result;
      total.value = res.PageInfo.Total;
    }

    console.log(List.value);
  });
};
// 添加
const formInline = reactive({
  Name: "",
  Type: "",
  ID: "",
});

const add = (formEl) => {
  dialogTitle.value = "新增";
  dialogShow.value = true;
  formInline.ID = "";
  formInline.Name = "";
  formInline.Type = "";
  if (!formEl) return;
  formEl.resetFields();
};
const formRef = ref(null);
const addList = async () => {
  try {
    await formRef.value.validate((valid) => {
      if (valid) {
        ApiRoleAdd(formInline).then((res) => {
          if (res.Status == 0) {
            getList();
            Tips();
            dialogShow.value = false;
          }
        });
        // 这里可以添加提交表单的逻辑
      } else {
        ElMessage({
          type: "warning",
          grouping: true,
          message: "必填参数为空",
        });
        return false;
      }
    });
  } catch (err) {
    console.error("表单校验出错：", err);
  }
};
const Tips = () => {
  ElMessage({
    type: "success",
    grouping: true,
    message: "成功",
  });
};
// 删除
const del = (row) => {
  ElMessageBox.confirm("此操作将永久删除该文件, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      ApiRoleDel(row.ID).then((res) => {
        if (res.Status == 0) {
          getList();
          Tips();
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        grouping: true,
        message: "已取消",
      });
    });
};
// 多选删除
const checkbox = () => {
  if (SelectList.value.length == 0) {
    ElMessage({
      message: "请至少选中一行",
      grouping: true,
      type: "warning",
      messageBox: true,
    });
  } else {
    ElMessageBox.confirm("此操作将永久删除该文件, 是否继续?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        SelectList.value.forEach((item) => {
          ApiRoleDel(item.ID).then((res) => {
            if (res.Status == 0) {
              Tips();
              getList();
            }
          });
        });
      })
      .catch(() => {
        ElMessage({
          type: "info",
          grouping: true,
          message: "已取消",
        });
      });
  }
};
// 编辑
const edit = async (row) => {
  console.log(row.Type, "王文杰");
  dialogTitle.value = "编辑";
  dialogShow.value = true;
  formInline.ID = row.ID;
  formInline.Name = row.Name;
  formInline.CreatorTime = row.CreatorTime;
  let t;
  if (row.Type == "0") {
    t = "管理员";
  } else if (row.Type == "1") {
    t = "运维人员";
  } else {
    t = "普通用户";
  }
  formInline.Type = t;
  try {
    await formRef.value.validate((valid) => {
      if (valid) {
      } else {
        console.log("校验失败，表单中有错误！");
        return false;
      }
    });
  } catch (err) {
    console.error("表单校验出错：", err);
  }
};
const addEdit = () => {
  ApiRoleUpdate(formInline).then((res) => {
    if (res.Status == 0) {
      dialogShow.value = false;
      Tips();
      getList();
    }
  });
};
const checEdit = async () => {
  if (SelectList.value.length == 0) {
    ElMessage({
      message: "请至少选中一行",
      grouping: true,
      type: "warning",
      messageBox: true,
    });
  } else if (SelectList.value.length > 1) {
    ElMessage({
      message: "最多选择一个设备",
      grouping: true,
      type: "warning",
      messageBox: true,
    });
  } else {
    SelectList.value.forEach((item) => {
      edit(item);
    });
  }
};
// 编辑权限
// 选中的编辑
const checked = ref("");
const tabPosition = ref("left");
const ce = ref([]);
const treeRef = ref();
const editPermissions = (row) => {
  // 检查是否有选中的节点
  ce.value = [];
  dialogTitle.value = "编辑权限";
  checked.value = row.ID;
  ApiModuleRoleId(checked.value).then((res) => {
    if (res.Status == 0) {
      ce.value = res.Result;
    } else {
      ce.value = [];
    }
    nextTick(() => {
      console.log(ce.value);
      // console.log(treeRef.value.data);
      // treeRef.value.setCheckedNodes(ce.value);
      treeRef.value.setCheckedNodes([]);
      setSelectedNodes(treeRef, ce.value);
    });
  });

  dialogShow.value = true;
};
function setSelectedNodes(treeRef, nodes) {
  nodes.forEach((node) => {
    // 直接设置当前节点的选中状态
    treeRef.value.setChecked(node, true);
    // 如果有子节点，仅设置直接子节点的选中状态
    if (node.children && node.children.length > 0) {
      node.children.forEach((child) => {
        treeRef.value.setChecked(child, true);
      });
    }
  });
}

// 调用函数来设置选中节点
setSelectedNodes(treeRef, ce.value);

// 搜索
const search = () => {
  ApiRolePageList(
    pageIndex.value,
    pageSize.value,
    roleName.value,
    permissionCharacter.value
  ).then((res) => {
    List.value = res.Result;
  });
};
// 获取选中的
const handleSelectionChange = (val) => {
  SelectList.value = val;
};
// 重置
const reset = () => {
  roleName.value = "";
  permissionCharacter.value = "";
  getList();
};
// 分页
const handleSizeChange = (val) => {
  pageSize.value = val;
  getList();
};
const handleCurrentChange = (val) => {
  pageIndex.value = val;
  getList();
};
// 页面权限选择的change事件
const treeList = ref([]);
const handleCheckChange = () => {
  let listF = treeRef.value.getHalfCheckedKeys();
  let listC = treeRef.value.getCheckedKeys();
  treeList.value = listF.concat(listC);
};
// 授权
const accredit = () => {
  const payload = {
    RoleId: checked.value,
    Moduleids: treeList.value,
  };
  // 暂无更改
  if (treeList.value.length == 0) {
    ElMessage({
      type: "success",
      grouping: true,
      message: "设置成功   重新登陆后生效",
    });
  } else {
    // 改变的
    ApisetModule(payload).then((res) => {
      if (res.data.Status == 0) {
        ElMessage({
          type: "success",
          grouping: true,
          message: "设置成功   重新登陆后生效",
        });
      }
    });
  }
  // 关闭弹窗
  dialogShow.value = false;
};
</script>
<style lang="scss" scoped>
.box {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  color: #fff;
}

.top {
  height: 120px;
  background-color: red !important;
  width: 100%;
}

.userBG-Rde {
  width: 67px;
  height: 28px;
  color: #f13f5c;
  background: rgba(241, 63, 92, 0.17);
  border: 1px solid #f13f5c;
  border-radius: 6px;
  margin: auto;
}

.userBG-Green {
  width: 67px;
  height: 28px;
  background: rgba(63, 241, 180, 0.17);
  border: 1px solid #3ff1b4;
  color: #3ff1b4;
  border-radius: 6px;
  margin: auto;
}

// .userBlue {
//   width: 67px;
//   height: 28px;
//   border: 1px solid #40f4f1;
//   color: #40f4f1;
//   cursor: pointer;
//   border-radius: 6px;
//   margin: auto;
// }
.userRde {
  width: 86px;
  height: 32px;
  color: #f13f5c;
  border: 1px solid #f13f5c;
  border-radius: 6px;
  cursor: pointer;
  margin: auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.img {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

::v-deep .el-form-item__label {
  color: #fff !important;
}

// 修改输入框
::v-deep .el-input__wrapper {
  background-color: transparent !important;
}

::v-deep .el-input__inner {
  color: #fff !important;
}

::v-deep .el-textarea__inner {
  background-color: transparent !important;
  color: #fff;
}

// 修改扇形
::v-deep .el-tree-node__content:active {
  background-color: transparent !important;
}

::v-deep .el-tree {
  background-color: transparent !important;
  color: #fff;

  :hover {
    background: rgba(10, 162, 238, 0.25);
  }
}

::v-deep .el-tabs__item.is-active {
  color: #409eff;
}

::v-deep .el-tabs__item {
  color: #fff;
}

.scroll-container {
  height: 350px;
  /* 设置容器高度 */
  overflow: auto;
  /* 显示滚动条 */

  /* 自定义滚动条样式 */
  scrollbar-color: #999 transparent;
  /* 滚动条颜色 */
  scrollbar-width: thin;
  /* 滚动条宽度 */
}

.scroll-container::-webkit-scrollbar {
  width: 6px;
  /* 设置滚动条宽度 */
  height: 6px;
  /* 设置滚动条高度 */
}

.scroll-container::-webkit-scrollbar-thumb {
  background-color: #4d89c9;
  /* 设置滚动条 thumb 颜色 */
}

::v-deep .el-select--large .el-select__wrapper {
  min-height: 32rem;
}

// xmm修改 2025 02 12  修改表格样式
::v-deep .el-table th {
  color: #ffb30f;
  /* 修改表头字体颜色 */
  font-weight: bold;
  /* 可选：加粗字体 */
}

/* 自定义高亮样式 */
::v-deep .el-table__body tr.current-row>td {
  background-color: #5e430a;
  /* 高亮背景色 */
}

// xmm修改 2025-02-13
::v-deep .el-dialog {
  // width: 850px;
  // height: 500px;
  background: rgb(0, 0, 0) !important;
  box-shadow: rgb(255, 179, 15) 0px 0px 20px inset !important;
  border: 1px solid rgba(255, 179, 15, 0.3) !important;
  border-radius: 10px !important;
}

::v-deep .el-button+.el-button {
  background-color: rgba(255, 179, 15, 0.3) !important;
  border: 1px solid rgba(232, 157, 6, 0.8) !important;
}

// xmm修改 2025 02 24  修改表格样式
::v-deep .el-table td.el-table__cell div {
  --un-text-opacity: 1;
  color: rgba(227, 216, 183, var(--un-text-opacity));

}

// 弹窗
::v-deep .el-dialog__body {
  width: 100%;
  height: 70%;
  display: flex;
  justify-content: center;
  align-items: center;
}

// xmm 修改input边框颜色 2025-03-04
::v-deep .el-input__wrapper {
  --el-input-focus-border-color: rgba(232, 157, 6, 0.8) !important;
}

// xmm 修改input边框颜色 2025-03-05
::v-deep .el-select__wrapper.is-focused {
  box-shadow: 0 0 0 1px rgba(232, 157, 6, 0.8) !important;
}
// xmm 优化弹窗大小布局 2025-03-17
::v-deep .addDialog{
  width: 28vw;
  height: 44vh;
  .el-dialog__body{
    height: 78%;
    padding-top: 6%;
    padding-left: 10%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
}
::v-deep .editDialog{
  width: 26vw;
  height: 28vh;
  .el-dialog__body{
    height: 66%;
  }
}

</style>