<template>
  <div style="height: 300px;">123</div>
  <el-tree
    :data="list"
    :props="defaultProps"
    node-key="id"
    ref="tree"
    show-checkbox
    @check-change="handleCheckChange"
  >
    <template v-slot:default="{ node, data }">
      <el-checkbox
        v-if="data.defaultChecked"
        v-model="data.checked"
        :disabled="data.disabled"
      >
        {{ node.label }}
      </el-checkbox>
    </template>
  </el-tree>
</template>  
    
  <script>
  import { ref, reactive } from 'vue';
  import { ElTree, ElCheckbox } from 'element-plus'; 

  export default {
    components: { ElTree, ElCheckbox },
    setup() {
      const list = [
        {
          id: 1,
          label: 'Level one 1',
          defaultChecked: true,
          children: [
            { id: 4, label: 'Level two 1-1' },
            { id: 5, label: 'Level two 1-2' },
          ],
        },
        { id: 2, label: 'Level one 2' },
        { id: 3, label: 'Level one 3' },
      ];
      const defaultProps = {
        children: 'children',
        label: 'label',
      };
      const checkedList = reactive(list.filter(item => item.defaultChecked)); // 根据defaultChecked筛选出默认选中的节点列表
      const handleCheckChange = (data, checked) => {
        console.log(data, checked); // 处理复选框的更改事件，可以根据实际需求修改逻辑
      };
      return { list, defaultProps, checkedList, handleCheckChange };
    },
  };
</script>