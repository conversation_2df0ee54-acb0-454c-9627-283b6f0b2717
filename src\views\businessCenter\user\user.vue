<template>
  <div class="box statementBox">
    <!-- 头部分装 -->
    <encapsulationTable>
      <template #title> 用户管理 </template>
      <!-- 标头 -->
      <!-- 左边 -->
      <template #top-left>
        <p class="bullue1" @click="add(formRef)">
          <img class="img" src="../img/plusNew.png" alt="" />新增
        </p>
      </template>
      <template #top-center>
        <p class="bullue" @click="checEdit">
          <img class="img" src="../img/editNew.png" alt="" />编辑
        </p>
      </template>
      <template #top-right>
        <p class="red" @click="checkbox">
          <img class="img" src="../img/del.png" alt="" />删除
        </p>
      </template>
      <!-- 右边 -->
      <template #right-01>
        <span mr-10px>登录账号</span>
        <el-input v-model="roleName" placeholder="请输入" style="width: 250rem"></el-input>
      </template>
      <template #right-02>
        <span mr-10>手机号 </span>
        <el-input v-model="permissionCharacter" placeholder="请输入" style="width: 250rem"></el-input>
      </template>
      <template #right-03>
        <p class="bullue1" @click="search">
          <img class="img" src="../img/searchNew.png" alt="" /> 搜索
        </p>
      </template>
      <template #right-04>
        <p class="bullue" @click="reset">
          <img class="img" src="../img/resetNew.png" alt="" />重置
        </p>
      </template>

      <template #table>
        <el-table ref="multipleTableRef" :data="List" highlight-current-row :cell-style="{ textAlign: 'center' }"
          :header-cell-style="{ 'text-align': 'center' }" @selection-change="handleSelectionChange"
          style="overflow: auto; height: 100%">
          <el-table-column type="selection" width="55" />
          <el-table-column type="index" width="80" label="序号" />
          <el-table-column property="Account" label="登录账号" />
          <el-table-column label="角色名称" width="200">
            <template #default="{ row }">
              <span v-if="row.Roles">
                <span v-for="(role, index) in row.Roles" :key="index">
                  {{ role.Name }}
                  <span v-if="index < row.Roles.length - 1">, </span>
                </span>
              </span>
            </template>
          </el-table-column>
          <el-table-column property="MobilePhone" label="用户手机号" />
          <el-table-column property="Email" label="邮箱" />
          <el-table-column label="角色状态">
            <template #default="scope">
              <div class="userBG-Rde" v-if="scope.row.Enable == false">
                停用
              </div>
              <div class="userBG-Green" v-if="scope.row.Enable == true">
                正常
              </div>
            </template>
          </el-table-column>
          <el-table-column property="Description" label="描述" />
          <el-table-column width="300" label="操作">
            <template #default="scope">
              <div style="display: flex">
                <div class="bullue1" @click="edit(scope.row)">编辑</div>
                <div class="bullue1 ml-100" style="width: 70px" @click="accredit(scope.row)">
                  用户授权
                </div>
                <div class="userRde" @click="del(scope.row)">删除</div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pages>
        <el-pagination :page-sizes="[50, 100, 200, 400]" layout="total, sizes, prev, pager, next, jumper" :total="total"
          @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </template>
    </encapsulationTable>

    <!-- 弹框 -->
    <el-dialog v-model="dialogShow" :title="dialogTitle">
      <div v-if="dialogTitle == '用户授权'">
        <p>
          <el-form :inline="true" :label-position="right" :model="formInline">
            <el-form-item label="角色称名">
              <el-input v-model="roleAccount" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="角色权限">
              <el-select style="width: 250rem" v-model="roleAUTH" placeholder="请选择" size="large">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <!--  <el-input v-model="roleAUTH" placeholder="请输入" /> -->
            </el-form-item>
            <el-form-item @click="getRoleList">
              <div class="bullue1">
                <img class="img" src="../img/searchNew.png" alt="" /> 搜索
              </div>
            </el-form-item>
          </el-form>
        </p>
        <el-table ref="tableRef" :data="accreditList" :cell-style="{ textAlign: 'center' }"
          :header-cell-style="{ 'text-align': 'center' }" @selection-change="accreditListChange"
          style="overflow: auto; height: 300px">
          <el-table-column type="selection" width="55" />
          <el-table-column type="index" width="80" label="序号" />
          <el-table-column property="Name" label="角色名称" />
          <el-table-column property="Type" label="角色名称">
            <template #default="scope">
              <p v-if="scope.row.Type == 0">管理员</p>
              <p v-if="scope.row.Type == 1">运维人员</p>
              <p v-if="scope.row.Type == 2">普通用户</p>
            </template>
          </el-table-column>
          <el-table-column property="CreatorTime" label="创建时间">
            <template #default="scope">
              {{
                scope.row.CreatorTime != null
                  ? scope.row.CreatorTime.split(".")[0]
                  : ""
              }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-if="dialogTitle == '新增' || dialogTitle == '编辑'" style="width: 80%">
        <el-form :label-position="right" :model="formInline" :rules="rules" ref="formRef">
          <el-form-item label="登录账号" prop="Account">
            <el-input v-model="formInline.Account" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="用&ensp;户&ensp;名" prop="UserName">
            <el-input v-model="formInline.UserName" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="手&ensp;机&ensp;号" prop="MobilePhone">
            <el-input v-model="formInline.MobilePhone" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="邮&emsp;&emsp;箱" prop="Email">
            <el-input v-model="formInline.Email" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="登录密码" v-if="dialogTitle == '新增'" prop="UserPassword">
            <el-input v-model="formInline.UserPassword" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label=" &emsp;角色状态">
            <el-switch v-model="formInline.Enable" class="mb-2" active-text="正常" inactive-text="停用" />
          </el-form-item>
          <el-form-item label="描&emsp;&emsp;述" prop="Description">
            <el-input v-model="formInline.Description" :rows="2" type="textarea" placeholder="请输入" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogShow = false">取消</el-button>
          <el-button v-if="dialogTitle == '新增'" type="primary" @click="addList">
            添加
          </el-button>
          <el-button v-if="dialogTitle == '编辑'" type="primary" @click="addEdit">
            确定
          </el-button>
          <el-button v-if="dialogTitle == '用户授权'" type="primary" @click="accreditAdd">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 授权弹框 -->
  </div>
</template>
<script setup>
// 引入样式
import "../css/index.scss";
import "../css/el-pagination.scss";
import { ElMessage, ElMessageBox } from "element-plus";
// 用户接口
import {
  ApiUserAdd,
  ApiUserDel,
  ApiUserfuzzyquery,
  ApiUserUpdate,
  ApidistributionRole,
} from "@/https/encapsulation/user";
// 用户授权接口
import { ApiRolePageList } from "@/https/encapsulation/Role";
import { ref, watch, nextTick } from "vue";
import encapsulationTable from "../components/table.vue";
import { reactive } from "vue";
import { onMounted } from "vue";
// 弹框名字
const dialogTitle = ref("");
const dialogShow = ref(false);
const roleName = ref("");
const permissionCharacter = ref("");
// 获取选中的
const SelectList = ref([]);
// 分页
const pageSize = ref(50);
const pageIndex = ref(1);
const total = ref(0);
// 角色权限
const options = [
  {
    value: "0",
    label: "管理员",
  },
  {
    value: "1",
    label: "运维人员",
  },
  {
    value: "2",
    label: "普通用户",
  },
];
// 进入页面执行
const List = ref([]);
onMounted(() => {
  getList().then((v) => {
    getRoleList();
  });
});
// 进入页面数据

const getList = async () => {
  ApiUserfuzzyquery(pageSize.value, pageIndex.value).then((res) => {
    if (res.Status == 0) {
      List.value = res.Result;
      total.value = res.PageInfo.Total;
    } else {
      List.value = [];
      total.value = 0;
    }
  });
};
const accreditList = ref([]);
const tableRef = ref();
const getRoleList = () => {
  ApiRolePageList("", "", roleAccount.value, roleAUTH.value).then((res) => {
    accreditList.value = res.Result;
  });
};
// 添加
const formInline = reactive({
  Account: "",
  UserName: "",
  MobilePhone: "",
  Email: "",
  Enable: true,
  Description: "",
  UserPassword: "",
});
// 表单校验
const rules = {
  Account: [{ required: true, message: "请输入登录账号", trigger: "blur" }],
  UserName: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  MobilePhone: [{ required: true, message: "请输入手机号" }],
  Email: [
    {
      type: "email",
      required: true,
      message: "请输入正确的邮箱",
      trigger: ["blur", "change"],
    },
  ],
  Description: [{ required: true, message: "请输入描述", trigger: "blur" }],
  UserPassword: [
    { required: true, message: "请输入角色名称", trigger: "blur" },
  ],
};
const add = (formEl) => {
  dialogTitle.value = "新增";
  formInline.UserId = "";
  formInline.Account = "";
  formInline.UserName = "";
  formInline.MobilePhone = "";
  formInline.Email = "";
  formInline.Enable = true;
  formInline.Description = "";
  dialogShow.value = true;
  // if (!formEl) return;
  formEl.resetFields();
};
const formRef = ref(null);
// 添加
const addList = async () => {
  try {
    await formRef.value.validate((valid) => {
      if (valid) {
        ApiUserAdd(formInline).then((res) => {
          if (res.Status == 0) {
            getList();
            Tips();
            dialogShow.value = false;
          } else {
            ElMessage({
              type: "warning",
              message: res.Msg,
            });
          }
        });
        // 这里可以添加提交表单的逻辑
      } else {
        ElMessage({
          type: "warning",
          grouping: true,
          message: "必填参数为空",
        });
        return false;
      }
    });
  } catch (err) {
    console.error("表单校验出错：", err);
  }
};
const Tips = () => {
  ElMessage({
    type: "success",
    grouping: true,
    message: "成功",
  });
};
// 删除
const del = (row) => {
  ElMessageBox.confirm("此操作将永久删除该文件, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      ApiUserDel(row.ID).then((res) => {
        if (res.Status == 0) {
          getList();
          Tips();
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "已取消",
      });
    });
};
// 多选删除
const checkbox = () => {
  if (SelectList.value.length == 0) {
    ElMessage({
      message: "请至少选中一行",
      grouping: true,
      type: "warning",
      messageBox: true,
    });
  } else {
    ElMessageBox.confirm("此操作将永久删除该文件, 是否继续?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        SelectList.value.forEach((item) => {
          ApiUserDel(item.UserId).then((res) => {
            if (res.Status == 0) {
              Tips();
              getList();
            }
          });
        });
      })
      .catch(() => {
        ElMessage({
          type: "info",
          message: "已取消",
        });
      });
  }
};
// 编辑
const edit = async (row) => {
  console.log(row);
  dialogTitle.value = "编辑";
  dialogShow.value = true;
  formInline.UserId = row.ID;
  formInline.Account = row.Account;
  formInline.UserName = row.UserName;
  formInline.MobilePhone = row.MobilePhone;
  formInline.Email = row.Email;
  formInline.Enable = row.Enable;
  formInline.Description = row.Description;
  formInline.UserPassword = row.UserPassword;
  try {
    await formRef.value.validate((valid) => {
      if (valid) {
      } else {
        console.log("校验失败，表单中有错误！");
        return false;
      }
    });
  } catch (err) {
    console.error("表单校验出错：", err);
  }
};
const addEdit = () => {
  ApiUserUpdate(formInline).then((res) => {
    if (res.Status == 0) {
      dialogShow.value = false;
      Tips();
      getList();
    }
  });
};
const checEdit = () => {
  if (SelectList.value.length == 0) {
    ElMessage({
      message: "请至少选中一行",
      type: "warning",
      grouping: true,
      messageBox: true,
    });
  } else if (SelectList.value.length > 1) {
    ElMessage({
      message: "最多选择一个设备",
      type: "warning",
      messageBox: true,
    });
  } else {
    SelectList.value.forEach((item) => {
      edit(item);
    });
  }
};
// 搜索
const search = () => {
  ApiUserfuzzyquery(
    pageSize.value,
    pageIndex.value,
    roleName.value,
    permissionCharacter.value
  ).then((res) => {
    List.value = res.Result;
  });
};
// 获取选中的
const handleSelectionChange = (val) => {
  SelectList.value = val;
};
// 重置
const reset = () => {
  roleName.value = "";
  permissionCharacter.value = "";
  getList();
};
// 角色手机号 搜索
const roleAccount = ref("");
const roleAUTH = ref("");
// 用户授权选中的
const roleList = ref([]);
const user = ref({});

const accredit = (row) => {
  dialogTitle.value = "用户授权";
  dialogShow.value = true;
  user.value = row;
  nextTick((v) => {
    accreditList.value.forEach((item) => {
      if (row.Roles.map((v) => v.ID).includes(item.ID)) {
        console.log(item);
        tableRef.value.toggleRowSelection(item, true);
      } else {
        tableRef.value.toggleRowSelection(item, false);
      }
    });
  });
};
// 获取选中的用户授权
const accreditListChange = (val) => {
  roleList.value = val;
};
// 添加
const accreditAdd = () => {
  let data = {
    UserId: user.value.ID,
    Roleids: roleList.value.map((v) => v.ID),
  };
  console.log(roleList.value.length);
  if (roleList.value.length != 0) {
    ApidistributionRole(data).then((res) => {
      if (res.data.Status == 0) {
        getList();
        Tips();
      }
    });
  }
  dialogShow.value = false;
};
// 分页
const handleSizeChange = (val) => {
  pageSize.value = val;
  getList();
};
const handleCurrentChange = (val) => {
  pageIndex.value = val;
  getList();
};
</script>
<style lang="scss" scoped>
.box {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  color: #fff;
}

.top {
  height: 120px;
  background-color: red !important;
  width: 100%;
}

.userBG-Rde {
  width: 67px;
  height: 28px;
  color: #f13f5c;
  background: rgba(241, 63, 92, 0.17);
  border: 1px solid #f13f5c;
  border-radius: 6px;
  margin: auto;
}

.userBG-Green {
  width: 67px;
  height: 28px;
  background: rgba(63, 241, 180, 0.17);
  border: 1px solid #3ff1b4;
  color: #3ff1b4;
  border-radius: 6px;
  margin: auto;
}

// .userBlue {
//   width: 67px;
//   height: 28px;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   border: 1px solid #40f4f1;
//   color: #40f4f1;
//   cursor: pointer;
//   border-radius: 6px;
//   margin: auto;
// }
.userwhite {
  width: 88px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #fff;
  color: #fff;
  cursor: pointer;
  border-radius: 6px;
  margin: auto;
}

.userRde {
  width: 86px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #f13f5c;
  border: 1px solid #f13f5c;
  border-radius: 6px;
  cursor: pointer;
  margin: auto;
}

.img {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

.characters_center_left_title {
  cursor: pointer;
  width: 86px;
  height: 32px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 32px;
  background: rgba(43, 155, 216, 0);
  background-image: url("../img/blue_border.png");
  background-size: 100% 100%;
}

::v-deep .el-form-item__label {
  color: #fff !important;
}

// 修改输入框
::v-deep .el-input__wrapper {
  background-color: transparent !important;
}

::v-deep .el-input__inner {
  color: #fff !important;
}

::v-deep .el-textarea__inner {
  background-color: transparent !important;
  color: #fff;
}

// 开关
/* 将关闭状态文字颜色设置为白色 */
::v-deep .el-switch__label {
  color: #ffffff;
}

/* 将开启状态文字颜色设置为红色 */
::v-deep .inactive-text {
  color: #ff0000;
}

::v-deep .el-select--large .el-select__wrapper {
  min-height: 32rem;
}

// xmm修改 2025 02 12  修改表格样式
::v-deep .el-table th {
  color: #ffb30f;
  /* 修改表头字体颜色 */
  font-weight: bold;
  /* 可选：加粗字体 */
}

/* 自定义高亮样式 */
::v-deep .el-table__body tr.current-row>td {
  background-color: #5e430a;
  /* 高亮背景色 */
}

// xmm修改 2025-02-13
::v-deep .el-dialog__header {
  height: 13% !important;
}

// xmm修改 2025-02-13
::v-deep .el-dialog {
  width: 38vw;
  height: 50vh;
  background: rgb(0, 0, 0) !important;
  box-shadow: rgb(255, 179, 15) 0px 0px 20px inset !important;
  border: 1px solid rgba(255, 179, 15, 0.3) !important;
  border-radius: 10px !important;
}

// xmm 修改 2025-03-17
::v-deep .dialogBox{
  ::v-deep .el-dialog__body{
    height: 62%;
    margin-top: 6%;
  }
}
::v-deep .el-button+.el-button {
  background-color: rgba(255, 179, 15, 0.3) !important;
  border: 1px solid rgba(232, 157, 6, 0.8) !important;
}

// xmm修改 2025 02 24  修改表格样式
::v-deep .el-table td.el-table__cell div {
  --un-text-opacity: 1;
  color: rgba(227, 216, 183, var(--un-text-opacity));
}

// 弹窗
::v-deep .el-dialog__body {
  width: 100%;
  height: 70%;
  display: flex;
  justify-content: center;
  align-items: center;
}

// xmm 修改input边框颜色 2025-03-04
::v-deep .el-input__wrapper {
  --el-input-focus-border-color: rgba(232, 157, 6, 0.8) !important;
}

// xmm 修改select边框颜色 2025-03-05
::v-deep .el-select__wrapper.is-focused {
  box-shadow: 0 0 0 1px rgba(232, 157, 6, 0.8) !important;
}

// xmm 修改textarea边框颜色 2025-03-05
::v-deep .el-textarea__inner {
  --el-input-focus-border-color: rgba(232, 157, 6, 0.8) !important;
}
</style>
