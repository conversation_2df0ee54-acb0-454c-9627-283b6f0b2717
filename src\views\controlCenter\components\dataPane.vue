<template>
    <!-- 矿井负压 -->
    <DataBox class=" fixed top-116px left-24px box1" title="矿井负压">
        <template #mainFan>
            <div class="flex justify-around mt-40px">
                <div class="text-center" v-for="(item, i) in box1Data" :key="item.name">
                    <div :class="'circleBox' + (i + 1)" class="flex justify-center items-center mx-auto">
                        <el-progress :width="70" :striped-flow="true" :indeterminate="true" :duration="3" type="circle"
                            :percentage="item.data" :color="item.color">
                            <template #default>
                                <!-- <div class="box1-value " :style="{ color: item.color }">
                                    {{ item.data }}%</div> -->
                                <div v-if="i == 0" class="box1-value " :style="{ color: item.color }">
                                    {{ Math.floor(number1) }}%</div>
                                <div v-else-if="i == 1" class="box1-value " :style="{ color: item.color }">
                                    {{ Math.floor(number2) }}%</div>
                                <div v-else class="box1-value " :style="{ color: item.color }">
                                    {{ Math.floor(number3) }}%</div>
                            </template>
                        </el-progress>
                    </div>
                    <div class="box1_name">{{ item.name }}</div>
                </div>
            </div>
        </template>
    </DataBox>
    <!-- 矿井风量 -->
    <DataBox class=" fixed top-367px left-24px box1 box2" title="矿井风量">
        <template #mainFan>
            <div flex justify-around mt-20px>
                <div class="flex items-center flex-col mt-10px">
                    <div class="box2-left-img">
                        <div class="w-40px h-40px">
                            <img src="../assets/img/dataPaneImg/box2_left_icon.png"
                                class="w-40px h-40px box2-left-icon ">
                        </div>
                    </div>
                    <div class="box2-left-data">-360.00m³/min</div>
                    <div class="box2-left-label">矿井总风量</div>
                </div>
                <div class="">
                    <div class="flex justify-around items-center" v-for="item in box2Data" :key="item.name">
                        <div class="box2-right-img">
                            <div class="w-16px h-16px turn"><img src="../assets/img/dataPaneImg/box2_right_icon.png" />
                            </div>
                        </div>
                        <div class="ml-5px">
                            <div class="box2-right-label">{{ item.name }}</div>
                            <div class="box2-right-data">{{ item.data }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </DataBox>
    <!--  等积孔-->
    <DataBox class=" fixed top-603px left-24px box1 box3" title="等积孔">
        <template #mainFan>
            <div flex justify-around mt-40px>
                <div class="flex justify-around items-center" v-for="item in box3Data" :key="item.name">
                    <div class="box3-data-bg">
                        <div class="box3-icon  mx-auto"><img class="w-51px h-51px turn"
                                src="../assets/img/dataPaneImg/box3_icon_fs.png"></div>
                        <div class="box3-data">{{ item.data }}</div>
                        <div class="box3-label">{{ item.name }}</div>
                    </div>
                </div>
            </div>
        </template>
    </DataBox>
    <!--  采煤工作面-->
    <DataBox class=" fixed top-843px left-24px box1 box4" title="采煤工作面">
        <template #mainFan>
            <div flex justify-around mt-70px>
                <div>
                    <div class="box4_data text-#2AEDFF">0</div>
                    <div class="box4_label"><span>生产</span></div>
                </div>
                <div><img class="w-159px h-114px" src="../assets/img/dataPaneImg/box4_zs.png" alt=""></div>
                <div>
                    <div class="box4_data text-#F6BB2E">0</div>
                    <div class="box4_label"><span>结束</span></div>
                </div>
            </div>
        </template>
    </DataBox>
    <!--  掘进工作面-->
    <DataBox class=" fixed top-116px right-24px box1 box3 box5" title="掘进工作面">
        <template #mainFan>
            <div flex justify-around mt-40px>
                <div class="flex justify-around items-center" v-for="item in box5Data" :key="item.name">
                    <div class="box3-data-bg">
                        <div class="box3-icon mx-auto mt-[-10px]">
                            <img class="box5_icon w-77px h-77px" :src="item.icon">
                            <div class="box5-data text-theme">{{ item.data }}个</div>
                        </div>

                        <div class="box5-label">{{ item.name }}</div>
                    </div>
                </div>
            </div>
        </template>
    </DataBox>
    <!--  实用局扇数量-->
    <DataBox class=" fixed top-359px right-24px box1 box6" title="实用局扇数量">
        <template #mainFan>
            <div flex justify-around mt-60px>
                <div>
                    <div class="box6_left_data relative">
                        <div class=" absolute top--10px left-25%">170台</div>
                    </div>
                    <div class="box6_left_label mt-10px mx-auto">总数量</div>
                </div>
                <div class="box6_right_box">
                    <div>
                        <div class="flex justify-between">
                            <span class="box6_right_label">运行中</span>
                            <span class="box6_right_data">57台</span>
                        </div>
                        <div> <el-slider disabled v-model="box6_data1" /></div>
                    </div>
                    <div mt-12px>
                        <div class="flex justify-between">
                            <span class="box6_right_label">备用停机</span>
                            <span class="box6_right_data">113台</span>
                        </div>
                        <div><el-slider disabled v-model="box6_data2" /></div>
                    </div>

                </div>
            </div>
        </template>
    </DataBox>
    <!--  实用风门数量-->
    <DataBox class=" fixed top-602px right-24px box1 box7" title="实用风门数量">
        <template #mainFan>
            <div flex justify-around mt-60px>
                <div v-for="item in box7Data" :key="item.name">
                    <div class="box7_data">
                        <div>{{ item.data }} 扇</div>
                    </div>
                    <div class="box7_label mt-5px">{{ item.name }}</div>
                </div>
            </div>
        </template>
    </DataBox>
    <!--  瓦斯抽采量-->
    <DataBox class=" fixed top-837px right-24px box1 box8" title="瓦斯抽采量">
        <template #mainFan>
            <div mt-60px ml-27px>
                <div class="box8_data_box px-20px mt-10px" v-for="item in box8Data" :key="item.name" flex items-center
                    justify-between>
                    <div><img class="w-18px h-24px" src="../assets/img/dataPaneImg/box8_icon.png" alt=""></div>
                    <div class="box8_label">{{ item.name }}</div>
                    <div class="box8_data">{{ item.data }}</div>
                </div>
            </div>
        </template>
    </DataBox>


</template>
<script setup>
import DataBox from '@/components/common/DataBox.vue'
import { ref, onMounted } from 'vue';
import gsap from 'gsap';
const number1 = ref(0)
const number2 = ref(0)
const number3 = ref(0)
const box1Data = ref([
    {
        color: '#5AADF4',
        name: '中央区主通风机',
        data: 0
    },
    {
        color: '#58E5E1',
        name: '南区主通风机',
        data: 0
    },
    {
        color: '#E07C39',
        name: '东区主通风机',
        data: 0
    },
])
onMounted(v => {
    box1Data.value = box1Data.value.map(v => {
        const data = Math.floor(Math.random() * 80 + 20);
        return { ...v, data }
    })
    gsap.fromTo(number1, { value: 0 }, { value: box1Data.value[0].data, duration: box1Data.value[0].data / 100 * 30 })
    gsap.fromTo(number2, { value: 0 }, { value: box1Data.value[1].data, duration: box1Data.value[1].data / 100 * 30 })
    gsap.fromTo(number3, { value: 0 }, { value: box1Data.value[2].data, duration: box1Data.value[2].data / 100 * 30 })
    // 
    gsap.fromTo('.box5_icon', { scale: 0.9 }, { scale: 1.1, duration: Math.random() * 1 + 2, repeat: -1, yoyo: true })
})

const box2Data = ref([
    {
        name: '中央区主通风机',
        data: '-360.00m³/min'
    },
    {
        name: '南区主通风机',
        data: '-360.00m³/min'
    },
    {
        name: '东区主通风机',
        data: '-360.00m³/min'
    },
])
const box3Data = ref([
    {
        name: '中央区主通风机',
        data: '-1.68'
    },
    {
        name: '南区主通风机',
        data: '-1.68'
    },
    {
        name: '东区主通风机',
        data: '-1.68'
    },
])
const box5Data = ref([
    {
        name: '现掘',
        data: 0,
        icon: 'box5_xj.png'
    },
    {
        name: '巷修',
        data: 0,
        icon: 'box5_hx.png'
    },
    {
        name: '准备',
        data: 0,
        icon: 'box5_zb.png'
    },
])
box5Data.value = box5Data.value.map((item) => ({
    ...item,
    icon: new URL(`../assets/img/dataPaneImg/${item.icon}`, import.meta.url),
}));

const box6_data1 = ref(12)
const box6_data2 = ref(35)

const box7Data = ref([
    {
        name: '总数量',
        data: 43
    },
    {
        name: '开状态',
        data: 0
    },
    {
        name: '关状态',
        data: 55
    },
])
const box8Data = ref([
    {
        name: '南区地面抽采泵站',
        data: '3859.8m³/min'
    },
    {
        name: '中央区新泵站管道',
        data: '6261.6m³/min'
    },
    {
        name: '中央区老泵站管道',
        data: '6261.6m³/min'
    },
])
onMounted(() => {

})
</script>
<style lang="scss" scoped>
// 


.circleBox1 {
    width: 92px;
    height: 92px;
    border: 2px solid #184E7F;
    border-radius: 50%;
}

.circleBox2 {
    @extend .circleBox1;
    border-color: #1A5D79;
}

.circleBox3 {
    @extend .circleBox1;
    border-color: #4B4B30;
}

:deep(.circleBox1 .el-progress-circle svg path:first-child) {
    stroke: #1F67B2;
}

:deep(.circleBox2 .el-progress-circle svg path:first-child) {
    stroke: #258FB3;

}

:deep(.circleBox3 .el-progress-circle svg path:first-child) {
    stroke: #8F681C;
}

.box1 {
    position: fixed;
    width: 488px;
    height: 229px;
    background-image: url("../assets/img/dataPaneImg/box_bg.png");
    background-size: 100%;

    .box1_name {
        width: 118px;
        height: 16px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #FFFFFF;
        line-height: 38px;
    }

    .box1-value {
        font-size: 28px;
        height: 72px;
        line-height: 72px;
        font-family: Alimama ShuHeiTi;
        font-weight: bold;
        font-size: 20px;
        font-style: italic;
        text-align: center;
    }
}

.box2 {
    .box2-left-img {
        width: 88px;
        height: 88px;
        background-image: url("../assets/img/dataPaneImg/box2_left_bg.png");
        background-size: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }


    .box2-left-data {
        width: 152px;
        height: 38px;
        font-family: DIN;
        font-weight: bold;
        font-size: 20px;
        color: #FFFFFF;
        line-height: 38px;
    }

    .box2-left-label {
        width: 75px;
        height: 38px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #76F7FF;
        line-height: 38px;
    }


    .box2-right-img {
        width: 39px;
        height: 39px;
        background-image: url("../assets/img/dataPaneImg/box2_right_bg.png");
        background-size: 100%;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
            width: 18px;
            height: 18px;
        }
    }

    .box2-right-label {
        width: 103px;
        height: 30px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #76F7FF;
        line-height: 30px;
    }

    .box2-right-data {
        width: 136px;
        height: 30px;
        font-family: DIN;
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        line-height: 30px;
    }
}

.box3 {
    .box3-data-bg {
        width: 134px;
        height: 134px;
        background-image: url("../assets/img/dataPaneImg/box3_icon_bg.png");
        background-size: 134px 134px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-around;
    }

    .box3-data {
        width: 61px;
        height: 17px;
        text-align: center;
        font-family: DIN;
        font-weight: bold;
        font-size: 24px;
        color: #FFFFFF;
        line-height: 17px;
    }

    .box3-label {
        width: 100px;
        text-align: center;
        height: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #76F7FF;
        line-height: 14px;
    }
}

.box4 {


    .box4_data {
        height: 21px;
        font-family: PingFang SC;
        font-weight: 800;
        font-size: 27px;
        line-height: 14px;
        text-align: center;
    }

    .box4_label {
        width: 109px;
        height: 77px;
        background-image: url("../assets/img/dataPaneImg/box4_data_zs.png");
        background-size: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        span {
            width: 32px;
            text-align: center;
            height: 16px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            font-size: 16px;
            color: #FFFFFF;
            line-height: 16px;
        }
    }
}

.box5 {
    .box5-data {
        height: 18px;
        text-align: center;
        font-family: DIN;
        font-weight: bold;
        font-size: 24px;
        color: #FFFFFF;
        height: 18px;
        /* text-shadow: 0 0 10px rgba(255, 255, 255, 0.8); */
        /* 设置文本阴影，模拟发光效果 */
    }

    .box5-label {
        width: 33px;
        height: 16px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #F0FAFB;
        line-height: 16px;
    }
}

.box6 {

    .box6_left_data {
        width: 154px;
        height: 75px;
        background-image: url("../assets/img/dataPaneImg/box6_left_img.png");
        background-size: 100%;
        text-align: center;

        div {
            display: block;
            height: 21px;
            text-align: center;
            font-weight: 500;
            font-size: 26px;
            color: #FFFFFF;
            line-height: 21px;
            color: #FFFFFF;
            text-shadow: 0 0 12px #fb8c00, 0 0 1px #fb8c00;
        }
    }

    .box6_left_label {
        margin-top: 10px auto;
        width: 50px;
        height: 16px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #F0FAFB;
        line-height: 16px;
    }

    .box6_right_box {
        width: 217px;

        .box6_right_label {
            width: 67px;
            height: 16px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            font-size: 16px;
            color: #F0FAFB;
            line-height: 16px;
        }

        .box6_right_data {
            width: 70px;
            height: 18px;
            font-family: DIN;
            font-weight: bold;
            font-size: 22px;
            color: #FFFFFF;
            line-height: 18px;
        }
    }

    :deep(.el-slider__runway) {
        background-color: #1B5C7A;
    }

    :deep(.el-slider__bar) {
        background-color: #36B3D1;
    }

    :deep(.el-slider__button) {
        background-color: transparent;
        position: relative;

        &::after {
            position: absolute;
            content: '⚪';
            font-size: 12px;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;

        }
    }
}

.box7 {
    .box7_data {
        width: 80px;
        height: 80px;
        background-image: url("../assets/img/dataPaneImg/box7_data_bg.png");
        background-size: 100%;
        display: flex;
        justify-content: center;
        align-items: center;

        div {
            width: 60px;
            height: 49px;
            background-image: url("../assets/img/dataPaneImg/box7_data_zs.png");
            background-size: 100%;
            text-align: center;
            font-family: DIN;
            font-weight: bold;
            font-size: 20px;
            color: #FEE74E;
            line-height: 49px;
        }
    }

    .box7_label {
        text-align: center;
        height: 16px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #F0FAFB;
        line-height: 16px;
    }
}

.box8 {
    .box8_data_box {
        width: 423px;
        height: 32px;
        background-image: url("../assets/img/dataPaneImg/box8_data_bg.png");
        background-size: 100%;
    }

    .box8_label {
        width: 137px;
        height: 16px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #76F7FF;
        line-height: 16px;
    }

    .box8_data {
        width: 127px;
        height: 19px;
        font-family: DIN;
        font-weight: 500;
        font-size: 22px;
        color: #FFFFFF;
        line-height: 19px;
    }
}
</style>