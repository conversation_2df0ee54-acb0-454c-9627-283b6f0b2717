<template>
    <!-- <div class="canvasBox" ref="sceneDiv"></div> -->
    <!-- <div v-if="!pageShow"> -->
    <div class="controlBg">
        <div class="icon_box z-2 glex flex flex-wrap justify-between items-center">
            <div :class="'gkzx_box' + (i + 1)" v-for="(v, i) in labelList" :key="v.name"
                class="icon_bg cursor-pointer  gkzx_box">

                <div class="card" @click="$router.push({ name: v.routeName })">
                    <div class="card-inner">
                        <div class="card-front">
                            <img :src="v.icon"
                                class="gkzx_img  w-60px h-60px absolute  left-50% translate-x-[-50%] top-29px" />
                            <span mt-80px>{{ v.name }}</span>
                        </div>
                        <div class="card-back">
                            <img :src="v.icon"
                                class="gkzx_img  w-60px h-60px absolute   left-50% translate-x-[-50%] top-29px" />
                            <span mt-80px>{{ v.name }}</span>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>


    <!-- </div> -->

    <!-- <DataPane v-else></DataPane> -->
</template>
<script setup>
import { onMounted, ref, toRaw, watch, watchEffect, nextTick, onUnmounted } from 'vue'
// import { mountRender, stopRender, labelShow } from './threeBg';
import { onBeforeRouteLeave, onBeforeRouteUpdate } from 'vue-router';
import { useNavBar } from '@/store/navBar';
import { useCommonStore } from '@/store/common';
import { useWindowSize } from '@vueuse/core'
import DataPane from './components/dataPane.vue';
import gsap from 'gsap';

const { width, height } = useWindowSize()
const commonStore = useCommonStore();
const store = useNavBar()

watchEffect(() => {
    if (store.currentRouteName === 'controlCenter') {
    }
})
onBeforeRouteLeave(() => {
    onUnmounted();
})
const sceneDiv = ref()
onMounted(() => {
    // mountRender(sceneDiv.value)
})
const pageShow = ref(false)
// threejs代码


let globalGlb = null;
var clickRemove = null;

// 旋转图标
const iconStyleList = [
    {
        icon: 'gqcw.png',
    },
    {
        icon: 'jbfj.png',
    },
    {
        icon: 'fmhjc.png',
    },
    {
        icon: 'znfm.png',
    },
    {
        icon: 'aqjc.png',
    },
    {
        icon: 'znfc.png',
    },
    {
        icon: 'swtf.png',
    },
    {
        icon: 'ztfj.png',
    },
    {
        icon: 'znjc.png',
    },

]
const labelList = ref([])
const menuList = []
onMounted(() => {
    setTimeout(() => {
        labelLoading()
    }, 200)
})

// watch(() => commonStore.ModuleRoleList, (val) => {
//     if (val.length == 10) {
//         labelLoading()
//     }
// })

function labelLoading() {
    commonStore.ModuleRoleList.forEach((v) => {
        if (v.ModuleId == "null" || !v.ModuleId) {
            menuList.push({ name: v.Name, routeName: v.Url })
        }
    });
    menuList.forEach((item, i) => {
        const { name, routeName } = menuList[i];
        if (name != '管控中心') {
            if (i < iconStyleList.length) {
                const icon = iconStyleList[i].icon;
                const obj = {
                    icon: new URL(`./assets/img/${icon}`, import.meta.url).href,
                    name,
                    routeName,
                }
                labelList.value.push(obj)
            }
        }
    })
}
// watch(() => commonStore.ModuleRoleList, (val) => {
//     if (val.length == 10) {
//         val.forEach((v) => {
//             if (v.ModuleId == "null" || !v.ModuleId) {
//                 menuList.push({ name: v.Name, routeName: v.Url })
//             }
//         });

//     }
// })





// 添加动画
onMounted(() => {
    gsap.fromTo('.gkzx_img', { opacity: 0.5, translateY: '-2px', transform: ' rotateY(-30deg)' }, { opacity: 1, translateY: '2px', transform: 'rotateY(30deg)', yoyo: true, duration: 2, repeat: -1 })
})





</script>
<style lang="scss" scoped>
.cssObj {
    background-color: #2EBBFCCC;
}

.icon_box {
    width: 1230px;
    height: 480px;
    margin: 10px auto;
    margin-top: 200px;

    .icon_bg {
        width: 279px;
        height: 69px;
        /* background-image: url(./assets/img/bg.png); */
        background-size: 279px 69px;
    }

    .icon_text {
        width: 250px;
        height: 54px;
        text-align: right;
        line-height: 54px;
        font-size: 24px;
        font-family: Microsoft YaHei;
        font-weight: bold;

    }
}

// card
.card {
    width: 300px;
    height: 200px;
    perspective: 1000px;
}

.card-inner {
    width: 100%;
    height: 100%;
    position: relative;
    transform-style: preserve-3d;
    transition: transform 0.999s;
}

.card:hover .card-inner {
    transform: rotateY(180deg);
}

.card-front,
.card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
}

.card-front {
    background: linear-gradient(135deg, #17ead9, #6078ea);
    color: #fff;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #17ead9, #6078ea);
    border-radius: 10px;
    justify-content: center;
    font-size: 24px;
    transform: rotateY(0deg);
}

.card-back {
    background: linear-gradient(135deg, #00c9ff, #92fe9d);
    color: #fff;
    display: flex;
    align-items: center;

    border-radius: 10px;
    justify-content: center;
    font-size: 24px;
    transform: rotateY(180deg);
}

.controlBg {
    width: 100vw;
    height: 100vh;
    opacity: 0.9;
    overflow: hidden;
    background: url(./assets/img/controlCenterBg.png) no-repeat;

    background-size: 100%;
}
</style>
