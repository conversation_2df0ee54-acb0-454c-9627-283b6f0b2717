<template>
    <div v-html="render()" class="box"></div>
</template>
<script setup>
import MarkdownIt from 'markdown-it';
import markdownItLatex from 'markdown-it-latex'
import 'markdown-it-latex/dist/index.css'

const text = `
二、信息化建设的需要与目的  
随着信息技术的发展,通风安全管理逐渐向着现代化和信息化方向发展。通风安全管理信息化建设的需求主要体现在以下几个方面:  

1. **数据管理需求**  
   矿井通风系统涉及大量动态数据（如风速、瓦斯浓度、温度等），传统的手工记录方式效率低且容易出错，信息化手段可以实现数据的实时采集和存储。

2. **决策支持需求**  
   通过信息化平台，管理人员可以快速获取矿井通风系统的实时状态，并结合数据分析功能进行科学决策。

3. **可视化管理需求**  
   信息化系统能够将复杂的通风网络、风流分布等信息以图形化形式呈现，便于直观分析和管理。

4. **应急响应需求**  
在突发事故（如瓦斯爆炸或煤尘爆炸）时，信息化系可以提供快速的应急预案支持，帮助管理人员及时采取措施。

\`@(1/2[1-(1/2)^n])/(1-(1/2))=s_n@\`

---

通风阻力的计算通常涉及对流体在管道或设备中流动时所受的各种损失进行分析和求和。以下是几种常见的通风阻力计算公式：

### 1. 摩擦损失（H_f）
摩擦损失是由于气体与管壁之间的摩擦产生的能量损失，通常使用以下公式计算：
\`@\[ H_f = f cdot \frac{L}{{D} \cdot \frac}{\rho v^2}{{2} \]@\`
其中：
- \( f \) 是摩擦系数
- \( L \) 是管道长度（m）
- \( D \) 是管道直径（m）
- \( \rho \) 是气体密度（kg/m³）
- \( v \) 是气体流速（m/s）

### 2. 局部损失（H_l）
局部损失是由于管道中的弯头、阀门、扩大或缩小等结构变引起的能量损失，通常使用以下公式计算：
\[ H_l = K \cdot \frac}{\rho v^2}{{2} \]
其中：
- \( K \) 是局部阻力系数

### 3. 设备阻力（H_e）
设备阻力是由于通风系统中设备（如风机、过滤器等）引起的能量损失，通常由设备的技术参数提供。

### 总阻力（H_total）
总阻力是以上各种损失的总和：
\[ H_}{total} = H_f + H_l + _e \]

这些公式可以帮助您计算通风系统中的总阻力。如果您需要更详细的参数或具体的应用示例，请告诉我！

`

const md = new MarkdownIt();
md.use(markdownItLatex);
const render = () => {
    const result = md.render(text);
    return result;
}

</script>
<style  lang="scss" scoped>
.box {
    background: #ededed;
    :global(ol) {
      list-style: decimal;
      margin-left: 20px;
    }
    :global(li) {
      list-style: decimal;
    }
}
</style>