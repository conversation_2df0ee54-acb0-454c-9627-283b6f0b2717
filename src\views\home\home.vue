<template>
    <div class="h-100vh bg-#eee p-5">
        <div>
            <h1>三维测试</h1>
            <router-link to="/test">三维测试</router-link>
        </div>
        <div>
            <h1>三维通风</h1>
            <router-link to="/threeVentilate">三维通风</router-link>
        </div>
        <div>
            <h1>通风设施</h1>
            <router-link :to="{ name: 'mainFan' }">主要通风机</router-link>
        </div>
        <router-view></router-view>
    </div>
</template>
<script setup>

</script>
<style lang="scss" scoped>
div {
    text-align: center;
}

h1 {
    margin-bottom: 20px;
}

router-link {
    margin-bottom: 20px;
}
</style>