<template>
  <!-- <ThreeBg /> -->
  <!-- <div class="video - background - container">
       
    </div> -->

  <div class="log">
    <video autoplay muted class="background - video">
      <source src="./login_back.mp4" type="video/mp4" />
    </video>
    <div class="animate__animated animate__fadeIn mask opacity-100">
      <div class="msk">
        <h1 style="
            width: 100%;
            text-align: center;
            height: 70px;
            line-height: 80px;
          ">
          矿&nbsp;井&nbsp;智&nbsp;能&nbsp;通&nbsp;风&nbsp;平&nbsp;台
        </h1>
        <div class="user">
          <div class="user-box">
            <!-- 欢迎登陆 -->
            <div class="welcome">
              <img class="img" src="./left.png" alt="" />
              <h3>欢迎登陆</h3>
              <img class="img" src="./right.png" alt="" />
            </div>
            <div class="input">
              <el-input v-model="aaa" size="large" placeholder="请输入用户名" />
            </div>
            <div>
              <el-input v-model="bbb" class="w-50 m-2" type="password" :prefix-icon="Search" placeholder="请输入密码"
                show-password />
            </div>
            <div class="input mt-20px">
              <span style="color: #01b2b9">
                <el-checkbox v-model="checked1" label="记住密码" style="color: #01b2b9" size="large" /></span>
            </div>
            <div h-40px>
              <el-button style="
                  width: 100%;
                  background-color: #01b2b9;
                  color: aliceblue;
                  border: 0px;
                  height: 100%;
                " @click="log">确定</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import ThreeBg from "./threeBg.vue";
import { Login } from "../../https/encapsulation/threeDimensional";
import { sign } from "../../https/encipher";
import { ref, onMounted, toRaw } from "vue";
import { ElMessage } from "element-plus";
import { useCommonStore } from "@/store/common";
import { useRouter } from "vue-router";
import { useLogin } from "@/store/login";
import { ApiModuleRoleId } from "@/https/encapsulation/Module";

const loginStore = useLogin();
const router = useRouter();
const store = useCommonStore();
const aaa = ref("");
const bbb = ref("");
onMounted(() => {
  store.$patch({
    token: {},
    name: "",
  });
});
const log = async () => {
  // 获取时间
  let t = new Date().getTime();
  //   加密
  var result = sign(t.toString(), bbb.value);
  console.log(result, "加密文件");
  //   接口请求
  const res = await Login(aaa.value, t, result);
  console.log(res);
  if (res.data.Result != null) {
    // console.log(res.data.Result.RoleIds,'返回的');
    let ModuleRoleList = [];
    store.$patch({
      ModuleRoleList: [],
    });
    res.data.Result.RoleIds.map(async (item) => {
      let ModuleRole = await ApiModuleRoleId(item);
      ModuleRole.Result.forEach((v) => {
        ModuleRoleList.push(v);
      });
      store.$patch({
        ModuleRoleList: removeDuplicateNames(ModuleRoleList),
      });
      store.$patch({
        userInfo: { name: aaa.value, password: bbb.value },
      });

    });
    setTimeout(() => {
      router.push("/controlCenter");
    }, 200);

    ElMessage({
      showClose: true,
      message: "登录" + res.data.Msg,
      type: "success",
    });
    function removeDuplicateNames(arr) {
      return arr.reduce((acc, curr) => {
        const found = acc.find((obj) => obj.ID === curr.ID);
        if (!found) {
          acc.push(curr);
        }
        return acc;
      }, []);
    }
    // 全局存储
    store.$patch({
      name: res.data.Result.Token.Account,
      token: res.data.Result.Token,
    });
  } else {
    ElMessage({
      showClose: true,
      message: res.data.Msg,
      type: "error",
    });
  }
};
</script>
<style lang="scss" scoped>
.log {
  width: 100vw;
  height: 100vh;
}

.lofin_back {
  position: relative;
  top: 0;
  width: 100%;
  padding: 0;
  margin: 0;

  overflow: hidden;

  height: 100vh;

}

.mask {
  color: aliceblue;
  position: absolute;
  width: 500px;
  height: 610px;
  top: 0;
  right: 200px;
  bottom: 0;
  margin: auto;
  border-radius: 5px;
}

.img {
  width: 35%;
}

.msk {
  width: 90%;
  height: 100%;
  margin: auto;
}

.user {
  width: 100%;
  height: 75%;
  // background-image: (url("./background.png"));
  background-image: (url("./background.png"));

  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-box {
  width: 75%;
  height: 90%;

  .welcome {
    height: 100px;
    align-items: center;
    justify-content: space-around;
    display: flex;
  }

  .input {
    width: 100%;
    height: 70px;
  }
}

// 修改输入框
::v-deep .el-input__wrapper {
  background-color: transparent !important;
}

::v-deep .el-input__inner {
  color: #fff !important;
}
</style>