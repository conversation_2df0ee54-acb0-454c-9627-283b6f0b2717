<template>
    <div ref="dom" class="canvasBox">
    </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { onBeforeRouteLeave, onBeforeRouteUpdate } from 'vue-router';
const dom = ref(null)
onMounted(() => {
    mountRender(dom.value)
})
onBeforeRouteLeave(() => {
    stopRender();
})

import { initThree } from '@/three/initThree1'
import { fbxLoader, gltfLoader, textureLoader } from "@/three/utils/loader";
import { addAmbientLight, addDirectionalLight, addHemisphereLight, addPointLight, addSpotLight } from "@/three/utils/light";
import { initFireflies, initShinePlane, initShinePoint } from '@/three/mesh/fireflies'
import { initNoise } from "@/three/mesh/noise";
import { findMesh, getArrayRandomItem, randomColor } from "@/three/utils/utils";
import { dbModelClick } from "@/three/utils/event";
import { initGlass, initGlass2 } from "@/three/material/glass";
import FlyLineShader from "@/three/mesh/FlyLineShader";
import { textureShine, glslShine } from '@/three/effect/shine';

const startCamera = [2.4, 3.2, 2.4]
const { gsap, TWEEN, scene, THREE, renderer, outlinePass, cameraControls, camera, addOutlinePass, ani, stopRender, addOutlinePass1 } = initThree(...startCamera, { isControl: false });


// scene.add(axesHelper)
/**
 * 
 */
const light1 = addAmbientLight(0xffffff, 0.5)
const light2 = addDirectionalLight(0xffffff, 1)
light2.position.set(0, 10, 0)

const group = new THREE.Group();
group.add(light1, light2)
const fireflies = initFireflies(0xffffff, { count: 30, rangeX: 5, rangeY: 8, rangeZ: 5 })
const noiseMesh = initNoise(0.6, 0x1890ff)
noiseMesh.position.set(0, -0.7, 0)


import { randFloatSpread } from 'three/src/math/MathUtils'
const map1 = textureLoader().load('./textures/top1.png')
const map2 = textureLoader().load('./textures/top2.png')
const spriteMaterial = new THREE.SpriteMaterial({
    color: 0x00acec,
    blending: THREE.AdditiveBlending,
})
const suspendGroup = new THREE.Group()
for (let i = 0; i < 10; i++) {
    /* 粒子对象 */
    spriteMaterial.map = getArrayRandomItem([map1, map2])
    const sprite = new THREE.Sprite(spriteMaterial)
    sprite.scale.set(0.02, 0.1, 0.02)
    sprite.position.x = randFloatSpread(1)
    sprite.position.y = randFloatSpread(3)
    sprite.position.z = randFloatSpread(1)
    suspendGroup.add(sprite)
}




// 星系
import { initGalaxy } from "@/three/mesh/galaxy";
const { galaxy, animate: galaxyAnimate } = initGalaxy({ color1: 0xfadb14 })

// 发光路线
const pointsArr = [];
pointsArr.push([-1.5, 1.5, 1.5]);
pointsArr.push([1.5, 1.5, 1.5]);
pointsArr.push([1.5, 1.5, -1.5]);
pointsArr.push([-1.5, 1.5, -1.5]);
pointsArr.push([-1.5, 1.5, 1.5]);
// for (var i = 0; i < pointsArr.length - 1; i++) {
//     let start = pointsArr.at(i), end = pointsArr.at(i + 1)
//     // 飞线
//     const flyLineShader = new FlyLineShader({ endPosition: end, color: randomColor(), startPosition: start })
//     group.add(flyLineShader.mesh)
// }
// for (var i = 0; i < pointsArr.length - 1; i++) {
//     let start = pointsArr.at(i), end = pointsArr.at(i + 1)
//     // 飞线
//     const flyLineShader = new FlyLineShader({ endPosition: end, color: randomColor(), startPosition: start, useCenter: false })
//     group.add(flyLineShader.mesh)
// }
// group.add(galaxy) // 星系
group.add(noiseMesh) //噪声
// group.add(fireflies) // 星星
group.add(suspendGroup) // 悬浮粒子

// 月球曲线运动
import { initCircleCurvilinearrMotion } from "@/three/effect/curvilinearMotion";

const moonGroup = new THREE.Group()
const moonGeometry = new THREE.SphereGeometry(0.07, 16, 16);
const moonMaterial = new THREE.MeshPhongMaterial({
    shininess: 10,
    color: 0x1890ff,
    // map: textureLoader().load("textures/starBall/moon_1024.jpg"),
});
const moon = new THREE.Mesh(moonGeometry, moonMaterial);
moonGroup.add(moon);
const { line, flyLine, meshMotion: moonMotion, points } = initCircleCurvilinearrMotion({ radius: 0.9 })
moonGroup.add(line)
moonGroup.add(flyLine)
moonGroup.position.set(0, 1, 0)
moonGroup.rotateX(Math.PI / 2.5)
group.add(moonGroup)


// 添加地球作为环境背景
const earthGeometry = new THREE.SphereGeometry(30, 64, 64);
const earthMaterial = new THREE.MeshPhongMaterial({
    specular: 0x333333,
    shininess: 5,
    map: textureLoader().load("textures/starBall/earth_atmos_2048.jpg"),
    specularMap: textureLoader().load("textures/starBall/earth_specular_2048.jpg"),
    normalMap: textureLoader().load("textures/starBall/earth_normal_2048.jpg"),
    normalScale: new THREE.Vector2(0.85, 0.85),
});

let earth = new THREE.Mesh(earthGeometry, earthMaterial);
// scene.add(earth);

// 执行动画
import { executeAnimation, executeMultipleAnimation } from '@/three/utils/animat'
import { randomMesh, testMesh } from '@/three/utils/testMesh';
import { useLogin } from "@/store/login";
const store = useLogin()
// glb
let ani1, ani2, modelGlb
gltfLoader().loadAsync('./model/login.glb').then((gltf) => {
    const shineMesh = findMesh(gltf.scene, 'faguang01')
    glslShine(shineMesh, 0x00ffff)
    const shineMesh2 = findMesh(gltf.scene, 'daguang02')
    glslShine(shineMesh2, 0x00ffff)
    const glassMat = initGlass()
    const glassMat2 = initGlass(0.8, 0x00ff00)
    const lucency1 = findMesh(gltf.scene, 'toumingti01')
    const lucency2 = findMesh(gltf.scene, 'toumingti002')
    const meshGroup = randomMesh({ count: 5, randomRange: { x: 0.5, y: 0.25, z: 0.5 } })
    lucency2.add(meshGroup)
    const lucency3 = findMesh(gltf.scene, '立方体018')
    lucency1.material = glassMat;
    lucency2.material = glassMat;
    lucency3.material = glassMat;
    group.add(gltf.scene)


    // 调用动画
    ani1 = executeMultipleAnimation(gltf.scene, [gltf.animations[0]], { isLoop: false })
    ani2 = executeMultipleAnimation(gltf.scene, gltf.animations.slice(1, gltf.animations.length))
    ani1.startMultipleAnimation();
    ani2.startMultipleAnimation();
    modelGlb = gltf.scene;
}).then(() => {
    setTimeout(v => {
        gsap.to(noiseMesh.position, {
            y: 0.7,
            duration: 2.5,
            onComplete: () => {
                // labelShow.value = true;
            }
        })
    }, 1500)
    setTimeout(() => {
        store.$patch({ modelLoadingEnd: true })
    }, 3000)
})


group.position.set(0, 2.5, -3.2)
// moveToCamera(controls, 0, [5, 2, -4], [0, 0, 0])
group.rotateY(-Math.PI / 2 * 1.30)
group.rotateZ(-Math.PI / 15)
group.rotateX(-Math.PI / 22)
scene.add(group)








/**
 * 
 */
ani((...args) => {
    const [time, camera, delta] = args;
    moonMotion(moon, time)
    // shinePointMesh.position.x += time * 0.01
    // 星系
    galaxyAnimate(time)
    // 萤火虫
    fireflies.material.uniforms.uTime.value = time;
    fireflies.material.opacity = Math.sin(time);
    //噪声
    noiseMesh.material.uniforms.uTime.value = time;
    // 悬浮粒子效果
    suspendGroup.children.forEach((sprite, i) => {
        sprite.position.y = Math.tan(time * 0.3 - i * 0.1) * 1 + 1
        if (sprite.position.y > 2) {
            sprite.position.y = 0
            // sprite.position.y = Math.cos(time * 0.2 - i * 0.1) * 1.5 + 1.5
        }
    })
    // 地球自转
    // earth.rotation.y += 0.001
    //

    ani1 && ani1.updateMultipleAnimation(delta * 10);
    ani2 && ani2.updateMultipleAnimation(0.02);
    // 添加风扇动画
    if (modelGlb) {
        const fs = findMesh(modelGlb, 'zt01')
        fs.rotation.y -= 0.15
    }
})


// var geometry = new THREE.SphereGeometry(5, 32, 16);
// var material = new THREE.MeshLambertMaterial({ color: 0xffff00 });
// var geometry = new THREE.BoxGeometry(1, 1, 1);
// var material = new THREE.MeshLambertMaterial({ color: 0xffff00 });
// var mesh = new THREE.Mesh(geometry, material);
// shineTexture(mesh)






// 挂载
function mountRender(el) {
    el.appendChild(renderer.domElement);
}

</script>

<style lang="scss" scoped></style>