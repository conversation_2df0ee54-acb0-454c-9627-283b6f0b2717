<template>

</template>
<script setup>
import { Login } from "../../https/encapsulation/threeDimensional";
import { sign } from "../../https/encipher";
import { ref, onMounted, toRaw } from "vue";
import { ElMessage } from "element-plus";
import { useCommonStore } from "@/store/common";
import { useRouter } from "vue-router";
import { ApiModuleRoleId } from "@/https/encapsulation/Module";

const state = ref(false)
const router = useRouter();
const store = useCommonStore();
const userName = ref("admin");
const passWord = ref("admin");
onMounted(() => {
    store.$patch({
        token: {},
        name: "",
    });
});
const log = async () => {
    // 获取时间
    let t = new Date().getTime();
    //   加密
    var result = sign(t.toString(), passWord.value);
    console.log(result, "加密文件");
    //   接口请求
    const res = await Login(userName.value, t, result);
    console.log(res);
    if (res.data.Result != null) {
        // console.log(res.data.Result.RoleIds,'返回的');
        let ModuleRoleList = [];
        res.data.Result.RoleIds.map(async (item) => {
            let ModuleRole = await ApiModuleRoleId(item);
            ModuleRole.Result.forEach((v) => {
                ModuleRoleList.push(v);
            });
            store.$patch({
                ModuleRoleList: removeDuplicateNames(ModuleRoleList),
            });
            store.$patch({
                userInfo: { name: userName.value, password: passWord.value },
            });

        });
        setTimeout(() => {
            router.push("/threeVentilate");
        }, 666);


        function removeDuplicateNames(arr) {
            return arr.reduce((acc, curr) => {
                const found = acc.find((obj) => obj.ID === curr.ID);
                if (!found) {
                    acc.push(curr);
                }
                return acc;
            }, []);
        }
        // 全局存储
        store.$patch({
            name: res.data.Result.Token.Account,
            token: res.data.Result.Token,
        });
    } else {
        ElMessage({
            showClose: true,
            message: res.data.Msg,
            type: "error",
        });
    }
};
log();
</script>
<style lang="scss" scoped></style>