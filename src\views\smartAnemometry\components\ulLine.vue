<template>
  <div id="main4" class="w-700px  h-260px mr-30px mt-10px ml-5px"></div>
</template>
<script setup>
import { watch } from "vue";
//  按需引入 echarts
import * as echarts from "echarts";
const props = defineProps(['data'])

watch(() => props.data, (v) => {
  init();
})
function init() {
  // 基于准备好的dom，初始化echarts实例
  var myChart = echarts.init(document.getElementById("main4"));
  myChart.clear();
  // 指定图表的配置项和数据
  var option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: "line", // 默认为直线
      },
    },
    //控制直角坐标系与
    grid: { // 控制图形距离
      top: "20px",
      left: "50px",
      right: "40px",
      bottom: "50px"
    },
    // x轴（指的是水平的那条线）相关配置
    xAxis: [
      {
        type: "category",
        data: props.data.map(v => v.Location), //类目轴数据
        axisLabel: {
          // 坐标轴标签
          show: true, // 是否显示
          inside: false, // 是否朝内
          color: "#fff",
          fontSize: 12,
          interval: 0, //设置刻度间间隔，0表示全部显示不间隔；auto:表示自动根据刻度个数和宽度自动设置间隔个数
        },
        axisLine: {
          // 坐标轴 轴线
          show: true, // 是否显示
          lineStyle: {
            color: "#483E1F",
            width: 1,
            type: "solid",
          },
        },
        axisTick: {
          //刻度相关
          show: false, //是否显示刻度
          alignWithLabel: true, //是否对齐标签
        },
        inverse: false, //翻转x轴数据
      },
    ],
    // y轴（垂直的那条线）设置
    yAxis: [
      {
        type: "value", //x轴数据类型，value时就是值，category时就是分类，可用于区分y轴与x轴
        axisLabel: {
          // 坐标轴标签
          show: true, // 是否显示
          // inside: true, // 标签是否朝内,默认false
          interval: 0, //设置刻度间间隔，0表示全部显示不间隔；auto:表示自动根据刻度个数和宽度自动设置间隔个数
          rotate: 0, // 旋转角度
          margin: 3, // 刻度标签与轴线之间的距离
          color: "#fff", // 标签颜色默认取轴线的颜色
          fontSize: 12, //标签字号
        },
        splitLine: {
          // 网格线
          show: true, // 是否显示，默认为true
          lineStyle: {
            color: ["#1A5A78"],
            width: 1,
            type: "solid",
          },
        },
        axisLine: {
          // 坐标轴 轴线
          show: true, // 是否显示
          lineStyle: {
            color: '#483E1F',//轴线颜色
            width: 1, //轴线宽度
            type: "solid", //轴线类型
          },
        },
      },
    ],

    series: [
      {
        name: "",
        type: "line", //表明是折线图line
        data: props.data.map(v => v.RealValue), //值数据，与类型数据一一对应
        itemStyle: {
          normal: {
            color: "#DD8150", //折线上的颜色
          },
        },
        symbol: "none", //去掉折线图节点
        smooth: true, //是否平滑曲线，true曲线，false直线
        // 区域填充样式
        areaStyle: {
          normal: {
            // 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置
            color: {
              //分隔区域的颜色
              x0: 0,
              y0: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "#73584C", // 0% 处的颜色
                },
                {
                  offset: 1,

                  color: "RGBA(61, 67, 75, 0)", // 100% 处的颜色；中间还可以设置50%、20%、70%时的颜色
                },
              ],
              globalCoord: false, // 缺省为 false，以确保上面的x,y,x2,y2表示的是百分比
            },
          },
        },
      },
    ],
  };
  // 使用刚指定的配置项和数据显示图表。
  myChart.setOption(option);
}
</script>

<style lang="scss" scoped></style>