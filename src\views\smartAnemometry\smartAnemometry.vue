<template>
  <div class="flex items-center overflow-y-hidden">
    <div class="mx-auto anemometry_box pt-120px flex ">
      <!-- 压差传感器 -->
      <!-- <DataBox style="position: relative;" class=" boxSense" title="压差传感器">
        <template #mainFan>
          <div class="senseBox flex justify-around">
            <div class="senseBox_div">
              <div v-for="(item, index) in diffsensorList" class="box_senseF  mt-15px" :key="index">
                <p class="senseBox_title">{{ item.Location }}</p>
                <div class="senseBox_ul flex justify-around">
                  <div class="w-48% mt-10px" v-for="(s, i) in senseData" :key="i">
                    <div class="senseBox_ul_value ">
                      <img :class="'sense_yl_icon' + (i + 1)" :src="s.icon" alt="" />
                      <p>{{ s.name }}</p>
                      <p class="value_se">{{ item[s.prop] + s.unit }}</p>
                    </div>
                  </div>
                </div>
      
                <div class="senseBox_div_button cursor-pointer" @click="checkCurve(item, 'yc')">
                  <img src="./assest/img/qux_icon.png" />
                  <span class="pt-3px">曲线分析</span>
                </div>
              </div>
            </div>
          </div>

        </template>
</DataBox> -->

      <div>
        <!-- 超声波风速 -->
        <DataBox style="position: relative;" class="boxUltrasonic" title="超声波风速" bgWidth="600px">
          <template #mainFan>
            <div class="boxUltrasonic_div">
              <div class="uls_maindiv">
                <el-scrollbar height="260rem">
                  <div class="uls_div flex justify-around" v-for="(u, i) in windsensorList" :key="i">
                    <div class="uls_div_icon ml-10px">
                      <img src="./assest/img/uls_icon.png" class="windsensorAni w-52px h-52px">
                    </div>
                    <!--  -->
                    <div class="uls_div_value  ml-30px mt-2px">
                      <div class="uls_div_place">
                        <span class="uls_con_title">安装地点:</span>
                        <span class="ml-15px">{{ u.Location }}</span>
                      </div>
                      <div class="uls_div_con  pt-12px">
                        <span class="uls_con_title">实时风速:</span>
                        <span class="ml-15px">{{ u.RealValue }}</span>
                      </div>
                    </div>
                    <!--  -->
                    <div class="boxUltrasonic_button cursor-pointer" @click="checkCurve(u, 'csb')">
                      <p>曲线分析</p>
                    </div>
                  </div>
                </el-scrollbar>
              </div>
              <!-- echarts图 -->
              <div id="ulEcharts" class="ml-40px ulEcharts ">
                <div class="anyls_bg">
                  <p>曲线分析</p>
                </div>
                <ulLine :data="windsensorList"></ulLine>
              </div>
            </div>
          </template>
        </DataBox>

        <!-- 多参数传感器 -->
        <DataBox style="position: relative;" class="boxSensor" title="多参数传感器" bgWidth="600px">
          <template #mainFan>
            <div class="div_Sensor">
              <!--  -->
              <div class="div_Sensor_ul" v-for="(se, i) in duoshensensorList" :key="i">
                <div class="div_Sensor_one ">
                  <div class="inline-block align-middle"><img src="./assest/img/ipaddress_icon.png"
                      class="w-14px  h-16px"></div>
                  <div style="width: 340rem;" class="inline-block ml-10px align-middle">{{ se.Location }}</div>
             
                  <div class="inline-block align-middle"> <img @click="checkCurve(se, 'dcs')"
                      src="./assest/img/quxian_icon.png" class="cursor-pointer w-25px  h-12px  "></div>
                </div>

                <div class="div_Sensor_two">
                  <div class="flex flex-col justify-between items-center">
                    <p>湿度</p>
                    <p class="Sensor_two_font">{{ se.Humidity }}</p>
                  </div>
                  <div class="flex flex-col justify-between items-center">
                    <p>温度</p>
                    <p class="Sensor_two_font">{{ se.Temp }}</p>
                  </div>
                  <div class="flex flex-col justify-between items-center">
                    <p>压力</p>
                    <p class="Sensor_two_font">{{ se.Pressure }}</p>
                  </div>
                </div>

              </div>


            </div>
          </template>
        </DataBox>
      </div>


      |
    </div>
<!-- v-if="(duoshensensorHislist.length > 0 && type==='yc') ||
          (windsensorHislist.length > 0 && type==='csb')||
          (duoshensensorHislist.length > 0 && type==='dcs')" -->
    <!-- 曲线分析弹窗 -->
    <el-dialog v-model="dialogParams.isShow" style="height: 700rem; width:900rem;">
      <div class=" absolute top-25px left-20px text-20px font-bold">{{ dialogParams.title }}</div>
      <div class="w-100% h-600px  flex justify-center items-center ">
        <!-- <div ref="myEchart" class="mx-auto  w-full h-full"></div> -->
         <div  
 v-if="isshowEchars===true"
         
             ref="myEchart" class="mx-auto mt-30px w-full h-full" id="main"></div>





        <!-- <div v-if="duoshensensorHislist.length > 0" ref="myEchart" class="mx-auto mt-30px w-full h-full" id="main"></div>  -->
       <div v-else>无数据</div> 
      </div>
    </el-dialog>
  </div>

</template>
<script setup>
import { onBeforeRouteLeave } from "vue-router";
import { ref, watch, reactive, onMounted, nextTick, onBeforeUnmount } from "vue";
import DataBox from "@/components/common/DataBox.vue";
import ulLine from "./components/ulLine.vue"
import gsap from "gsap";
import { get } from '@/https/request';
import * as echarts from 'echarts';

//压差传感器列表查询
const diffsensorList = ref()
const diffsensorHislist = ref()
async function getDiffsensorList() {
  let api = `/api/v2/Intelligentwind/diffsensor/list`;
  const { data } = await get({
    api: api,
    url: api
  })
  const { Result } = data;
  diffsensorList.value = Result;
  return data
}
// 压差传感器历史曲线查询
async function getDiffsensorHislist(startTime, endTime, code) {
  let api = `/api/v2/Intelligentwind/diffsensor/hislist`;
  const { data } = await get({
    api: api,
    url: `${api}?startTime=${startTime}&endTime=${endTime}&code=${code}`
  })
  return data
}
//风速传感器列表查询
const windsensorList = ref([])
const windsensorHislist = ref([])
async function getWindsensorList() {
  let api = `/api/v2/Intelligentwind/windsensor/list`;
  const { data } = await get({
    api: api,
    url: api
  })
  const { Result } = data;
  windsensorList.value = Result;
  return data
}
// 风速传感器历史曲线查询
//zg
async function getWindsensorHislist(startTime, endTime, code) {
  let api = `/api/v2/Intelligentwind/windsensor/hislist`;
  const { data } = await get({
    api: api,
    url: `${api}?startTime=${startTime}&endTime=${endTime}&code=${code}`
  })
  return data
}
//多参数传感器列表查询
const duoshensensorList = ref([])
const duoshensensorHislist = ref([])
async function getDuoshensensorList() {
  let api = `/api/v2/Intelligentwind/duoshensensor/list`;
  const { data } = await get({
    api: api,
    url: api
  })
  const { Result } = data;
  duoshensensorList.value = Result;
  return data
}
// 多参数传感器历史曲线查询
async function getDuoshensensorHislist(startTime, endTime, code) {
  let api = `/api/v2/Intelligentwind/duoshensensor/hislist`;
  const { data } = await get({
    api: api,
    url: `${api}?startTime=${startTime}&endTime=${endTime}&code=${code}`
  })
  return data
}

// 压差所需字段素材
const senseData = ref([
  {
    name: "温度",
    prop: "Temp",
    unit: '℃',
    icon: "sense_group_icon1.png",
  },
  {
    name: "湿度",
    prop: "Humidity",
    unit: '%',
    icon: "sense_group_icon2.png",
  },
  {
    name: "压力",
    prop: "Pressure",
    unit: 'KPa',
    icon: "sense_group_icon3.png",
  },
  {
    name: "压差",
    prop: "DiffPressure",
    unit: 'KPa',
    icon: "sense_group_icon4.png",
  },
]
);

const newData = senseData.value.map((item) => ({
  ...item,
  icon: new URL(`./assest/img/${item.icon}`, import.meta.url),
}));
senseData.value = newData;
// 压力传感器
getDiffsensorList()
// 风速传感器
getWindsensorList()
// 多参数传感器
getDuoshensensorList()


// 动画
onMounted(() => {
 
  // 压差
  gsap.fromTo('.sense_yl_icon1', { opacity: 0.5, translateY: '-2px' }, { opacity: 1, translateY: '2px', yoyo: true, duration: Math.random() * 4 + 1, repeat: -1 })
  gsap.fromTo('.sense_yl_icon2', { opacity: 0.5, translateY: '2px' }, { opacity: 1, translateY: '-2px', yoyo: true, duration: Math.random() * 4 + 1, repeat: -1 })
  gsap.fromTo('.sense_yl_icon3', { opacity: 0.5, translateX: '-2px' }, { opacity: 1, translateX: '2px', yoyo: true, duration: Math.random() * 4 + 1, repeat: -1 })
  gsap.fromTo('.sense_yl_icon4', { opacity: 0.5, translateX: '2px' }, { opacity: 1, translateX: '-2px', yoyo: true, duration: Math.random() * 4 + 1, repeat: -1 })

  // 风速
  gsap.fromTo('.windsensorAni', { opacity: 0.5, translateY: '-2px', transform: ' rotateY(-30deg)' }, { opacity: 1, translateY: '2px', transform: 'rotateY(30deg)', yoyo: true, duration: 2, repeat: -1 })

  // 多参数
  gsap.fromTo('.windsensorAni', { opacity: 0.5, translateY: '-2px', transform: ' rotateY(-30deg)' }, { opacity: 1, translateY: '2px', transform: 'rotateY(30deg)', yoyo: true, duration: 2, repeat: -1 })
 
})
// 动态刷新获取数据
let time = ref(null);
function getData() {
  if (time.value) {
    clearInterval(time.value);
  }
  time.value = setInterval(() => {
    getDiffsensorList()
    getWindsensorList()
    getDuoshensensorList()
  }, 5000)
}
// 路由离开页面时触发
onBeforeRouteLeave((to, from, next) => {
  if (time.value) {
    clearInterval(time.value);
  }
  next();
});
onMounted(() => {
  getData();
})
onBeforeUnmount(() => {
  if (time.value) {
    clearInterval(time.value);
  }
})

// 格式化日期为指定字符串格式
function formatDate(date, format) {
  const pad = (num) => num.toString().padStart(2, '0');
  return (
    date.getFullYear() + '-' + pad(date.getMonth() + 1) + '-' + pad(date.getDate()) + ' ' + 
    pad(date.getHours()) + ':' + pad(date.getMinutes()) + ':' + pad(date.getSeconds())
  );
}


// 曲线分析弹窗
const dialogParams = reactive({
  isShow: false,
  title: null,
})


// 曲线分析 
function checkCurve(item, type) {
  dialogParams.title = item.Location;

  const now = new Date();
    // 构造当前日期 00:00:00 的时间对象
    const startDate = new Date(now);
    startDate.setHours(0, 0, 0); 
    const start = formatDate(startDate, 'YYYY-MM-DD HH:mm:ss');
    
    // 构造当前日期 23:59:59 的时间对象
    const endDate = new Date(now);
   
    const end = formatDate(endDate, 'YYYY-MM-DD HH:mm:ss');
       
  if (type == 'yc') {

    getDiffsensorHislist(start, end, item.Code).then(v => {
      const { Result } = v;
      diffsensorHislist.value = Result;
      dialogParams.isShow = true;
    }).then((v) => {
     

        if(diffsensorHislist.value.length>0){
          isshowEchars.value=true
                nextTick(() => {
    cureEchart(diffsensorHislist.value, 'yc')
      })
        }else{
          isshowEchars.value=false
        } 





       
     
      
    })
  } else if (type == 'csb') {
    
    getWindsensorHislist(start, end, item.Code).then(v => {
      //接口有数据放开
      const { Result } = v;
  
      windsensorHislist.value = Result;



      dialogParams.isShow = true;
    }).then((v) => {
  
        if(windsensorHislist.value.length>0){
          isshowEchars.value=true
                nextTick(() => {
    cureEchart(windsensorHislist.value, 'csb')
      })
        }else{
          isshowEchars.value=false
        }   
    })
  } else {
    
    getDuoshensensorHislist(start, end, item.Code).then(v => {
      const { Result } = v;
      duoshensensorHislist.value = Result;
      dialogParams.isShow = true;
    }).then((v) => {


        if(duoshensensorHislist.value.length>0){
          isshowEchars.value=true
                nextTick(() => {
    cureEchart(duoshensensorHislist.value, 'yc')
      })
        }else{
          isshowEchars.value=false
        } 

       
    })
  }
}
const myEchart = ref(null)
let isshowEchars = ref(false)
// 曲线分析折线图
function cureEchart(hislist = [], type = 'yc') {
//  这里暂时这样用,不用改
  // const myChart = echarts.init(myEchart)
  
   const myChart = echarts.init(document.getElementById('main'))
  
  myChart.clear() 
 
  const xData = hislist.map(v => v.TimeStamp.slice(0, 19))
  let data = []
  if (type == 'yc') {
    data = [
      { name: '温度', data: hislist.map(v => v.Temp), unit: '℃' },
      { name: '湿度', data: hislist.map(v => v.Humidity), unit: '%' },
      { name: '压力', data: hislist.map(v => v.Pressure), unit: 'KPa' },
      { name: '压差', data: hislist.map(v => v.DiffPressure), unit: 'KPa' },
    ]
  } else if (type == 'csb') {
    data = [
      { name: '风速', data: hislist.map(v => v.RealValue), unit: 'm/s' },
    ]
    console.log(data,'datadatadata')
  } else {
    data = [
      { name: '压力', data: hislist.map(v => v.Pressure), unit: 'KPa' },
      { name: '湿度', data: hislist.map(v => v.Humidity), unit: '%' },
      { name: '温度', data: hislist.map(v => v.Temp), unit: '℃' },
    ]
  }

  let option = {
    backgroundColor: 'rgba(255, 152, 0,0.5)',
    color: ['rgba(28, 205, 255)', 'rgba(47, 255, 208)', 'rgba(245,222,111)', 'rgba(230, 41, 101)'],
    tooltip: {
      trigger: 'axis',
      borderWidth: 0,
      backgroundColor: 'rgba(255, 152, 0,0.5)',
      textStyle: { // 添加 textStyle 属性
        color: '#fff' // 设置字体颜色
      },
      formatter: function (params) {
        console.log(params, 'params');
        const time = params[0].name || ''
        let str = `${time}<br/>`
        params && params.forEach((v, i) => {
          if (v) {
            const style = 'display: inline-block; width: 5px; height: 14px; margin-right:3px; background-color: ' + v.color + ';';
            const content = `${'<span style="' + style + '"></span>'} ${v.seriesName}: ${v.value ?? ''}${data[i].unit} <br />`
            str += `${content}`
          }
        })
        return str
      },
    },
    legend: {
      icon: 'stack',
      itemWidth: 10,
      itemHeight: 5,
      margin: 10,
      textStyle: {
        fontSize: 16,
        color: '#e0e1e2', // 设置字体颜色
        padding: [10, 10] // 设置文字与图例的距离
      },
      itemStyle: {
        borderWidth: 0
      },
      formatter: function (name) {	// 添加
        return name + data.find(v => v.name == name).unit
      },
      itemGap: 20 // 设置图例项之间的间距
    },
    grid: {
      left: '4%',
      right: '8%',
      bottom: '9%',
      top: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,//两侧留白
      axisLine: {
        lineStyle: {
          color: 'rgba(2, 119, 175)',
          width: 2,
          type: 'solid',
        },
        show: true,
        onZero: false // 将 x 轴坐标轴置于最低刻度上
      },
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: {
            type: 'line',
            x: 0,
            y: 0,
            x2: 1,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(38, 159, 193,.5)' // 100% 处的颜色
            }, {
              offset: 1, color: 'rgba(38, 159, 193,.5)' // 0% 处的颜色  #000613','#00334f', '#77f0ff'
            }],
          },
          type: 'solid',
          width: 24
        },
      },
      axisLabel: {
        interval: 1,
        color: 'rgba(255,255,255)', //更改坐标轴文字颜色
        fontSize: 14, //更改坐标轴文字大小
        margin: 20
      },
      axisTick: {
        show: true,
        alignWithLabel: true,
        lineStyle: {
          color: 'rgba(2, 119, 175)', // 刻度线颜色
          width: 2, // 刻度线宽度
        },
        length: 6, // 刻度线的长度
      },
      data: xData,
    },
    yAxis: [
      {
        // name: '温度',
        type: 'value',
        min: "dataMin",
        // max: 100,
        splitLine: {
          interval: 0,
          show: true, // 开启分割线
          lineStyle: {
            color: 'rgba(6, 88, 142)',
            width: 2,
            type: [6, 3],
          },
        },
        axisTick: {
          show: false
        },
        axisLine: {
          show: false
        },
        axisLabel: {
          color: 'rgba(207, 219, 230)'
        },
        position: 'left',
      }
    ],
    dataZoom: [
      {
        type: 'slider',
        xAxisIndex: 0,
        filterMode: 'none',
        bottom:'3%'
      },

      {
        type: 'inside',
        xAxisIndex: 0,
        filterMode: 'none'
      },

    ],
    series: []
  };
  option.series = data.map(v => {
    return {
      name: v.name,
      type: 'line',
      symbol: 'circle',
      symbolSize: 8,
      emphasis: {
        focus: 'series',
      },
      itemStyle: {
        //折线拐点标志的样式
        borderColor: 'rgba(255,255,255)',
        borderWidth: 2
      },
      data: v.data,
      smooth: true,
    }
  })
  myChart.setOption(option)
  //添加图表渲染完成的日志
  console.log('ECharts 图表渲染完成');
  
}

</script>
<style lang="scss" scoped>
@use '../ventilateFacility/mainFan/assets/index.scss';

.boxUltrasonic {
  width: 96vw;
  background-size: 96vw 368px;
}

.boxSensor {
  width: 96vw;
  height: 565px;
  background-size: 96vw 565px;
}

.senseBox {
  margin-top: 20px;
  color: #fff;
}

.senseBox_div {
  margin-top: 25px;
  /* margin-left: 40px; */
}

.senseBox_title {
  margin: auto;
  width: 398px;
  height: 45px;
  background-image: url(./assest/img/senseBox_icon.png);
  background-size: 398px 45px;
  padding-left: 60px;
  padding-top: 12px;
  margin-left: 20px;
}

.senseBox_ul {
  display: flex;

  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-around;


  img {
    width: 58px;
    height: 58px;
  }

  .senseBox_ul_value {
    flex-direction: column;
    font-size: 14px;
    text-align: center;

    .value_se {
      color: #fda97b;
      font-size: 20px;
      padding-top: 20px;
    }
  }
}

.senseBox_div_button {
  background-image: url(./assest/img/qux_cionBg.png);
  width: 450px;
  height: 40px;
  background-size: 450px 40px;
  margin-top: 50px;
  display: flex;
  flex-direction: row;
  padding-top: 7px;
  margin-bottom: 60px;

  &:hover {
    color: #76F7FF;
  }

  img {
    width: 25px;
    height: 15px;
    padding-left: 181px;
  }


}

// 超声波风速

.boxUltrasonic_div {
  display: flex;
  flex-direction: row;
  color: #fff;
  margin-left: 30px;
  margin-top: 20px;
}

.uls_maindiv {
  display: flex;
  flex-direction: column;
  margin-top: 5px;
  width: 40%;

}
.ulEcharts{
  width: 40%;
}
.uls_div {
  background-image: url(./assest/img/uls_bg.png);
  width: 609px;
  height: 74px;
  background-size: 609px 74px;
  display: flex;
  flex-direction: row;
  padding-top: 15px;
  padding-left: 10px;
  font-size: 16px;
  margin-bottom: 15px;
}


.uls_con_title {
  color: #76F7FF;
}

.boxUltrasonic_button {
  background-image: url(./assest/img/uls_button.png);
  width: 112px;
  height: 40px;
  background-size: 112px 40px;



  p {
    padding-top: 10px;
    text-align: center;
  }
}

.div_Sensor {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  margin-left: 10px;
  margin-right: 10px;
  margin-top: 30px;
  color: #fff;

}

.div_Sensor_ul {
  width: 428px;
  height: 205px;
  border-radius: 6px;
  border: 1px solid rgba(241, 173, 26, 0.3);
  background-color: rgba(140, 91, 19, 0.1);
  opacity: 0.8;
  margin-top: 30px;
  margin-left: 20px;
}

.div_Sensor_one {
  margin-top: 25px;
  margin-left: 20px;
  padding-bottom: 25px;
  border-bottom: 2px solid rgba(241, 173, 26, 0.5);
}

.div_Sensor_two {
  display: flex;
  flex-direction: row;
  justify-content: space-around;

  p {
    padding-top: 22px;
  }

  .Sensor_two_font {
    font-size: 20px;
    color: #FDA97B;
  }
}

.anyls_bg {
  background-image: url(./assest/img/senseBox_icon.png);
  width: 298px;
  height: 45px;
  background-size: 298px 45px;
  text-align: center;
  margin-left: 150px;

  p {
    padding-top: 15px;
  }

}

::v-deep .el-dialog {
  // width: 850px;
  // height: 500px;
  background: rgb(0, 0, 0) !important;
  box-shadow: rgb(255, 179, 15) 0px 0px 20px inset !important;
  border: 1px solid rgba(255, 179, 15, 0.3) !important;
  border-radius: 10px !important;
}

::v-deep .el-button+.el-button {
  background-color: rgba(255, 179, 15, 0.3) !important;
  border: 1px solid rgba(232, 157, 6, 0.8) !important;
}
</style>