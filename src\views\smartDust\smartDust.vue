<template>
  <!-- 智能降尘 -->
  <div ref="dustDiv" class="canvasBox"></div>
  <div class="Dust_box top-116px left-50px">
    <!-- 球阀 -->
    <div class="">
      <div></div>


    </div>

    <CenterTitle title="1621(3)工作面"></CenterTitle>

    <DataBox class="bottom-30px left-74px boxDust_box" title="工作面基本信息" bgWidth="97%">
      <template #mainFan>
        <div class="flex">
          <div class="dustData_box">
            <img src="./assest/jc_bottom.png" class="w-172px h-138px mt-30px ml-20px" />

            <div class="dustData_box_div2 ml-35px mt-10px">
              <span class="dust_span">正在运行</span>
              <span class="pt-15px pl-24px ">12天05时58分</span>
            </div>
          </div>
          <!-- 工作面名称开始 -->
          <div class="work_box mt-30px ml-20px">
            <div class="pt-5px" v-for="(work, index) in workData.data" :key="index">
              <div class="work_box_div">
                <span class="pt-20px ">{{ work.title }}</span>
                <span class="color-#41F591 pl-30px">{{ work.name }}</span>
              </div>
            </div>
          </div>
        </div>
      </template>
    </DataBox>

    <DataBox class="bottom-30px left-790px boxDust_box" title="当前配置信息" bgWidth="97%">
      <template #mainFan>
        <div class="ml-10px mt-40px dust_cong">
          <div v-for="(conf, index) in confData.data" :key="index" class="mt-10px mb-30px">
            <div class="conf_div">
              <div class="conf_div_img">
                <div class="pz_img"><img :src="conf.img" /></div>
              </div>
              <div class="pz_title">
                <span class="color-#D8CEB0 font-size-14px pt-5px">{{
                  conf.name
                }}</span>
                <span class="font-size-24px pl-18px pt-5px">{{ conf.num }}</span>

              </div>
            </div>
          </div>
        </div>
      </template>
    </DataBox>

    <DataBox class="bottom-30px left-1500px boxDust_box" title="实时监测" bgWidth="97%">
      <template #mainFan>
        <div class="mon_Dust ml-30px mr-30px mt-40px">
          <div v-for="(jc, index) in dustMonData.data" :key="index">
            <div class="dust_div2 mt-10px ml-20px">
              <div class="dust_div2_absoulte">
                <span class="position-absolute top-20px left-52px  font-size-14px color-#E19F10">{{ jc.moname }}</span>
                <span class="dust_div2_num">{{ jc.monum }}</span>
                <!-- <img src="./assest/minotor_png.png" class="" /> -->




              </div>
            </div>
          </div>
        </div>
      </template>
    </DataBox>
  </div>
</template>
<script setup>
import CenterTitle from "@/components/common/CenterTitle.vue";
import DataBox from "@/components/common/DataBox.vue";
import { ref, reactive, onMounted, watch } from "vue";
import { useThree } from "@/three/useThree";
import { onBeforeRouteLeave } from 'vue-router';
import valve_icon from './assest/valve_icon.png';
import up_icon_ball from './assest/up_icon_ball.png';
import down_icon_ball from './assest/down_icon_ball.png';
import spray_icon from './assest/spray_icon.png';
import interval_icon2 from './assest/interval_icon2.png';
import interval_icon from './assest/interval_icon.png';


//模型开始
const startCamera = [1000, 300, 1000]
const endCamera = [30, 10, 0]
const gltfGlobal = ref()
const restMark = ref(false) // 重置标识
const zntf = ref(false)
let modelInit = ref()
modelInit.value = useThree({ startCamera, endCamera })
const centerTitleDesc = ref('1621（3）轨顺')
// 详细信息请看 three/useThree/index.js 文件
const { mountRender, ani, scene, stopRender, camera, cameraControls, initModel, changeCurrentMesh, changeCurrentMeshByName, findMesh, restrictCameraVerticalDirection,
  matcapTexture, dbModelClick, executeMultipleAnimation, setMeshScale, removeMeshByName, setPosition,
  executeAnimation, restCamera, addAmbientLight } = modelInit.value
restrictCameraVerticalDirection(cameraControls, Math.PI / 2.2, Math.PI / 1.8);

initModel('./model/shear.glb', [0, 0, 0], (gltf) => {
  if (gltf) {
    gltfGlobal.value = gltf;

    const { startMultipleAnimation, updateMultipleAnimation, stopMultipleAnimation } = executeMultipleAnimation(gltf.scene, [gltf.animations[0], gltf.animations[1]])
    // 风机02风扇动画
    const { startMultipleAnimation: startAni2, updateMultipleAnimation: updateAni2, stopMultipleAnimation: stopAni2 } = executeMultipleAnimation(gltf.scene, [gltf.animations[2], gltf.animations[3]])
    setPosition(0, 0, 25, gltf.scene)
    // 初始化风机选中  高亮
    // const mesh1 = findMesh(gltf.scene, 'JBTFXT_HD')
    // const mesh2 = findMesh(gltf.scene, 'JBTFXT_HD3')




    // 监听复位方法
    watch(restMark, (val) => {
      if (val) {
        executeFanSelect()
      }
      setTimeout(() => {
        restMark.value = false
      }, 200)
    })
    // 切换风机 启动动画
    // function executeFanSelect() {
    //     if (fanActive.value == 0) {
    //         startMultipleAnimation()
    //         stopAni2()
    //         changeCurrentMeshByName('JBTFXT_Door_01', gltf.scene)
    //     } else {
    //         stopMultipleAnimation()
    //         startAni2()
    //         changeCurrentMeshByName('JBTFXT_HD2', gltf.scene)
    //     }
    // }

    // 渲染函数
    ani(function (...args) {
      const [time, camera, delta] = args
      // updateAnimation(delta)
      // 更新动画
      updateMultipleAnimation(0.15)
      updateAni2(0.15)
    })
  }

}).then((gltf) => {
  zntf.value = true

})
// 查看指定物体信息
function checkMeshData(name) {
  isShow.value = false
  // 测试内容
  if (name == '上风门状态') {
    // 通过物体名称获取物体 并添加效果 或者显示标签 需要让那个物体高亮 就传入其对应的名称 目前物体名称没有做修改 使用其默认的名称
    // changeCurrentMeshByName('DJ_01', gltfGlobal.value.scene, {
    //     isLabel: true,
    //     // 当需要显示标签时 callback 的第一个参数可以获取到 标签的坐标位置
    //     callback: (val) => {
    //         isDataShow.value = val
    //         console.log(isDataShow.value);
    //     }
    // })
  } else {
    // changeCurrentMeshByName('DJ_02', gltfGlobal.value.scene, {
    //     isLabel: true,
    //     callback: (val) => {
    //         isDataShow.value = val
    //         console.log(isDataShow);
    //     }
    // })
  }
}


// 模型结束

// 模型挂载
onMounted(() => {
  mountRender(dustDiv.value);

});
// 路由离开页面时触发
onBeforeRouteLeave((to, from, next) => {
  stopRender();
  modelInit.value = null;

  next();
});
const dustDiv = ref(null)
const workData = ref({
  data: [
    {
      title: "工作面名:",
      name: "1621(3)工作面",
    },
    {
      title: "运行方向:",
      name: "右",
    },

    {
      title: "风流方向:",
      name: "左",
    },

    {
      title: "通信状态:",
      name: "通讯正常",
    },
  ],
});

const confData = ref({
  data: [
    {
      img: valve_icon,
      num: "61",
      name: "电动球阀数量",
    },
    {
      img: up_icon_ball,
      num: "3",
      name: "上开数量",
    },
    {
      img: down_icon_ball,
      num: "1",
      name: "下开数量",
    },
    {
      img: spray_icon,
      num: "20s",
      name: "喷雾延迟",
    },
    {
      img: interval_icon2,
      num: "3",
      name: "上间隔数量",
    },
    {
      img: interval_icon,
      num: "3",
      name: "下间隔数量",
    },
  ],
});

const imgData = confData.value.data.map((item) => ({
  ...item,
  img: new URL(item.img, import.meta.url).href,
}));
confData.value.data = imgData;

const dustMonData = ref({
  data: [
    {
      moname: "温度",
      monum: "29.50℃",
    },
    {
      moname: "氧气",
      monum: "29.50%",
    },
    {
      moname: "O2",
      monum: "30.00%",
    },
    {
      moname: "甲烷",
      monum: "28.00%",
    },
    {
      moname: "粉尘",
      monum: "29.50%",
    },
    {
      moname: "未配置",
      monum: "29.50%",
    },
  ],
});
</script>

<style lang="scss" scoped>
@use '../ventilateFacility/mainFan/assets/index.scss';

.Dust_box {
  position: relative;
  color: aliceblue;
}

.dustData_box {
  display: flex;
  flex-direction: column;
}

.dustData_box_div2 {
  display: flex;
  flex-direction: column;
  color: #E4D8B7;
}

.dust_span {
  // background-image: url(./assest/background_basic.png);
  // width: 115px;
  // height: 32px;
  font-size: 24px;
  font-weight: bolder;
  font-family: “微软雅黑”;
  // background-size: 115px 32px;
  text-align: center;
  padding-top: 2px;
  color: #41f591;
}

.work_box_div {
  background-image: url(./assest/work-right.jpg);
  width: 370px;
  height: 50px;
  // background-color: rgba(27,166,0,0.2);
  background-size: 370px 50px;
  // border: 2px solid #063012;
  padding-left: 40px;
  padding-top: 18px;
  // border-left-color:#1BA600;
  // text-align: left;

}

.work_box {
  display: flex;
  flex-direction: column;
}

.dust_cong {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
}

.conf_div {
  background-image: url(./assest/jc_background.png);
  width: 172px;
  height: 83px;
  background-size: 172px 83px;
  display: flex;
  flex-wrap: wrap;
  position: relative;
}

.conf_div_img {
  background-image: url(./assest/qiufa-bg.jpg);
  width: 70px;
  height: 70px;
  background-size: 70px 70px;
  position: relative;
  top: 8px;
  left: 10px;
}

.pz_img {
  img {
    width: 24px;
    height: 24px;
    position: absolute;
    top: 22px;
    left: 22px;
  }
}

.pz_title {
  display: flex;
  flex-direction: column;
  margin-left: 70px;
  position: absolute;
  top: 14px;
  left: 15px;
  color: #DE9D11;
}

.mon_Dust {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  margin-right: 20px;

  .dust_div2 {
    position: relative;
    background: rgba(44, 34, 18, .2);
    border: 1px solid #515151;
    box-shadow: inset 0 0 10px #6f6450;
    // background-image: url(./assest/minotor_backg.png);
    width: 143px;
    height: 82px;
    // background-size: 122px 60px;
  }

  .dust_div2_absoulte {


    img {
      width: 75px;
      height: 31px;
      position: absolute;
      top: 10px;
      left: 27px;
    }

    .dust_div2_num {
      position: absolute;
      left: 28px;
      top: 40px;
      font-size: 24px;
      font-weight: bold;
      z-index: 999 !important;
    }
  }
}
</style>