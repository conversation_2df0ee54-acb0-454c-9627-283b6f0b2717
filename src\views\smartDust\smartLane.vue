<template>
  <div class="VdSilky">
    <div class="vs-content">
      <!--装置汇总-->
      <div class="content-left">
        <div class="vs-content-left">
          <div class="title_vs">装置汇总</div>
          <div class="content_vs">
            <div class="" v-for="(vs, i) in deviceList.data" :key="i">
              <div class="mt-15px pl-10px content_name">
                <span>{{ vs.name }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 装置汇总结束 -->

      <!-- 右边展示控制开始 -->
      <div class="content-right">
        <div
          class="device-dashboard bg-[#0f172a] text-gray-100 min-h-screen p-6"
        >
          <!-- 设备网格：2列布局 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 单个设备卡片 -->
            <div
              v-for="device in devices"
              :key="device.id"
              class="device-card relative aspect-video overflow-hidden"
            >
              <!-- 信息面板：左上角悬浮 -->
              <div class="info-panel">
                <div class="info-panel_title">
                  <strong class="ph-title">{{ device.name }}</strong>
                </div>
                <div
                  class="content_panel"
                  v-for="(param, idx) in device.params"
                  :key="idx"
                >
                  <p class="panel_button">
                    <i class="ph_icon"></i>
                     <span class="panel_label">{{ param.label }}:</span>
                     <strong 
    class="panel_value"
    :style="{ 
      color: param.label === '运行状态' 
        ? (device.status === 'on' ? '#41D18D' : '#F45375') 
        : '#fff' 
    }"
  >
    {{ 
      param.label === '运行状态' 
        ? (device.status === 'on' ? '开启' : '关闭') 
        : param.value 
    }}
  </strong>
                    <!-- <strong>{{ param.label }}：{{ param.value }}</strong> -->
                  </p>
                </div>
                <!-- 控制按钮 -->
                <div class="control_button">
                  <div class="ph_on" @click="toggleStatus(device, 'on')">
                    <i class=""></i>
                    <span class="">开启</span>
                  </div>
                  <div class="ph_off" @click="toggleStatus(device, 'off')">
                    <i class=""></i>
                    <span>关闭</span>
                  </div>
                  <!-- <div 
              class="absolute left-100"
            >自动</div> -->
                </div>
              </div>

              <!-- 媒体区域：视频/图片切换 -->
              <video
                v-if="device.status === 'on'"
                class="w-full h-full object-cover"
                :src="device.videoSrc"
                autoplay
                muted
                loop
                :ref="(el) => setVideoRef(device.id, el)"
              ></video>
              <img
                v-else
                class="w-full h-full object-cover"
                :src="device.imgSrc"
              />

              <!-- 操作按钮：右下角悬浮 -->
              <div
                class="action-buttons absolute bottom-4 right-4 flex space-x-2"
              >
                <button
                  class="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center hover:bg-blue-700 transition"
                >
                  <i class="fa fa-video-camera"></i>
                </button>
                <button
                  class="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center hover:bg-blue-700 transition"
                >
                  <i class="fa fa-cog"></i>
                </button>
              </div>

              <!-- 位置标签：右上角悬浮 -->
              <div
                class="position-label absolute top-4 right-4 bg-black/70 px-3 py-1 rounded text-sm"
              >
                {{ device.position }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 右边展示控制结束 -->
    </div>
  </div>
</template>

<script  setup>
import {
  ref,
  onMounted,
  watch,
  nextTick,
  computed,
  onBeforeUnmount,
  reactive,
} from "vue";
import video1 from "./video/dg.mp4";
import video2 from "./video/peizhi.mp4";
import video3 from "./video/wpz.mp4";
import jcimg from "./img/dgi.jpg";
import pzimg from "./img/pz.jpg";
import wpzimg from "./img/wp.jpg";

const deviceList = reactive({
  data: [
    {
      id: "1",
      name: "001Z粉尘浓度超限降尘装置",
    },
    {
      id: "2",
      name: "002Z触控喷雾降尘装置",
    },
    {
      id: "3",
      name: "003Z光控喷雾参数控制装置",
    },
    {
      id: "4",
      name: "004Z雷达喷雾降尘装置",
    },
  ],
});
// 模拟设备数据（可扩展）
const devices = reactive([
  {
    id: 1,
    name: "光控喷雾参数控制装置",
    status: "on",
    params: [
      { label: "运行状态", value: "开启" },
      { label: "下次运行时间段", value: "6:30 - 7:30" },
      { label: "远程控制", value: "" },
    ],
    videoSrc: video2,
    imgSrc: pzimg,
    position: "11205回顺 500m",
  },
  {
    id: 2,
    name: "雷达喷雾降尘装置",
    status: "on",
    params: [
      { label: "运行状态", value: "开启" },
      { label: "远程控制", value: "" },
    ],
    videoSrc: video1,
    imgSrc: jcimg,

    position: "11205回顺 1000m",
  },
  {
    id: 3,
    name: "开停喷雾装置",
    status: "on",
    params: [
      { label: "运行状态", value: "开启" },
      { label: "远程控制", value: "" },
    ],
    videoSrc: video3,
    imgSrc: wpzimg,
    position: "11207运顺 200m",
  },
  {
    id: 4,
    name: "模拟量传感器超限喷雾降尘装置",
    status: "on",
    params: [
      { label: "运行状态", value: "开启" },
      { label: "温度", value: "26 °C" },
      { label: "湿度", value: "68 %RH" },
      { label: "超限值", value: "30 °C" },
      { label: "远程控制", value: "" },
    ],
    videoSrc: video1,
    imgSrc: jcimg,
    position: "11207运顺 900m",
  },
]);

// 视频引用管理（控制播放/暂停）
const videoRefs = reactive({});
const setVideoRef = (deviceId, el) => {
  videoRefs[deviceId] = el;
};

// 状态切换逻辑
const toggleStatus = (device, newStatus) => {
  device.status = newStatus;
  // 处理视频行为
  if (newStatus === "on") {
    const video = videoRefs[device.id];
    video &&
      video.play().catch((err) => {
        console.warn("视频自动播放失败（浏览器策略）:", err);
      });
  } else {
    const video = videoRefs[device.id];
    video && (video.pause(), (video.currentTime = 0)); // 暂停并重置
  }
};
</script> 




<style lang="scss"  scoped>
.VdSilky {
  width: 100%;
  height: 91%;
  position: absolute;
  left: 0;
  top: 10%;
  color: #fff;
  // background-color: #746565;
}
.vs-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  width: 100%;
  height: 100%;
}
.content-left {
  width: 15%;
  height: 95%;
  position: relative;
  left: 30px;
  top: 10px;
  background-image: url("../thereVentilation/assets/img/data_box_bg.png");
  background-size: 100% 100%;
}

.title_vs {
  position: absolute;
  top: 4.5%;
  left: 40%;
  font-size: 25px;
  background-image: linear-gradient(0deg, #ffb30f 20%, #fff 65%);
  font-family: pang;
  font-weight: bolder;
  padding-left: 5rem;
  font-style: oblique;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
.content_vs {
  position: absolute;
  top: 100px;
  left: 40px;
  font-size: 20px;
}

.content_name {
  color: rgba(235, 243, 255, 0.54);
  &:hover {
    color: #fff;
  }
}

.content-right {
  width: 82%;
  height: 92%;
  position: relative;
  top: 10px;
  // left: 10px;
  box-sizing: border-box;
  background: rgba(47, 44, 27, 0.5);
  /* box-shadow: 1px 1px 1px $bg inset; */
  border: 1px solid #e4b22b66;
  border-radius: 5px;
}
/* 核心样式增强 */
.device-card {
  position: relative;
}
.info-panel {
  position: absolute;
  left: 20px;
  top: 20px;
  z-index: 8;
  border-radius: 15px;
  background-color: rgba(17, 22, 29, 0.5);
  // width: 300px;
  // height: 200px;
  padding: 20px 20px 20px 20px;
  min-width: 200px;
  box-shadow: 0 0 1px #000;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
  
}
.info-panel_title {
  font-size: 18px;
  position: relative;
  min-width: 180px;
  height: 30px;
  // background-image: url(./img/title_bg.png);
  // margin-left: 10px;
}

.ph-title {
  display: inline-block;
  padding-right: 10px;
  margin-left: 15px;
  padding-top: 2px;
  background-image: linear-gradient(0deg, #ffb30f 20%, #fff 65%);
  font-weight: bolder;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
.content_panel {
  position: relative;
  padding: 15px;
  margin-left: 10px;
}
.panel_label{
  color: #fff;
  margin-right: 4px;
}
.ph_icon {
  position: absolute;
  top: 13px;
  width: 20px;
  height: 20px;
  left: -15px;
  background-image: url(./img/i_icon.png);
  background-size: 100% 100%;
}
.control_button {
  display: flex;
  flex-direction: row;
  width: 100%;
  position: relative;
  margin-left: 10px;
  // margin-bottom:20px;
  height: 45px;
}
.ph_on {
  // width: 50%;
  i {
    width: 40px;
    height: 40px;
    position: absolute;
    left: -15px;
    top: -10px;
    background-image: url(./img/on_button.png);
    background-size: 100% 100%;
  }

  span {
    position: absolute;
    left: -15px;
    top: 30px;
  }
}
.ph_off {
  // position: relative;
  // width: 50%;
  i {
    width: 40px;
    height: 40px;
    position: absolute;
    left: 50px;
    top: -9px;
    background-image: url(./img/off_button.png);
    background-size: 100% 100%;
  }
  span {
    position: absolute;
    left:50px;
    top: 30px;
  }
}
// .info-panel, .action-buttons, .position-label {
//   z-index: 10; /* 确保悬浮层在媒体上层 */
// }
video {
  object-fit: cover; /* 保持视频比例并填满容器 */
}
.device-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* 2列布局，可根据需求调整 */
  gap: 20px;
  padding: 20px;
}

.device-card {
  border: 1px solid #222;
  border-radius: 8px;
  padding: 16px;
  background: #1a1a1a;
  color: #fff;
}

.media-container {
  width: 100%;
  height: 240px; /* 固定高度，保证布局稳定 */
  margin: 12px 0;
  overflow: hidden;
}

.media {
  width: 100%;
  height: 100%;
  object-fit: cover; /* 保持比例，填充容器 */
}

.toggle-btn {
  padding: 8px 16px;
  background: #409eff;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
.toggle-btn:hover {
  background: #66b1ff;
}
</style>
