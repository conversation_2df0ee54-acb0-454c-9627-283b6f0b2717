<template>
   <div ref="sceneDiv4" class="canvasBox">
    </div>

     <!-- 中间列表 -->
     <div class="san_pic  bottom-30px  left-415px" >
<div class="temperature-list">
    <table class="temp-table">
      <tbody>
        <tr v-for="(row, rowIndex) in groupedData" :key="rowIndex">
          <td v-for="(item, colIndex) in row" :key="colIndex">
            <div class="title">{{ item.Label }}</div>
            <div class="value">{{ item.Value }}</div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
     </div>
  <!-- 控制面板 -->
    <DataBox class="top-116px right-25px boxDoor_box4" title="控制面板">
      <template #mainFan>
        <div class="flex justify-around mt-15px control_mb_div">
          <div></div>
          <div class="">
            <div class="mt-5px control_zt">
              <p>控制区域</p>
            </div>
            <div class="flex forItemBox mt-5px mr-50px ml-20px">
              <div
                v-for="(item, index) in forList.data"
                :key="index"
                class="control_forItem cursor-pointer"
                @click="openDialog(index)"
              >
                <div class="control_back flex pt-10px pl-30px">
                  <div class="control_back_div">
                    <div class="div_forItemBox flex pt-10px pl-5px">
                      <img :src="item.icon" />
                      <p class="pt-3px pl-10px">{{ item.name }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </DataBox>
    <!-- 控制表单 -->
    <el-dialog
      title="验证面板"
      :lock-scroll="false"
      modal-class="abc"
      draggable
      :modal="false"
      :close-on-click-modal="false"
      style="height: 300rem; z-index: 20"
      width="400px"
      v-model="controlDialog"
      @close="onClose"
    >
      <el-form
        class="mt-20px"
        :model="form"
        ref="form"
        :rules="rules"
        label-width="100px"
        :inline="false"
        size="normal"
      >
        <el-form-item
          label="登录密码："

          style="color: white"
          :rules="[
            {
              required: true,
              message: '请输入密码',
              trigger: 'blur',
            },
          ]"
        >
          <el-input
            v-model="password"
            type="password"
            placeholder="请输入登录密码"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer mt-50px">
          <el-button size="medium" @click="handleCancel">取 消</el-button>
          <el-button size="medium" type="primary" @click="handleOk"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
    <!-- 控制表单结束 -->
</template>
<script setup>
import DataBox from "@/components/common/DataBox.vue";
// import Table from "./components/tab.vue"
import { ref, reactive, onMounted,watch,computed } from "vue";
 import { useThree } from "@/three/useThree";
 import { onBeforeRouteLeave } from 'vue-router'
//  智能抽采接口
import { ApiExtractList} from "@/https/encapsulation/IntelligentExtract"
const startCamera = [1000, 300, 1000]
const endCamera = [400 * 0.7, 300, 600 * 0.95]
const gltfGlobal = ref()
const restMark = ref(false) // 重置标识
const zntf = ref(false)
let modelInit = ref()
modelInit.value = useThree({ startCamera, endCamera })
// src\views\ventilateFacility\airWindow\assets\img\open_icon.png
import open_icon from "../ventilateFacility/airWindow/assets/img/open_icon.png";
import lock_icon from "../ventilateFacility/airWindow/assets/img/lock_icon.png";

// 详细信息请看 three/useThree/index.js 文件
const { mountRender, ani, scene, camera,stopRender, cameraControls, initModel, changeCurrentMesh, changeCurrentMeshByName, findMesh, restrictCameraVerticalDirection,
    matcapTexture, dbModelClick, executeMultipleAnimation, setMeshScale,removeMeshByName,
    executeAnimation, restCamera, addAmbientLight } =  modelInit.value

// 加载模型
// initModel('./model/tfj.glb', [0, 0, 0], (gltf) => {
initModel('./model/choucai1.glb', [0, 0, 0], (gltf) => {
    if (gltf) {
        gltfGlobal.value = gltf;
        console.log(gltf, '智能抽采');
        // 移除物体模型模块
        // removeMeshByName(gltf.scene,'JBTFXT_HD1')
        // removeMeshByName(gltf.scene,'JBTFXT_HD2')
        // 缩放模型大小
        setMeshScale(gltf.scene,2.5)
        // // 需要进行贴图的物体名称 后续唐森会将物体名称进行修改 方便操作
        const matName = ['柱体151','柱体001','柱体003','NURBS_路径','NURBS_路径001','NURBS_路径002','NURBS_路径003','NURBS_路径005','NURBS_路径006','NURBS_路径007','NURBS_路径008',
        'NURBS_路径009','NURBS_路径010','NURBS_路径011','NURBS_路径012','NURBS_路径013','NURBS_路径014','NURBS_路径015','NURBS_路径016','NURBS_路径019',
        'NURBS_路径020','NURBS_路径021','NURBS_路径022',
        'NURBS_路径023','NURBS_路径024','NURBS_路径025','NURBS_路径026','NURBS_路径027',
        'NURBS_路径028','NURBS_路径029','NURBS_路径030','NURBS_路径031','NURBS_路径032',
        'NURBS_路径033','NURBS_路径034','NURBS_路径035','NURBS_路径036','NURBS_路径037',
        'NURBS_路径038','NURBS_路径039','NURBS_路径040','NURBS_路径041','NURBS_路径042',
        'NURBS_路径043','NURBS_路径044','NURBS_路径045','NURBS_路径046','NURBS_路径047',
        'NURBS_路径048','NURBS_路径049','NURBS_路径050','NURBS_路径051','NURBS_路径052',
        'NURBS_路径053','NURBS_路径054','NURBS_路径055','NURBS_路径056','NURBS_路径057',
        'NURBS_路径058','NURBS_路径059','NURBS_路径060','NURBS_路径061','NURBS_路径062',
        'NURBS_路径063','NURBS_路径064','NURBS_路径065','NURBS_路径066','NURBS_路径067',
        'NURBS_路径068','NURBS_路径069','NURBS_路径070','NURBS_路径071','NURBS_路径072',
        'NURBS_路径073','NURBS_路径074','NURBS_路径075','NURBS_路径076','NURBS_路径077',]
       // 贴图函数 根据模型上物体的名称选择需要进行贴图的物体
        matcapTexture(gltf.scene, matName, './textures/84.png')

        /**
         *  初始化动画函数 详细介绍请看 three/utils/animat.js executeMultipleAnimation的第二个参数支持传多个动画 参数必须是数组 动画数组中传入多少个动画会执行多少个动画
         *  updateMultipleAnimation更新方法 需要传入下次动画与上次动画之间的时间间隔 传0不执行
         *  对于动画和模型的对应关系 可以问唐森
         *  executeAnimation用来执行单个动画 用法与 executeMultipleAnimation类似 前者只能传入一个动画对象 非数组形式
         */

        // 风机01风扇动画
        const { startMultipleAnimation, updateMultipleAnimation, stopMultipleAnimation } = executeMultipleAnimation(gltf.scene, [gltf.animations[0], gltf.animations[1]])
        // 风机02风扇动画
        const { startMultipleAnimation: startAni2, updateMultipleAnimation: updateAni2, stopMultipleAnimation: stopAni2 } = executeMultipleAnimation(gltf.scene, [gltf.animations[2], gltf.animations[3]])

        // 初始化风机选中  高亮
        // const mesh1 = findMesh(gltf.scene, 'JBTFXT_HD')
        // const mesh2 = findMesh(gltf.scene, 'JBTFXT_HD3')
       

       

      
      // 监听复位方法
      watch(restMark, (val) => {
            if (val) {
                executeFanSelect()
            }
            setTimeout(() => {
                restMark.value = false
            }, 200)
        })
       

        // 渲染函数
        ani(function (...args) {
            const [time, camera, delta] = args
            // updateAnimation(delta)
            // 更新动画
            updateMultipleAnimation(0.15)
            updateAni2(0.15)
        })
    }

}).then((gltf) => {
    zntf.value = true

})
// 查看指定物体信息
function checkMeshData(name) {
    isShow.value = false
    // 测试内容
    if (name == '上风门状态') {
        // 通过物体名称获取物体 并添加效果 或者显示标签 需要让那个物体高亮 就传入其对应的名称 目前物体名称没有做修改 使用其默认的名称
        // changeCurrentMeshByName('DJ_01', gltfGlobal.value.scene, {
        //     isLabel: true,
        //     // 当需要显示标签时 callback 的第一个参数可以获取到 标签的坐标位置
        //     callback: (val) => {
        //         isDataShow.value = val
        //         console.log(isDataShow.value);
        //     }
        // })
    } else {
        // changeCurrentMeshByName('DJ_02', gltfGlobal.value.scene, {
        //     isLabel: true,
        //     callback: (val) => {
        //         isDataShow.value = val
        //         console.log(isDataShow);
        //     }
        // })
    }
}

// 模型挂载
onMounted(() => {
    mountRender(sceneDiv4.value);
   

});

// 路由离开页面时触发
onBeforeRouteLeave((to, from, next) => {
  stopRender();
  modelInit.value = null;
  
  next();
});

const drainageData = ref({})
const controlDialog = ref(false)
//页面列表接口数据
ApiExtractList().then((ext)=>{
  
  if(ext.Status === 0){
    drainageData.value = ext.Result
    console.log(drainageData.value,'智能抽采接口数据')


  }

})
const sceneDiv4= ref(null)
const fanActive = ref(0)

// 计算属性：将数据分组为每行4个
const groupedData = computed(() => {
  const chunkSize = 4;
  const result = [];
  for (let i = 0; i < drainageData.value.length; i += chunkSize) {
    result.push(drainageData.value.slice(i, i + chunkSize));
  }
  return result;
});

function openDialog(index) {
controlDialog.value = true;
}

const forList = ref({
  data: [
    {
      icon: open_icon,
      name: "开1#瓦斯泵",
    },

    {
      icon: lock_icon,
      name: "关1#瓦斯泵",
    },
    {
      icon: open_icon,
      name: "开2#瓦斯泵",
    },
    {
      icon: lock_icon,
      name: "关2#瓦斯泵",
    },
    {
      icon: open_icon,
      name: "开3#瓦斯泵",
    },
    {
      icon: lock_icon,
      name: "关3#瓦斯泵",
    },
    {
      icon: open_icon,
      name: "开4#瓦斯泵",
    },
    {
      icon: lock_icon,
      name: "关4#瓦斯泵",
    },
  ],
});

const forData = forList.value.data.map((item) => ({
  ...item,
  icon: new URL(item.icon, import.meta.url).href,
}));
forList.value.data = forData;
</script>
<style lang="scss" scoped>
@use  '../ventilateFacility/mainFan/assets/index.scss';


.san_pic{
  position: absolute;
  width: 900px;
  height: 270px;
  background-color: #222;
//   top: 693px;
//  left: 403px;

}

.temperature-list {
  width: 100%;
  overflow-y: scroll; 
overflow-x: hidden;
min-height: 270px;
  /* 让滚动条样式生效的前提，需配合下面的伪类 */
  // position: relative; 
/* 滚动条样式（可选，保持项目风格） */
  &::-webkit-scrollbar { width: 1px; }
  &::-webkit-scrollbar-track { background-color: #74787a; }
  &::-webkit-scrollbar-thumb { 
    background: #797d80; 
    border-radius: 2px; 
    &:hover { 
      background-color: #555;
     }
    }
}
.temp-table {
  width: 100%;
  border-collapse: collapse;
  text-align: center;
}
.temp-table td {
  border: 1px solid #444;
  padding: 12px 8px;
  color: #fff;
  background: #222;
  vertical-align: middle;
}
.title {
  font-weight: bold;
  margin-bottom: 4px;
  color: #f9d350;
}
.value {
  font-size: 0.9em;
}

.control_mb_div {
  color: #fff;
  display: flex;
  flex-direction: column;
}

.control_zt {
  background-image: url(../ventilateFacility/airWindow/assets/img/fengj_icon_title.png);
  width: 170px;
  height: 32px;
  background-size: 170px 32px;
  margin-left: 150px;

  p {
    text-align: center;
    padding-top: 10px;
  }
}

.control_back {
  width: 130px;
  height: 39px;
  background-size: 130px 39px;
}

.forItemBox {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.div_forItemBox {
  background-image: url(../ventilateFacility/airWindow/assets/img/panel_green_open.png);
  width: 130px;
  height: 38px;
  background-size: 130px 38px;

  img {
    width: 20px;
    height: 20px;
  }
}
.item-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #e3d8b7;
    padding: 10px 0;
    margin-top: 10px;

  }
  .center_guz{
      display: flex;
      flex-direction: row;
    }
.control_forItem {
  margin-left: 20px;
  margin-right: 20px;
  margin-top: 10px;
}




</style>