<template>
  <div class="Monitor_box">
    <div class="top-120px left-30px boxMonitor_left">
      <div class="">
        <div class="pt-10px aside_type">监测类型</div>
        <div class="flex Moitor_sear">
          <el-input
            v-model="adress"
            placeholder="请输入"
            clearable
            @clear="getrest"
            style="width: 100px; margin-top: 10px; margin-left: 5px;
           "
          ></el-input>
          <p class="p_bull flex" @click="search">
            <!-- <img class="img" src="../businessCenter/img/searchNew.png" alt="" /> -->
           <span>搜索</span>
          </p>
        </div>

        <!-- 左侧勾选框组 -->
        <el-scrollbar>
          <el-checkbox-group
            v-model="checkedTypes"
            :min="0"
            :max="4"
            @change="changeTypes(checkedTypes)"
          >
            <el-checkbox
              v-for="(item, index) in typeData"
              :label="item"
              :key="index"
              class="monitor_div"
              >{{ item }}</el-checkbox
            >
          </el-checkbox-group>
        </el-scrollbar>
      </div>
    </div>
    <!-- 左边侧边栏 -->

    <!-- 右边内容 -->

    <div class="flexBox">
      <template v-for="(boxTitle, index) in checkedTypes" :key="index">
        <DataBoxNew class="boxMonitor" :title="boxTitle">
          <template #mainFan>
            <el-scrollbar height="92%" class="pt-10px pb-20px">
              <div class="boxMonitor_div">
                <div
                  class="boxMonitor_mon"
                  v-for="(boxM, index) in CoData[boxTitle]"
                  :key="index"
                >
                  <div class="boxMonitor_mon_div">
                    <div class="flex boxM_ic">
                      <!-- <img :src="boxM.icon" /> -->
                      <img src="./assest/img/locate.png" />

                      <p>{{ boxM.DevAddress }}</p>
                    </div>
                    <div class="flex mb-20px mt-10px">
                      <p>监测值：</p>
                      <p class="ml-120px">{{ boxM.RealState }}</p>
                    </div>
                    <div class="flex mt-10px">
                      历史曲线：
                      <div
                        class="minotor_check ml-100px -mt-5px"
                        @click="checkEcharts(boxM.DevCode)"
                      >
                        <p>查看</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-scrollbar>
          </template>
        </DataBoxNew>
      </template>
    </div>

    <!-- 弹窗 -->
    <el-dialog
      v-model="dialogShow"
      :title="dialogTitle"
      style="width: 780px; height: 470px"
    >
      <!-- 时间选择 -->

      <div class="time_pick">
        <el-date-picker
          v-model="value2"
          type="datetimerange"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          date-format="YYYY/MM/DD ddd"
          time-format="A hh:mm:ss"
          @change="dateChange"
          :locale="locale"
        />
      </div>

      <div
        id="minoBox"
        style="
          width: 650px;
          height: 320px;
          margin-right: 30px;
          margin-top: 20px;
          margin-left: 40px;
        "
      ></div>
    </el-dialog>
  </div>
</template>
<script setup>
import { ref, onMounted, reactive } from "vue";

//  按需引入 echarts
import * as echarts from "echarts";
import DataBoxNew from "@/components/common/DataBoxNew.vue";
import {
  ApiMeasuringPoint,
  ApiSecurityMonitoringType,
  ApiSecurityMonitoringList,
  ApiHistoricalCurve,
} from "@/https/encapsulation/Safety";

const dialogShow = ref(false);
const dialogTitle = ref("");
// 默认勾选值
const checkedTypes = ref([]);
const typeData = ref([]);
const data = reactive({
  devCode: "",
  startTime: "",
  endTime: "",
});

const adress = ref("");
//模糊搜索条件
const checkedLists = ref([]);
// 定义时间选择
const value2 = ref([]);
let xAxisData = ref([]);
let yAxisData = ref([]);

const dateChange = () => {
  data.startTime = timeFormat(value2.value[0]);
  data.endTime = timeFormat(value2.value[1]);
  getData();
};
// 历史曲线图
function checkEcharts(devCode) {
  dialogShow.value = true;
  data.devCode = devCode;
  dialogTitle.value = devCode;

  getData();
}
function getData() {
  ApiHistoricalCurve({
    code: data.devCode,
    startTime: data.startTime,
    endTime: data.endTime,
  }).then((res) => {
    console.log(res, "ApiHistoricalCurve");

    if (res.data.Status == 0) {
      let list = res.data.Result;
      xAxisData.value = [];
      yAxisData.value = [];
      list.forEach((item) => {
        let time = item.IOTime.slice(0, 16);
        let value = Number(item.RealState);
        xAxisData.value.push(time);
        yAxisData.value.push(value);
      });
      console.log(xAxisData.value, "xAxisData.value");
      console.log(yAxisData.value, "yAxisData.value");
      echarts1();
    } else {
    }
  });
}
// 时间格式化
function timeFormat(date) {
  const dateTime = new Date(date);
  // 获取年份
  const Y = dateTime.getFullYear() + "-";
  // 获取当前年的月份 月份要 + 1 （0代表1月）date.getMonth() + 1
  const M =
    dateTime.getMonth() + 1 < 10
      ? "0" + (dateTime.getMonth() + 1) + "-"
      : dateTime.getMonth() + 1 + "-";
  // 获取当前日（1 - 31）
  const D =
    dateTime.getDate() < 10 ? "0" + dateTime.getDate() : dateTime.getDate();
  // 获取当前小时（0-23）
  const h =
    dateTime.getHours() < 10
      ? "0" + dateTime.getHours() + ":"
      : dateTime.getHours() + ":";
  // 获取当前分钟（0-59）
  const m =
    dateTime.getMinutes() < 10
      ? "0" + dateTime.getMinutes() + ":"
      : dateTime.getMinutes() + ":";
  // 获取当前秒数（0-59）
  const s =
    dateTime.getSeconds() < 10
      ? "0" + dateTime.getSeconds()
      : dateTime.getSeconds();
  return Y + M + D + " " + h + m + s;
}
const CoData = ref({});
// 只展示类型
function changeTypes(checkedList) {
  checkedLists.value = checkedList;
  //有模糊搜索条件
  if (
    adress.value !== undefined &&
    adress.value !== "" &&
    adress.value !== null
  ) {
    console.log(adress.value,'走if@@@@@');
    checkedList.forEach((lx) => {
      ApiSecurityMonitoringList(lx, adress.value).then((res) => {
        if (res.data.Result && res.data.Result.length != 0) {
          CoData.value[lx] = [];

          res.data.Result.forEach((item) => {
            CoData.value[lx].push({
              ...item,
              icon: new URL(
                "./assest/img/minotor_adress_icon.png",
                import.meta.url
              ).href,
            });
          });
        }
      });
    });
  }

  else {
    //无模糊搜索条件或者初始化时
    console.log(adress.value,'走else@@@@@');
    checkedList.forEach((lx) => {
      ApiSecurityMonitoringList(lx).then((res) => {
        // console.log(res,'安全监控搜索')
        // 判断返回数据
        if (res.data.Result && res.data.Result.length != 0) {
          CoData.value[lx] = [];

          res.data.Result.forEach((item) => {
            CoData.value[lx].push({
              ...item,
              icon: new URL(
                "./assest/img/minotor_adress_icon.png",
                import.meta.url
              ).href,
            });
          });
        }
      });
    });
  }
}
// 搜索条件
function search() {
  changeTypes(checkedLists.value);

  
}

// 重置
const getrest = () => {
  adress.value = "";
  changeTypes(checkedLists.value);
};
// 钩子
onMounted(() => {
  // 监测类型
  ApiSecurityMonitoringType().then((monitor) => {
    if (monitor.data.Status == 0) {
      const { Result } = monitor.data;
      typeData.value = Result;
      checkedTypes.value = [
        typeData.value[0],
        typeData.value[1],
        typeData.value[2],
        typeData.value[3],
      ];
    }
    changeTypes(checkedTypes.value);
  });
  checkedLists.value = checkedTypes;
});
// echarts图
function echarts1() {
  // 基于准备好的dom，初始化echarts实例
  var myChart = echarts.init(document.getElementById("minoBox"));
  // 指定图表的配置项和数据
  var option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: "line", // 默认为直线
      },
    },
    //控制直角坐标系与
    grid: {
      // 控制图形距离
      top: "10px",
      left: "50px",
      right: "40px",
      bottom: "60px",
    },

    dataZoom: [
      {
        show: true,
        realtime: true,
        start: 0,
        height: "20px",
        end: 10,
        bottom: "10px",
        xAxisIndex: [0, 1],
      },
    ],
    // x轴（指的是水平的那条线）相关配置
    xAxis: [
      {
        type: "category",
        data: xAxisData.value, //类目轴数据
        axisLabel: {
          // 坐标轴标签
          show: true, // 是否显示
          inside: false, // 是否朝内
          color: "#fff",
          fontSize: 12,
          interval: 0, //设置刻度间间隔，0表示全部显示不间隔；auto:表示自动根据刻度个数和宽度自动设置间隔个数
        },
        axisLine: {
          // 坐标轴 轴线
          show: true, // 是否显示
          lineStyle: {
            color: "#1E6889",
            width: 1,
            type: "solid",
          },
        },
        axisTick: {
          //刻度相关
          show: false, //是否显示刻度
          alignWithLabel: true, //是否对齐标签
        },
        inverse: false, //翻转x轴数据
      },
    ],
    // y轴（垂直的那条线）设置
    yAxis: [
      {
        type: "value", //x轴数据类型，value时就是值，category时就是分类，可用于区分y轴与x轴
        axisLabel: {
          // 坐标轴标签
          show: true, // 是否显示
          // inside: true, // 标签是否朝内,默认false
          interval: 0, //设置刻度间间隔，0表示全部显示不间隔；auto:表示自动根据刻度个数和宽度自动设置间隔个数
          rotate: 0, // 旋转角度
          margin: 3, // 刻度标签与轴线之间的距离
          color: "#fff", // 标签颜色默认取轴线的颜色
          fontSize: 12, //标签字号
        },
        splitLine: {
          // 网格线
          show: true, // 是否显示，默认为true
          lineStyle: {
            color: ["#1A5A78"],
            width: 1,
            type: "solid",
          },
        },
        axisLine: {
          // 坐标轴 轴线
          show: true, // 是否显示
          lineStyle: {
            color: "#1E6889", //轴线颜色
            width: 1, //轴线宽度
            type: "solid", //轴线类型
          },
        },
      },
    ],

    series: [
      {
        name: "",
        type: "line", //表明是折线图line
        data: yAxisData.value, //值数据，与类型数据一一对应
        itemStyle: {
          normal: {
            color: "#40F3EF", //折线上的颜色
          },
        },
        symbol: "none", //去掉折线图节点
        smooth: true, //是否平滑曲线，true曲线，false直线
        // 区域填充样式
        areaStyle: {
          normal: {
            // 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置
            color: {
              //分隔区域的颜色
              x0: 0,
              y0: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "#1D7985", // 0% 处的颜色
                },
                {
                  offset: 1,

                  color: "RGBA(20, 90, 106, 0)", // 100% 处的颜色；中间还可以设置50%、20%、70%时的颜色
                },
              ],
              globalCoord: false, // 缺省为 false，以确保上面的x,y,x2,y2表示的是百分比
            },
          },
        },
      },
    ],
  };
  // 使用刚指定的配置项和数据显示图表。
  myChart.setOption(option);
}
</script>
<style lang="scss" scoped>
@use "../ventilateFacility/mainFan/assets/index.scss";

.Monitor_box {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: row;
  color: #fff;
}

.aside_Monitor {
  width: 161px;
  height: 939px;
  background-image: url(./assest/img/minotor_aside.png);
  background-size: 161px 939px;
}

.aside_type {
  text-align: center;
  padding-bottom: 20px;
  border-bottom: 2px solid rgba(223, 148, 39, 0.6);
  padding-left: 2px;
  padding-right: 2px;
}

.el-checkbox-group {
  height: 770px;
}

.monitor_div {
  width: 155px;
  height: 34px;
  border: 1px solid rgba(255, 179, 15, 0.3); //xmm修改 2025-02-20 #0bdff2
  background-color: rgba(140, 91, 19, 0.1); //xmm修改 2025-02-20  #0e667b
  margin-top: 20px;
  margin-left: 7px;
  padding-left: 5px;
  color: #fff;
}

// 多选框样式
::v-deep .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #fff !important;
}

::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #fcc131 !important; //xmm修改
}

::v-deep .el-date-editor .el-range-input {
  color: #fff;
}

::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #9c6b19 !important; //xmm修改
  border-color: #fcc131 !important; //xmm修改
}

::v-deep .el-checkbox__input.is-checked .el-checkbox__inner::after {
  border-color: #fcc131 !important; //xmm修改
}

.el-checkbox__label {
  color: #fff;
}

.boxMonitor_div {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  font-size: 16px;
  padding-left: 4%;
}

.boxMonitor_mon {
  width: 262px;
  height: 160px;
  border-radius: 5px;
  margin-top: 10px;
  margin-bottom: 20px; // xmm修改 2025-02-21
  // xmm修改 2025-02-20
  background-color: rgba(53, 52, 41, 0.7);
  border: 1px solid rgba(233, 148, 39, 0.3);
  margin: 2% 2%;
}

.time_pick {
  // margin-top: -10px;
  margin-left: 50px;
  width: 70%;
}

::v-deep .el-date-editor {
  width: 88%;
  margin-left: 20%;
  --el-input-bg-color: #04233b !important;
  --el-input-border-color: #16b9f7 !important;
}

.boxMonitor_mon_div {
  display: flex;
  flex-direction: column;
  padding-left: 10px;
}

.boxM_ic {
  margin-top: 20px;
  margin-bottom: 20px;

  img {
    width: 13px;
    height: 15px;
  }

  p {
    padding-left: 10px;
  }
}

.minotor_check {
  // background-image: url(./assest/img/icon_check.png);
  width: 63px;
  height: 29px;
  background-size: 63px 29px;
  border: 1px solid rgba(252, 181, 46, 0.8);
  border-radius: 5px;

  p {
    padding-left: 15px;
    padding-top: 5px;
  }
}

.flexBox {
  position: absolute;
  top: 120px;
  left: 220px;
  width: 1700px;
  height: 939px;
  display: flex;
  flex-wrap: wrap;
  // xmm修改 2025-02-20
  justify-content: space-around;
  width: 89vw;
}

// xmm修改 2025-02-20
.boxMonitor_left {
  width: 8vw;
  height: 88vh;
  border-top: 1px solid#e29308;
  border-right: 1px solid#4b4d50;
  border-bottom: 1px solid#4b4d50;
  border-left: 1px solid#4b4d50;
  background-image: none;
}

.boxMonitor {
  width: 43vw;
  height: 42vh;
}

// xmm修改 2025-02-21
::v-deep .el-dialog {
  // width: 850px;
  // height: 500px;
  background: rgb(0, 0, 0) !important;
  box-shadow: rgb(255, 179, 15) 0px 0px 20px inset !important;
  border: 1px solid rgba(255, 179, 15, 0.3) !important;
  border-radius: 10px !important;
}

::v-deep .el-button + .el-button {
  background-color: rgba(255, 179, 15, 0.3) !important;
  border: 1px solid rgba(232, 157, 6, 0.8) !important;
}

//yj添加搜索框样式
.Moitor_sear {
  .el-input {
    width: 100px;
    height: 30px;
  }
}

::v-deep .el-input__wrapper {
  border: 1rem solid rgba(255, 179, 15, 0.3);
  background-color: rgba(140, 91, 19, 0.1);
}

.p_bull {
  width: 38px;
  height: 25px;
  margin-top: 16px;
  margin-left: 10px;
  font-size: 12px;
  background-color: rgba(232, 157, 6, 0.3);
  border: 1px solid rgba(232, 157, 6, 0.8);
  border-radius: 5px;

  span {
    margin-top: 3px;
    margin-left: 4px;
    margin-top: 5px;
  }
}
</style>
