.card {
    width: 190px;
    height: 254px;
    background-color: black;
    opacity: 0.8;
    background-image: url('../../../../public/textures/xx.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    display: flex;
    place-content: center;
    place-items: center;
    overflow: hidden;
    border-radius: 20px;
}

.card .image {
    width: 190px;
    height: 254px;
}

.card h2 {
    z-index: 1;
    color: white;
    font-size: 2em;
}

.card::before {
    content: '';
    position: absolute;

    width: 100px;
    /* background-image: linear-gradient(180deg, rgb(0, 183, 255), rgb(0, 183, 255)); */
    height: 130%;
    z-index: -1;
    animation: rotBGimg 3s linear infinite;
    transition: all 0.2s linear;
}

@keyframes rotBGimg {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.card::after {
    content: '';
    position: absolute;
    width: 190px;
    height: 254px;
    overflow: hidden;
    /* background: #07182E; */

    inset: 5px;
    border-radius: 15px;
}

/* .card:hover:before {
    background-image: linear-gradient(180deg, rgb(81, 255, 0), purple);
    animation: rotBGimg 3.5s linear infinite;
  } */

.line {
    height: 2px;
    width: 100%;
    background-color: red;
    animation: flow 6s linear infinite;
}

@keyframes flow {
    0% {
        transform: translateX(-1920px);
    }

    100% {
        transform: translateX(0);
    }
}

#path {
    stroke-dasharray: 3px, 1px;
    stroke-dashoffset: 0;
}