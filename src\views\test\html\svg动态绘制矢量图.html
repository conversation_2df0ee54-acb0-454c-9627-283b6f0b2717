<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>SVG</title>
    <style type="text/css">
        .icon {
            width: 500px;
            height: 500px;
        }

        .path {
            fill: #ea2411;
            font-size: 200px;
            font-family: cursive;
        }



        svg {
            /* position: absolute; */
            width: 100%;
            height: 100%;
            background-color: #000;
        }


        .use-path {
            fill: none;
            stroke: white;
            stroke-dashoffset: 35%;
            stroke-dasharray: 0 87.5%;
            stroke-width: 10px;
        }


        .use-path:nth-child(1) {
            stroke: #360745;
            animation: animation1 8s infinite ease-in-out forwards;

        }

        .use-path:nth-child(2) {
            stroke: #D61C59;
            animation: animation2 8s infinite ease-in-out forwards;

        }

        .use-path:nth-child(3) {
            stroke: #E7D84B;
            animation: animation3 8s infinite ease-in-out forwards;

        }

        .use-path:nth-child(4) {
            stroke: #EFEAC5;
            animation: animation4 8s infinite ease-in-out forwards;

        }

        .use-path:nth-child(5) {
            stroke: #1B8798;
            animation: animation5 8s infinite ease-in-out forwards;

        }

        @keyframes animation1 {
            50% {
                stroke-dasharray: 7% 28%;
                stroke-dashoffset: 7%;
            }

            70% {
                stroke-dasharray: 7% 28%;
                stroke-dashoffset: 7%;
            }
        }

        @keyframes animation2 {
            50% {
                stroke-dasharray: 7% 28%;
                stroke-dashoffset: 14%;
            }

            70% {
                stroke-dasharray: 7% 28%;
                stroke-dashoffset: 14%;
            }
        }

        @keyframes animation3 {
            50% {
                stroke-dasharray: 7% 28%;
                stroke-dashoffset: 21%;
            }

            70% {
                stroke-dasharray: 7% 28%;
                stroke-dashoffset: 21%;
            }
        }

        @keyframes animation4 {
            50% {
                stroke-dasharray: 7% 28%;
                stroke-dashoffset: 28%;
            }

            70% {
                stroke-dasharray: 7% 28%;
                stroke-dashoffset: 28%;
            }
        }

        @keyframes animation5 {
            50% {
                stroke-dasharray: 7% 28%;
                stroke-dashoffset: 35%;
            }

            70% {
                stroke-dasharray: 7% 28%;
                stroke-dashoffset: 35%;
            }
        }
    </style>
</head>

<body>

    <svg version="1.1" t="1695627829777" class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
        <!-- 基本路径 -->
        <path class="path"
            d="M518.455652 92.382609a422.956522 422.956522 0 0 0-422.956522 422.956521 422.956522 422.956522 0 0 0 422.956522 422.956522 422.956522 422.956522 0 0 0 422.956522-422.956522 422.956522 422.956522 0 0 0-422.956522-422.956521zM779.130435 581.676522l-80.139131 16.695652 51.2 28.93913a18.253913 18.253913 0 0 1 6.678261 23.596522 17.363478 17.363478 0 0 1-24.932174 6.90087l-51.2-28.939131 24.932174 75.909565v2.893913a17.586087 17.586087 0 0 1-12.466087 22.26087 18.253913 18.253913 0 0 1-22.260869-13.801739l-37.398261-113.530435L534.26087 547.172174v111.304348l81.697391 89.043478c1.335652 0 1.335652 1.335652 2.671304 1.335652a17.808696 17.808696 0 0 1-4.006956 24.932174 16.695652 16.695652 0 0 1-24.932174-2.671304L534.26087 712.347826v58.10087a18.253913 18.253913 0 0 1-17.808696 18.031304 17.363478 17.363478 0 0 1-18.031304-18.031304V712.347826l-55.429566 59.659131a1.335652 1.335652 0 0 1-1.335652 1.335652 17.586087 17.586087 0 1 1-24.932174-24.932174l81.697392-89.043478v-111.304348l-98.393044 55.206956-38.733913 113.530435v2.893913a17.14087 17.14087 0 0 1-22.26087 9.572174 18.476522 18.476522 0 0 1-11.130434-23.596522l26.267826-75.909565-52.535652 28.93913a16.250435 16.250435 0 0 1-23.596522-5.565217 18.031304 18.031304 0 0 1 5.565217-24.932174l51.2-28.93913-78.803478-16.695652c-1.335652 0-2.893913 0-2.893913-1.335653a16.918261 16.918261 0 0 1-12.243478-22.260869 19.812174 19.812174 0 0 1 22.260869-12.466087l120.431305 24.932174 98.170434-55.429565-98.170434-55.429566-120.431305 24.932174h-2.671304a19.589565 19.589565 0 0 1-20.925217-15.137391 18.476522 18.476522 0 0 1 16.695652-20.702609l78.803478-16.695652-51.2-28.93913a17.808696 17.808696 0 0 1-6.90087-23.596522 18.476522 18.476522 0 0 1 24.932174-6.90087l52.535652 28.939131-26.267826-76.132174v-2.671305a17.586087 17.586087 0 0 1 12.466087-22.260869 18.69913 18.69913 0 0 1 22.26087 13.801739l38.733913 113.307826 98.393043 56.765218v-111.304348l-81.697391-89.043479c-1.335652 0-1.335652 0-1.335652-1.335652a16.695652 16.695652 0 0 1 2.671304-24.932174 18.921739 18.921739 0 0 1 24.932174 2.671305l55.429565 59.436521v-60.104347a18.031304 18.031304 0 0 1 35.84 0v58.100869l55.429566-59.436522 1.335652-1.335652a19.144348 19.144348 0 0 1 26.267826 0 16.473043 16.473043 0 0 1-1.335652 24.932174l-81.697392 89.043478v111.304348l99.728696-56.765217 37.398261-113.307826V311.652174a19.812174 19.812174 0 0 1 24.709565-9.572174 18.031304 18.031304 0 0 1 9.794783 23.373913l-24.932174 76.132174 51.2-28.93913A19.366957 19.366957 0 0 1 756.869565 378.434783a19.812174 19.812174 0 0 1-6.900869 24.932174l-51.2 28.93913 80.13913 16.695652 1.558261 1.335652a18.031304 18.031304 0 1 1-9.794783 34.504348l-118.873043-24.932174-98.393044 55.429565 98.393044 55.429566 118.873043-24.932174h2.671305a18.031304 18.031304 0 1 1 5.787826 35.84z"
            p-id="4585"></path>
        <!-- 绘制路径 通过symbol整合再一起 可以添加多条路径 -->
        <symbol id="path">
            <path class="path"
                d="M515.33913 25.6a489.73913 489.73913 0 0 0-489.73913 489.73913 489.73913 489.73913 0 0 0 489.73913 489.739131 489.73913 489.73913 0 0 0 489.739131-489.739131 489.73913 489.73913 0 0 0-489.739131-489.73913z m456.347827 489.73913a456.347826 456.347826 0 0 1-456.347827 456.347827 456.347826 456.347826 0 0 1-456.347826-456.347827 456.347826 456.347826 0 0 1 456.347826-456.347826 456.347826 456.347826 0 0 1 456.347827 456.347826z"
                p-id="4584"></path>

        </symbol>

        <g>
            <use xlink:href="#path" class="use-path"></use>
            <use xlink:href="#path" class="use-path"></use>
            <use xlink:href="#path" class="use-path"></use>
            <use xlink:href="#path" class="use-path"></use>
            <use xlink:href="#path" class="use-path"></use>
        </g>

    </svg>

    <svg t="1695691875906" version="1.1" class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
        <!-- 基本路径 -->

        <!-- 绘制路径 通过symbol整合再一起 可以添加多条路径 -->
        <symbol id="path">
            <path class="path"
                d="M511.552 575.893 319.429 575.893 319.429 639.833 565.893 639.833 565.893 639.541 574.893 639.541 574.893 383.833 511.552 383.833ZM511.552 175.078c-221.887 0-401.759 179.935-401.759 401.828 0 108.72 43.185 207.356 113.332 279.694l-94.95 94.95 47.156 47.156 98.134-98.134c66.632 49.107 148.969 78.134 238.087 78.134 88.38 0 170.093-28.541 236.429-76.907l100.414 100.414 47.156-47.156-96.991-96.991c70.988-72.468 114.757-171.704 114.757-281.159C913.317 355.014 733.445 175.078 511.552 175.078zM511.552 914.682c-185.689 0-336.221-150.88-336.221-336.984s150.532-336.984 336.221-336.984c185.695 0 336.221 150.88 336.221 336.984S697.247 914.682 511.552 914.682zM233.15 22.963c-49.234 0-98.83 15.707-140.789 48.159-100.651 77.846-119.138 222.546-41.292 323.196l364.489-281.904C370.163 53.721 302.019 22.963 233.15 22.963zM72.587 296.766c-5.595-20.725-7.127-42.552-4.324-64.488 5.633-44.087 28.096-83.341 63.253-110.532 29.422-22.756 64.567-34.784 101.634-34.784 24.927 0 49.936 5.677 72.323 16.418 3.488 1.674 6.9 3.458 10.235 5.351L72.587 296.766zM932.549 73.149c-41.958-32.452-91.555-48.159-140.789-48.159-68.87 0-137.014 30.759-182.408 89.451l364.489 281.904C1051.687 295.694 1033.2 150.994 932.549 73.149zM952.323 298.794l-243.121-188.035c3.335-1.893 6.747-3.677 10.235-5.351 22.387-10.741 47.396-16.418 72.323-16.418 37.067 0 72.211 12.028 101.634 34.784 35.157 27.191 57.621 66.445 63.253 110.532C959.45 256.242 957.919 278.069 952.323 298.794z"
                p-id="2860"></path>

        </symbol>

        <g>
            <use xlink:href="#path" class="use-path"></use>
            <use xlink:href="#path" class="use-path"></use>
            <use xlink:href="#path" class="use-path"></use>
            <use xlink:href="#path" class="use-path"></use>
            <use xlink:href="#path" class="use-path"></use>
        </g>

    </svg>

</body>

</html>