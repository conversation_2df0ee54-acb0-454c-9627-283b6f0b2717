<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>活动邀请函</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }

        .invitation {
            max-width: 600px;
            margin: 20px auto;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background-color: #3498db;
            color: #fff;
            text-align: center;
            padding: 40px 20px;
        }

        .header h1 {
            margin: 0;
            font-size: 32px;
        }

        .content {
            padding: 20px;
        }

        .content p {
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .countdown {
            text-align: center;
            margin-bottom: 20px;
        }

        .countdown span {
            display: inline-block;
            padding: 10px;
            background-color: #3498db;
            color: #fff;
            border-radius: 4px;
            margin: 0 5px;
            font-size: 24px;
        }

        .form {
            text-align: center;
        }

        .form input {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        .form button {
            background-color: #3498db;
            color: #fff;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .form button:hover {
            background-color: #2980b9;
        }
    </style>
</head>

<body>
    <div class="invitation">
        <div class="header">
            <h1>诚挚邀请您参加我们的活动</h1>
        </div>
        <div class="content">
            <p>亲爱的朋友，我们诚挚地邀请您参加我们即将举办的精彩活动！这将是一个充满乐趣和惊喜的时刻，与您一起度过！</p>
            <p>活动详情：</p>
            <ul>
                <li>活动时间：2025 年 04 月 01 日 14:00</li>
                <li>活动地点：XX 市 XX 区 XX 路 XX 号</li>
            </ul>
            <div class="countdown">
                <span id="days">00</span>
                <span id="hours">00</span>
                <span id="minutes">00</span>
                <span id="seconds">00</span>
            </div>
            <div class="form">
                <input type="text" placeholder="您的姓名">
                <input type="email" placeholder="您的邮箱">
                <button>报名参加</button>
            </div>
        </div>
    </div>
    <script>
        const countdownDate = new Date("2025-04-01T14:00:00").getTime();

        const x = setInterval(() => {
            const now = new Date().getTime();
            const distance = countdownDate - now;

            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);

            document.getElementById("days").innerHTML = days;
            document.getElementById("hours").innerHTML = hours;
            document.getElementById("minutes").innerHTML = minutes;
            document.getElementById("seconds").innerHTML = seconds;

            if (distance < 0) {
                clearInterval(x);
                document.getElementById("countdown").innerHTML = "活动已开始！";
            }
        }, 1000);
    </script>
</body>

</html>