import { ref, watch, reactive } from "vue";
import gsap from "gsap";
// 导入gui对象
// import gui from "@/three/gui";
// 导入辅助坐标轴
import axesHelper from "@/three/helper";
import { initThree, meshRemove } from '@/three/initThree1'
import { meshPickup } from '@/three/raycaster'
import { cameraAnimate, createCameraTween } from '@/three/camera'
import { initAirFlow } from "@/three/effect/flow";
import { initMatcapThree } from "@/three/effect/matcap";
const startCamera = [0, 0, 20]
const { scene, THREE, renderer, outlinePass, cameraControls, camera, addOutlinePass, ani, addOutlinePass1 } = initThree(...startCamera);
export const controls = cameraControls;

scene.add(axesHelper)
// 扫描轮廓：Shape表示一个平面多边形轮廓
const shape = new THREE.Shape([
    // 按照特定顺序，依次书写多边形顶点坐标
    new THREE.Vector2(0, 0), //多边形起点
    new THREE.Vector2(0, 10),
    new THREE.Vector2(10, 10),
    new THREE.Vector2(5, -5),
    new THREE.Vector2(10, 0),
]);


// 扫描轨迹：创建轮廓的扫描轨迹(3D样条曲线)
const curve = new THREE.CatmullRomCurve3([
    new THREE.Vector3(-10, -50, -50),
    new THREE.Vector3(10, 0, 0),
    new THREE.Vector3(8, 50, 50),
    new THREE.Vector3(-5, 0, 100)
]);

//扫描造型：扫描默认没有倒角
const geometry = new THREE.ExtrudeGeometry(
    shape, //扫描轮廓
    {
        extrudePath: curve,//扫描轨迹
        steps: 100//沿着路径细分精度，越大越光滑
    }
);

const material = initMatcapThree()
const mesh = new THREE.Mesh(geometry, material);
scene.add(mesh);







ani(() => {
})
// 挂载
export function mountRender(el) {
    el.appendChild(renderer.domElement);
}
