.threeDataBox {
    .introduce_text {
        height: 24px;
        font-size: 15px;
        font-family: Source <PERSON> Sans CN;
        font-weight: 400;
        color: #ffc107;
        line-height: 24px;
    }

    .el-scrollbar__wrap--hidden-default {
        scrollbar-width: none;
        background: #453F37;
    }

    .el-table--fit .el-table__inner-wrapper:before {
        width: 100%;
        background-color: transparent;
    }

    // 重置原样式
    // table
    .el-table th.el-table__cell {
        background: #453F37 !important; //xmm修改 2025-02-13
        color: #eee !important;
        text-align: center;
        font-size: 14rem;
        font-weight: bold;
        border-bottom: none !important;
        border-bottom: 1px solid #FCC233 !important;
    }



    .el-table td.el-table__cell {
        height: 40rem !important;
        color: rgba(227, 216, 183) !important;
        background: #453F37 !important; //xmm修改 2025-02-13
        text-align: center;
        font-size: 14rem;
        border-bottom: none !important;
        border-bottom: 1px solid #ffc10766 !important;
    }

    // .el-table__body-wrapper {
    //     background-color: #0A2642;
    // }

    .el-table tbody tr:hover td {
        background-color: #262626 !important;
    }

    // 折叠面板
    .el-collapse-item,
    .el-collapse,
    .el-collapse-item__content,
    .el-collapse-item__wrap {
        border: none !important;
        padding: none !important;
    }


    .el-collapse-item__arrow {
        color: #EFF7F9 !important;
    }

    // list列表
    .el-collapse-item__header {
        background-color: #0A273E99 !important;
        width: 408rem !important;
        height: 50rem !important;
        background-image: url('/src/views/thereVentilation/assets/img/list_bg.png') !important;
        background-size: 408rem 48rem;
        border: none !important;

    }

    .el-collapse-item__content {
        padding-top: 5px !important;
        padding-bottom: 5px !important;
    }

    .el-collapse-item__wrap {
        background-color: #0A273E99 !important;
        width: 408rem !important;
    }

    .el-table__empty-block {
        background-color: #453F37 !important;
    }

    // 修改数据为空时 展示的文字
    .el-table__empty-text {
        color: #453F37;
        position: relative;


        &::before {
            content: "";
            position: absolute;
            width: 100%;
            height: 100%;
            left: 50%;
            transform: translateX(-50%);
            font-weight: 400;
            color: #eee;
            z-index: 9;
        }
    }


}