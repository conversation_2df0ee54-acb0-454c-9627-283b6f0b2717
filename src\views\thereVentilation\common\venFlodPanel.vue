<template>
    <el-collapse accordion v-model="onlineActiveName">
        <el-collapse-item v-for="(v, i) in flodData" :key="v.id" :name="i + 1">
            <template #title>
                <div class="list_title" ><span>{{ v.name }}</span></div>
            </template>
            <template #default>
                <div class="bg-[#262626]">
                    <slot :slotData="v" :slotIndex="i" name="content"></slot>
                </div>
            </template>
        </el-collapse-item>
    </el-collapse>
</template>
<script setup>
import { ref, watch } from 'vue';

const props = defineProps(['flodData'])
const emit = defineEmits(['collapseActiveItem'])
const onlineActiveName = ref(1)

watch(onlineActiveName, (val) => {
    if (val) {
        emit('collapseActiveItem', val)
    }
})
</script>
<style lang="scss" scoped>
.list_title {
    width: 250px;
    height: 50px;
    text-align: left;
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #EFF7F9;
    margin-left: 40px;
    line-height: 50px;
    /* margin-top: -px; */
}
</style>