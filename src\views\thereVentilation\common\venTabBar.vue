<template>
    <div v-if="style" class="w-100% flex " :style="{ 'justify-content': tabList.length == 1 ? 'start' : 'center' }">
        <div class="cursor-pointer" v-for="(v, i) in tabList" :key="v" @click="changeCurrentIndex(i)">
            <div v-if="!activePattern" :class="isEqualCurrentIndex(i) ? 'data_box_tab_active2' : 'data_box_tab2'">
                <span>{{ v }}</span>
            </div>
            <div v-else class="data_box_tab2">
                <span>{{ v }}</span>
            </div>
        </div>
    </div>
    <div v-else class="w-100% flex justify-center " :class="tabList.length == 1 ? 'one_item' : 'justify-content'">
        <div class=" cursor-pointer" v-for="(v, i) in tabList" :key="v" @click="changeCurrentIndex(i)">
            <div v-if="!activePattern" :class="isEqualCurrentIndex(i) ? 'data_box_tab_active1' : 'data_box_tab1'">
                <span>{{ v }}</span>
            </div>
            <div v-else class="data_box_tab1">
                <span>{{ v }}</span>
            </div>
        </div>
    </div>
</template>
<script setup>
import { ref, watch } from 'vue';
import { UseCurrentIndex } from '@/hooks/UseCurrentIndex'
const props = defineProps(['style', 'tabList', 'immediate', 'activePattern'])
const { currentIndex, changeCurrentIndex, isEqualCurrentIndex } = UseCurrentIndex();
const emit = defineEmits(['getCurrentIndex'])
watch(currentIndex, (val) => {
    emit('getCurrentIndex', val)
    if (val == -1) {
        currentIndex.value = 0
    }
}, { immediate: props.immediate ?? true })
</script>
<style lang="scss" scoped>
// 只有一个item属性
.one_item {
    justify-content: start;
    margin-left: 12px;
}

.data_box_tab_active1 {
    text-align: center;
    width: 80px;
    height: 40px;
    box-shadow: inset 0 -5px 5px rgba(252, 194, 51, 0.6);
    margin-right: 2px;

    span {
        width: 63px;
        height: 40px;
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #ffc107;
        line-height: 40px;

    }
}

.data_box_tab1 {
    @extend .data_box_tab_active1;
    box-shadow: inset 0 0 5px rgba(252, 194, 51, 0.6);

    span {
        color: #D0F2F7;
    }

    &:hover {
        @extend .data_box_tab_active1;
    }
}

.data_box_tab_active2 {
    @extend .data_box_tab_active1;
    box-shadow: none;
    background-image: url('../assets/img/btn_bg.png');
    background-size: 80px 40px;
}

.data_box_tab2 {
    @extend .data_box_tab_active2;

    span {
        color: #D0F2F7;
    }

    &:hover {
        @extend .data_box_tab_active2;
    }
}
</style>