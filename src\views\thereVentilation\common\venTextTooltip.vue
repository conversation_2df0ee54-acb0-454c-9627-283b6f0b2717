<template>
    <!-- <el-tooltip v-if="determineCharacterLength(str, maxLength ?? 8)" :content="str ?? 'null'" placement="top"
        effect="customized"> -->
    <span v-if="determineCharacterLength(str, maxLength ?? 8)" :style="{ width: width, fontSize: fontSize }"
        class="w-80px mb-2" truncated>
        &nbsp;{{ Number(str).toFixed(2) ?? 'null' }}
    </span>
    <!-- </el-tooltip> -->
    <span v-else> &nbsp;{{ str ?? 'null' }}</span>
</template>
<script setup>
const props = defineProps(['str', 'maxLength', 'width', 'fontSize'])
// 判断字符长度
function determineCharacterLength(str, num) {
    let str1 = str;
    if (!(str instanceof String)) {
        str1 = String(str);
    }
    return str1.length > num
}
</script>
<style lang="scss" scoped></style>