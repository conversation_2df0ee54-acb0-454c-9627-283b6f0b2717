<template>
    <div @mouseover="disableEvents = false" @mouseout="disableEvents = true" class="relative h-886px">
        <!-- 返回框 -->
        <div class="cursor-pointer mb-3px" @click="handleBack">
            <img class="data_box_img1 w-17px h-23px" src="../assets/img/ven_box_back.png" />
            <img class="data_box_img2 w-17px h-23px" src="../assets/img/ven_box_back.png" />
        </div>
        <div ref="ven_data_box" class="ven_data_box_bg relative pl-9px">
            <div class="data_box_title mt-36px mx-auto"><span>{{ title }}</span></div>
            <!-- 介绍栏 -->
            <div v-if="introduceShow ?? true" class="data_box_introduce">
                <slot name="introduce"></slot>
            </div>
            <div v-else>
                <div class="data_box_tunName">{{ tunName }}</div>
            </div>
            <!-- tab栏 -->
            <div v-if="tabList && tabList.length" class="mt-[-10px]">
                <VenTabBar :tab-list="tabList" @get-current-index="getCurrentIndex" />
            </div>
            <div class="pt-10px">
                <slot name="content"></slot>
            </div>
        </div>
        <!-- 动画装饰 -->
        <div ref="ani1" class="ven_ani_box1 z-10 w-1rem h-20rem bg-[#54F4F1]  absolute rounded-5rem"></div>
        <div ref="ani2" class="ven_ani_box2 z-10 w-1rem h-20rem bg-[#54F4F1] absolute rounded-5rem"></div>
        <div ref="ani3" class="ven_ani_box3 absolute">
            <img class="w-119px h-35px " src="../assets/img/ven_gy.png" />
        </div>
    </div>
</template>
<script setup>
import { ref, watch, onMounted } from 'vue';
import VenTabBar from './venTabBar.vue';
import mitt from '@/utils/eventHub'
import gsap from 'gsap';


const props = defineProps(['title', 'tunName', 'tabList', 'introduceShow', 'useMitt'])
const emit = defineEmits(['getCurrentIndex', 'handleBack'])
const disableEvents = ref(false)

watch(disableEvents, (val) => {
    mitt.emit('disableEvents', val)
})
function getCurrentIndex(val) {
    emit('getCurrentIndex', val)
}
function handleBack() {
    (props.useMitt ?? true) && mitt.emit('handleBack', -1)
    emit('handleBack', false)
}
const ven_data_box = ref(null)


const ani1 = ref()
const ani2 = ref()
const ani3 = ref()




onMounted(() => {
    gsap.fromTo(".ven_ani_box1", { top: '200rem', left: '4.8rem' }, { top: '60rem', duration: 2, yoyo: true, repeat: -1 });
    gsap.fromTo(".ven_ani_box2", { bottom: '200rem', left: '5.9rem' }, { bottom: '60rem', duration: 2.5, yoyo: true, repeat: -1 });
    gsap.fromTo(".ven_ani_box3", { opacity: 0.2 }, { opacity: 1, scaleX: 1.2, duration: 1, yoyo: true, repeat: -1 });
    gsap.fromTo(".data_box_img1", { opacity: 0.4 }, { opacity: 1, duration: 3, yoyo: true, repeat: -1 });
    gsap.fromTo(".data_box_img2", { opacity: 1 }, { opacity: 0.4, duration: 5, yoyo: true, repeat: -1 });
})

</script>
<style lang="scss" scoped>
@use '../assets/index.scss';

.ven_ani_box {}

.ven_ani_box2 {}

.ven_ani_box3 {
    left: 160px;
    bottom: 50px;
    z-index: 10;
    opacity: 0.2;
    filter: brightness(1.5);
    /* 增加亮度，1.0是原始亮度 */

}

.ven_data_box_bg {
    position: fixed;
    /* top: 119px; */
    width: 438px;
    height: 826px;
    background-image: url('../assets/img/data_box_bg.png');
    background-size: 438px 826px;

    .data_box_title {
        width: 100px;
        height: 30px;
        font-size: 18px;
        font-family: AlimamaShuHeiTi;
        font-weight: bold;
        color: #EBF5F8;
        line-height: 30px;
    }

    .data_box_introduce {
        margin: 10px 0 30px 19px;
        width: 385px;
        min-height: 112px;
        background-image: url('../assets/img/data_box_jx.png');
        background-size: 385px 100%;
    }

    .data_box_tunName {
        height: 16px;
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #41F5F1;
        line-height: 38px;
        text-align: center;
    }
}
</style>