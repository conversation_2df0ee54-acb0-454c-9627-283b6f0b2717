<template>
    <div @mouseenter="disableEvents = false" @mouseleave="disableEvents = true" class="cursor-pointer fixed bottom-0">
        <div class="flex justify-around w-745px mx-auto mb-[-36px]">
            <div v-for="(v, i) in bottomTabList" :key="v.name" class="w-full h-full cursor-pointer "
                @click="event => tabClick(v, i, event)" :style="{ opacity: currentIndex == i ? 1 : '' }">
                <div :id="'bt_tab_icon' + i"
                    class="icon_bg flex justify-center items-center opacity-60 hover:opacity-100"
                    :class="currentIndex === i ? 'icon_bg_active bottom_tab_ani ' : 'icon_bg'">
                    <div>
                        <img :src="currentIndex === i ? v.iconActive : v.icon" class="bottom_tab_icon w-36px h-36px"
                            alt="">
                    </div>
                </div>
                <div class="bottom_tab_name_active">
                    <span>{{ v.name }}</span>
                </div>
            </div>
        </div>
        <div>
            <img src="@/views/thereVentilation/assets/img/bottom_tab_bg.png" class="w-100vw h-94.7px">
        </div>
    </div>
    <!-- 监测分析 -->
    <transition leave-active-class="animate__animated animate__fadeOutLeft "
        enter-active-class="animate__animated animate__fadeInLeftBig">
        <div v-show="isEqualCurrentIndex(0)" class="fixed left-25px top-135px">
            <MonitoringAnalysis v-if="isEqualCurrentIndex(0)"></MonitoringAnalysis>
        </div>
    </transition>
    <!-- 设备设施 -->
    <transition leave-active-class="animate__animated animate__fadeOutLeft "
        enter-active-class="animate__animated animate__fadeInLeftBig">
        <div v-show="isEqualCurrentIndex(1)" class="fixed left-25px top-135px">
            <EquipmentFacilities v-if="isEqualCurrentIndex(1)"></EquipmentFacilities>
        </div>
    </transition>
    <!-- 实时解算 -->
    <transition leave-active-class="animate__animated animate__fadeOutLeft "
        enter-active-class="animate__animated animate__fadeInLeftBig">
        <div v-show="isEqualCurrentIndex(2)" class="fixed left-25px top-135px">
            <RealTimeSettlement @onHighlight="handleChildHighlight"  v-if="isEqualCurrentIndex(2)"></RealTimeSettlement>
        </div>
    </transition>

    <!-- 故障诊断 -->
    <transition leave-active-class="animate__animated animate__fadeOutLeft "
        enter-active-class="animate__animated animate__fadeInLeftBig">
        <div v-show="isEqualCurrentIndex(3)" class="fixed left-25px top-135px">
            <FaultDiagnosis v-if="isEqualCurrentIndex(3)"></FaultDiagnosis>
        </div>
    </transition>
    <!-- 灾难模拟 -->
    <transition leave-active-class="animate__animated animate__fadeOutLeft "
        enter-active-class="animate__animated animate__fadeInLeftBig">
        <div v-show="isEqualCurrentIndex(4)" class="fixed left-25px top-135px">
            <DisasterSimulation v-if="isEqualCurrentIndex(4)"></DisasterSimulation>
        </div>
    </transition>
    <!-- 用风地点 -->
    <transition leave-active-class="animate__animated animate__fadeOutLeft "
        enter-active-class="animate__animated animate__fadeInLeftBig">
        <div v-show="isEqualCurrentIndex(5)" class="fixed left-25px top-135px">
            <WindPoint v-if="isEqualCurrentIndex(5)"></WindPoint>
        </div>
    </transition>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import MonitoringAnalysis from '../monitoringAnalysis/monitoringAnalysis.vue';
import EquipmentFacilities from '../equipmentFacilities/equipmentFacilities.vue'
import RealTimeSettlement from '../realTimeSettlement/realTimeSettlement.vue'
import FaultDiagnosis from '../faultDiagnosis/faultDiagnosis.vue'
import WindPoint from '../windPoint/windPoint.vue'
import DisasterSimulation from '../disasterSimulation/disasterSimulation.vue'
import { UseCurrentIndex } from '@/hooks/UseCurrentIndex';
import mitt from '@/utils/eventHub';
// 图片
import jcfx from '@/views/thereVentilation/assets/img/jcfx.png'
import jcfxActive from '@/views/thereVentilation/assets/img/jcfx_active.png'
import sbss from '@/views/thereVentilation/assets/img/sbss.png'
import sbssActive from '@/views/thereVentilation/assets/img/sbss_active.png'
import ssjs from '@/views/thereVentilation/assets/img/ssjs.png'
import ssjsActive from '@/views/thereVentilation/assets/img/ssjs_active.png'
import yfdd from '@/views/thereVentilation/assets/img/yfdd.png'
import yfddActive from '@/views/thereVentilation/assets/img/yfdd_active.png'
import gzzd from '@/views/thereVentilation/assets/img/gzzd.png'
import gzzdActive from '@/views/thereVentilation/assets/img/gzzd_active.png'
import zhmn from '@/views/thereVentilation/assets/img/zhmn.png'
import zhmnActive from '@/views/thereVentilation/assets/img/zhmn_active.png'
import xfyc from '@/views/thereVentilation/assets/img/xfyc.png'
import xfycActive from '@/views/thereVentilation/assets/img/xfyc_active.png'
//
import { useThereVentilation } from '@/store/thereVentilation';
import { defineEmits } from 'vue';

// 声明转发的事件
const emit = defineEmits(['onHighlight']); 
// 监听子组件事件并转发
const handleChildHighlight = (tunIds) => {
  emit('onHighlight', tunIds); 
};
import { stopEvent } from '@/utils/window';
const disableEvents = defineModel({ default: false })
const store = useThereVentilation();
// 底部菜单栏
const bottomTabList = ref(
    [
        {
            id: '1',
            name: '监测分析',
            icon: jcfx,
            iconActive: jcfxActive
        },
        {
            id: '2',
            name: '设备设施',
            icon: sbss,
            iconActive: sbssActive
        },
        {
            id: '3',
            name: '实时解算',
            icon: ssjs,
            iconActive: ssjsActive
        },

        {
            id: '4',
            name: '故障诊断',
            icon: gzzd,
            iconActive: gzzdActive
        },
        {
            id: '5',
            name: '灾害模拟',
            icon: zhmn,
            iconActive: zhmnActive
        },
        {
            id: '6',
            name: '用风地点',
            icon: yfdd,
            iconActive: yfddActive
        },
        // {
        //     id: '7',
        //     name: '需风预测',
        //     icon: xfyc,
        //     iconActive: xfycActive
        // },

    ]
)

// 悬浮事件
const tabCurrentIndex = ref(-1)
function tabHoverEvent(item, index, event) {
    stopEvent(event);

}
const { currentIndex, isEqualCurrentIndex } = UseCurrentIndex()
currentIndex.value = 0;
function tabClick(item, index, event) {
    stopEvent(event);
    if (currentIndex.value === index) return
    currentIndex.value = index
    store.$patch({ bottomTabCurrentIndex: currentIndex.value })
}
// 
onMounted(() => {
    mitt.on('handleBack', (val) => {
        currentIndex.value = val
    })
    mitt.on('disableEvents', (val) => {
        disableEvents.value = val;
    })
})
</script>
<style lang="scss" scoped>
.icon_bg {
    width: 88px;
    height: 82px;
    background-image: url('@/views/thereVentilation/assets/img/icon_bg.png');
    background-size: 88px 82px;

}

.icon_bg_active {
    @extend .icon_bg;
    background-image: url('@/views/thereVentilation/assets/img/icon_bg_active.png');
}

.bottom_tab_ani {

    animation: bgAni 1.5s linear infinite alternate;

    .bottom_tab_icon {
        animation: iconAni 2s linear infinite alternate;
    }
}

@keyframes bgAni {
    0% {
        scale: 1
    }

    100% {
        scale: 1.05
    }
}



@keyframes iconAni {
    0% {
        transform: translateY(5px);
    }

    100% {
        transform: translateY(-5px);
    }
}

.bottom_tab_name_active {
    width: 88px;
    text-align: center;
    height: 16px;
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #f57c00;
    line-height: 36px;
}

.bottom_tab_name {
    @extend .bottom_tab_name_active;
    color: #ffc107;
}
</style>