<template>
    <div>
        <VenDataBox title="灾难模拟">
            <template #introduce>
                <div class="p-13px">
                    <p class="introduce_text">灾害类型:{{ titlePanel.type }}</p>
                    <p class="introduce_text">发生位置:{{ titlePanel.location }}</p>
                    <!-- <p class="introduce_text">发生时间:{{ titlePanel.time }}</p> -->
                    <VenTabBar active-pattern="hover" :immediate="false" @get-current-index="getCurrentIndex"
                        :style="true" :tab-list="tabListTitle">
                    </VenTabBar>

                </div>
            </template>
            <template #content>
                <!-- 灾难模拟选项 -->
                <div v-show="znPanelShow == -1" class="ml-16px w-397px flex justify-around mt-30px">
                    <div class="hzmn flex flex-col justify-around items-center py-10px">
                        <div class=""><img class="znmn_icon" src="../../assets/img/hz.png"></div>
                        <div class="znmn_title">火灾模拟</div>
                        <div class="znmn_btn" @click="startSimulation(0)"><span>开始模拟</span></div>
                    </div>
                    <div class="wsmn flex flex-col justify-around items-center py-10px">
                        <div class=""><img class="znmn_icon" src="../../assets/img/ws.png"></div>
                        <div class="znmn_title">瓦斯模拟</div>
                        <div class="znmn_btn" @click="startSimulation(1)"><span>开始模拟</span></div>
                    </div>
                </div>

                <div v-show="znPanelShow != -1" class="mt-[-10px]">
                    <VenTabBar @get-current-index="getCurrentIndex2" :tab-list="tabList"></VenTabBar>
                    <el-scrollbar height="440rem" style="width: 405rem;margin: 0 auto;">
                        <!-- 蔓延记录 -->
                        <!-- <div v-show="isEqualCurrentIndex(0)">
                            <div class="mx-auto mt-14px" v-for="(v) in spreadData" :key="v.id"
                                :class="v.startTime ? 'znmy' : 'znmy2'">
                                <div class="znmy_title_box flex justify-between items-center">
                                    <div flex>
                                        <div v-if="v.startTime"><img class="znmy_img"
                                                src="../../assets/img/hm_orange.png">
                                        </div>
                                        <div v-else><img class="znmy_img" src="../../assets/img/hm_green.png"></div>
                                        <div class="znmy_title ml-5px"><span>{{ v.tunName }}</span></div>
                                    </div>

                                    <div hover="text-[#0BDFF2]"
                                        class="text-[#fff] znmy_title_right flex items-center cursor-pointer">
                                        <Location style="width: 1.15em;" />
                                        <div><span>定位</span></div>
                                    </div>
                                </div>
                                <div v-if="v.startTime" class="px-15px flex flex-col justify-around h-89px ">
                                    <div class="flex justify-between items-center">
                                        <div class="znmy_text">巷道状态</div>
                                        <div class="znmy_text">开始时间</div>
                                        <div class="znmy_text">结束时间</div>
                                        <div class="znmy_text">持续时间</div>
                                    </div>
                                    <div class="w-365px mx-auto h-1px opacity-[0.3]" border="1px dashed #fff"></div>
                                    <div class="flex justify-between items-center">
                                        <div class="znmy_data">{{ v.state }}</div>
                                        <div class="znmy_data">{{ v.startTime }}</div>
                                        <div class="znmy_data">{{ v.endTime }}</div>
                                        <div class="znmy_data">{{ v.duration }}</div>
                                    </div>
                                </div>
                                <div v-else class="px-15px flex flex-col justify-around h-89px ">
                                    <div class="w-65% flex justify-between items-center">
                                        <div class="znmy_text">巷道状态</div>
                                        <div class="znmy_text">预计时间</div>
                                    </div>
                                    <div class="w-365px mx-auto h-1px opacity-[0.3]" border="1px dashed #fff"></div>
                                    <div class="w-65% flex justify-between items-center">
                                        <div class="znmy_data">{{ v.state }}</div>
                                        <div class="znmy_data">{{ v.estimatedTime }}</div>
                                    </div>
                                </div>
                            </div>
                        </div> -->
                        <!-- 避灾路线 -->
                        <div v-show="isEqualCurrentIndex(0)" ml-20px text="light-50" class="p-5px">
                            <div class=" ml-[-20px] mt-14px" v-for="(v) in lineData" :key="v.id"
                                :class="v.startTime ? 'znmy' : 'znmy2'">
                                <div class="znmy_title_box flex justify-between items-center">
                                    <div flex>
                                        <div v-if="znPanelShow == 0"><img class="znmy_img"
                                                src="../../assets/img/hm_orange.png">
                                        </div>
                                        <div v-else><img class="znmy_img" src="../../assets/img/ws.png"></div>
                                        <div class="znmy_title ml-5px"><span>节点：{{ v.NodeID }}</span></div>
                                    </div>

                                    <div hover="text-[#0BDFF2]" @click="goNodeLocation(v.NodeID)"
                                        class="text-[#fff] znmy_title_right flex items-center cursor-pointer">
                                        <Location style="width: 1.15em;" />
                                        <div><span>定位</span></div>
                                    </div>
                                </div>
                                <!-- <div v-if="v.startTime" class="px-15px flex flex-col justify-around h-89px ">
                                    <div class="flex justify-between items-center">
                                        <div class="znmy_text">巷道状态</div>
                                        <div class="znmy_text">开始时间</div>
                                        <div class="znmy_text">结束时间</div>
                                        <div class="znmy_text">持续时间</div>
                                    </div>
                                    <div class="w-365px mx-auto h-1px opacity-[0.3]" border="1px dashed #fff"></div>
                                    <div class="flex justify-between items-center">
                                        <div class="znmy_data">{{ v.state }}</div>
                                        <div class="znmy_data">{{ v.startTime }}</div>
                                        <div class="znmy_data">{{ v.endTime }}</div>
                                        <div class="znmy_data">{{ v.duration }}</div>
                                    </div>
                                </div>
                                <div v-else class="px-15px flex flex-col justify-around h-89px ">
                                    <div class="w-65% flex justify-between items-center">
                                        <div class="znmy_text">巷道状态</div>
                                        <div class="znmy_text">预计时间</div>
                                    </div>
                                    <div class="w-365px mx-auto h-1px opacity-[0.3]" border="1px dashed #fff"></div>
                                    <div class="w-65% flex justify-between items-center">
                                        <div class="znmy_data">{{ v.state }}</div>
                                        <div class="znmy_data">{{ v.estimatedTime }}</div>
                                    </div>
                                </div> -->
                            </div>
                        </div>
                    </el-scrollbar>
                    <!-- 退出模拟 -->
                    <div class="znmy_quit" @click="exit">
                        <span>退出模拟</span>
                    </div>
                </div>



            </template>
        </VenDataBox>
        <!-- 预案弹窗 -->
        <el-dialog v-model="dialogShow" :title="dialogTitle" style="width:850px ; height: 500px;">
            <!-- <div>{{ dialogTitle }}</div> -->
        </el-dialog>
    </div>
</template>
<script setup>
import { ref, watch } from 'vue';
import VenDataBox from '../../common/ventilationDataBox.vue'
import VenTabBar from '../../common/venTabBar.vue';
import VenFlodPanel from '../../common/venFlodPanel.vue';
import { Search, Location } from '@element-plus/icons-vue'
import { UseCurrentIndex } from '@/hooks/UseCurrentIndex';
import { useThereVentilation } from '@/store/thereVentilation';
import { ElMessage } from 'element-plus';
import { storeToRefs } from 'pinia';
import { tobtainDisasterAvoidanceRoutes } from '@/https/encapsulation/threeDimensional';
const store = useThereVentilation();
const { isFlag, currentTun: currentTunData, nodeList, positioningNodes } = storeToRefs(store);
// 
const tabListTitle = ['综合预案', '专项预案', '处置方案']
const dialogShow = ref(false)
const dialogTitle = ref('')

function getCurrentIndex(index) {
    dialogShow.value = true
    dialogTitle.value = tabListTitle.at(index)
}
function goNodeLocation(nid) {
    positioningNodes.value = nid;
}
// 标题数据
const titlePanel = ref({
    type: '火灾模拟',
    location: '南冀胶带机大巷',
})
// const tabList = ref(['蔓延记录', '避灾路线'])
const tabList = ref(['避灾路线'])


// 灾难蔓延数据
const { currentIndex, isEqualCurrentIndex } = UseCurrentIndex()
function getCurrentIndex2(index) {
    currentIndex.value = index
}
// 显示开始模拟后的数据界面
const znPanelShow = ref(-1)
// console.log(znPanelShow.value, 'znPanelShow');
//开始模拟
function startSimulation(type) {
    znPanelShow.value = type;
    ElMessage.warning('请拾取巷道')
    isFlag.value = true;
}
// 灾难线路数据
const lineData = ref([])
watch(() => store.currentTun, (val) => {
    tobtainDisasterAvoidanceRoutes(val.TunID).then(({ data }) => {
        if (data.Status == 0) {
            console.log(nodeList.value);
            const arr = data.Result.split('-').map((v) => {
                return nodeList.value.find(k => k.NodeID == v)
            });
            lineData.value = arr;
            store.$patch(
                {
                    lineData: arr,
                }
            )
        }
    })
})
function exit() {
    znPanelShow.value = -1;
    positioningNodes.value = null;
    store.$patch({
        lineData: [],
    })
    isFlag.value = false;
}

</script>

<style lang="scss" scoped>
@use '../../assets/index.scss';

.equ_btn {
    width: 88px;
    height: 40px;
    background-image: url('../../assets/img/btn_bg.png');
    background-size: 88px 40px;
    cursor: pointer;
    text-align: center;

    span {
        width: 30px;
        height: 29px;
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 40px;
    }
}

.znmn_start {
    width: 388px;
    height: 129px;
    background: rgba(233, 134, 80, 0.08);

    .znmn_start_title {
        width: 388px;
        height: 40px;
        background: rgba(233, 134, 80, 0.08);
        border: 1px solid #E98650;
    }
}

.hzmn {
    width: 192px;
    height: 161px;
    background-image: url('../../assets/img/hzmn_bg.png');
    background-size: 192px 161px;

    .znmn_icon {
        width: 34px;
        height: 34px;
        margin: 0 auto;
    }

    .znmn_title {
        width: 87px;
        height: 24px;
        font-size: 20px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: #FFFFFF;
        line-height: 24px;
        background: linear-gradient(-3deg, #E66140 0%, #FFFFFF 99.12109375%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .znmn_btn {
        width: 110px;
        cursor: pointer;
        height: 31px;
        background-image: url('../../assets/img/hzmn_btn.png');
        background-size: 110px 31px;
        text-align: center;

        span {
            width: 110px;
            height: 31px;
            font-size: 16px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #F2493D;
            line-height: 31px;
        }
    }
}

.wsmn {
    @extend .hzmn;
    background-image: url('../../assets/img/wsmn_bg.png');

    .znmn_title {
        background: linear-gradient(-3deg, #FFAB00 0%, #FFFFFF 99.12109375%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .znmn_btn {
        background-image: url('../../assets/img/wsmn_btn.png');

        span {
            color: #FFAB00;
        }
    }
}

.znmy {
    width: 388px;
    background: rgba(233, 134, 80, 0.08);

    .znmy_img {
        width: 24px;
        height: 28px;
    }

    .znmy_title_box {
        width: 388px;
        padding: 0 5px;
        height: 40px;
        background: rgba(233, 134, 80, 0.08);
        border: 1px solid #E98650;

        .znmy_title {
            width: 126px;
            height: 18px;
            font-size: 18px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #FFFFFF;
            line-height: 24px;
        }

        .znmy_title_right {
            width: 60px;
            height: 24px;
            font-size: 16px;
            font-family: Source Han Sans CN;
            font-weight: 400;

            line-height: 24px;
        }
    }


    .znmy_text {
        text-align: center;
        width: 70px;
        height: 24px;
        font-size: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 24px;
    }

    .znmy_data {
        text-align: center;
        width: 70px;
        height: 24px;
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FD945B;
        line-height: 24px;
    }

}



.znmy2 {
    @extend .znmy;
    background: rgba(65, 245, 145, 0.08);

    .znmy_title_box {
        background: rgba(65, 245, 145, 0.08);
        border: 1px solid #41F591;
    }

    .znmy_data {
        color: #41F591;
    }
}
</style>
<style lang="scss">
.znmy_quit {
    width: 365px;
    height: 41px;
    background-image: url('../../assets/img/tcmn.png');
    background-size: 365px 41px;
    text-align: center;
    margin: 5px auto;
    cursor: pointer;
    color: #FFFFFF;

    span {
        width: 100px;
        height: 41px;
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        line-height: 41px;
    }

    &:hover {
        color: #0BDFF2;
    }

}
</style>