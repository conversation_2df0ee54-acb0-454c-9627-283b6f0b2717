<template>
    <VenDataBox title="设备设施" :tab-list="tabList" @get-current-index="getCurrentIndex" @handleBack="handleBack">
        <template #introduce>
            <div class="p-13px">

                <p class="introduce_text">为了达到矿井通风的目的，必须克联空一部加过程中的通风阻力，矿并设管回风井3个，布置通风机3对。</p>
            </div>
        </template>
        <template #content>
            <div ml-15px class="mt-[-10px] ">
                <el-scrollbar height="530rem " class="ml-[-10px] px-5px">
                    <!-- 主扇 -->
                    <div v-show="isEqualCurrentIndex(0)">
                        <div v-for="(v, i) in mainFanData" :key="v" class="equ_data_box">
                            <div flex justify="between" ml-49px mt-13px>
                                <!-- 后端返回的字段不统一 -->
                                <div class="equ_title"><span>{{ typeof v[mainFanDataField[3].prop].DevName !=
                                    'undefined' ? v[mainFanDataField[3].prop].DevName
                                    : v[mainFanDataField[3].prop].devName
                                        }}</span></div>
                                <div class="turn w-21px h-21px mt-10px mr-10px"><img src="../../assets/img/zs.png"
                                        w-21px h-21px />
                                </div>
                            </div>
                            <div flex justify="around" h-full>
                                <div v-for="(v1) in mainFanDataField" mt-25px>
                                    <div v-if="v1.name">
                                        <div class="equ_value"><span>{{ v[v1.prop] }}</span></div>
                                        <div class="equ_text"><span>{{ v1.name }}</span></div>
                                        <!-- <div class="equ_btn"><span>{{ v1.btnName }}</span></div> -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 局扇 -->
                    <div v-show="isEqualCurrentIndex(1)" text-white>
                        <div v-for="(v, i) in localFanData" :key="v" class="equ_data_box">
                            <div flex justify="between" ml-49px mt-13px>
                                <div class="equ_title"><span>
                                        {{ typeof v[localFanDataField[3].prop].DevName !=
                                            'undefined' ? v[localFanDataField[3].prop].DevName
                                            : v[localFanDataField[3].prop].devName
                                        }}</span></div>
                                <div class="turn w-21px h-21px mt-10px mr-10px"><img src="../../assets/img/bule_fj.png"
                                        w-21px h-21px />
                                </div>
                            </div>
                            <div flex justify="around" h-full>
                                <div v-for="(v1) in localFanDataField" mt-25px>
                                    <div v-if="v1.name">
                                        <div class="equ_value"><span>{{ v[v1.prop] }}</span></div>
                                        <div class="equ_text"><span>{{ v1.name }}</span></div>
                                        <!-- <div class="equ_btn"><span>{{ v1.btnName }}</span></div> -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 风门 -->
                    <div v-show="isEqualCurrentIndex(2)" text-white>
                        <div v-for="(v, i) in windDoorData" :key="v" class="equ_data_box">
                            <div flex justify="between" ml-49px mt-13px>
                                <div class="equ_title"><span>{{
                                    typeof v[windDoorDataField[3].prop].DevName !=
                                        'undefined' ? v[windDoorDataField[3].prop].DevName
                                        : v[windDoorDataField[3].prop].devName }}</span></div>
                                <div class=" w-21px h-21px mt-10px mr-10px"><img src="../../assets/img/fm.png" w-21px
                                        h-21px />
                                </div>
                            </div>
                            <div flex justify="around" h-full>
                                <div v-for="(v1) in windDoorDataField" mt-25px>
                                    <div v-if="v1.name">
                                        <div class="equ_value"><span>{{ v[v1.prop] }}</span></div>
                                        <div class="equ_text"><span>{{ v1.name }}</span></div>
                                        <!-- <div class="equ_btn"><span>{{ v1.btnName }}</span></div> -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 风窗 -->
                    <div v-show="isEqualCurrentIndex(3)" text-white class="pr-20px">
                        <div v-for="(v, i) in windWindowData" :key="v" class="equ_data_box">
                            <div flex justify="between" ml-49px mt-13px>
                                <div class="equ_title"><span>
                                        {{ typeof v[windWindowDataField[3].prop].DevName !=
                                            'undefined' ? v[windWindowDataField[3].prop].DevName
                                            : v[windWindowDataField[3].prop].devName }}</span></div>
                                <div class=" w-21px h-21px mt-10px mr-10px"><img src="../../assets/img/fc.png" w-21px
                                        h-21px />
                                </div>
                            </div>
                            <div flex justify="around" h-full>
                                <div v-for="(v1) in windWindowDataField" mt-25px>
                                    <div v-if="v1.name">
                                        <div class="equ_value"><span>{{ v[v1.prop] }}</span></div>
                                        <div class="equ_text"><span>{{ v1.name }}</span></div>
                                        <!-- <div class="equ_btn"><span>{{ v1.btnName }}</span></div> -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </el-scrollbar>
            </div>
        </template>
    </VenDataBox>
</template>
<script setup>
import VenDataBox from '../../common/ventilationDataBox.vue'
import { UseCurrentIndex } from '@/hooks/UseCurrentIndex';
import { ApiMainFanList } from '@/https/encapsulation/MainFan.js';
import { ApiLocalFanList } from '@/https/encapsulation/LocalFan.js';
import { ApiWindDoorList } from '@/https/encapsulation/WindDoor.js';
import { ApitWindwindowList } from '@/https/encapsulation/Windwindow.js';
import { ref, onMounted, watch } from 'vue';
const tabList = ['主扇', '局扇', '风门', '风窗']
const { currentIndex, changeCurrentIndex, isEqualCurrentIndex } = UseCurrentIndex();
function getCurrentIndex(index) {
    currentIndex.value = index
    getData(index)
}

// 主扇字段
const mainFanDataField =
    [
        {
            name: '风量(m³/s)',
            prop: 'AirVolume',
            // btnName: '场景'
        },
        {
            name: '负压(Pa)',
            prop: 'InletVacuum',
            // btnName: '定位'
        },
        {
            name: '电压(kV)',
            prop: 'InlineVoltage',
            // btnName: '曲线'
        },
        {
            prop: 'Fannerinfo'
        }
    ]
// 局扇字段
const localFanDataField =
    [
        {
            name: '风速(m/s)',
            prop: 'AirSpeed',
            // btnName: '场景'
        },
        {
            name: '馈电电流(A)',
            prop: 'InletVacuum',
            // btnName: '定位'
        },
        {
            name: '馈电电压(V)',
            prop: 'InlineVoltage',
            // btnName: '曲线'
        },
        {
            prop: 'LocalFaninfo'
        }
    ]
// 风门字段
const windDoorDataField =
    [
        {
            name: '风速(m/s)',
            prop: 'WindSpeed',
            // btnName: '场景'
        },
        {
            name: '压力(kg/cm³)',
            prop: 'Pressure',
            // btnName: '定位'
        },
        {
            name: '压差(kg/cm³)',
            prop: 'DiffPressure',
            // btnName: '曲线'
        },
        {
            prop: 'AirDoorinfo'
        }
    ]
const windWindowDataField =
    [
        {
            name: '风速(m/s)',
            prop: 'WindSpeed',
            // btnName: '场景'
        },
        {
            name: '压力(kg/cm³)',
            prop: 'Pressure',
            // btnName: '定位'
        },
        {
            name: '压差(kg/cm³)',
            prop: 'DiffPressure',
            // btnName: '曲线'
        },
        {
            prop: 'WindWindowInfo'
        }
    ]
let time = ref()
onMounted(() => {
    startGetData();
})
function startGetData() {
    if (time.value) handleBack();
    time.value = setInterval(() => {
        getData(currentIndex.value);
    }, 5000)
}
watch(currentIndex, (val) => {
    switch (val) {
        case 0: !mainFanData.value.length && handleBack(); break;
        case 1: !localFanData.value.length && handleBack(); break;
        case 2: !windDoorData.value.length && handleBack(); break;
        case 3: !windWindowData.value.length && handleBack(); break;
    }
})

// 主扇数据
const mainFanData = ref([])
const localFanData = ref([])
const windDoorData = ref([])
const windWindowData = ref([])
function getData(index) {
    switch (index) {
        case 0:
            // 主扇
            ApiMainFanList().then(res => {
                const { Result } = res ?? { Result: [] };
                console.log(Result, 'Result');

                mainFanData.value = handleData(Result, mainFanDataField)
            }); break;
        case 1:
            // 局扇
            ApiLocalFanList().then(res => {
                const { Result } = res ?? { Result: [] };
                localFanData.value = handleData(Result, localFanDataField)
            }); break;
        case 2:
            // 风门
            ApiWindDoorList().then(res => {
                const { Result } = res ?? { Result: [] };
                windDoorData.value = handleData(Result, windDoorDataField)
            }); break;
        case 3:
            // 风窗
            ApitWindwindowList().then(res => {
                const { Result } = res ?? { Result: [] };
                windWindowData.value = handleData(Result, windWindowDataField)
            }); break;

    }
    console.log(mainFanData.value, ' mainFanData.valu');

    function handleData(arr = [], fieldList = []) {
        return fieldList.length && arr.map((v) => {
            const needField = fieldList.map(v => v.prop);
            const obj = {}
            obj[needField[0]] = v[needField[0]] && ((v[needField[0]].toFixed(2)) ?? 0);
            obj[needField[1]] = v[needField[1]] && ((v[needField[1]].toFixed(2)) ?? 0);
            obj[needField[2]] = v[needField[2]] && ((v[needField[2]].toFixed(2)) ?? 0);
            obj[needField[3]] = (v[needField[3]]) ?? 0;
            return obj
        })
    }
}

// 离开时停止刷新数据
function handleBack() {
    clearInterval(time.value);
}


</script>
<style lang="scss" scoped>
@use '../../assets/index.scss';

.equ_data_box {
    width: 410px;
    height: 172px;
    background-size: 410px 172px;
    background-image: url('../../assets/img/sbss_date_box_bg.png');

    .equ_title {
        width: 250px;
        height: 40px;
        font-size: 18px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #FFFFFF;
        line-height: 40px;
    }

    .equ_value {
        text-align: center;

        width: 88px;
        height: 32px;
        font-size: 24px;
        font-family: DIN;
        font-weight: 500;
        color: #E98650;
        line-height: 32px;
    }

    .equ_text {
        text-align: center;

        width: 88px;
        height: 32px;
        font-size: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #E3F7F8;
        line-height: 32px;
    }

    .equ_btn {
        width: 88px;
        height: 40px;
        background-image: url('../../assets/img/btn_bg.png');
        background-size: 88px 40px;
        cursor: pointer;
        text-align: center;

        span {
            width: 30px;
            height: 29px;
            font-size: 16px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #FFFFFF;
            line-height: 40px;
        }
    }
}
</style>