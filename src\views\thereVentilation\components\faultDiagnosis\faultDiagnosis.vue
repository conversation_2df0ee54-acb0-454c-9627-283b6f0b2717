<template>
    <div>
        <VenDataBox @get-current-index="getCurrentIndex" title="故障诊断" :tab-list="tabList">
            <template #introduce>
                <div class="p-13px">

                    <p class="introduce_text">对矿井通风系统进行在线监测，具有全方位，多元化的监控与控制，自动故障预警报警，实现无人值守模式</p>
                </div>
            </template>
            <template #content>
                <!-- 设备异常 -->
                <div v-show="currentIndex == 0">
                    <el-table class="ven_custom-table" :data="deviceAbnormalityData.tableData" height="490rem"
                        style="width: 405rem;margin: 0 auto;">
                        <el-table-column v-for="(v, i) in deviceAbnormalityData.tableField" :key="v.prop" :prop="v.prop"
                            :label="v.label" :width="v.width && v.width">
                            <template #default="scope">
                                <span v-if="i != 2">{{ scope.row[v.prop] ?? '-' }}</span>
                                <span v-else>{{ scope.row[v.prop].slice(0, -8) ?? '-' }}</span>

                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <!-- 风速异常 -->
                <div v-show="currentIndex == 1" text-white ml-20px>
                     <el-table class="ven_custom-table" :data="deviceAbnormalityData.tableData" height="490rem"
                        style="width: 405rem;margin: 0 auto;">
                        <el-table-column v-for="(v, i) in deviceAbnormalityData.tableField" :key="v.prop" :prop="v.prop"
                            :label="v.label" :width="v.width && v.width">
                            <template #default="scope">
                                <span v-if="i != 2">{{ scope.row[v.prop] ?? '-' }}</span>
                                <span v-else>{{ scope.row[v.prop].slice(0, -8) ?? '-' }}</span>

                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </template>
        </VenDataBox>
    </div>
</template>
<script setup>
import { ref, watch, reactive } from 'vue';
import VenDataBox from '../../common/ventilationDataBox.vue'
import VenFlodPanel from '../../common/venFlodPanel.vue';
import { UseCurrentIndex } from '@/hooks/UseCurrentIndex';
import { Search, Location } from '@element-plus/icons-vue'
import { get } from '@/https/request';
// 
const { currentIndex } = UseCurrentIndex()
const getCurrentIndex = (val) => {
    currentIndex.value = val
}
const tabList = ['设备异常', '风速异常']

// 获取设备异常信息
async function getDeviceExceptionInformation() {
    let api = `/api/v1/gis/abnormal`;
    const { data } = await get({
        api: api,
        url: api
    })
    return data
}
const deviceAbnormalityData = reactive(
    {
        tableData: [],
        tableField: [
            {
                prop: 'DeviceName',
                label: '设备名称'
            },
            {
                prop: 'DeviceTypeName',
                label: '设备类型'
            },
            {
                prop: 'FaultTime',
                label: '发生时间',
                width: 180
            }
        ]
    })

getDeviceExceptionInformation().then((res) => {
    const { Result } = res;
    deviceAbnormalityData.tableData = Result;
})


</script>

<style lang="scss">
@use '../../assets/index.scss';
</style>