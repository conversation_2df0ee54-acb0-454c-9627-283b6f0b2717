<template>
    <div class="relative">
        <VentilationDataBox title="实体信息" :tunName="tunInfo.DevAddress" :introduce-show="false" :use-mitt="false"
            @handleBack="handleBack">
            <template #content>
                <VenFlodPanel class="ml-[10px] mt-15px" :flod-data="data">
                    <template #content="{ slotIndex }">
                        <div v-show="slotIndex == 0" class="flex h-355px">
                            <div class="h-355px w-50px mr-10px" style="border-right: 1px solid #2CAAE7;">
                            </div>
                            <el-space direction="vertical" alignment="flex-start">
                                <div class="flex text-white items-center mt-10px" v-for="v in dataNameList" :key="v">
                                    <div class="ml-[-10px] w-15px h-10px flex items-center">
                                        <div class="w-full h-1px bg-[#2CAAE7]"></div>
                                    </div>
                                    <div class="w-12px h-12px bg-[#11AEEE] rounded-2px rotate-45 mr-3px"></div>
                                    <div class="flex items-center ">
                                        <span text="16px #41F5F1">&nbsp;{{ v.name }}：</span>
                                        <VenTextTooltip :str="tunInfo[v.prop]" :max-length="10" width="180px"
                                            font-size="12px" />
                                    </div>
                                </div>
                            </el-space>
                        </div>
                    </template>
                </VenFlodPanel>
                <div class="znmy_quit absolute left-50% translate-x-[-50%] bottom-45px">
                    <span>确认</span>
                </div>
            </template>
        </VentilationDataBox>
    </div>
</template>
<script setup>
import VentilationDataBox from '../../common/ventilationDataBox.vue';
import VenFlodPanel from '../../common/venFlodPanel.vue';
import { ref, onMounted } from 'vue';
import VenTextTooltip from '../../common/venTextTooltip.vue';
import { ApiUseList, ApiShapeList, ApiSupportTypeList, ApiRoadwayTypeList } from '@/https/encapsulation/threeDimensional';

const props = defineProps(['tunInfo']);
const emit = defineEmits(['handleBack'])
function handleBack() {
    emit('handleBack', false)
}
// 模拟数据
const data = [
    {
        id: '1',
        name: '基本信息',
    },
    {
        id: '2',
        name: '实时曲线',
    },
]
// 基本信息中需要展示的内容
const dataNameList = [
    { name: '设备编号', prop: 'DevCode' },
    { name: '测点地址', prop: 'DevAddress' },
    { name: '设备类型', prop: 'DevTypeName' },
    { name: '实时值', prop: 'RealValue' },
    { name: '单位', prop: 'Unit' },
]
</script>

<style lang="scss" scoped>
:deep(.el-select) {

    --el-select-border-color-hover: #22c78b;

    .el-input__wrapper {
        --el-input-border-color: rgba(54, 186, 226, 0.5);
        background-color: #103956 !important;
        border: none;
        color: #fff;
    }


    .el-input__inner {
        color: #fff;
    }
}
</style>
<style lang="scss">
.entityInfo_popover {
    background-color: #103956 !important;
    border: 1rem solid #37BBE3 !important;
    padding: 5px;

    &.is-light .el-popper__arrow::before {
        background: #37BBE3;
        border: 1px solid #37BBE3;
    }

    /* &.is-light {
        color: #fff !important;
    } */

    .el-select-dropdown__item {
        color: #fff;
        background-color: #103956;
        border-radius: 2px;

        &:hover {
            background-color: rgba(54, 186, 226, 0.2);
        }

        &.selected {
            background-color: rgba(54, 186, 226, 0.5);
        }
    }
}
</style>