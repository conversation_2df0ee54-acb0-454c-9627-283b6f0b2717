<template>
    <div v-show="!entityInfoShow">
        <VenDataBox @get-current-index="getCurrentIndex" title="监测分析" :tab-list="tabList">
            <template #introduce>
                <div class="p-13px">

                    <p class="introduce_text">矿井安装监测传感器386个，精确测风设备3套，防火监测2套，降尘设备1套</p>
                </div>
            </template>
            <template #content>

                <!-- 安全监测历史数据弹窗 -->
                <el-dialog title="历史数据" v-model="historyDialogShow" width="800rem">
                    <div v-if="historyData" class="bg-[#123F5B] ">
                        <div class="w-90% h-446px" ref="hisEchart"></div>
                    </div>
                    <el-empty class="h-400rem" v-else description="暂无历史数据" />
                </el-dialog>

                <!-- 在线阻力 -->
                <div v-show="currentIndex == 0">
                    <VenTabBar :immediate="true" :style="true" :tab-list="onLineTabList"
                        @get-current-index="getOnlineIndex">
                    </VenTabBar>
                    <!-- 阻力列表 -->
                    <div class=" flex justify-center">
                        <el-table class="ven_custom-table" v-show="isEqualCurrentIndex(0)" :data="onlineData.tableData"
                            height="450rem" style="width:400rem;margin: 0 auto;">
                            <el-table-column v-for="(v, i1) in onlineData.tableField" :width="v.width && v.width"
                                :key="v.prop" :label="v.label" :show-overflow-tooltip="true">
                                <template #default="scope">
                                    <span   @click="handleLocation(scope.row)">{{ v.prop == 'TunName' ? (scope.row[v.prop] ?? '-') :
                                        (Number(scope.row[v.prop].toFixed(2)) ?? '-') }}</span>
                             
                                </template>
                              
                            </el-table-column>
                        </el-table>
                    </div>
                    <!-- 误差分析 -->
                    <VenFlodPanel class="ml-10px" v-show="isEqualCurrentIndex(1)" :flod-data="errorAnalysisData">
                        <template #content="{ slotData }">
                            <div class="w-full flex flex-wrap pl-15px h-300px text-[#fff] text-[15px]">
                                <div class="w-49% flex items-center" v-for="(v, i) in slotData.useDataLabel"
                                    :key="v.id">
                                    <div class="w-12px h-12px bg-[#FB954B] rounded-2px rotate-45 mr-3px"></div>
                                    <span class="text-[#ffc107] text-[16px] ">&nbsp;{{ v }} : </span>
                                    <VenTextTooltip :str="slotData[slotData.useDataName.at(i)]" />
                                </div>
                            </div>
                        </template>
                    </VenFlodPanel>
                </div>

                <!-- 在线风速 -->
                <!-- <div v-show="currentIndex == 1">
                    <el-table class="ven_custom-table" :data="onlineWindSpeedData.tableData" height="442rem"
                        style="width: 450rem;margin: 0 auto;">
                        <el-table-column :prop="v.prop && v.prop" v-for="( v, i ) in onlineWindSpeedData.tableField "
                            :type="v.type && v.type" :label="v.label" :width="v.width && v.width" />
                    </el-table> 
                </div>-->
                <div v-show="currentIndex == 1">
                    <el-table class="ven_custom-table" :data="onlineWindSpeedData.tableData" height="490rem"
                        style="width:400rem;margin: 0 auto;">
                        <el-table-column v-for="(v, i1) in onlineWindSpeedData.tableField" :width="v.width && v.width"
                            :key="v.prop" :label="v.label" :show-overflow-tooltip="true">
                            <template #default="scope">
                                <span>{{ v.prop == 'TunName' ? (scope.row[v.prop] ?? '-') :
                                    (Number(scope.row[v.prop].toFixed(2)) ?? '-') }}</span>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>

                <!-- 在线CO -->
                <div v-show="currentIndex == 2">
                    <el-table class="ven_custom-table" :data="onlineCOData.tableData" height="490rem"
                        style="width: 405rem;margin: 0 auto;">
                        <el-table-column :prop="v.prop && v.prop" v-for="(v, i) in onlineCOData.tableField"
                            :type="v.type && v.type" :label="v.label" :width="v.width && v.width"  :show-overflow-tooltip="true"/>
                             <!-- 分页组件 -->
   
                    </el-table>
                     <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="totalItems"
      style="margin-top: 10px; text-align: right;"
    />
                </div>
                <!-- 在线瓦斯 -->
                <div v-show="currentIndex == 3">
                    <el-table class="ven_custom-table" :data="onlineGasData.tableData" height="490rem"
                        style="width: 405rem;margin: 0 auto;">
                        <el-table-column :prop="v.prop && v.prop" v-for="(v, i) in onlineGasData.tableField"
                            :type="v.type && v.type" :label="v.label" :width="v.width && v.width" :show-overflow-tooltip="true"/>
                    </el-table>
                </div>
                <!-- 安全监测 -->
                <div v-show="currentIndex == 4">
                    <el-table :data="securityMonitoringData.tableData" height="490rem"
                        style="width: 405rem;margin: 0 auto;">
                        <el-table-column v-for="(v, i) in securityMonitoringData.tableField" :key="v.prop"
                            :width="v.width && v.width" :label="v.label" :show-overflow-tooltip="true">
                            <template #default="scope">
                                <span @click="checkDetail(scope.row)" class="cursor-pointer" v-if="i == 0">
                                    {{ scope.row[v.prop] ?? '-' }}
                                </span>
                                <span v-else-if="i == 1">{{ scope.row[v.prop] ?? '-' }}</span>
                                <div v-else>
                                    <el-button link size="default"
                                        @click="checkHistoryData(scope.row.DevCode, scope.row)">
                                        <span color="#ffc107">
                                            查看>
                                        </span>
                                    </el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="mx-auto flex justify-center">
                        <el-pagination @current-change="handleCurrentChange" :current-page="page.pageIndex"
                            :page-size="page.pageSize" layout="total,prev, pager, next" :total="page.total" />
                    </div>
                </div>
            </template>
        </VenDataBox>
    </div>
    <!-- 实体信息 -->
    <transition leave-active-class="animate__animated animate__fadeOutLeft "
        enter-active-class="animate__animated animate__fadeInLeftBig">
        <div v-if="entityInfoShow">
            <EntityInfo @handle-back="handleBack" :tun-info="tunInfo"></EntityInfo>
        </div>
    </transition>
</template>
<script setup>
import { ref, watch, reactive, inject,onMounted } from 'vue';
import VenDataBox from '../../common/ventilationDataBox.vue'
import VenFlodPanel from '../../common/venFlodPanel.vue';
import VenTabBar from '../../common/venTabBar.vue';
import VenTextTooltip from '../../common/venTextTooltip.vue';
import { UseCurrentIndex } from '@/hooks/UseCurrentIndex';
import { Search, Location } from "@element-plus/icons-vue";
import { ApiInlineResistanceList, ApiInlineResistanceAnalyze, ApiInlineWindspeed, ApiInlineCO, ApiInlineGas, ApiRoadwayIDInquire } from '@/https/encapsulation/threeDimensional';
import { ApiSecurityMonitoringPageList, ApiHistoricalCurve,ApiSecurityMonitoringList } from '@/https/encapsulation/Safety'
import EntityInfo from './entityInfo.vue';
import mitt from '@/utils/eventHub';
import * as echarts from 'echarts';
const flyToMesh = inject("flyToMesh");
import { useCommonStore } from '@/store/common';
import { ApiTunList } from '@/https/encapsulation/threeDimensional';

const addMeshOutlineShine = inject('addMeshOutline').addMeshOutlineShine;
const store = useCommonStore();

const currentIndex = ref(0)
const getCurrentIndex = (val) => {
    currentIndex.value = val
    if (val == 1) {
        getCollapseActive1(1)
    }
}
const tabList = ['在线阻力', '在线风量', 'CO', '瓦斯', '其他环境']

// 安全监测数据
const securityMonitoringData = reactive(
    {
        tableData: []
        ,
        tableField: [
            {
                prop: 'DevAddress',
                label: '地点名称',
                width: 150
            },
            {
                prop: 'RealState',
                label: '实时值'
            },
            {
                prop: 'historyLine',
                label: '历史曲线'
            },
        ]
    }
)
const page = reactive({
    pageIndex: 1,
    pageSize: 10,
    total: 0
})
const handleCurrentChange = (i) => {
    page.pageIndex = i
    ApiSecurityMonitoringPageList(page.pageSize, page.pageIndex, '').then(res => {
        const { Result, Pagination } = res.data;
        page.size = Pagination && Pagination.PageSize;
        page.total = Pagination && Pagination?.Total;
        securityMonitoringData.tableData = Result;
    })
}
handleCurrentChange(1)

const tunInfo = ref()
const entityInfoShow = ref(false)
// 查看安全监测详情信息
function checkDetail(row) {
    if (JSON.stringify(row) != '{}') {
        entityInfoShow.value = true;
        tunInfo.value = row;
        console.log(tunInfo.value, 'tunInfo.value');

    }
}
//定位巷道位置
function handleLocation(row) {
  flyToMesh(row.TunID);
  console.log(row,'$$$$$$11111')
}
// 格式化日期为指定字符串格式
function formatDate(date, format) {
  const pad = (num) => num.toString().padStart(2, '0');
  return (
    date.getFullYear() + '-' + pad(date.getMonth() + 1) + '-' + pad(date.getDate()) + ' ' + 
    pad(date.getHours()) + ':' + pad(date.getMinutes()) + ':' + pad(date.getSeconds())
  );
}

// 查看安全监测历史数据
const historyData = ref()
const historyDialogShow = ref(false)
const hisEchart = ref(null)
function checkHistoryData(code, scope) {
    const now = new Date();
    // 构造当前日期 00:00:00 的时间对象
    const startDate = new Date(now);
    startDate.setHours(0, 0, 0); 
    const start = formatDate(startDate, 'YYYY-MM-DD HH:mm:ss');
    
    // 构造当前日期 23:59:59 的时间对象
    const endDate = new Date(now);
   
    const end = formatDate(endDate, 'YYYY-MM-DD HH:mm:ss');
    const { DevAddress, Unit } = scope;
    ApiHistoricalCurve({ startTime: start, endTime: end, code }).then(res => {
        const { Result } = res.data;
        historyData.value = Result;
        historyDialogShow.value = true;

    }).then(() => {
        if (historyData.value) {
            renderEchart(DevAddress, Unit)

        }
    })
}
function renderEchart(title, unit) {
    var myChart = echarts.init(hisEchart.value);
    var option = {
        // backgroundColor: '#123F5B',
        title: {
            text: title,
            top: "1%",
            textAlign: "left",
            left: "1%",
            textStyle: {
                color: "#38adb9",
                fontSize: 16,
                fontWeight: "600",
            },
        },
        grid: {
            left: 80,
            right: 20,
        },
        tooltip: {
            show: true,
            trigger: "axis",
            backgroundColor: "#0a2b45", // 设置背景颜色
            textStyle: {
                color: '#fff',
                fontSize: 14,
            },
            borderColor: "rgba(255, 255, 255, .16)",
            axisPointer: {
                lineStyle: {
                    color: "rgba(28, 124, 196)",
                },
            },
        },

        xAxis: [
            {
                type: "category",
                boundaryGap: false,
                axisLabel: {
                    align: "center",
                    color: "#eee",
                    textStyle: {
                        fontSize: 13,
                    },
                },
                data: historyData.value.map(v => v.IOTime.slice(0, -3))
            },
        ],

        yAxis: [
            {
                type: "value",
                name: "单位" + ' : ' + unit,
                nameTextStyle: {
                    //y轴上方单位的颜色
                    color: "#eee",
                },

                splitLine: {
                    show: true,
                    lineStyle: {
                        color: "#eee",
                        // type: "dashed",
                    },
                },
                axisLabel: {
                    color: "#eee",
                    textStyle: {
                        fontSize: 14,
                    },
                },
                axisTick: {
                    show: false,
                },
            },
        ],
        series: [
            {
                data: historyData.value.map(v => v.RealState),
                type: 'line',
                smooth: true,
                areaStyle: {}
            }
        ]
    };
    myChart.setOption(option);
}


function handleBack(val) {
    entityInfoShow.value = val;
    mitt.emit('handleBack', store.$state.bottomTabCurrentIndex);
}
// 在线阻力tab列表
const onLineTabList = ['阻力列表', '误差分析']
const { currentIndex: onLineCurrentIndex, isEqualCurrentIndex } = UseCurrentIndex();

//阻力列表数据
const collapseActive = ref(0)
const onlineData = ref({})
function getCollapseActive1(val) {
    collapseActive.value = val - 1;
    let arr;
    // switch (val) {
    //     case 1:
    //         arr = onlineData.value.at(collapseActive.value)?.tableData.map(v => v.TunName);
    //         break;
    //     case 2:
    //         arr = onlineData.value.at(collapseActive.value)?.tableData.map(v => v.TunName);
    //         break;
    // }
    addMeshOutlineShine(arr)
}
// 获取阻力列表数据
// ApiInlineResistanceList().then(res => {
ApiTunList().then(({ Status, Result }) => {
    console.log(Result, 'ApiTunListApiTunListApiTunList');
    if (Status == 0) {
        onlineData.value = {
            tableData: Result,
            tableField: [
                {
                    prop: 'TunName',
                    label: '巷道名称',
                    width: 100,
                },
                {
                    prop: 'H',
                    label: '阻力(Pa)'
                },
                {
                    prop: 'V',
                    label: '风速(m/s)'
                },
                {
                    prop: 'QCalculate',
                    label: '风量(m²/s)',
                    width: 120,
                },
               
            ]
        }
        onlineWindSpeedData.value.tableData = Result ?? [];
        onlineWindSpeedData.value.tableField = [

            {
                prop: 'TunName',
                label: '巷道名称',
                width: 90,
            },
            {
                prop: 'S',
                label: '巷道面积(㎡)',
                width: 90
            },
            {
                prop: 'V',
                label: '巷道风速(m/s)',
                width: 90
            },
            {
                prop: 'QCalculate',
                label: '巷道风量(m³/s)',
                width: 90
            },
            {
                prop: 'QCalculate',
                label: '解算风量(m³/s)',
                width: 90
            },
            // {
            //     prop: 'Jdwc',
            //     label: '绝对误差',
            //     width: 90
            // },
            // {
            //     prop: 'Xdwc',
            //     label: '相对误差',
            //     width: 90
            // },
        ]

    }
})

// })
// 阻力列表误差分析数据
const errorAnalysisData = ref([])
ApiInlineResistanceAnalyze().then(res => {
    if (res.Status == 0) {
        const { Result } = res;
        errorAnalysisData.value = Result.map((v) => {
            const { SystemID, Name } = v;
            return {
                ...v,
                id: SystemID,
                name: Name,
                useDataLabel: ['风机H', '风机Q', 'HN', 'HV', 'H绝对误差', 'Q绝对误差', 'H相对误差', 'Q相对误差', '评价'],
                useDataName: ['Hw', 'Qw', 'Hn', 'Hv', 'Hmj', 'Qmj', 'Hmx', 'Hmx', 'Remark'],
            }
        })
    }
})



//在线风速数据
let onlineWindSpeedData = ref({ tableData: [], tableField: null })
// ApiInlineWindspeed().then((res) => {
//     if (res.Status == 0) {
//         const { Result } = res;
//         onlineWindSpeedData.value.tableData = Result ?? [];
//         onlineWindSpeedData.value.tableField = [
//             {
//                 prop: '',
//                 label: '序号',
//                 type: 'id',
//             },
//             {
//                 prop: 'TunName',
//                 label: '巷道名称',
//                 width: 90,
//             },
//             {
//                 prop: 'S',
//                 label: '巷道面积',
//                 width: 90
//             },
//             {
//                 prop: 'Value',
//                 label: '巷道风速',
//                 width: 90
//             },
//             {
//                 prop: 'QMonitor',
//                 label: '巷道风量',
//                 width: 90
//             },
//             {
//                 prop: 'QCalculate',
//                 label: '解算风量',
//                 width: 90
//             },
//             // {
//             //     prop: 'Jdwc',
//             //     label: '绝对误差',
//             //     width: 90
//             // },
//             // {
//             //     prop: 'Xdwc',
//             //     label: '相对误差',
//             //     width: 90
//             // },
//         ]
//     }
// })
//在线CO数据
const onlineCOData = ref({ tableData: [], tableField: null })
const lx = '一氧化碳'
 ApiSecurityMonitoringList(lx).then((res) => {

   if (res.data.Status == 0) {
        const { Result } = res.data;
         // 关键：遍历 Result，给每条数据加 id（序号）
    const resultWithIndex = Result.map((item, index) => ({
      ...item, 
      id: index + 1 // 序号从 1 开始
    }));
         onlineCOData.value.tableData = resultWithIndex;
      
         onlineCOData.value.tableField= [
            {
                prop: 'id',
                label: '序号',
                // type: 'id',
                 width: 60,
            },
            {
                prop: 'DevAddress',
                label: '地点',
            },
            {   
                prop: 'RealValue',
                label: '实时值(%)',
                
            }
            
        ]
    }

 })

//在线瓦斯数据
const onlineGasData = ref({ tableData: [], tableField: null })

const ws = '激光瓦斯'
 ApiSecurityMonitoringList(ws).then((res) => {
    if (res.data.Status == 0) {
      const { Result } = res.data;
       // 关键：遍历 Result，给每条数据加 id（序号）
    const resultWithIndex = Result.map((item, index) => ({
      ...item, 
      id: index + 1 // 序号从 1 开始
    }));
         onlineGasData.value.tableData = resultWithIndex;
      
      
        onlineGasData.value.tableField = [
            {
                prop: 'id',
                label: '序号',
                type: 'id',
                 width: 60,
            },
            {
                prop: 'DevAddress',
                label: '地点',
            },
            {
                prop: 'RealValue',
                label: '实时值(%)',
                width: 120,
            },
           
        ]

    }
 })
// ApiInlineGas().then((res) => {
//     // console.log(res, 'ApiInlineGasApiInlineGasApiInlineGas在线瓦斯');
//     if (res.Status == 0) {
       
//         onlineGasData.value.tableData = Result;
//         onlineGasData.value.tableField = [
//             {
//                 prop: '',
//                 label: '序号',
//                 type: 'id',
//             },
//             {
//                 prop: 'TunName',
//                 label: '地点',
//             },
//             {
//                 prop: 'Value',
//                 label: '瓦斯浓度(％)',
//                 width: 120,
//             },
//             {
//                 prop: 'S',
//                 label: '面积(㎡)',
//                 width: 100,
//             },
//             {
//                 prop: 'QDemand',
//                 label: '需风量(m³)',
//                 width: 100,
//             },
//             {
//                 prop: 'QCalculate',
//                 label: '解算风量',
//                 width: 100,
//             },
//             {
//                 prop: 'Pj',
//                 label: '评价'
//             },
//             {
//                 prop: 'Jdwc',
//                 label: '绝对误差',
//                 width: 100,
//             },
//             {
//                 prop: 'Xdwc',
//                 label: '相对误差',
//                 width: 100,
//             },
//         ]
//     }
// })

function getOnlineIndex(val) {
    onLineCurrentIndex.value = val

}
// getOnlineIndex(0)


// 判断字符长度
function determineCharacterLength(str, num) {
    let str1 = str;
    if (!(str instanceof String)) {
        str1 = String(str);
    }
    return str1.length > num
}


</script>

<style lang="scss" scoped>
@use '../../assets/index.scss';
</style>