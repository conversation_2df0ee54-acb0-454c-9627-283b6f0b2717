<template>
    <div>
        <!-- 
        实时解算
     -->
        <div v-show="isShow" class="legend-wrap animated bounceInRight">
            <div class="legend-title">风量</div>
            <div class="legend-item"><span class="legend-bg"
                    style="background: #99f9f6;"></span><span><strong>-44.30</strong>
                    以下</span>
            </div>
            <div class="legend-item"><span class="legend-bg"
                    style="background: rgb(12, 158, 163);"></span><span><strong>-44.30</strong> 至
                    <strong>27.28</strong></span></div>
            <div class="legend-item"><span class="legend-bg"
                    style="background: rgb(26, 137, 3);"></span><span><strong>27.28</strong> 至
                    <strong>98.87</strong></span></div>
            <div class="legend-item"><span class="legend-bg"
                    style="background: rgb(255, 255, 0);"></span><span><strong>98.87</strong> 至
                    <strong>170.45</strong></span></div>
            <div class="legend-item"><span class="legend-bg"
                    style="background: rgb(0, 255, 0);"></span><span><strong>170.45</strong> 至
                    <strong>242.03</strong></span></div>
            <div class="legend-item"><span class="legend-bg"
                    style="background: rgb(80, 101, 89);"></span><span><strong>242.03</strong> 至
                    <strong>313.62</strong></span></div>
            <div class="legend-item"><span class="legend-bg"
                    style="background: rgb(88, 90, 230);"></span><span><strong>313.62</strong> 至
                    <strong>385.20</strong></span></div>
            <div class="legend-item"><span class="legend-bg"
                    style="background: rgb(0, 0, 128);"></span><span><strong>385.20</strong> 以上</span>
            </div>
        </div>
    </div>
</template>
<script setup>
import { defineProps, ref } from 'vue'

const props = defineProps({
    isShow: {
        type: Boolean,
        default: false
    }
})
</script>
<style lang="scss" scoped>
.legend-wrap {
    transition: all s;
    position: absolute;
    top: 100px;
    left: 20px;
    z-index: 1000;
    padding: 10px 20px;
    border-radius: 4px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    background: rgba(0, 0, 0, .2);
}

.animated.bounceInRight {
    -webkit-animation-name: bounceInRight;
    animation-name: bounceInRight;
}

.animated {
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-duration: 2s;
    animation-duration: 2s;
    -webkit-animation-delay: 0s;
    animation-delay: 0s;
    -webkit-animation-iteration-count: 1;
    animation-iteration-count: 1;
}

.legend-title {
    color: #fff;
    font-size: 16px;
    font-weight: bolder;
    margin-bottom: 12px;
    text-align: center;
}

.legend-item {
    display: flex;
    align-items: center;
    font-weight: normal;
    color: #fff;

    .legend-bg {
        width: 16px;
        height: 16px;
        margin-right: 10px;
    }
}
</style>