<template>
    <div class="relative">
        <VentilationDataBox title="实体信息" :tunName="tunInfo.tunName" :introduce-show="false" :use-mitt="false"
            @handleBack="handleBack">
            <template #content>
                <VenFlodPanel class="ml-[10px] mt-15px" :flod-data="data">
                    <template #content="{ slotData, slotIndex }">
                        <div class="flex" :style="{ height: slotData.height }">
                            <div class="h-100% w-50px mr-10px" style="border-right: 1px solid #2CAAE7;">
                            </div>
                            <el-space direction="vertical" alignment="flex-start">
                                <div v-for="v in slotData.dataList">
                                    <div v-if="v.type == 'select'" class="mt-3px">
                                        <div class="flex text-white items-center text-15px">
                                            <div class="ml-[-10px] w-15px h-10px flex items-center">
                                                <div class="w-full h-1px bg-[#2CAAE7]"></div>
                                            </div>
                                            <div class="w-12px h-12px bg-[#11AEEE] rounded-2px rotate-45 mr-3px"></div>
                                            <div>&nbsp;{{ v.name }}</div>
                                        </div>
                                        <div>
                                            <el-select popper-class="entityInfo_popover" class="mt-3px"
                                                v-model="detailForm[v.prop]" placeholder="请选择">
                                                <el-option v-for="v in selectDataList[v.selectDataName]" :key="v.ID"
                                                    :label="v.TypeName" :value="v.TypeID"></el-option>
                                            </el-select>
                                        </div>
                                    </div>
                                    <div v-else-if="v.type == 'basic'" class="flex text-white items-center mt-3px">
                                        <div class="ml-[-10px] w-15px h-10px flex items-center">
                                            <div class="w-full h-1px bg-[#2CAAE7]"></div>
                                        </div>
                                        <div class="w-12px h-12px bg-[#11AEEE] rounded-2px rotate-45 mr-3px"></div>
                                        <div class="flex items-center text-15px">
                                            <span :style="{ width: v.width }" class="w-80px">&nbsp;{{ v.name
                                            }}&nbsp;:</span>
                                            <span>{{ detailForm[v.prop] }}</span>
                                        </div>
                                    </div>
                                    <div v-else class="flex text-white items-center mt-3px">
                                        <div class="ml-[-10px] w-15px h-10px flex items-center">
                                            <div class="w-full h-1px bg-[#2CAAE7]"></div>
                                        </div>
                                        <div class="w-12px h-12px bg-[#11AEEE] rounded-2px rotate-45 mr-3px"></div>
                                        <div class="flex justify-start items-center text-15px">
                                            <span class="w-100px" :style="{ width: v.width }">&nbsp;{{ v.name
                                            }}&nbsp;:&nbsp;</span>
                                            <div class="w-80% ml-[-10px]">
                                                <el-input v-model="detailForm[v.prop]" placeholder="请输入"
                                                    size="normal"></el-input>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-space>
                        </div>
                    </template>
                </VenFlodPanel>
                <div class="znmy_quit absolute left-50% translate-x-[-50%] bottom-45px">
                    <span>确认</span>
                </div>
            </template>
        </VentilationDataBox>
    </div>
</template>
<script setup>
import VentilationDataBox from '../../common/ventilationDataBox.vue';
import VenFlodPanel from '../../common/venFlodPanel.vue';
import { ref, watch, reactive } from 'vue';
import { ApiUseList, ApiShapeList, ApiSupportTypeList, ApiRoadwayTypeList } from '@/https/encapsulation/threeDimensional';

const props = defineProps(['tunInfo']);
const emit = defineEmits(['handleBack'])
function handleBack() {
    emit('handleBack', false)
}
// 数据
const data = ref([
    {
        id: '1',
        name: '断面参数',
        height: '390rem',
        dataList: [
            {
                name: '支护类型',
                prop: 'ShoringType',
                type: 'select',
                selectDataName: 'supportTypeList'
            },
            {
                name: '巷道类型',
                prop: 'Ventflow',
                type: 'select',
                selectDataName: 'tunTypeList'
            },
            {
                name: '断面形状',
                prop: 'ShapeType',
                type: 'select',
                selectDataName: 'shapeTypeList'
            },
            {
                name: '宽度(m)',
                prop: 'Width',
                type: 'basic'
            },
            {
                name: '高度(m)',
                prop: 'Height',
                type: 'input'
            },
            {
                name: '长度(m)',
                prop: 'Length',
                type: 'basic'
            },
            {
                name: '面积(㎡)',
                prop: 'S',
                type: 'input'
            },
        ]

    },
    {
        id: '2',
        name: '风量与风速',
        height: '160rem',
        dataList: [
            {
                name: '风速(m³/s)',
                prop: 'V',
                type: 'basic',
                width: '120rem'
            },
            {
                name: '解算风量(m³/s)',
                prop: 'QCalculate',
                type: 'basic',
                width: '120rem',
            },
            {
                name: '监测风量(m³/s)',
                prop: 'QMonitor',
                type: 'basic',
                width: '120rem',
            },
            {
                name: '需风量(m³/s)',
                prop: 'QDemand',
                type: 'input',
                width: '145rem'
            },
        ]
    },
    {
        id: '3',
        name: '阻力相关',
        height: '400rem',
        dataList: [
            {
                name: '摩擦阻力系数',
                prop: 'A',
                type: 'input',
                width: '140rem'
            },
            {
                name: '风阻(kg/m7)',
                prop: 'R',
                type: 'input',
                width: '140rem',
            },
            {
                name: '阻力(Pa)',
                prop: 'H',
                type: 'basic',
                width: '120rem',
            },
            {
                name: '百米阻力(Pa)',
                prop: 'H100m',
                type: 'basic',
                width: '120rem'
            },
            {
                name: '沿途阻力(Pa)',
                prop: 'HWay',
                type: 'basic',
                width: '120rem'
            },
            {
                name: '局部阻力(Pa)',
                prop: 'HParte',
                type: 'basic',
                width: '120rem'
            },
            {
                name: '相对压差(Pa)',
                prop: 'HRelate',
                type: 'basic',
                width: '120rem'
            },
            {
                name: '动压差(Pa)',
                prop: 'HDynamic',
                type: 'basic',
                width: '120rem'
            },
            {
                name: '静压差(Pa)',
                prop: 'HStatic',
                type: 'basic',
                width: '120rem'
            },
            {
                name: '位能差(Pa)',
                prop: 'HEnergy',
                type: 'basic',
                width: '120rem'
            },
        ]
    },
    {
        id: '4',
        name: '标识',
        height: '120rem',
        dataList: [
            {
                name: '巷道ID',
                prop: 'TunID',
                type: 'basic',
            },
            {
                name: '用途类型',
                prop: 'Lx',
                type: 'select',
                selectDataName: 'usageTypeList'
            },
        ]
    },
])
// 详情表单
let detailForm = reactive({})
watch(() => props.tunInfo, (v) => {
    detailForm = v;
}, { immediate: true })
const selectDataList = reactive({
    supportTypeList: [],
    tunTypeList: [
        {
            TypeName: '进风',
            TypeID: 1,
        },
        {
            TypeName: '用风',
            TypeID: 0,
        },
        {
            TypeName: '回风',
            TypeID: -1,
        }
    ],
    shapeTypeList: [],
    usageTypeList: []
})
// 支护类型
const supportType = ref()
watch(supportType, v => {
    console.log(v, 'v');
})
//支护类型列表
ApiSupportTypeList().then(res => {
    if (res.Status == 0) {
        const { Result } = res;
        selectDataList.supportTypeList = Result;
        data.value
    }
})

// 断面形状
ApiShapeList().then(res => {
    if (res.Status == 0) {
        const { Result } = res;
        selectDataList.shapeTypeList = Result;
    }
})
// 用途类型
ApiUseList().then(res => {
    if (res.Status == 0) {
        const { Result } = res;
        selectDataList.usageTypeList = Result;
    }
})
</script>

<style lang="scss" scoped>
:deep(.el-select) {
    --el-select-border-color-hover: #22c78b;

    .el-input__wrapper {
        --el-input-border-color: rgba(54, 186, 226, 0.5);
        background-color: #103956 !important;
        border: none;
        color: #fff;
    }

    .el-input__inner {
        color: #fff;
    }
}

:deep(.el-input) {
    --el-input-text-color: #fff;
    --el-input-border-color: rgba(54, 186, 226, 0.5);
    --el-input-bg-color: #103956;
    --el-input-hover-border-color: #22c78b;
}
</style>
<style lang="scss" >
.entityInfo_popover {
    background-color: #103956 !important;
    border: 1rem solid #37BBE3 !important;
    padding: 5px;

    &.is-light .el-popper__arrow::before {
        background: #37BBE3;
        border: 1px solid #37BBE3;
    }

    /* &.is-light {
        color: #fff !important;
    } */

    .el-select-dropdown__item {
        color: #fff;
        background-color: #103956;
        border-radius: 2px;

        &:hover {
            background-color: rgba(54, 186, 226, 0.2);
        }

        &.selected {
            background-color: rgba(54, 186, 226, 0.5);
        }
    }
}
</style>