<template>
  <div v-show="!entityInfoShow">
    <VenDataBox
      @get-current-index="getCurrentIndex"
      title="实时解算"
      :tab-list="tabList"
    >
      <template #introduce>
        <div class="p-13px">
          <p class="introduce_text">
            利用完善得通风理论，实用的数字模型和先进的计算机算法，实现风网解算结果的可视化展示，加强通风管理，提供通风系统的抗灾能力。
          </p>
        </div>
      </template>
      <template #content>
        <!-- 通风解算 -->
        <div v-show="currentIndex == 0">
          <el-table
            :data="venTable"
            height="480rem"
            style="width: 405rem; margin: 0 auto"
          >
            <el-table-column
              v-for="(v, i) in ventilationCalculation.tableField"
              :key="v.prop"
              :width="v.width && v.width"
            >
              <template #header align="center">
                <div v-if="i != 2">{{ v.label }}</div>
                <el-icon v-else size="20rem" color="#ffc107">
                  <Search />
                </el-icon>
              </template>
              <template #default="scope">
                <span
                  @click="checkDetail(scope.row)"
                  class="cursor-pointer"
                  v-if="i == 0"
                  >{{ scope.row[v.prop] ?? "-" }}</span
                >
                <span v-else-if="i == 1">{{
                  Number(scope.row[v.prop]).toFixed(2) ?? "-"
                }}</span>
                <div
                  v-else
                  class="cursor-pointer mx-auto rounded-20px w-60rem h-30rem flex items-center justify-center"
                  @click="handleLocation(scope.row)"
                  border="1px solid #ffc107 hover:red"
                  text="#ffc107 hover:red"
                >
                  <Location style="width: 20rem; height: 20rem" />
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="mx-auto flex justify-center">
            <el-pagination
              @current-change="handleCurrentChange"
              :current-page="page.pageIndex"
              :page-size="page.pageSize"
              layout="total,prev, pager, next"
              :total="page.total"
            />
          </div>
        </div>
        <!-- 困难路线 -->
        <div v-show="currentIndex == 1" ml-20px mt-10px>
          <VenFlodPanel class="ml-[-9px]" :flod-data="difficultRouteData"  
          >
          
            <template #content="{ slotData }">
               
              <el-table
                class="ven_custom-table"
                :data="slotData.tableData"
                @click="handleSystemClick(slotData)"
                height="360rem"
                style="width: 400rem; margin: 0 auto"
              >
                <el-table-column
                  v-for="(v, i) in slotData.tableField"
                  :width="v.width && v.width"
                  :key="v.prop"
                  :prop="v.prop"
                  :label="v.label"
                />
              </el-table>
            </template>
          </VenFlodPanel>
        </div>
        <!-- 三区分布 -->
        <div class="sqfb" v-show="currentIndex == 2" text-white ml-20px>
          <div
            class="sqfb_zs relative"
            v-for="(v, i) in sqfbData"
            :key="i"
            :class="i >= 1 ? 'mt-30px' : 'mt-10px'"
          >
            <div class="absolute left-20px top-21px">
              <img class="w-31px h-31px" :src="v.icon" />
            </div>
            <div class="flex justify-between">
              <div class="sqfb_title">{{ v.name }}</div>
              <div class="sqfb_title_data">{{ v.value }}pa</div>
            </div>
            <div class="flex justify-between">
              <div>
                <div class="sqfb_text">进风：</div>
                <div class="sqfb_data">{{ v.jf }}</div>
              </div>
              <div>
                <div class="sqfb_text">用风：</div>
                <div class="sqfb_data">{{ v.yf }}</div>
              </div>
              <div>
                <div class="sqfb_text">回风：</div>
                <div class="sqfb_data">{{ v.hf }}</div>
              </div>
            </div>
          </div>
          <!-- 图表 -->
          <div class="flex items-center">
            <div id="sqfb_bt" class="w-250px h-250px"></div>
            <div>
              <div class="" v-for="(v, i) in sqfbTbData" :key="v.value">
                <div class="flex items-center">
                  <div
                    :style="{ background: v.color }"
                    class="w-10px h-10px rounded-[50%]"
                  ></div>
                  <span class="sqfb_title">&nbsp;{{ v.label }}</span>
                  <span :class="v.class">&nbsp;{{ v.value }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 阻力评价 -->
        <!-- <div v-show="currentIndex == 3" text-white ml-20px>阻力评价</div> -->
        <!-- 误差分析 -->
        <!-- <div v-show="currentIndex == 4" text-white ml-20px>误差分析</div> -->
      </template>
    </VenDataBox>
  </div>
  <!-- 实体信息 -->
  <transition
    leave-active-class="animate__animated animate__fadeOutLeft "
    enter-active-class="animate__animated animate__fadeInLeftBig"
  >
    <div v-if="entityInfoShow">
      <EntityInfo @handle-back="handleBack" :tun-info="tunInfo"></EntityInfo>
    </div>
  </transition>
</template>
<script setup>
import { ref, watch, onMounted, reactive, inject,provide } from "vue";
import VenDataBox from "../../common/ventilationDataBox.vue";
import VenFlodPanel from "../../common/venFlodPanel.vue";
import { UseCurrentIndex } from "@/hooks/UseCurrentIndex";
import { Search, Location } from "@element-plus/icons-vue";
//
import { defineEmits } from 'vue';

// 声明可触发的事件
const emit = defineEmits(['onHighlight']); 


import {
  ApiHardroute,
  ApiThreezonesDistribution,
} from "@/https/encapsulation/threeDimensional";
import { ApiVentilationCalculation } from "@/https/api";
import { useThree } from "@/three/useThree";
import { cameraFlyToMesh, moveToCamera } from "@/three/cameraControls";
const endCamera = [-6792, 8180, -3620]
//导入变量
import {  meshGroup,  } from '../../js/tunInit';// 变量
import EntityInfo from "./entityInfo.vue";
import * as echarts from "echarts";
import mitt from "@/utils/eventHub";
import * as TWEEN from '@tweenjs/tween.js';
const { scene,  renderer,  addOutlinePass1,
    cameraControls, camera,  addOutlinePass, ani, stopRender, outlinePass } 
    = useThree({ endCamera, isCameraAni: false, isAxes: false, isDepthBuffer: true, isComposer: true })
import { useThereVentilation } from "@/store/thereVentilation";
const resetEffect = inject("resetEffect");
const flyToMesh = inject("flyToMesh");
const store = useThereVentilation();
const { currentIndex } = UseCurrentIndex();
const getCurrentIndex = (val) => {
  currentIndex.value = val;
};
const tabList = ["通风解算", "困难路线", "三区分布"];
provide('addMeshOutline', {  addOutlinePass });
// 通风结算数据
const venTable = ref([]);
const ventilationCalculation = ref({
  id: "1",
  name: "顾桥南区",
  tableData: null,

  tableField: [
    {
      prop: "TunName",
      label: "巷道名称",
      width: 150,
    },
    {
      prop: "QCalculate",
      label: "风量(m²/s)",
    },
    {
      prop: "",
      label: "搜索",
    },
  ],
});
// 通风解算模拟数据
ApiVentilationCalculation()
  .then((res) => {
    const { Result } = res.data ?? { Result: [] };
    ventilationCalculation.value.tableData = Result;
    page.total = Result.length;
  })
  .then((res2) => {
    handleCurrentChange(1);
  });

// 通风结算分页
const page = reactive({
  pageIndex: 1,
  pageSize: 10,
  total: 0,
});
const tunInfo = ref();
const entityInfoShow = ref(false);
// 查看安全监测详情信息
function checkDetail(row) {
  if (JSON.stringify(row) != "{}") {
    tunInfo.value = row;
    entityInfoShow.value = true;
    flyToMesh(tunInfo.value.TunID);
  }
}
function handleLocation(row) {
  flyToMesh(row.TunID);
 
}
// handleLocation
function handleBack(val) {
  entityInfoShow.value = val;
  mitt.emit("handleBack", store.$state.bottomTabCurrentIndex);
  // 取消管道高亮
  resetEffect();
}

const handleCurrentChange = (i) => {
  page.pageIndex = i;
  venTable.value = ventilationCalculation.value.tableData.slice(
    (i - 1) * page.pageSize,
    i * page.pageSize
  );
};

// 困难路线数据
const difficultRouteData = ref([]);
ApiHardroute().then((res) => {
  if (res.Status == 0) {
    const { Result } = res;
    difficultRouteData.value = Result.map((v) => {
      const { SystemID, SystemName, TunList } = v;
      const tableData = TunList.filter((v) => v.TunName);
      return {
        ...v,
        id: SystemID,
        name: SystemName,
        tableData,
        tableField: [
          {
            prop: "TunName",
            label: "巷道名称",
            width: 150,
          },
          {
            prop: "H",
            label: "阻力(Pa)",
            width: 100,
          },
          {
            prop: "V",
            label: "风速(m/s)",
            width: 110,
          },
          {
            prop: "Q",
            label: "风量(m²/s)",
            width: 110,
          },
          {
            prop: "Length",
            label: "长度(M)",
            width: 100,
          },
          {
            prop: "S",
            label: "面积(㎡)",
            width: 100,
          },
        ],
      };
    });
  }
});

// // 点击大类：通知三维场景高亮巷道
const handleSystemClick = (slotData) => {
  // 提取该大类下的所有巷道id
  const tunIds = slotData.tableData.map(tun => tun.TunID); 
   emit('onHighlight', tunIds); 
// // 2. 筛选 meshGroup 中匹配的 3D 模型
//   const filteredMeshes = [];
//   meshGroup.children.forEach(child => {
//     // 关键：根据模型存储的 TunID 匹配（需与加载时一致）
//     const meshTunID = child.mid; // 假设模型 userData 存储了 TunID
//     // console.log(meshTunID,'ces111111')
//     if (meshTunID && tunIds.includes(meshTunID)) {
//       filteredMeshes.push(child);
//     }
//   });

//   console.log('筛选出的模型数量:', filteredMeshes);
  
  // addOutlinePass1(filteredMeshes)
  // console.log(addOutlinePass1(filteredMeshes),'44444444')
//  flyToMesh(filteredMeshes);
};


// 三区分布赋值

// 百分比（响应式）
const InWindPercent = ref(0);
const OutWindPercent = ref(0);
const UseWindPercent = ref(0);
// 三区分布数据
const threeZoneDistributionData = ref();
const sqfbData = ref([]);
const sqfbTbData = ref([]);
function checkPercentValue(value, label) {
    if (isNaN(value)) {
        console.log(`${label} 是 NaN，已处理为默认值 0`);
        return 0;
    } else if (value === 0) {
        console.log(`${label} 的值为 0，请注意！`);
    } else {
        console.log(`${label} 的值正常：`, value);
    }
    return value;
}
// 处理数据 百分比
function formatToPercent(value, key) {
    if (typeof value === 'number') {
        return Math.round(value * 100) ; 
    }
    console.warn(`字段 ${key} 传入值不是有效数字，原始值：`, value);
    return value;
}
ApiThreezonesDistribution().then((res) => {
  if (res.Status == 0) {
    const { Result } = res;
    // console.log(Result,'fenbu');
    const systemNameValue1 = res.Result.Data[0].SystemName;
    const systemNameValue2 =  res.Result.Data[1].SystemName;
    const systemNameValue3 = res.Result.Data[2].SystemName;
    const TunDrag1 =  res.Result.Data[0].TunDrag;
    const TunDrag2 =  res.Result.Data[1].TunDrag;
    const TunDrag3 =  res.Result.Data[2].TunDrag;
    const InWind1 =  res.Result.Data[0].InWind;
    const InWind2 =  res.Result.Data[1].InWind;
    const InWind3 =  res.Result.Data[2].InWind;
    const UseWind1 =  res.Result.Data[0].UseWind;
    const UseWind2 = res.Result.Data[1].UseWind;
    const UseWind3 = res.Result.Data[2].UseWind;
     const OutWind1 = res.Result.Data[0].OutWind;
    const OutWind2 = res.Result.Data[1].OutWind;
    const OutWind3 = res.Result.Data[2].OutWind;
    // 百分比
    
 InWindPercent.value = formatToPercent(res.Result.InWindPercent, 'InWindPercent');
 OutWindPercent.value = formatToPercent(res.Result.OutWindPercent, 'OutWindPercent');
 UseWindPercent.value = formatToPercent(res.Result.UseWindPercent, 'UseWindPercent');

    sqfbData.value = [
      {
        name: systemNameValue1,
        value: TunDrag1,
        jf: InWind1,
        yf: UseWind1,
        hf: OutWind1,
        icon: "zzl.png",
      },
      {
        name: systemNameValue2,
        value: TunDrag2,
        jf: InWind2,
        yf: UseWind2,
        hf: OutWind2,
        icon: "cd.png",
      },
      {
        name: systemNameValue3,
        value: TunDrag3,
        jf: InWind3,
        yf: UseWind3,
        hf: OutWind3,
        icon: "zl_100.png",
      },
    ].map((item) => {
      return {
        ...item,
        icon: new URL(`../../assets/img/${item.icon}`, import.meta.url),
      };
    });

    // 百分比

  sqfbTbData.value = [
  {
    color: "#71D86A",
    value: InWindPercent.value,
    label: "进风",
    class: "green_text",
  },
  {
    color: "#33BEFC",
    value: UseWindPercent.value,
    label: "用风",
    class: "bule_text",
  },
  {
    color: "#EEB040",
    value: OutWindPercent.value,
    label: "回风",
    class: "orange_text",
  },
];
  }
});

//

onMounted(() => {
  const el = document.getElementById("sqfb_bt");
  
  if (el) {
    ApiThreezonesDistribution().then((res) => {
     // 调用接口获取 res 数据后，处理各个百分比字段
    InWindPercent.value = formatToPercent(res.Result.InWindPercent, 'InWindPercent');
    OutWindPercent.value = formatToPercent(res.Result.OutWindPercent, 'OutWindPercent');
    UseWindPercent.value = formatToPercent(res.Result.UseWindPercent, 'UseWindPercent');
   

      setEchart(el, InWindPercent.value, UseWindPercent.value, OutWindPercent.value);
    })
   
  }
});
// 三区分布饼图
function setEchart(el, InWindPercent, UseWindPercent, OutWindPercent) {

  var myChart = echarts.init(el);
  const option = {
    // legend: {
    //     top: '5%',
    //     left: 'center'
    // },
    // tooltip: {
    //     trigger: 'item'
    // },
    series: [
      {
        name: "三区分布",
        type: "pie",
        radius: ["40%", "65%"],
        avoidLabelOverlap: false,
        color: ["#71D86A", "#33BEFC", "#EEB040"],
        label: {
          show: false,
          position: "center",
        },
        emphasis: {
          label: {
            show: true,
            color: "#69CFD9",
            fontSize: 16,
            fontWeight: "bold",
          },
        },
        data: [
          { value: InWindPercent, name: "进风" },
          { value: UseWindPercent, name: "用风" },
          { value:OutWindPercent, name: "回风" },
        ],
      },
    ],
  };

  myChart.setOption(option);
}

</script>

<style lang="scss">
@use "../../assets/index.scss";

.el-pager li {
  background-color: transparent;
  color: white;

  &:hover {
    color: #76f7ff;
  }

  &.is-active {
    color: #11aeee;
  }
}

.el-pagination > .is-last {
  background-color: transparent;
}

.el-pagination__total {
  color: #fff;
}

.btn-prev {
  color: #fff;

  .el-icon {
    color: #fff;
  }
}

.btn-next {
  @extend .btn-prev;
}

.el-pagination button[class*="prev"] {
  background: none;
}

.el-pagination button[class*="next"] {
  background: none;
}
</style>
<style lang="scss" scoped>
.sqfb {
  .sqfb_zs {
    padding-left: 100px;
    padding-right: 20px;
    width: 383px;
    height: 78px;
    background-image: url("../../assets/img/sqfb_zs.png");
    background-size: 100% 100%;
  }

  .sqfb_title {
    width: auto;
    height: 38px;
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #eff7f9;
    line-height: 38px;
  }

  .sqfb_title_data {
    width: 94px;
    height: 38px;
    font-size: 20px;
    font-family: DIN;
    font-weight: bold;
    color: #ff975f;
    line-height: 38px;
  }

  .sqfb_text {
    width: 53px;
    height: 28px;
    font-size: 15px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #76f7ff;
    line-height: 28px;
  }

  .sqfb_data {
    width: 53px;
    height: 28px;
    font-size: 16px;
    font-family: DIN;
    font-weight: 500;
    color: #eff7f9;
    line-height: 28px;
  }
}

.green_text {
  width: 63px;
  height: 20px;
  font-size: 18px;
  font-family: DOUYU;
  font-weight: 600;
  color: #eff7f9;
  line-height: 20px;

  background: linear-gradient(-15deg, #7ff074 0%, #4bd93c 99.365234375%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.bule_text {
  @extend .green_text;
  background: linear-gradient(-15deg, #66d3fc 0%, #12bfff 99.365234375%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.orange_text {
  @extend .green_text;
  background: linear-gradient(-15deg, #facf7b 0%, #f1af2e 99.365234375%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>