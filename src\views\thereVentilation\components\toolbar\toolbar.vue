<template>
    <div class="fixed bottom-200px right-60px toolBar_box">
        <div :ref="`dom${i + 1}`" v-for="(v, i) in toolList" :key="v" 
            class="w-full h-full  flex items-center justify-center p-20px cursor-pointer w-70px h-70px  flex items-center justify-center cursor-pointer hover:border-solid hover:border-3px hover:border-sky-500/80"
            @click="handleClick(i)">
            <div v-if="i != toolList.length - 1">
                <el-tooltip :content="v.content" placement="left" effect="customized">
                    <img :src="v.icon" class="w-32px h-32px cursor-pointer">
                </el-tooltip>
            </div>
            <div v-else>
                <div v-show="labelShow" @click="labelShow = false">
                    <el-icon size="32rem" class="fill-color">
                        <View />
                    </el-icon>
                </div>
                <div v-show="!labelShow" @click="labelShow = true">
                    <el-icon size="32rem" class="fill-color">
                        <Hide />
                    </el-icon>
                </div>
            </div>
        </div>
        <!-- <div :ref="`dom${i + 1}`" v-for="(v, i) in toolList" :key="v" class="mb-9px cursor-pointer">
            <div>
                <el-tooltip :content="v.content" placement="left" effect="customized">
                    <img :src="v.icon" class="w-61px h61px cursor-pointer" @click="handleClick(i)">
                </el-tooltip>
            </div> -->
        <!-- <div v-show="i == 1">
                <el-popover popper-class="toolbar_popover" placement="left" title="图层工具箱" :width="200" trigger="click">
                    <template #default>
                        <div class="flex justify-between flex-wrap">
                            <div class="data_box_tab cursor-pointer" @click="cameraAngle('up')"><span>俯视</span></div>
                            <div class="data_box_tab cursor-pointer" @click="cameraAngle('front')"><span>正视</span></div>
                            <div class="data_box_tab cursor-pointer" @click="cameraAngle('side')"><span>侧视</span></div>
                            <div class="data_box_tab cursor-pointer w-150px" @click="isShowTunLabel(0)">
                                <span>{{ isShowLabel1 ? '隐藏巷道标签' : '显示巷道标签' }}</span>
                            </div>
                            <div class="data_box_tab cursor-pointer w-150px" @click="isShowTunLabel(1)">
                                <span>{{ isShowLabel2 ? '隐藏设施标签' : '显示设施标签' }}</span>
                            </div>
                        </div>
                    </template>
<template #reference>
                        <div v-show="i == 1">
                            <el-tooltip :content="v.content" placement="left" effect="customized">
                                <img :src="v.icon" class="w-61px h61px cursor-pointer">
                            </el-tooltip>
                        </div>
                    </template>
</el-popover>
</div> -->
        <!-- </div> -->
    </div>
</template>
<script setup>
import { ref, onMounted, inject, toRefs, watch } from 'vue';
import { useRouter } from 'vue-router';
import { showOrHideLabels } from '../../js/tunInit';// 变量
import { View, Hide } from '@element-plus/icons-vue';
const router = useRouter();
const resetEffect = inject('resetEffect');
const props = defineProps(['cameraAngle', 'changeSpriteLabel', 'resetEffect', 'changeTun', 'changeShape', 'needFunction', 'needData','is3D'])
const { cameraAngle, changeSpriteLabel } = toRefs(props)

// 图标显示隐藏
let isShowLabel1 = ref(true);
let isShowLabel2 = ref(true);
const labelShow = ref(true)
watch(labelShow, (val) => {
    changeSpriteLabel.value(val)
})
function isShowTunLabel(type) {
    if (type == 0) {
        isShowLabel1.value = !isShowLabel1.value;
        showOrHideLabels(isShowLabel1.value, type)
    } else {
        isShowLabel2.value = !isShowLabel2.value;
        showOrHideLabels(isShowLabel2.value, type)
    }

}
const svgCurrentIndex1 = ref(-1);
const toolList = ref(
    [
        {
            icon: 'fullScreen.svg',
            content: '全屏展示'
        },
        {
            icon: 'lookDown.svg',
            content: '俯视'
        },
        {
            icon: 'look.svg',
            content: '正视'
        },
        {
            icon: 'sideLook.svg',
            content: '侧视'
        },
        {
            icon: 'reset.svg',
            content: '重置相机'
        },
       
     
         {
           icon: 'toggle.svg',
           content: '切换2d'
        },
           {
            icon: '',
            content: '重置相机'
        },
        
    ]
)

// const url = '../../../tunEdit/assets/svg'
toolList.value = toolList.value.map(item => ({
   
    ...item,
    icon: new URL(`../../../tunEdit/assets/svg/${item.icon}`, import.meta.url).href,
    
}));

// watch(
//     () => props.is3D,
//     (is3D) => {
//         // 只更新内容文字，保持图标不变
//         toolList.value[5].content = is3D ? '切换至二维' : '切换至三维';
//     },
//     { immediate: true }  // 初始化时立即执行
// );

let isFullScreen = ref(false)

const handleClick = (i) => {
    svgCurrentIndex1.value = i;
    console.log(i,'点击')
    switch (i) {
        case 0:
            isFullScreen.value ? exitFullscreen() : enterFullscreen();
            break;
        case 1: cameraAngle.value('up'); break;
        case 2: cameraAngle.value('front'); break;
        case 3: cameraAngle.value('side'); break;
        case 4: resetEffect(); break;
        case 5:  emit('toggle-3d-mode'); break; // 触发二三维切换事件
        case 6:   break;
       
     
    }
    // 进入全屏模式
    function enterFullscreen() {
        var element = document.documentElement;
        if (element.requestFullscreen) {
            element.requestFullscreen();
        } else if (element.mozRequestFullScreen) { // Firefox
            element.mozRequestFullScreen();
        } else if (element.webkitRequestFullscreen) { // Chrome, Safari and Opera
            element.webkitRequestFullscreen();
        } else if (element.msRequestFullscreen) { // IE/Edge
            element.msRequestFullscreen();
        }
        isFullScreen.value = true
    }

    // 退出全屏模式
    function exitFullscreen() {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.mozCancelFullScreen) { // Firefox
            document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) { // Chrome, Safari and Opera
            document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) { // IE/Edge
            document.msExitFullscreen();
        }
        isFullScreen.value = false
    }

}

// 像父组件传
const emit = defineEmits(['toggle-3d-mode','getZnzDom'])

onMounted(() => {
     // 处理 SVG 路径
//   toolList.value = toolList.value.map(item => {
//     if (!item.icon) return item;
//     const iconUrl = new URL(`../../tunEdit/assets/svg/${item.icon}`, import.meta.url);
//     return { ...item, icon: iconUrl.href };
//   });

})


</script>
<style lang="scss" scoped>
.data_box_tab_active {
    text-align: center;
    min-width: 100px;
    height: 40px;
    background-image: url('../../assets/img/btn_bg.png');
    background-size: 100% 40px;
    margin-right: 2px;
    margin-top: 5px;

    span {
        width: 63px;
        height: 40px;
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #0BDFF2;
        line-height: 40px;

    }
}


.data_box_tab {
    @extend .data_box_tab_active;

    span {
        color: #D0F2F7;
    }
}

.norem-btn1 {
    width: auto;

    .norem-btn1-1 {
        background-color: rgba(255, 255, 255, 0.8);
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #fff;
        text-align: center;
        line-height: 20px;


        &:hover {
            background-color: #0BDFF2;
        }
    }
}
</style>
<style lang="scss">
@use '../../assets/index.scss';

.toolbar_popover {
    background: none !important;
    background-image: url('@/assets/png/nav_select.png') !important;
    background-size: 100% 300px !important;
    border-radius: 0;
    border: none !important;
    color: white;
    padding: 5px;

    .el-popover__title {
        color: #76F7FF;
    }

    &.is-light .el-popper__arrow::before {
        background: #fff;
        border: 1px solid #fff;
    }

    &.is-light {
        padding: 20px;
        height: 300px;
        width: 250px !important;
    }
}
</style>