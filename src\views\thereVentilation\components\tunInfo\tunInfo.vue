<template>
    <div class="tun_info flex items-center" ref="dhk">
        <img class="w-136px h-35px" src="../../assets/img/tun_info_line.png" alt="">
        <div class="tun_info_box">
            <div class="tun_info_title">
                {{ title ?? '-' }}
            </div>
            <div class="w-346px flex flex-wrap justify-around items-center">
                <div class="flex w-50% justify-center" v-for="(v) in dataLabel" :key="v.id">
                    <div>
                        <img class="w-38px h-38px" :src="v.icon">
                    </div>
                    <div class="ml-5px">
                        <div class="tun_info_text mt-4px">{{ v.name }}</div>
                        <div class="tun_info_data mt-4px">{{ v.num ?? '0' }}{{ v.unit }}</div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</template>
<script setup>
import { ref, watch } from 'vue';
import fl from '../../assets/img/tun_info_fl.png'
import fs from '../../assets/img/tun_info_fs.png'
import zl from '../../assets/img/tun_info_zl.png'
import mj from '../../assets/img/tun_info_mj.png'
import { getType } from '@/utils/utils';
const props = defineProps(['currentTun'])
// 监听鼠标位置 创建悬浮框
const dhk = ref(null)
const dhkContent = ref(null)
const title = ref()
watch(() => props.currentTun, (val) => {
    if (val) {
       
        if (val.isShow) {
            //  console.log(val,'悬浮333333333333333333333')
            dhk.value.style.visibility = 'visible'
            const x = val.x - dhk.value.clientWidth / 2
            const y = val.y - dhk.value.clientHeight
            dhk.value.style.left = x + 'px'
            dhk.value.style.top = y + 'px'
            dhkContent.value = `x:${x},y:${y}`
            const { H, QCalculate, S, V, tunName } = val;

            dataLabel.value.at(0).num = getType(QCalculate) == 'number' ? QCalculate.toFixed(2) : '0';
            dataLabel.value.at(1).num = getType(V) == 'number' ? V.toFixed(2) : '0';
            dataLabel.value.at(2).num = getType(H) == 'number' ? H.toFixed(2) : '0';
            dataLabel.value.at(3).num = getType(S) == 'number' ? S.toFixed(2) : '0';

            title.value = tunName;
        } else {
            //  console.log(val,'隐藏333333333333333333333')
            dhk.value.style.visibility = 'hidden'
        }
    }
})

// 数据
const dataLabel = ref([
    {
        id: 0,
        name: '风量：',
        icon: fl,
        num: '0',
        unit: "m³/s"
    },
    {
        id: 1,
        name: '风速：',
        icon: fs,
        num: '0',
        unit: "m/s"
    },
    {
        id: 2,
        name: '阻力：',
        icon: zl,
        num: '0',
        unit: 'pa'
    },
    {
        id: 3,
        name: '面积：',
        icon: mj,
        num: '0',
        unit: 'm³'
    },
])


</script>
<style lang="scss" scoped>
.tun_info {
    position: fixed;
    left: 0;
    top: 0;
    margin-left: 240px;
    margin-top: 85px;
    visibility: hidden;
    text-align: center;
    line-height: 75px;
    font-size: 12px;
    transform: all 0.2s;
}

.tun_info_box {
    width: 346px;
    height: 194px;
    background-size: 346px 194px;
    background-image: url('../../assets/img/tun_info.png');

    .tun_info_title {
        width: 100%;
        height: 38px;
        margin-top: 18px;
        font-size: 18px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #FFFFFF;
        line-height: 38px;
    }

    .tun_info_text {
        width: 60px;
        text-align: start;
        height: 18px;
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #54D9E9;
        line-height: 18px;
    }

    .tun_info_data {
        width: 60px;
        text-align: start;
        height: 18px;
        font-size: 20px;
        font-family: DIN;
        font-weight: bold;
        color: #FEFFFF;
        line-height: 18px;
    }
}
</style>