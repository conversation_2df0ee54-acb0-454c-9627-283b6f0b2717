<template>
    <div>
        <VenDataBox @get-current-index="getCurrentIndex" title="用风地点" :tab-list="tabList">
            <template #introduce>
                <div class="p-13px">

                    <p class="introduce_text">实时查看解算的用风地点风是否满足需求。</p>
                </div>
            </template>
            <template #content>
                <!-- 用风地点 -->
                <div v-show="currentIndex == 0">
                    <el-table class="ven_custom-table" :data="windUsageLocationTableData" height="440rem"
                        style="width: 400rem;margin: 0 auto;">
                        <el-table-column v-for="(v1, i1) in windUsageLocationTableField" :width="v1.width && v1.width"
                            :key="v1.prop" :label="v1.label" :show-overflow-tooltip="true">
                            <template #default="scope">
                                <VenTextTooltip v-if="i1 == 1 || i1 == 2" :str="scope.row[v1.prop] ?? '-'" />
                                <span v-else>{{ scope.row[v1.prop] ?? '-' }}</span>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </template>
        </VenDataBox>
    </div>
</template>
<script setup>
import { ref, watch } from 'vue';
import VenDataBox from '../../common/ventilationDataBox.vue'
import { UseCurrentIndex } from '@/hooks/UseCurrentIndex';
import { ApiWindsite } from '@/https/encapsulation/threeDimensional.js'
import VenTextTooltip from '../../common/venTextTooltip.vue';
const { currentIndex } = UseCurrentIndex()
const getCurrentIndex = (val) => {
    currentIndex.value = val
}
const tabList = ['用风地点']
// 用风地点数据

// const windUsageLocationData = ref({ tableData: [], tableField: [] })
const windUsageLocationTableData = ref([])
const windUsageLocationTableField = ref([])
ApiWindsite().then((res) => {
    if (res.Status == 0) {
        const { Result } = res;
        windUsageLocationTableData.value = Result;

        windUsageLocationTableField.value = [
            {
                prop: 'TunName',
                label: '巷道名称',
                width: 100,
            },
            // {
            //     prop: 'Ventflow',
            //     label: '巷道类型',
            //     width: 100,
            // },
            {
                prop: 'QDemand',
                label: '需风量'
            },
            {
                prop: 'QCalculate',
                label: '解算风量',
                width: 100,
            },
            {
                prop: 'Vssz',
                label: '风速'
            },
            {
                prop: 'Remark',
                label: '评价'
            },
        ]
    }
})



</script>

<style lang="scss">
@use '../../assets/index.scss';
</style>