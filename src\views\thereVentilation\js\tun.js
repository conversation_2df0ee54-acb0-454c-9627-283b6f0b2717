import * as THREE from 'three'
import { textureLoader } from '@/three/utils/loader'
import { initMatcapThree } from '@/three/effect/matcap'
import gsap from "gsap";
import { creartTrapezoidShapeGeo, createCircleShapeGeo, createArchShape<PERSON>eo, recreate } from "./tunShape";
import { initRoadFlow, initAirFlow } from '@/three/effect/flow';
import { setMapRepeat, computeTunLenght, addTunObject } from "@/views/thereVentilation/js/tunUtils.js";
import { meshRemove } from '@/three/initThree1';
import { TunConfig, NodeConfig } from './config'
import { mergeGeometries } from 'three/examples/jsm/utils/BufferGeometryUtils';
import { } from 'three'
import { createLine2Mat, createLine2 } from '@/three/mesh/Line2Mesh';
import { loadingCgqTuyere } from './tunSceneMesh';

class SemiCircleTubeGeometry extends THREE.BufferGeometry {
    constructor(path, tubularSegments = 64, radius = 1, radialSegments = 8, closed = false) {
        super();

        this.type = 'SemiCircleTubeGeometry';

        this.parameters = {
            path: path,
            tubularSegments: tubularSegments,
            radius: radius,
            radialSegments: radialSegments,
            closed: closed
        };

        // 生成几何体
        this.generateGeometry(path, tubularSegments, radius, radialSegments, closed);
    }

    generateGeometry(path, tubularSegments, radius, radialSegments, closed) {
        const frames = path.computeFrenetFrames(tubularSegments, closed);

        // 顶点、法线和 UV 数据
        const vertices = [];
        const normals = [];
        const uvs = [];

        // 生成顶点
        for (let i = 0; i <= tubularSegments; i++) {
            const u = i / tubularSegments;
            const pos = path.getPointAt(u);

            // 生成半圆形截面
            for (let j = 0; j <= radialSegments; j++) {
                const v = j / radialSegments;
                const angle = Math.PI * v; // 角度从 0 到 π

                // 计算截面顶点位置
                const normal = frames.normals[i].clone().multiplyScalar(Math.cos(angle))
                    .add(frames.binormals[i].clone().multiplyScalar(Math.sin(angle)));
                const vertex = pos.clone().add(normal.multiplyScalar(radius));

                vertices.push(vertex.x, vertex.y, vertex.z);
                normals.push(normal.x, normal.y, normal.z);
                uvs.push(u, v);
            }

            // 添加下方闭合面的顶点
            const bottomVertex = pos.clone().add(frames.normals[i].clone().multiplyScalar(-radius));
            vertices.push(bottomVertex.x, bottomVertex.y, bottomVertex.z);
            normals.push(frames.normals[i].x, frames.normals[i].y, frames.normals[i].z);
            uvs.push(u, 0.5); // UV 映射调整
        }

        // 生成索引
        const indices = [];
        for (let i = 0; i < tubularSegments; i++) {
            for (let j = 0; j < radialSegments; j++) {
                const a = i * (radialSegments + 2) + j;
                const b = a + 1;
                const c = (i + 1) * (radialSegments + 2) + j;
                const d = c + 1;

                // 半圆形截面的三角形
                indices.push(a, b, d);
                indices.push(a, d, c);

                // 下方闭合面的三角形
                const bottomA = i * (radialSegments + 2) + radialSegments + 1;
                const bottomB = (i + 1) * (radialSegments + 2) + radialSegments + 1;
                indices.push(a, bottomA, c);
                indices.push(bottomA, bottomB, c);
            }
        }

        // 设置几何体属性
        this.setIndex(indices);
        this.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
        this.setAttribute('normal', new THREE.Float32BufferAttribute(normals, 3));
        this.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2));
    }
}


// 创建巷道
const tuyereMat = initMatcapThree({ path: './textures/1.png', color: 0xffffff })
export function createTun({ tubePoints = [], shape, radius, originPoints, opacity }) {
    const { Lx } = originPoints;
    const color = '#ffffff'
    // const color = Ventflow == 1 ? "#00db77" : (Ventflow == -1 ? "#dd212f" : "#ffb30f")
    // let path = Ventflow == 1 ? "./textures/jt2-1.png" : (Ventflow == -1 ? "./textures/jt2-2.png" : "./textures/jt2-2.png")
    let meshArr = [];
    let newArr = []

    newArr = tubePoints.map(({ x, y, z }) => {
        return new THREE.Vector3(x, y, z);
    });
    const len = newArr.length;
    let tubeMesh = null;
    if (len <= 1) return { meshArr };

    if (Lx == 41) {
        tubeMesh = excavationTun(newArr, { radius: radius ?? TunConfig.excavationRadius }).tubeMesh;
        const { x, y, z } = tubePoints[0];
        loadingCgqTuyere({
            position: [x, y, z], positionList: tubePoints, callback: (mesh) => {
                mesh.material = tuyereMat;
                tubeMesh.add(mesh);
            }
        });
    } else {
        tubeMesh = tun(newArr, { ventflow: originPoints.Ventflow, color, opacity, radius: radius ?? TunConfig.radius, originPoints }).tubeMesh;
    }
    const Length = computeTunLenght(originPoints.Nodes);
    tubeMesh.mid = originPoints.TunID;
    tubeMesh.originPoints = { Length, ...originPoints };
    meshArr.push(tubeMesh);
    return { meshArr }
}
// 使用烘培纹理
const sphereMat = initMatcapThree({ path: './textures/444.png', color: NodeConfig.color })
const sphereMiddle1 = initMatcapThree({ path: './textures/444.png', color: NodeConfig.middleColor1 })
const sphereMiddle2 = initMatcapThree({ path: './textures/444.png', color: NodeConfig.middleColor2 })
const sphereMiddle3 = initMatcapThree({ path: './textures/444.png', color: NodeConfig.middleColor3 })
const sphereGeo = new THREE.SphereGeometry(NodeConfig.radius, 16, 16);
const sphereGeo2 = new THREE.SphereGeometry(NodeConfig.radius2, 16, 16);
export function createPoint(options = { isEditMat: false, x, y, z, color, radius, opacity, originPoint, type, ventflow }) {
    const { x, y, z, radius, color, originPoint, isEditMat, type, ventflow, opacity } = options;
    let type1 = type ?? 0
    const editMat = initMatcapThree({ path: './textures/444.png', color: NodeConfig.middleColor1 })
    let mat;
    if (ventflow != null) {
        mat = ventflow == 1 ? sphereMiddle1 : (ventflow == -1 ? sphereMiddle2 : sphereMiddle3)
        mat.color = new THREE.Color(ventflow == 1 ? NodeConfig.middleColor1 : (ventflow == -1 ? NodeConfig.middleColor2 : NodeConfig.middleColor3));
    } else {
        mat = isEditMat ? editMat : (type1 == 0 ? sphereMat : sphereMiddle1);
        mat.color = new THREE.Color(color ?? (type1 == 0 ? NodeConfig.color : NodeConfig.middleColor1));
    }
    mat.side = THREE.DoubleSide;
    mat.transparent = true;
    mat.opacity = opacity ?? 0.6;

    const sphere = new THREE.Mesh((type1 == 0 ? sphereGeo : sphereGeo2), mat);
    sphere.mid = originPoint && originPoint.NodeID;
    sphere.point = new THREE.Vector3(x, y, z);
    originPoint && (sphere.originPoint = originPoint, sphere.mid = originPoint.NodeID);
    sphere.renderOrder = 3;
    sphere.position.set(x, y, z);
    function remove() {
        meshRemove(sphere);
    }
    return { sphere, mat, sphereGeo, remove };
}

// 使用烘培纹理
const map1 = textureLoader().load('./textures/jt_green_jf.png')
const map2 = textureLoader().load('./textures/jt_red_hf.png')
const map3 = textureLoader().load('./textures/jt_yellow_jf.png')
export const tunMat1 = initMatcapThree({ path: './textures/444.png', map: map1 }) //贼节省性能  进风
export const tunMat2 = initMatcapThree({ path: './textures/444.png', map: map2 }) // 回风
export const tunMat3 = initMatcapThree({ path: './textures/444.png', map: map3 }) // 用风

// const mat 4
export function tun(linePoints = [], { myMat = null, isEditMat = false, ventflow = 1, opacity = 0.7, radius, originPoints = {} }) {
    // console.log(ventflow, 'ventflowventflow');

    if (!linePoints.length || linePoints.length < 1) return;
    const { NetLine } = originPoints;
    const mat4 = initMatcapThree({ path: './textures/43.png', color: 0xffffff })
    const matEdit = initMatcapThree({ path: './textures/444.png', color: 0xffffff, map: textureLoader().load('./textures/jt_green_jf.png') })
    let mat = isEditMat ? matEdit : (ventflow == 1 ? tunMat1 : (ventflow == -1 ? tunMat2 : tunMat3))
    let path = isEditMat ? './textures/jt_green_jf.png' : (ventflow == 1 ? './textures/jt_green_jf.png' : (ventflow == -1 ? './textures/jt_red_hf.png' : './textures/jt_yellow_jf.png'))
    const map = textureLoader().load(path)
    if (myMat) {
        mat = myMat;
    } else {
        mat = NetLine ? mat : mat4;
        mat.map = map;
    }
    mat.side = THREE.DoubleSide;
    if (mat.map) {
        mat.map.wrapS = THREE.RepeatWrapping;
        mat.map.wrapT = THREE.MirroredRepeatWrapping;
        mat.map.repeat.set(12, 10);
        mat.transparent = true;
        mat.opacity = myMat ? 1 : (NetLine ? (opacity ?? 0.7) : 0.3);
    }


    let geometrys = [];
    let curve2 = new THREE.CatmullRomCurve3(linePoints) // 曲线路径
    for (let index = 0, len = linePoints.length - 1; index < len; index++) {
        if (!linePoints[index] instanceof THREE.Vector3 || !linePoints[index + 1] instanceof THREE.Vector3) return;
        const arr = [linePoints[index], linePoints[index + 1]];
        // CatmullRomCurve3创建一条平滑的三维样条曲线
        let curve = new THREE.CatmullRomCurve3(arr) // 曲线路径
        const points = curve.getPoints(20);
        let curve2 = new THREE.CatmullRomCurve3(points) // 曲线路径

        // 创建管道
        let tubeGeometry = new THREE.TubeGeometry(curve, 20, radius ?? TunConfig.radius, 8)
        // let tubeGeometry = createArchShapeGeo(curve2, radius ?? TunConfig.radius)
        geometrys.push(tubeGeometry);
    }
    // 合并几何体
    const mergedGeometry = mergeGeometries(geometrys);
    let tubeMesh = new THREE.Mesh(mergedGeometry, mat);
    const curves = curve2.getPoints(TunConfig.pointsNum);

    tubeMesh.middlePoint = curves[Math.floor(TunConfig.pointsNum / 2)];
    tubeMesh.points = linePoints;

    tubeMesh.renderOrder = 2;
    mat.map && setMapRepeat(tubeMesh, linePoints);
    mat.map && gsap.to(mat.map.offset, {
        x: -1,
        duration: 5,
        repeat: -1,
        ease: "none",
    });

    return { tubeMesh, tubeGeometry: mergedGeometry, mat }
}
const createTunLineMat = initMatcapThree({ path: './textures/582.png', color: 0xffffff })
export function createTunLine(linePoints = [], { radius, originPoints = {} }) {
    if (!linePoints.length || linePoints.length < 1) return;
    let mat = createTunLineMat
    mat.side = THREE.DoubleSide;
    let geometrys = [];
    let curve2 = new THREE.CatmullRomCurve3(linePoints) // 曲线路径
    for (let index = 0, len = linePoints.length - 1; index < len; index++) {
        if (!linePoints[index] instanceof THREE.Vector3 || !linePoints[index + 1] instanceof THREE.Vector3) return;
        const arr = [linePoints[index], linePoints[index + 1]];
        // CatmullRomCurve3创建一条平滑的三维样条曲线
        let curve = new THREE.CatmullRomCurve3(arr) // 曲线路径
        // 创建管道
        let tubeGeometry = new THREE.TubeGeometry(curve, 20, radius, 4)
        // let tubeGeometry = createArchShapeGeo(curve2, radius ?? TunConfig.radius)
        geometrys.push(tubeGeometry);
    }
    // 合并几何体
    const mergedGeometry = mergeGeometries(geometrys);
    let tubeMesh = new THREE.Mesh(mergedGeometry, mat);
    const curves = curve2.getPoints(TunConfig.pointsNum);

    tubeMesh.middlePoint = curves[Math.floor(TunConfig.pointsNum / 2)];
    tubeMesh.points = linePoints;

    tubeMesh.renderOrder = 1;

    return { tubeMesh, tubeGeometry: mergedGeometry, mat }
}
const excavationTunMat = initMatcapThree({ path: './textures/5.png', color: 0xffffff })

export function excavationTun(linePoints = [], { opacity = 0.7, color = 0xffffff, radius }) {
    if (!linePoints.length || linePoints.length < 1) return;
    const mat = excavationTunMat.clone();
    mat.map = null;
    mat.opacity = 0.01;

    let geometrys = [];
    let curve2 = new THREE.CatmullRomCurve3(linePoints) // 曲线路径
    for (let index = 0, len = linePoints.length - 1; index < len; index++) {
        if (!linePoints[index] instanceof THREE.Vector3 || !linePoints[index + 1] instanceof THREE.Vector3) return;
        const arr = [linePoints[index], linePoints[index + 1]];
        // CatmullRomCurve3创建一条平滑的三维样条曲线
        let curve = new THREE.CatmullRomCurve3(arr) // 曲线路径
        const points = curve.getPoints(50);
        let curve2 = new THREE.CatmullRomCurve3(points) // 曲线路径

        // 创建管道
        let tubeGeometry = new THREE.TubeGeometry(curve, 10, radius ?? TunConfig.radius, 8)
        // let tubeGeometry = createArchShapeGeo(curve2, radius ?? TunConfig.radius)
        geometrys.push(tubeGeometry);


    }
    // 合并几何体
    const mergedGeometry = mergeGeometries(geometrys);
    let tubeMesh = new THREE.Mesh(mergedGeometry, mat);
    const { x, y, z } = tubeMesh.position;
    tubeMesh.position.set(x, y + TunConfig.radius + TunConfig.excavationRadius, z);
    const curves = curve2.getPoints(TunConfig.pointsNum);
    tubeMesh.middlePoint = curves[Math.floor(TunConfig.pointsNum / 2)];
    tubeMesh.points = linePoints;
    tubeMesh.renderOrder = 2;


    return { tubeMesh, tubeGeometry: mergedGeometry, mat }
}

// const material = new THREE.LineBasicMaterial();
// 创建线路 批量导点 中使用
// export function createLine({ linePoints = [], color = 0xffff00, isCurve = false }) {
//     material.color = new THREE.Color(0xffff00);
//     const lineArr = isCurve ? new THREE.CatmullRomCurve3(linePoints).getPoints(TunConfig.pointsNum) : linePoints;

//     const geometry = new THREE.BufferGeometry().setFromPoints(lineArr);
//     const lineMesh = new THREE.Line(geometry, material);
//     lineMesh.points = linePoints;
//     return lineMesh;
// }
const lineMat = createLine2Mat(0xfff000, 6)
export function createLine({ linePoints = [], color = 0xffff00, isCurve = false }) {
    const arr1 = linePoints.map((item) => {
        const { x, y, z } = item;
        return [x, y, z]
    }).flat();
    const lineMesh = createLine2(arr1, lineMat)
    lineMesh.points = linePoints;
    return lineMesh;
}