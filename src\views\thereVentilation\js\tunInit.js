import { Api<PERSON>ode<PERSON><PERSON>, ApiTunList, roadwayList } from "@/https/encapsulation/threeDimensional";
import { Group, Vector3 } from "three";
import * as THREE from "three";
import { ref } from "vue";
import { useThereVentilation } from "@/store/thereVentilation";
import { positionTransform } from "@/views/thereVentilation/js/tunUtils.js";
import {
  createAGrid,
  addTunLable,
  loadingTfjj,
  loadingFc,
  loadingFm,
  loadingCgq,
  loadingEquipment,
  miningAreaWorkingFace,
  loadingTfj,
  loadingLxAirDoor,
  loadingChamber,
} from "@/views/thereVentilation/js/tunSceneMesh.js";
import { createTun, createPoint, tun } from "@/views/thereVentilation/js/tun.js";
import { ElMessage } from "element-plus";
import { moveToCamera } from "@/three/cameraControls.js";
import { setMeshScale, setPosition, changeMeshOrientation } from "@/three/utils/meshUtils";
import { objectPropertyToArray, throttleFn } from "@/utils/utils";
import { TunConfig } from "./config";
import { findFacilityBindingRoadway } from "@/https/encapsulation/threeVentilateConfig";
import { ApiSecurityMonitoringList } from "@/https/encapsulation/Safety";
import mitt from "@/utils/eventHub";
const store = useThereVentilation();

// 获取所有巷道信息
export async function getTunList() {
  tunStr.value = [];
  const { Result } = await ApiTunList();
  apiTunList.value = Result ?? [];
  tunStr.value = Result.map(({ SnodeID, EnodeID, MnodeID }) => {
    return MnodeID ? [SnodeID, ...MnodeID.split("-").map((v) => Number(v)), EnodeID] : [SnodeID, EnodeID];
  });
}
// 获取所有节点信息
async function getNodeList() {
  const { Result } = await ApiNodeList();
  apiNodeList.value = Result ?? [];
  return Result;
}

let tunOriginList = []; // 巷道真实数据
let pointMeshArr = [];
let tunMeshArr = ref([]);
let tunPointsAll = []; // 存储每一条巷道的点位形成的数组信息 基于相对坐标生成的数组信息
let pointShowAll = [];
let allGroup = new Group(); // 整体组
let meshGroup = new Group(); // 巷道组
let pointGroup = new Group(); // 节点组
let middlePointList = []; // 中间节点列表
let spriteLabelGroup = new Group(); // 标签组
let deviceGroup = new Group(); // 设备组
// 节流闭包
let tunLabelShowByCamera = null;
let getRecentNodeThrottleFn = null;

let maxPosition = [0, 0, 0];
let minPosition = [0, 0, 0];
let averagePosition = [0, 0, 0];
let transformMaxPosition = [0, 0, 0];
let transformMinPosition = [0, 0, 0];
let setY = 0;
let n = 1.7; //放大系数

const tunStr = ref([]);
const apiTunList = ref([]);
const apiNodeList = ref([]);
const middleNode = ref([]); // 中间节点坐标
const maxNodeID = ref(0); // 最大节点ID
const deviceFilterList = ref([]);

let grid = null;
export {
  // 通用变量
  tunOriginList,
  tunMeshArr,
  pointMeshArr,
  tunPointsAll,
  pointShowAll,
  allGroup,
  meshGroup,
  pointGroup,
  spriteLabelGroup,
  maxPosition,
  minPosition,
  averagePosition,
  transformMaxPosition,
  transformMinPosition,
  setY,
  n,
  middlePointList,
  // vue变量
  tunStr,
  apiTunList,
  apiNodeList,
  middleNode,
  maxNodeID,
  deviceFilterList,
  // 闭包函数
  tunLabelShowByCamera,
  getRecentNodeThrottleFn,
  dynamicLoadingLabel,
};

//控制相机大小距离
export let tunInit = function (scene, cameraControls) {
  cameraControls.minDistance = 60;
  cameraControls.maxDistance = 8000;
  function dataReset(callback = () => {}) {
    tunLabelShowByCamera = null;
    tunStr.value = [];
    apiTunList.value = [];
    apiNodeList.value = [];
    tunOriginList = []; // 巷道真实数据 未转换前的数据
    pointMeshArr = [];
    tunMeshArr.value = [];
    tunPointsAll = []; // 存储每一条巷道的点位形成的数组信息 基于相对坐标生成的数组信息
    pointShowAll = [];
    middlePointList = [];
    maxPosition = [0, 0, 0];
    minPosition = [0, 0, 0];
    averagePosition = [0, 0, 0];
    transformMaxPosition = [0, 0, 0];
    transformMinPosition = [0, 0, 0];
    middleNode.value = []; // 中间节点坐标
    maxNodeID.value = 0; // 最大节点ID
    deviceFilterList.value = [];
    // clearMesh();
    scene.remove(...scene.children);
    pointGroup.remove(...pointGroup.children);
    deviceGroup.remove(...deviceGroup.children);
    meshGroup.remove(...meshGroup.children);
    spriteLabelGroup.remove(...spriteLabelGroup.children);
    allGroup.remove(...allGroup.children);
    grid && grid.remove(...grid.children);
    callback();
  }
  // 数据初始化传层级
  function dataInit(numberLevel) {
    console.log("dataInit");
    getNodeList().then((Result) => {
      // w1z
      let zMax = 0,
        zMin = 0;
      Result.forEach(({ x, y, z }) => {
        const [maxX, maxY, maxZ] = maxPosition;
        x > maxX ? (maxPosition[0] = x) : (minPosition[0] = x);
        y > maxY ? (maxPosition[1] = y) : (minPosition[1] = y);
        if (z > zMax) {
          zMax = z;
        }
        if (z < zMin) {
          zMin = z;
        }
      });
      maxPosition[2] = zMax;
      minPosition[2] = zMin;
      let vec3 = Result.length == 1 ? Result[0] : Result[Math.floor(Result.length / 2)] ?? { x: 0, y: 0, z: 0 };
      const [x1, y1, z1] = maxPosition;
      const [x2, y2, z2] = minPosition;
      averagePosition = Result.length == 1 ? Result[0] : [(x1 + x2) / 2, (y1 + y2) / 2, (z1 + z2) / 2];

      const { x, y, z } = vec3 ?? { x: 0, y: 0, z: 0 };
      middleNode.value = [x, y, z];

      store.$patch({ loadingShow: true });
      getTunList()
        .then((v) => {
          Result.length &&
            Result.forEach((v) => {
              const { NodeID } = v;
              const index = tunStr.value.findIndex((v) => v.includes(NodeID));

              if (index != -1) {
                const { Ventflow } = apiTunList.value[index];
                const { x, y, z } = v;
                const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { n, middlePosition: middleNode.value, type: 0 });
                const { sphere } = createPoint({ x: x1, y: y1, z: z1, originPoint: v, type: 0, ventflow: Ventflow });
                pointMeshArr.push(sphere);
                pointGroup.add(sphere);
                pointShowAll.push({ point: { x: x1, y: y1, z: z1 }, originPoint: v });
              }
            });
        })
        .then(() => {
          roadwayList()
            .then(({ Result }) => {
              if (!Result || !Result.length) {
                store.$patch({ loadingShow: false });
                return;
              }
              tunOriginList = Result.filter((v) => v.Nodes.length > 1).map((v) => {
                return { TunID: v.TunID, Nodes: v.Nodes.filter((k) => k.NodeID != 0), ...apiTunList.value.find((k) => k.TunID == v.TunID) };
              });
              //console.log(tunOriginList.length);
              const nodesList = tunOriginList.map((v) => v.Nodes);

              let zMax = 0,
                zMin = 0;
              nodesList.forEach((v, i) => {
                let isShaft = false;
                const linePoints = v.map((k, i) => {
                  const { NodeID, x, y, z } = k;

                  if (maxNodeID.value < NodeID) {
                    maxNodeID.value = NodeID;
                  }

                  const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { n, middlePosition: middleNode.value });
                  if (z == maxPosition[2]) {
                    isShaft = true;
                    // loadingTfjj(scene, x1, y1, z1, 6);
                  }

                  const [maxX, maxY, maxZ] = transformMaxPosition;
                  x1 > maxX ? (transformMaxPosition[0] = x1) : (transformMinPosition[0] = x1);
                  y1 > maxY ? (transformMaxPosition[1] = y1) : (transformMinPosition[1] = y1);
                  if (z1 > zMax) {
                    zMax = z1;
                  }
                  if (z1 < zMin) {
                    zMin = z1;
                  }
                  return { ...k, x: x1, y: y1, z: z1 };
                });

                tunPointsAll.push(linePoints);

                const { meshArr } = createTun({
                  tubePoints: linePoints,
                  radius: isShaft ? TunConfig.shaftRadius : null,
                  originPoints: tunOriginList[i],
                });
                const { x, y, z } = meshArr[0].middlePoint;
                const Lx = tunOriginList[i].Lx;
                if (Lx == 7 || Lx == 8) {
                  // 找出该巷道起始点中y轴最高的点
                  const maxPoint = linePoints.reduce((a, b) => (a.y > b.y ? a : b));
                  const { x, y, z } = maxPoint;
                  setLxDevice(scene, [x, y, z], Lx, i);
                } else {
                  setLxDevice(scene, [x, y + TunConfig.radius, z], Lx, i);
                }
                if (meshArr.length > 0) {
                  meshGroup.add(...meshArr);
                }

                // test
                if (i != 500) return;
                linePoints.forEach((v, i) => {
                  if (i == 12) {
                    const { x, y, z } = v;
                    miningAreaWorkingFace(scene, [x, y + 10, z], { x: 10, y: 30, z: 10 }, "采区工作面");
                  }
                });
              });
              transformMaxPosition[2] = zMax;
              transformMinPosition[2] = zMin;
            })
            .then(() => {
              setTunDevice(scene);
              // 添加标签
              if (tunOriginList.length > 0) {
                const arr = addTunLable(scene, meshGroup.children, tunOriginList, numberLevel);
                spriteLabelGroup.add(...arr);
                allGroup.add(pointGroup, meshGroup, spriteLabelGroup);
                scene.add(allGroup);
                deviceGroup.position.y = -6;
                scene.add(deviceGroup);
              }
              // 添加经纬格
              const [maxX, maxY, maxZ] = maxPosition;
              const [minX, minY, minZ] = minPosition;

              //console.log([maxX, maxY, maxZ], [minX, minY, minZ], middleNode.value);
              setY = middleNode.value[2] + 5000;
              console.log(setY, "444444sexY");
              const grid = createAGrid({ size: setY * n * 4, xSize: maxX - minX, zSize: maxY - minY, xMin: minX, zMin: minY, scale: 1 });
              
              moveToCamera(cameraControls, 0, [setY, setY, setY / 0.75], [0,3300,3400]);
              cameraControls.rotate(0, -Math.PI /4);
              //  moveToCamera(cameraControls, 0, [setY / 6, setY / 0.8, setY / 6], [0, 0, 0]);
              // 可根据需要添加视角调整
              //  cameraAngle('front'); // 设置为正面视角，可根据实际图二角度调整参数
              setPosition(grid, (averagePosition[0] - middleNode.value[0]) * n, 0, (minZ - middleNode.value[2]) * n);
              // setPosition(grid, ((averagePosition[0] - middleNode.value[0])) * n, ((minZ - middleNode.value[2])) * n - 10, 0);

              scene.add(grid);
              // 标签动态显示的函数
              isCreateTunLabelShowByCamera(true);
              openLinkageEvent();
              setTimeout(() => {
                store.$patch({ loadingShow: false });
              }, 0);
            });
        });
    });
  }
  // 本地数据初始化
  function dataInitLocal(nodeList, tunList, roadwayList, tunLinkList, isResetCamera = true) {
    console.log("nodeList", nodeList);
    let zMax = 0,
      zMin = 0;
    nodeList.forEach(({ x, y, z }) => {
      const [maxX, maxY, maxZ] = maxPosition;
      x > maxX ? (maxPosition[0] = x) : (minPosition[0] = x);
      y > maxY ? (maxPosition[1] = y) : (minPosition[1] = y);
      if (z > zMax) {
        zMax = z;
      }
      if (z < zMin) {
        zMin = z;
      }
    });
    maxPosition[2] = zMax;
    minPosition[2] = zMin;
    let vec3 = nodeList.length == 1 ? nodeList[0] : nodeList[Math.floor(nodeList.length / 2)] ?? { x: 0, y: 0, z: 0 };
    // let vec3 = nodeList[0];
    const [x1, y1, z1] = maxPosition;
    const [x2, y2, z2] = minPosition;
    // let vec3 = nodeList.length == 1 ? nodeList[0] : ({ x: (x1 + x2) / 2, y: (y1 + y2) / 2, z: (z1 + z2) / 2 });
    averagePosition = nodeList.length == 1 ? nodeList[0] : [(x1 + x2) / 2, (y1 + y2) / 2, (z1 + z2) / 2];

    const { x, y, z } = vec3 ?? { x: 0, y: 0, z: 0 };
    middleNode.value = [x, y, z];

    // 渲染点
    nodeList.forEach((v) => {
      const { x, y, z } = v;
      const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { n, middlePosition: middleNode.value, type: 0 });
      const { sphere } = createPoint({ x: x1, y: y1, z: z1, originPoint: v, type: 1, ventflow: 0 });
      pointMeshArr.push(sphere);
      pointGroup.add(sphere);
      pointShowAll.push({ point: { x: x1, y: y1, z: z1 }, originPoint: v });
    });

    if (!roadwayList || !roadwayList.length) {
      // isDialogShow.value = true;
      return;
    }
    tunOriginList = roadwayList.filter((v) => v.Nodes.length > 1);
    //console.log(tunOriginList.length);

    const nodesList = tunOriginList.map((v) => v.Nodes);

    let zMax1 = 0,
      zMin1 = 0;
    nodesList.forEach((v, i) => {
      let isShaft = false;
      const linePoints = v
        .filter((v) => v)
        .map((k, i) => {
          const { NodeID, x, y, z } = k;

          if (maxNodeID.value < NodeID) {
            maxNodeID.value = NodeID;
          }

          const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { n, middlePosition: middleNode.value });
          if (z == maxPosition[2]) {
            isShaft = true;
            // loadingTfjj(scene, x1, y1, z1, 6);
          }

          const [maxX, maxY, maxZ] = transformMaxPosition;
          x1 > maxX ? (transformMaxPosition[0] = x1) : (transformMinPosition[0] = x1);
          y1 > maxY ? (transformMaxPosition[1] = y1) : (transformMinPosition[1] = y1);
          if (z1 > zMax1) {
            zMax1 = z1;
          }
          if (z1 < zMin1) {
            zMin1 = z1;
          }
          return { ...k, x: x1, y: y1, z: z1 };
        });

      tunPointsAll.push(linePoints);
      // console.log(linePoints, 'linePointslinePointslinePointslinePoints');

      const { meshArr } = createTun({
        tubePoints: linePoints,
        radius: isShaft ? TunConfig.shaftRadius : null,
        originPoints: tunOriginList[i],
      });
      if (meshArr.length > 0) {
        const { x, y, z } = meshArr[0].middlePoint;
        const Lx = tunOriginList[i].Lx;
        if (Lx == 7 || Lx == 8) {
          // 找出该巷道起始点中y轴最高的点
          const maxPoint = linePoints.reduce((a, b) => (a.y > b.y ? a : b));
          const { x, y, z } = maxPoint;
          setLxDevice(scene, [x, y, z], Lx, i);
        } else {
          setLxDevice(scene, [x, y + TunConfig.radius, z], Lx, i);
        }
        meshGroup.add(...meshArr);
        tunMeshArr.value.push(...meshArr);
      }

      // test
      if (i != 500) return;
      linePoints.forEach((v, i) => {
        if (i == 12) {
          const { x, y, z } = v;
          miningAreaWorkingFace(scene, [x, y + 10, z], { x: 10, y: 30, z: 10 }, "采区工作面");
        }
      });
    });
    transformMaxPosition[2] = zMax;
    transformMinPosition[2] = zMin;
    setTunDevice(scene);
    // 添加标签
    if (tunOriginList.length > 0) {
      const arr = addTunLable(scene, meshGroup.children, tunOriginList);
      // spriteLabelGroup.add(...arr);
      allGroup.add(pointGroup, meshGroup, spriteLabelGroup);
      scene.add(allGroup);
      deviceGroup.position.y = -6;
      scene.add(deviceGroup);
    }
    // 添加经纬格
    const [maxX, maxY, maxZ] = maxPosition;
    const [minX, minY, minZ] = minPosition;
    //console.log([maxX, maxY, maxZ], [minX, minY, minZ], middleNode.value);
    setY = middleNode.value[2] + 6000;
    grid = createAGrid({ size: setY * n * 4, xSize: maxX - minX, zSize: maxY - minY, xMin: minX, zMin: minY, scale: 1 });
    isResetCamera && moveToCamera(cameraControls, 0, [setY / 6, setY / 1.2, setY / 1.2], [0, 0, 0]);
    console.log(setY, "333333");
    // setPosition(grid, ((averagePosition[0] - middleNode.value[0])) * n, 0, ((minZ - middleNode.value[2])) * n);
    setPosition(grid, (averagePosition[0] - middleNode.value[0]) * n, 0, (averagePosition[2] - middleNode.value[2]) * n);
    scene.add(grid);

    // 标签动态显示的函数
    isCreateTunLabelShowByCamera(true);
    openLinkageEvent();
  }

  // 根据Lx 用途添加巷道标识
  function setLxDevice(scene, position, Lx, index) {
    const positionList = tunPointsAll[index];
    switch (Lx) {
      case 7:
        loadingTfjj(scene, ...position, 4);
        break;
      case 8:
        loadingTfjj(scene, ...position, 4);
        break;
      case 42:
        loadingChamber({ position, scale: 15, spriteLabelGroup, positionList: tunPointsAll[index], scene });
        break;
      case 43:
        loadingLxAirDoor({ position, scale: 4, spriteLabelGroup, positionList: tunPointsAll[index], scene });
        break;
    }
  }
  //
  function refreshCamera(callback = () => {}) {
    callback();
    // moveToCamera(cameraControls, 0, [-setY * n / 2, setY * n, -setY * n / 2], [0, 0, 0]);
    moveToCamera(cameraControls, 0, [setY / 3, setY / 1.5, setY / 1.5], [0, 0, 0]);
  }
  // 切换相机视角
  function cameraAngle(direction) {
    const DEG90 = Math.PI * 0.5;
    const DEG180 = Math.PI;
    const DEG315 = Math.PI * 1.75;
    function angle(num = 1) {
      return Math.PI * num;
    }
    switch (direction) {
      case "up":
        cameraControls.rotateTo(DEG180, 0, true);
        break;
      case "front":
        cameraControls.rotateTo(DEG315, angle(0.48), true);
        break;
      case "side":
        cameraControls.rotateTo(angle(1.65), angle(0.25), true);
        break;
    }
  }
  return {
    dataReset,
    dataInit,
    dataInitLocal,
    refreshCamera,
    cameraAngle,
  };
};

// utils

/**
 * 显示或者隐藏标签信息
 * @param {Boolean} isShow
 * @param {Number} type 0:tunLabel 1:equipmentLabel
 * @returns
 */
export function showOrHideLabels(isShow = true, type = 0) {
  if (spriteLabelGroup.children.length == 0) return;
  spriteLabelGroup.children.forEach((mesh) => {
    if (mesh.Level == 9) return;
    if (type == 0 && mesh.labelType == "tunLabel") {
      mesh.visible = isShow;
      mesh.showState = isShow;
    } else if (type == 1 && mesh.labelType == "equipmentLabel") {
      mesh.visible = isShow;
      mesh.showState = isShow;
    }
  });
  let bool = spriteLabelGroup.children.every((v) => v.showState == false);
  isCreateTunLabelShowByCamera(!bool);
}

// 根据相机距离显示标签
function isCreateTunLabelShowByCamera(state = true) {
  console.log("camera");
  tunLabelShowByCamera = throttleFn(dynamicLoadingLabel,200); // 节流 减少性能消耗
}
function dynamicLoadingLabel(camera, isShow, zoomLevel) {
  if (!isShow) return;
  const point2 = camera.position;
  spriteLabelGroup.children.forEach((mesh) => {
    if (!mesh.showState || mesh.Level === 9) return;
    // 更新标签大小
    if (mesh.updateSize) {
      mesh.updateSize(zoomLevel);
    }
    // 原有的可见性逻辑
    const distance = point2.distanceTo(mesh.position);
    mesh.visible = distance < 1800;
  });
}
//判断距离最近的节点 并更新联动相机状态
let cameraPosition = { x: null, y: null, z: null };
function getRecentNode(camera, cameraControls) {
  const { x, y, z } = camera.position;
  const { x: x1, y: y1, z: z1 } = cameraPosition;
  if (x == x1 && y == y1 && z == z1) {
    return null;
  }
  cameraPosition = { x, y, z };
  const point2 = camera.position;

  if (!pointGroup.children.length) return;
  let minDistance = 1000;
  let xz = 1500;
  let zjMesh = null;
  const arr = [...pointGroup.children, ...middlePointList];
  arr.forEach((mesh, i) => {
    const point = mesh.position;
    const distance = point2.distanceTo(point); // 计算距离 要从相机到物体 这样计算
    if (distance > xz) return; // 大于 xz 不进行计算
    if (distance < minDistance) {
      minDistance = distance;
      zjMesh = mesh;
    }
  });
  return zjMesh ? zjMesh.mid : null;
}
function getRecentNodeResult(mid) {
  mid && mitt.emit("get3DRecentNodeEdit", mid);
}
// 重新开启联动事件
function openLinkageEvent() {
  getRecentNodeThrottleFn = throttleFn(getRecentNode, 1000, getRecentNodeResult);
}

// 将点位转为正式点位
export function createOriginPoint(x, y, z) {
  const obj = positionTransform(x, y, z, { n, type: 1, middlePosition: middleNode.value });
  const originPoint = { NodeID: "", NodeName: "", ...obj };
  return originPoint;
}

// 创建Nodes 它将被携带到巷道之中
export function createTunOrigin(originPoints, options = { Ventflow: 1, TunName: "-" }) {
  const { Ventflow, TunName } = options;
  return { TunName, TunID: "", Ventflow, S: "-", Q: "-", V: "-", H: "-", Nodes: originPoints };
}

// 设备绑定关系
export async function getDeviceBindByType(num) {
  let type = "";
  switch (num) {
    case 1:
      type = "安全监控";
      break;
    case 2:
      type = "主通风机";
      break;
    case 4:
      type = "自动风门";
      break;
    case 5:
      type = "自动风窗";
      break;
    case 6:
      type = null;
      break;
  }
  return await findFacilityBindingRoadway({ type }).then(({ data }) => {
    deviceFilterList.value = data.Result;
    return data.Result;
  });
}
// 设置传感器 根据巷道进行匹配
// getDeviceBindByType(6);
export async function setTunDevice() {
  if (deviceGroup.children.length > 0) {
    deviceGroup.remove(...deviceGroup.children);
  }
  spriteLabelGroup.children.forEach((mesh) => {
    if (mesh.labelType == "equipmentLabel") {
      spriteLabelGroup.remove(mesh);
    }
  });
  const cgqList = await ApiSecurityMonitoringList().then(({ data: { Result } }) => Result);
  getDeviceBindByType(6).then((data) => {
    const tunIDList = tunOriginList.map((v) => v.TunID);
    data.forEach(async (k) => {
      const { TunID, FacilityID, FacilityType, X, Y, Z } = k;
      const index = tunIDList.findIndex((v) => v == TunID);
      if (index !== -1) {
        const { x: x1, y: y1, z: z1 } = positionTransform(X, Y, Z, { n, middlePosition: middleNode.value });
        //console.log(x1, y1, z1);

        switch (FacilityType) {
          case "安全监控":
            const cgq = cgqList.find((v) => v.ID == FacilityID);
            loadingCgq({
              text: cgq.DevTypeName,
              position: [x1, y1, z1],
              spriteLabelGroup,
              deviceGroup,
              positionList: tunPointsAll[index],
              deviceInfo: { TunID, FacilityID, FacilityType, X, Y, Z },
            });
            break;
          case "主通风机":
            loadingTfj({
              position: [x1, y1, z1],
              spriteLabelGroup,
              deviceGroup,
              positionList: tunPointsAll[index],
              deviceInfo: { TunID, FacilityID, FacilityType, X, Y, Z },
            });
            break;
          case "自动风门":
            loadingFm({
              position: [x1, y1, z1],
              spriteLabelGroup,
              deviceGroup,
              positionList: tunPointsAll[index],
              deviceInfo: { TunID, FacilityID, FacilityType, X, Y, Z },
            });
            break;
          case "自动风窗":
            loadingFc({
              position: [x1, y1, z1],
              spriteLabelGroup,
              deviceGroup,
              positionList: tunPointsAll[index],
              deviceInfo: { TunID, FacilityID, FacilityType, X, Y, Z },
            });
            break;
        }
      }
    });
  });
}

// 设备定位
export async function devicePositioning(ID, cameraControls, cameraFlyToMesh) {
  let mesh = deviceGroup.children.find((v) => v.mid == ID);
  cameraFlyToMesh(cameraControls, { mesh: mesh, paddingTop: 30, paddingRight: 30, paddingBottom: 30, paddingLeft: 30 });
}

// 存储相机位置 方便复原
export function saveCameraPosition(cameraControls, initPosition = [0, 0, 0]) {
  const lastCameraPosition = ref({ x: null, y: null, z: null });

  function setCameraFly(flag = false) {
    const { x, y, z } = lastCameraPosition.value;
    if (flag) {
      moveToCamera(cameraControls, 0, initPosition, [0, 0, 0]);
      return;
    }
    if (x && y && z) {
      //console.log(x, y, z, 'x-y-z');

      moveToCamera(cameraControls, 0, [x, y, z], [0, 0, 0]);
    } else {
      moveToCamera(cameraControls, 0, initPosition, [0, 0, 0]);
    }
  }

  return { lastCameraPosition, setCameraFly };
}
