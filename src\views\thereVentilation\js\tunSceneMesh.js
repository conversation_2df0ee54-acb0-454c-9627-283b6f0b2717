import { BoxGeometry, GridHelper, Mesh, MeshBasicMaterial, Vector3 } from 'three';
import { createCss3DText, createCss3DSprite, createCss3DObj } from '@/three/css3DRender';
import { setMeshScale, setPosition, changeMeshOrientation } from '@/three/utils/meshUtils';
import { gltfLoader, textureLoader } from '@/three/utils/loader';
import { initMatcapThree } from '@/three/effect/matcap';
import { calculateDistance } from "@/views/thereVentilation/js/tunUtils.js";
// 创建经纬格
export function createAGrid({ divisions = 20, size = 20000, color1 = '#888888', color2 = '', scale = 1.8, xSize = 0, zSize = 0, xMin = 0, zMin = 0 }) {
    const gridHelper = new GridHelper(size, divisions, color1);
    for (var i = 0; i <= divisions; i++) {
        const [x, y, z] = [(-size / 2 - (size / divisions / 2)), 1, (size / 2) - i * (size / divisions)]
        const [x1, y1, z1] = [(size / 2) - i * (size / divisions), 1, (-size / 2 - (size / divisions / 2))]
        const textX = i * (zSize / divisions) + xMin
        const textZ = i * (xSize / divisions) + zMin
        gridHelper.add(createCss3DText([-x, y, z], Math.round(textX), 5))
        gridHelper.add(createCss3DText([x1, y1, z1], Math.round(textZ), 5))
    }
    return gridHelper
}

// 添加巷道标签
export function addTunLable(scene, tunPointsAll, tunNameList, numberLevel) {
    const spriteMeshArr = [];
    tunPointsAll.forEach((val, i) => {
        const { TunName, TunID, Level } = tunNameList.find(v => v.TunID == val.mid);
        if (!TunName) return;
        const index = tunNameList.findIndex(v => v.TunID == val.mid);
        const len = val.length;
        if (TunName == null || TunName == 'null' || TunName == 'undefined') return;
        if (len <= 1 && index != i) return;
        
        // 添加标注
        const { x, y, z } = val.middlePoint;
        const sprite = createSpriteLabel(x, y, z, TunName, TunID, numberLevel);
        if (Level === 0) {
            sprite.visible = false;
             sprite.showState = false; 
           
        } else {
            sprite.visible = true;
            sprite.showState = true;
        }
        
        sprite.Level = Level;
        sprite.labelType = 'tunLabel';
        spriteMeshArr.push(sprite);
    });
    return spriteMeshArr;
}

export function createSpriteLabel(x, y, z, TunName, TunID, numberLevel) {
    // 根据缩放层级计算标签大小
    const scale = calculateLabelScale(numberLevel);
    
    // 添加精灵标注
    const sprite = createCss3DSprite('./textures/data_label.png', TunName, scale, `
        <div class="data_label_box">
            <div class="data_label">
                <span>${TunName}</span>
            </div>
        </div>
    `);
    
    sprite.position.set(x, y, z); // 设置标签的位置
    sprite.mid = TunID;
    sprite.updateSize = (newLevel) => {
        const newScale = calculateLabelScale(newLevel);
        sprite.scale.set(newScale, newScale, newScale);
    };
    return sprite;
}

// 根据缩放层级计算标签大小的函数
function calculateLabelScale(zoomLevel) {
    // zoomLevel 越小（视图越远），标签越小；zoomLevel 越大（视图越近），标签越大
    return Math.max(0.8, zoomLevel);
}
// 添加通风机井
const mat = initMatcapThree({ path: './textures/66.png' })
export function loadingTfjj(scene, x, y, z, scale) {
    gltfLoader().load('./model/通风1.glb', (gltf) => {
        setPosition(gltf.scene, x + 60, y + 15, z + 40);
        setMeshScale(gltf.scene, scale);
        // gltf.scene.rotateY(Math.PI * (Math.random() * 1 + 0.2))
        scene.add(gltf.scene)
        gltf.scene.traverse(v => {
            if (typeof v.material != 'undefined') {
                v.material = mat;
            }
        })
    })
}
// const mat2 = initMatcapThree({ path: './textures/444.png' })
//添加设置设施并添加标签标注
export function loadingEquipment(path, position = [0, 0, 0], scale, text, callback = () => { }) {
    gltfLoader().load(path, (gltf) => {
        // setPosition(gltf.scene, );
        let mesh = gltf.scene;
        setPosition(mesh, ...position);
        setMeshScale(mesh, scale);
        mesh.renderOrder = 1;
        mesh.meshType = 'device';

        const c3Sprite = createCss3DSprite('', text, 1, `
            <div class="data_label_box1">
                <div class="data_label">
                    <span  >${text}</span>
                </div>
            </div>
            `)
        const [x, y, z] = position;
        setPosition(c3Sprite, x, y, z);
        c3Sprite.visible = false;
        c3Sprite.showState = true;
        c3Sprite.labelType = 'equipmentLabel'
        callback(mesh, c3Sprite);
    })
}
// 应用
// loading 风机 并设置物体朝向 
export function loadingTfj({ spriteLabelGroup, position = [0, 0, 0], positionList, deviceGroup, deviceInfo, callback = () => { } }) {
    const num = computedTargetPoint(position, positionList)
    loadingEquipment('./model/tfj.glb', position, 100, '主通风机', (mesh, sprit) => {
        changeMeshOrientation(mesh, positionList[num], new Vector3(1, 0, 0));
        spriteLabelGroup.add(sprit);
        deviceGroup.add(mesh);
        mesh.deviceInfo = deviceInfo;
        mesh.mid = deviceInfo.FacilityID
        callback(mesh)
    })
}

// loading 风门 并设置物体朝向 
export function loadingFm({ spriteLabelGroup, position = [0, 0, 0], positionList, deviceGroup, deviceInfo, callback = () => { } }) {
    const num = computedTargetPoint(position, positionList)
    loadingEquipment('./model/airDoor.glb', position, 1.4, '风门', (mesh, sprit) => {
        changeMeshOrientation(mesh, positionList[num], new Vector3(1, 0, 0));
        spriteLabelGroup.add(sprit);
        deviceGroup.add(mesh);
        mesh.deviceInfo = deviceInfo;
        mesh.mid = deviceInfo.FacilityID
        callback(mesh)
    })
}
// loading 风窗 并设置物体朝向 
export function loadingFc({ spriteLabelGroup, position = [0, 0, 0], positionList, deviceGroup, deviceInfo, callback = () => { } }) {
    const num = computedTargetPoint(position, positionList)
    loadingEquipment('./model/airWindow.glb', position, 1.2, '风窗', (mesh, sprit) => {
        changeMeshOrientation(mesh, positionList[num], new Vector3(1, 0, 0));
        spriteLabelGroup.add(sprit);
        deviceGroup.add(mesh);
        mesh.deviceInfo = deviceInfo;
        mesh.mid = deviceInfo.FacilityID
        callback(mesh)
    })
}
// loading 传感器 并设置物体朝向 
export function loadingCgq({ spriteLabelGroup, position = [0, 0, 0], text, positionList, deviceGroup, deviceInfo, callback = () => { } }) {
    const num = computedTargetPoint(position, positionList)
    loadingEquipment('./model/传感器.glb', position, 1, text ?? '传感器', (mesh, sprit) => {
        changeMeshOrientation(mesh, positionList[num], new Vector3(0, 0, 1));
        spriteLabelGroup.add(sprit);
        deviceGroup.add(mesh);
        mesh.deviceInfo = deviceInfo;
        mesh.mid = deviceInfo.FacilityID
        callback(mesh)
    })
}
// 加载局部通风机掘进嘴
export function loadingCgqTuyere({ position = [0, 0, 0], scale = 1, text, positionList, deviceInfo, callback = () => { } }) {
    gltfLoader().load('./model/localFanTuyere.glb', (gltf) => {
        let mesh = gltf.scene;
        setPosition(mesh, ...position);
        setMeshScale(mesh, 4);
        mesh.renderOrder = 1;
        mesh.meshType = 'localFanTuyere';
        changeMeshOrientation(mesh, positionList[1], new Vector3(0, -1, 0));
        callback(mesh);
    })
}
// 硐室
export function loadingChamber({ spriteLabelGroup, scene, scale = 1, position = [0, 0, 0], text, positionList, callback = () => { } }) {
    const num = computedTargetPoint(position, positionList)
    loadingEquipment('./model/chamber.glb', position, 1, text ?? '避难硐室', (mesh, sprit) => {
        // changeMeshOrientation(mesh, positionList[num], new Vector3(1, 0, 0));
        spriteLabelGroup.add(sprit);
        mesh.meshType = '避难硐室';
        setMeshScale(mesh, scale);
        mesh.add(addAmbientLight(0xffffff, 0.5))
        scene.add(mesh);
        callback(mesh)
    })
}
// 巷道风门 
export function loadingLxAirDoor({ spriteLabelGroup, scene, scale = 1, position = [0, 0, 0], text, positionList, callback = () => { } }) {
    const num = computedTargetPoint(position, positionList)
    loadingEquipment('./model/lxAirDoor.glb', position, 1, text ?? '风门', (mesh, sprit) => {
        // changeMeshOrientation(mesh, positionList[num], new Vector3(1, 0, 0));
        spriteLabelGroup.add(sprit);
        mesh.meshType = '风门';
        mesh.add(addAmbientLight(0xffffff, 0.5))
        setMeshScale(mesh, scale);
        scene.add(mesh);
        callback(mesh)
    })
}

// 计算距离最近的点作为目标点
function computedTargetPoint(position, positionList) {
    let num = 0;
    let minDistance = 0;
    // 查找距离该点最近的巷道点位
    positionList.forEach((v, i) => {
        const distance = calculateDistance(new Vector3(...position), v);
        i == 0 && (minDistance = distance);
        if (distance < minDistance) {
            minDistance = distance;
            num = i;
        }
    })
    num = (num + 1) == positionList.length ? num : num + 1
    return num
}



import { canvasTexture2 } from '@/three/material/canvasTexture';
import { addAmbientLight, addSpotLight } from '@/three/utils/light';
const mat2 = initMatcapThree({ path: './textures/7.png' })
const boxGeo = new BoxGeometry(15, 2, 12);
// 添加采区工作面
export function miningAreaWorkingFace(scene, position, scale, text, callback = () => { }) {

    // 创建材质数组
    const materials = [
        mat2,  // 前面
        mat2,  // 后面
        new MeshBasicMaterial({ map: canvasTexture2(text ?? '采区工作面').texture }),  // 上面
        mat2,  // 下面
        mat2,  // 左面
        mat2   // 右面
    ];
    const mesh = new Mesh(boxGeo, materials)
    setPosition(mesh, ...position);
    setMeshScale(mesh, scale);
    scene.add(mesh);
}