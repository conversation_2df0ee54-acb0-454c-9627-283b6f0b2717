import { Shape, Path, CatmullRomCurve3, ExtrudeGeometry, LineSegments, BufferGeometry, LineBasicMaterial, WireframeGeometry } from 'three';
// 创建拱形巷道模型
export function createArchShapeGeo(points, radius, option = { depth: 10 }) {
    const curve = new CatmullRomCurve3(points);

    const shape = new Shape();
    // 绘制图形
    shape.moveTo(0, 0)
    shape.arc(0, 0, radius, Math.PI / 2, -Math.PI / 2);
    shape.moveTo(0, -radius)
    shape.lineTo(radius / 2, -radius)
    shape.lineTo(radius / 2, radius)

    const path = new Path()
    path.moveTo(0, 0)
    path.arc(0, 0, radius, Math.PI / 2, -Math.PI / 2);
    path.moveTo(0, -radius)
    path.lineTo(radius / 2, -radius)
    path.lineTo(radius / 2, radius)
    path.lineTo(0, radius)
    shape.holes.push(path)

    const geometry = new ExtrudeGeometry(
        shape, //扫描轮廓
        {
            extrudePath: curve,//扫描轨迹
            steps: 10,//沿着路径细分精度，越大越光滑
            bevelEnabled: false,
            bevelSize: 0,
            ...option
        }
    );
    return geometry
}

// 创建圆形巷道模型
export function createCircleShapeGeo(points, radius, option = {}) {
    const shape = new Shape();
    // 绘制图形
    shape.moveTo(0, 0)
    shape.arc(0, 0, radius, 0, 2 * Math.PI);

    // 圆形打孔
    const path = new Path()
    path.moveTo(0, 0)
    path.arc(0, 0, radius - 0.1, 0, Math.PI * 2);
    shape.holes.push(path)

    const curve = new CatmullRomCurve3(points);
    const geometry = new ExtrudeGeometry(
        shape, //扫描轮廓
        {
            extrudePath: curve,//扫描轨迹
            steps: 3,//沿着路径细分精度，越大越光滑
            ...option
        }
    );
    return geometry
}

// 创建梯形巷道
export function creartTrapezoidShapeGeo(points, radius, ishollow, option = {}) {
    const shape = new Shape();
    // 绘制图形
    shape.moveTo(-radius * 0.625, radius * 0.625)
    shape.lineTo(radius * 0.625, radius * 1.2)
    shape.lineTo(radius * 0.625, -radius * 1.2)
    shape.lineTo(-radius * 0.625, -radius * 0.625)

    // 圆形打孔
    const path = new Path()
    const pathRadius = radius - 0.2
    path.moveTo(-pathRadius * 0.625, pathRadius * 0.625)
    path.lineTo(pathRadius * 0.625, pathRadius * 1.2)
    path.lineTo(pathRadius * 0.625, -pathRadius * 1.2)
    path.lineTo(-pathRadius * 0.625, -pathRadius * 0.625)
    ishollow && shape.holes.push(path)

    const curve = points.length && new CatmullRomCurve3(points);
    const geometry = new ExtrudeGeometry(
        shape, //扫描轮廓
        {
            extrudePath: points.length ? curve : null,//扫描轨迹
            steps: 10,//沿着路径细分精度，越大越光滑
            ...option
        }
    );
    return geometry
}

// 装饰形状配置
const params = {
    numberOfPoints: 2,
    radialSegments: 10,
    elbowNum: 3,
    elbowOffset: 0.33,
    radius: 5,
    wireframe: false,
};
// 创建巷道外部的装饰 
import { TubePath } from '@/three/mesh/TubePath'
export function recreate(tube, points, radius, shape) {
    const path = new CatmullRomCurve3(points);
    tube.geometry = new TubePath(path, TubePath.pathToUMapping(path, params.elbowNum, params.elbowOffset), radius ?? params.radius, params.radialSegments, false, shape);
    if (!tube.wire) {
        tube.wire = new LineSegments(new BufferGeometry(), new LineBasicMaterial());
        tube.wire.visible = params.wireframe;
        tube.add(tube.wire);
    }
    tube.wire.geometry = new WireframeGeometry(tube.geometry);
}
