import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Vector3, LOD, Object3D } from 'three';
import { createCss3DText, createCss3DSprite } from '@/three/css3DRender';
// 设置x方向的贴图重复数量
export function setMapRepeat(mesh, points) {
    let distance = 0;
    for (let i = 0; i < points.length - 1; i++) {
        distance += calculateDistance(points[i], points[i + 1]);
    }
    // let num = Math.ceil(distance / 2000);
    mesh.material.map.repeat.set(1, 3);
}
// 求巷道的长度 根据巷道坐标点位进行计算
export function computeTunLenght(points) {
    let distance = 0;
    for (let i = 0; i < points.length - 1; i++) {
        distance += calculateDistance(points[i], points[i + 1]);
    }
    return distance;
}
export function calculateDistance(pointA, pointB) {
    if (pointB && pointA) {
        const vec1 = new Vector3(pointA.x, pointA.y, pointA.z)
        const vec2 = new Vector3(pointB.x, pointB.y, pointB.z)
        // const dx = pointB.x - pointA.x;
        // const dy = pointB.y - pointA.y;
        // const dz = pointB.z - pointA.z;
        // return Math.sqrt(dx * dx + dy * dy + dz * dz);
        return vec1.distanceTo(vec2);
    }
}
// canvas纹理
export function createCanvas(text) {
    const canvas = document.createElement("canvas");
    const arr = text.split(""); //分割为单独字符串
    let num = 0;
    const reg = /[\u4e00-\u9fa5]/;
    for (let i = 0; i < arr.length; i++) {
        if (reg.test(arr[i])) { //判断是不是汉字
            num += 1;
        } else {
            num += 0.5; //英文字母或数字累加0.5
        }
    }
    // 根据字符串符号类型和数量、文字font-size大小来设置canvas画布宽高度
    const h = 120; //根据渲染像素大小设置，过大性能差，过小不清晰
    const w = h + num * 32;
    canvas.width = w;
    canvas.height = h;
    const h1 = h * 0.8;
    const c = canvas.getContext('2d');
    // 定义轮廓颜色，黑色半透明
    c.fillStyle = "rgba(0,255,255,0.6)";
    // 绘制矩形
    c.fillRect(0, 0, w, h1); // 填充矩形
    c.fill();
    // 绘制箭头
    c.beginPath();
    const h2 = h - h1;
    c.moveTo(w / 2 - h2, h1);
    c.lineTo(w / 2 + h2, h1);
    c.lineTo(w / 2, h * 0.9);
    c.fill();
    // 文字
    c.beginPath();
    c.translate(w / 2, h1 / 2);
    c.fillStyle = "#ffffff"; //文本填充颜色
    c.font = "bold 32px 宋体"; //字体样式设置
    c.textBaseline = "middle"; //文本与fillText定义的纵坐标
    c.textAlign = "center"; //文本居中(以fillText定义的横坐标)
    c.fillText(text, 0, 0);
    return canvas;
}

// 创建经纬格
export function createAGrid({ divisions = 20, size = 20000, color1 = '#81d4fa', color2 = '', scale = 1.8, xSize = 0, zSize = 0, xMin = 0, zMin = 0 }) {
    const gridHelper = new GridHelper(size, divisions, color1);
    for (var i = 0; i <= divisions; i++) {
        const [x, y, z] = [(-size / 2 - (size / divisions / 2)), 1, (size / 2) - i * (size / divisions)]
        const [x1, y1, z1] = [(size / 2) - i * (size / divisions), 1, (-size / 2 - (size / divisions / 2))]
        const textX = i * (zSize / divisions) + xMin
        const textZ = i * (xSize / divisions) + zMin
        gridHelper.add(createCss3DText([-x, y, z], Math.round(textX), 5))
        gridHelper.add(createCss3DText([x1, y1, z1], Math.round(textZ), 5))
    }
    return gridHelper
}

// 添加标签
export function addTunLable(scene, tunPointsAll, tunNameList,numberLevel) {
    const spriteMeshArr = []
    tunPointsAll.forEach((val, i) => {
        const { TunName, TunID } = tunNameList.find(v => v.TunID == val.mid);
        const len = val.length
        if (TunName == null || TunName == 'null' || TunName == 'undefined') return;
        if (len <= 1) return;
        // 添加标注
        const { x, y, z } = val.middlePoint;
        const sprite = createSpriteLabel(x, y, z, TunName, TunID,numberLevel);
        sprite.visible = false;
        sprite.showState = true;
        sprite.labelType = 'tunLabel';
        spriteMeshArr.push(sprite);
    })
    return spriteMeshArr;
}
export function  createSpriteLabel(x, y, z, TunName, TunID,numberLevel) {
    // 添加精灵标注
    const sprite = createCss3DSprite('./textures/data_label.png', TunName, numberLevel, `
            <div class="data_label_box">
                <div class="data_label">
                    <span>${TunName}</span>
                </div>
            </div>
            `)

    sprite.position.set(x, y, z);// 设置标签的位置
    sprite.mid = TunID;
    return sprite;
}


// 点位换算
export function positionTransform(x, y, z, { middlePosition, n = 2, addX = 0, addY, addZ, minPosition, type = 0 }) {
    const [x1, y1, z1] = middlePosition; // x  y  z
    if (type == 0) {
        const x2 = (x - x1) * n;
        const z2 = (z - z1) * n; // y
        const y2 = (y - y1) * n; // z
        return new Vector3(x2, z2, -y2);
    } else {
        // const x2 = Number((x / n + x1).toFixed(3));
        // const y2 = Number((Math.abs(z) / n + y1).toFixed(3));
        // const z2 = Number((y / n + z1).toFixed(3));
        const x2 = x / n + x1;
        const y2 = -z / n + y1;
        const z2 = y / n + z1;
        return { x: x2, y: y2, z: z2 };
    }

}
// const y2 = z / n + z1 - (Math.abs(z - minPosition[1]) + 10);

// 根据用途类型添加对应物体
export function addTunObject(tunMesh, Lx) {
    switch (tunUseTypeList) {
        case 41:
            break;
        case '2':
            break;
    }
}