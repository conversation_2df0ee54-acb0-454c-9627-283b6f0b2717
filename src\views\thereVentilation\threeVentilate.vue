<template>
    <div class="threeDataBox">
        <!-- 三维初始显示 -->
        <div class="canvasBox" ref="sceneDiv"  v-if="is3D"  ></div>
         <twoTunEdit 
      v-else 
        ref="twoDRef" 
      class="two-d-container"
    />
        <CenterTitle @mouseover="disableEvents = false" @mouseout="disableEvents = true" title="全矿预览"></CenterTitle>
        <!-- 悬浮框 -->
        <TunInfo :currentTun="currentTun" />
        <!-- 右边侧边栏 -->
        <!-- <ToolBar @getZnzDom="getZnzDom" :changeTun="changeTun1" :changeShape="changeShape" :cameraAngle="cameraAngle" /> -->
        <ToolBar :is3D="is3D"  @mouseover="disableEvents = false" @mouseout="disableEvents = true" @getZnzDom="getZnzDom"
            :cameraAngle="cameraAngle" :changeSpriteLabel="changeSpriteLabel"  @toggle-3d-mode="handleToggle3D"></ToolBar> />
        <!-- 底部标签栏 -->
        <BottomTabBar v-model="disableEvents"  @onHighlight="handleHighlight" ></BottomTabBar>
        <!-- 图例 -->
        <div class="legend fixed top-100px right-20px">
            <div class="legend-item mb-20px">
                <div class="color-block intake"></div>
                <span class="label">进风</span>
            </div>
            <div class="legend-item mb-20px">
                <div class="color-block usage"></div>
                <span class="label">用风</span>
            </div>
            <div class="legend-item mb-20px">
                <div class="color-block return"></div>
                <span class="label">回风</span>
            </div>
            <div class="legend-item">
                <div class="color-block fan"></div>
                <span class="label">局部通风机</span>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch, onUnmounted, watchEffect, provide,computed,nextTick  } from 'vue'
// import { mountRender, cameraControls, currentTun, endCamera, ani, stopRender, tunInit, changeTun, changeShape } from './js/main'
import CenterTitle from '@/components/common/CenterTitle.vue';
import ToolBar from './components/toolbar/toolbar.vue'
import BottomTabBar from './components/bottomTabBar/bottomTabBar.vue'
import twoTunEdit from '../tunEdit/components/2DTunEdit.vue'
import TunInfo from './components/tunInfo/tunInfo.vue'
// import { ApiRoadwayIDInquire, ApiRoadwayLis, roadwayList } from '@/https/encapsulation/threeDimensional'
import mitt from '@/utils/eventHub';
import { onBeforeRouteLeave } from 'vue-router';
import { useThereVentilation } from '@/store/thereVentilation';
const store = useThereVentilation();
const { isFlag, currentTun: currentTunData } = storeToRefs(store);
const disableEvents = ref(true)
// 获取二维组件实例，用于手动触发初始化
const twoDRef = ref(null);
const sceneDiv = ref(null)
const znzDom = ref()
function getZnzDom(dom) {
    znzDom.value = dom
    
}
onBeforeRouteLeave(() => {
    dataReset();
    stopRender();
    removeEvent && removeEvent();
    addEvenetThrottle = null;
})

const is3D = computed(() => store.is3D);


onMounted(() => {
    mountRender(sceneDiv.value, znzDom.value)
    
    // mitt.on('quitLogin', () => {
    //     stopRender();
    // })
});


// 修改管道粗细
const radius = ref(5)
function changeTun1(num) {
    radius.value += num;
    radius.value = radius.value < 3 ? 5 : (radius.value > 9 ? 5 : radius.value);

}



// 导入辅助坐标轴
import { createTun, createPoint, createLine, tun } from './js/tun.js';
import { modelClick, modelHover, dbModelClick } from "@/three/utils/event";
import { useThree } from "@/three/useThree";
import { creartTrapezoidShapeGeo, createCircleShapeGeo, createArchShapeGeo, recreate } from "./js/tunShape";
import { cameraFlyToMesh, moveToCamera } from "@/three/cameraControls";
import { apiTunList, tunInit, tunLabelShowByCamera, getRecentNodeThrottleFn } from '../thereVentilation/js/tunInit';
import { tunOriginList, meshGroup, showOrHideLabels, dynamicLoadingLabel,apiNodeList, pointMeshArr, spriteLabelGroup } from '../thereVentilation/js/tunInit';// 变量
import { throttleFn } from '@/utils/utils';
import { stopEvent } from '@/utils/window';
import { storeToRefs } from 'pinia';
import { createTunOrigin } from './js/tunInitVentilationCalculation';
import { initMatcapThree } from '@/three/effect/matcap';
import { textureLoader } from '@/three/utils/loader';
import * as TWEEN from '@tweenjs/tween.js'
//原来
const endCamera = [-6792, 8180, -7620]
//现在修改  增大z轴负向距离，相机后退，巷道缩小
// const endCamera = [-6792, 8180, -5000];


const { scene, THREE, findMesh, renderer, setPosition, css3Renderer, gsap, changeCurrentMesh, mountRender, outlinePass1, 
limitCameraVerticalAngle, restCamera, cameraControls, camera, addOutlinePass1, addOutlinePass, ani, stopRender, outlinePass } 
    = useThree({ endCamera, isCameraAni: false, isAxes: false, isDepthBuffer: true, isComposer: true })
cameraControls.dollySpeed = 0.32;
cameraControls.truckSpeed = 1.1;
camera.far = 60000;

// camera.position.set(1000, 1000, 1000); 
// 初始化配置
outlinePass.edgeThickness = 4.0;
outlinePass.edgeGlow = 2.0;
outlinePass.edgeStrength = 4.0;
outlinePass.pulsePeriod = 3.0;
outlinePass1.edgeThickness = 4.0;
outlinePass1.edgeGlow = 5.0;
outlinePass1.edgeStrength = 4.0;
outlinePass1.pulsePeriod = 3.0;

const { dataInit, dataReset, refreshCamera, cameraAngle } = tunInit(scene, cameraControls);
dataInit()
store.$patch((state) => {
    setTimeout(() => {
        state.nodeList = apiNodeList.value;
    }, 200)
})
const labelShow = ref(true)
function changeSpriteLabel(val) {
    labelShow.value = val;
    showOrHideLabels(val, 0);
}
let tunMesh = null;

watch(() => store.$state.lineData, (data) => {
    if (tunMesh) { scene.remove(tunMesh) }
    if (data.length == 0) {
        changeSpriteLabel(true);
        tunMesh = null;
        return
    }
    const linePoints = data.map(k => {
        let point = pointMeshArr.find(v => v.mid == k.NodeID).point;
        return point
    }).map(({ x, y, z }) => { return new THREE.Vector3(x, y + 10, z) })
    // 创建一个路径
    const mat = initMatcapThree({ path: './textures/444.png', map: textureLoader().load('./textures/right-run.png') })
    changeSpriteLabel(false);
    tunMesh = tun(linePoints, { myMat: mat, radius: 5, opacity: 1 }).tubeMesh;
    addOutlinePass1([tunMesh], 0x389e0d)
    console.log(addOutlinePass1([tunMesh], 0x389e0d),'%%%%%%%%%%%%%%%%%')
    scene.add(tunMesh)
})
watch(() => store.$state.positioningNodes, (val) => {
    restCamera();
    setTimeout(() => {
        goNodeLocation(val);
    }, 900)
})
let removeEvent = null;

const handleToggle3D = async () => {
  console.log('$$$$$切换巷道数据');
  // 切换前先清理当前场景
  if (is3D.value) {
    console.log(is3D.value, 'is3d切换巷道数据');
    stopRender(); 
    await nextTick(); 

    // 关键：先校验 twoDRef 是否存在
    if (!twoDRef.value) {
      console.error('二维组件未挂载，无法渲染巷道');
      return;
    }

    // 1. 异步获取巷道数据
    const { Result: tunList } = await ApiTunList();
    if (!tunList || !tunList.length) {
      console.warn('未获取到巷道数据，跳过渲染');
      return;
    }

    // 2. 再次校验组件是否仍存在（防止请求期间组件被卸载）
    if (twoDRef.value) {
      twoDRef.value.renderTuns(tunList);
    } else {
      console.error('组件已卸载，终止渲染流程');
    }
  } else {
    // 二维切三维的逻辑（同理增加校验）
    if (twoDRef.value && twoDRef.value.closeImportNode) {
      twoDRef.value.closeImportNode();
    }
    await nextTick(); 
    init3DScene(); 
  }
  store.toggle3DMode(); 
};
// onUnmounted(() => {
//   // 清理三维/二维渲染循环、定时器等
//   stopRender(); // 假设存在停止渲染的方法
//   lineMeshArr.forEach(mesh => {
//     mesh.geometry.dispose();
//     mesh.material.dispose();
//   });
//   lineMeshArr = [];
// });
// 同时在 watch 中确保状态同步
watch(is3D, async (newVal) => {
  if (!newVal) {
    // 二维模式
    await nextTick();
    if (twoDRef.value) {
      twoDRef.value.init2DScene();
      twoDRef.value.handleRender();
    }
  } else {
    // 三维模式
    init3DScene();
  }
}, { immediate: true });

// 关键3：监听状态变化，确保二维组件正确初始化

// 初始化三维场景（复用原有逻辑）
function init3DScene() {
  if (sceneDiv.value) {
    mountRender(sceneDiv.value, znzDom.value);
  
  }
}

// 暂停三维渲染（复用原有逻辑）
function pause3DRender() {
  stopRender(); // 停止三维渲染循环
}


// 鼠标悬浮事件
const shineMeshList = ref([])// 全部高亮列表
const moveMesh = ref()// 当前悬浮的物体高亮
const clickMesh = ref()// 当前点击的物体高亮
const currentEvent = ref('')

// 物体检测
let addEvenetThrottle = throttleFn(addEvent, 1500);
// watch(disableEvents, (val) => {
//     console.log(val);
// })
function addEvent() {
    /*单击*/
    const { clickRemove } = modelClick(meshGroup.children, camera, (currentMesh, event) => {
        if (!disableEvents.value) return
        if (currentMesh && isFlag.value) {
            const mesh = currentMesh.object.parent.children.length == 1 ? currentMesh.object.parent : currentMesh.object
            currentTunData.value = currentMesh.object.originPoints;
            addOutlinePass([mesh])
        }
    })

    /*悬浮*/
    const { hoverRemove } = modelHover(meshGroup.children, camera, (currentMesh, event) => {
        if (!disableEvents.value) return
        if (isFlag.value) return;
        if (currentMesh) {
            const mesh = currentMesh.object.parent.children.length == 1 ? currentMesh.object.parent : currentMesh.object
            commonEvent('mousemove', mesh, event)
        } else {
            commonEvent('mousemove', '', event)
        }
    })
    /*双击*/
    const { dblRemove } = dbModelClick(meshGroup.children, camera, (currentMesh, event) => {
        if (!disableEvents.value) return
        if (isFlag.value) return;
        moveToCamera(cameraControls, 0, [0, 10, 0]);
        cameraControls.fitToBox(currentMesh.object, true);
    })
    function handleRemove() {
        console.log('handle remove');
        dblRemove();
        hoverRemove();
        clickRemove();
    }
    removeEvent = throttleFn(handleRemove, 10);
}
// 点击中线
// let interruptCurrentTun = null;
// function interruptClick(currentMesh) {
//     if (currentMesh) {
//         if (interruptCurrentTun && interruptCurrentTun.mid != currentMesh.mid) {
//             // 恢复上一个
//             interruptCurrentTun.visible = true;
//         }
//         interruptCurrentTun = currentMesh;
//         interruptCurrentTun.visible = false;
//         if (!interruptMiddleLine) {
//             interruptMiddleLine = createLine({ linePoints: interruptCurrentTun.points })
//             scene.add(interruptMiddleLine)
//         } else {
//             interruptMiddleLine.geometry = createLine({ linePoints: interruptCurrentTun.points }).geometry;
//         }
//     }
// }
onMounted(() => {
    addEvent();
    init3DScene();
    const handleWheel = (event) => {
        
    if (!disableEvents.value) return;
    
    event.preventDefault();
    
    const currentZoom = getZoomLevel();
   
    // if (event.deltaY < 0) {
    //   cameraControls.dollyIn();
    // } else {
    //   cameraControls.dollyOut();
    // }
  };
  window.addEventListener('wheel', handleWheel, { passive: false });
  onUnmounted(() => {
    window.removeEventListener('wheel', handleWheel);
  });
})



// 处理子组件传递的巷道 ID
const handleHighlight = (tunIds) => {
 
    outlinePass1.edgeThickness =3.0;//高亮发光描边厚度
    outlinePass1.edgeGlow = 3.0;    // 边缘发光
    outlinePass1.edgeStrength = 3.0;    //高亮描边发光强度
    outlinePass1.pulsePeriod = 3.0;  
  // 筛选匹配的 3D 模型
  const filteredMeshes = [];
  meshGroup.children.forEach(child => {
    const meshTunID = child.mid; // 假设模型用 mid 存储 TunID
    if (meshTunID && tunIds.includes(meshTunID)) {
      filteredMeshes.push(child);
     
    }
  });
if (filteredMeshes.length > 0) {
    addOutlinePass1(filteredMeshes); // 0x20c997是青绿色
     
    const center = calculateMeshesCenter(filteredMeshes);
    moveCameraToTarget(center,filteredMeshes);
  }
  // 执行高亮
//   addOutlinePass1(filteredMeshes, 0x20c997); 


};

// 辅助函数：计算多个模型的中心位置
function calculateMeshesCenter(meshes) {
    // console.log(meshes, 'meshes');
  // 创建包围盒，包含所有高亮模型
  const boundingBox = new THREE.Box3();
  meshes.forEach(mesh => {
    boundingBox.expandByObject(mesh); // 将每个模型纳入包围盒
  });

  // 计算包围盒中心点（即高亮区域的中心）
  const center = new THREE.Vector3();
  boundingBox.getCenter(center);
  console.log(center, 'center');
  return center;
}

// 辅助函数：相机平滑移动到目标位置附近
function moveCameraToTarget(targetCenter,mesh) {
    console.log('变化前的camera', camera);
    console.log(targetCenter, 'targetCenter');
  // 目标位置：在中心点基础上后移一段距离（避免与模型重叠，可根据场景调整）
  
  const targetPosition = new THREE.Vector3(
    targetCenter.x,       // x轴与中心对齐
    targetCenter.y,   // y轴略高于中心（假设y是高度方向）
    targetCenter.z   // z轴后移15单位（距离模型的距离）
  );
  
  // 目标朝向：始终看向中心点
  const targetLookAt = targetCenter.clone();
  moveToCamera(cameraControls, 0, targetLookAt, targetPosition);
  console.log('变化后的camera', camera);
//   // 动画配置：1秒内平滑移动
//   new TWEEN.Tween(camera.position)
//     .to(targetPosition, 1000) // 1000ms=1秒
//     .easing(TWEEN.Easing.Quadratic.InOut) // 缓动效果（先慢后快再慢）
//     .start();

//   // 同时让相机看向中心点（平滑过渡朝向）
//   new TWEEN.Tween(camera.target || new THREE.Vector3())
//     .to(targetLookAt, 1000)
//     .easing(TWEEN.Easing.Quadratic.InOut)
//     .onUpdate(() => {
//       camera.lookAt(targetLookAt); // 更新朝向
//     })
//     .start();

 
}

// 动画循环（确保 Tween 生效）
// function animate() {
//   requestAnimationFrame(animate);
// //   TWEEN.update(); // 驱动动画更新
//   renderer.render(scene, camera);
// }
// animate();

// // 存储鼠标坐标
const currentTun = ref({})
//监听鼠标点击事件
function commonEvent(type, currentMesh, event) {
    currentEvent.value = type
    const shineList = [...shineMeshList.value]
    if (currentMesh) {
        if ((shineMeshList.value.findIndex(v => v.tunName == currentMesh.tunName)) != -1) { return; } // 高亮显示的一组物体不做处理
        const { originPoints, mid } = currentMesh;
        // console.log(currentMesh, 'currentMesh');

        if (mid) {
            const { H, QCalculate, S, V } = tunOriginList.find(v => v.TunID == mid);
            currentTun.value = { x: event.x, y: event.y, H, QCalculate, S, V, tunName: originPoints.TunName, isShow: false }
        }

        switch (type) {
            case 'click':
                currentTun.value.isShow = false;
                clickMesh.value = currentMesh;
                break;
            case 'mousemove':
                currentTun.value.isShow = true;
                // moveMesh.value = currentMesh;
                break;
        }
        if (moveMesh.value && clickMesh.value && moveMesh.value.mid == clickMesh.value.mid) {
            moveMesh.value = null
        }
        clickMesh.value && shineList.push(clickMesh.value)
        moveMesh.value && shineList.push(moveMesh.value)
        addOutlinePass(shineList)
    } else {
        currentTun.value = { isShow: false }
        if (type === 'click') {
            clickMesh.value = null
            moveMesh.value = null
            addOutlinePass(shineList)
        }
    }
}

provide('flyToMesh', flyToMesh);
provide('resetEffect', resetEffect);
provide('addMeshOutline', { addMeshOutlineShine, addOutlinePass, addOutlinePass1 });

// 按名称查找物体进行高亮显示
function addMeshOutlineShine(nameList = [], type) {
    // shineMeshList.value = []
    // meshGroup.children.forEach(mesh => {
    //     if (nameList.includes(mesh.tunName)) {
    //         shineMeshList.value.push(mesh)
    //     }
    // })
    // addOutlinePass(shineMeshList.value)
}
function goNodeLocation(NodeID, addOffset = 100, isAddOutlinePass = true) {
    let mesh = pointMeshArr.find(v => v.mid == NodeID);
    addOutlinePass1([mesh], 0x20c997);
    cameraFlyToMesh(cameraControls, { mesh: mesh, paddingTop: addOffset, paddingRight: addOffset, paddingBottom: addOffset, paddingLeft: addOffset });
}
// 飞跃到物体附近
function flyToMesh(id) {
    const mesh = meshGroup.children.find(v => v.mid == id)
    if (!mesh) return;
    const [point1, point2] = [mesh.points[0], mesh.points.at(-1)];
    const { x: x1, y: y1, z: z1 } = point1;
    const { x: x2, y: y2, z: z2 } = point2;
    const [x, y, z] = [(x1 + x2) / 2, (y1 + y2) / 2, (z1 + z2) / 2]
    // shineMeshList.value.push(mesh1)
    resetEffect()
    addOutlinePass1([mesh], 0xfd7e14)
    setTimeout(() => {
        moveToCamera(cameraControls, 0, [0, 500, 0]);
        cameraControls.fitToBox(mesh, true);
    }, 1000)
}

// 重置效果
function resetEffect() {
    addOutlinePass([])
    addOutlinePass1([])

//     
    refreshCamera(() => {
        restCamera()
    })
}
const zoomLevel = ref(10); // 默认缩放等级

// 获取缩放等级函数标签的地方
function getZoomLevel() {
  const distance = camera.position.distanceTo(new THREE.Vector3(0, 0, 0));
  const maxDistance = 9200;
  const minDistance = 100;
  const clamped = Math.min(Math.max(distance, minDistance), maxDistance);
  const normalized = 1 + ((clamped - minDistance) / (maxDistance - minDistance)) * 8;
//   console.log(Math.round(normalized),'Math.round(normalized)');
  return Math.round(normalized);
}
// 修改渲染循环
ani((...args) => {
    zoomLevel.value = getZoomLevel();
    dynamicLoadingLabel(camera, labelShow.value, zoomLevel.value);
    if (tunLabelShowByCamera) {
        tunLabelShowByCamera(camera, labelShow.value, zoomLevel.value);
    }
    // init3DScene();
});
watch(zoomLevel, (newLevel) => {
    spriteLabelGroup.children.forEach(mesh => {
        if (mesh.updateSize) {
            mesh.updateSize(newLevel);
        }
    });
});
// 传递给标签创建函数
provide('zoomLevel', zoomLevel);


// 当前巷道形状
const tunShape = ref('circle')
// 修改巷道粗细方法
function changeTun(radius1) {
    // 修改巷道粗细
    meshGroup.children.forEach((v, i) => {
        const radius = radius1 ?? v.radius;
        v.children[0].geometry = new THREE.SphereGeometry(radius + 0.12, 32, 32);
        if (tunShape.value == 'circle') {
            let geo = createCircleShapeGeo(v.points, radius)
            v.geometry = geo
            recreate(meshGroup.children[i], meshGroup.children[i].points, radius + 0.6, tunShape.value)
        } else if (tunShape.value == 'arch') {
            let geo = createArchShapeGeo(v.points, radius)
            v.geometry = geo
            recreate(meshGroup.children[i], meshGroup.children[i].points, radius + 0.6, tunShape.value)
        } else {
            let geo = creartTrapezoidShapeGeo(v.points, radius)
            v.geometry = geo
            recreate(meshGroup.children[i], meshGroup.children[i].points, radius + 0.8, tunShape.value == 'trapezoid' ? 'arch' : tunShape.value)
        }
    })
}
// 修改管道形状
function changeShape(shape) {
    tunShape.value = shape;
    switch (shape) {
        case 'circle':
            changeTun()
            break;
        case 'arch':
            changeTun()
        case 'trapezoid':
            changeTun()
            break;
    }
}






</script>
<style lang="scss"></style>

<style lang="scss" scoped>
@use './assets/index.scss';
.data_label_box {
    display: inline-block;
    transform: scale(var(--scale, 1.5)); /* 默认放大 1.5 倍 */
    transform-origin: center;
}

.data_label {
    font-size: 14px; /* 固定字体大小，或根据 scale 动态调整 */
    white-space: nowrap;
    background: rgba(0, 0, 0, 0.7);
    padding: 4px 8px;
    border-radius: 4px;
}
.two-d-container {
  width: 100%;
  height: 100vh; /* 与三维容器保持一致的尺寸 */
  position: relative;
  overflow: hidden;
}

/* 整体样式 */
.legend {
    font-family: Arial, sans-serif;
    border: 1px solid #332E26;
    padding: 20px;
    width: 200px;
    border-radius: 8px;
    height: auto;
    background-color: #332E26;
}

/* 图例项样式 */
.legend-item {
    display: flex;
    align-items: center;

    /* 颜色块样式 */
    .color-block {
        width: 40px;
        height: 20px;
        margin-right: 10px;
        border-radius: 4px;
    }

    /* 文字样式 */
    .label {
        font-size: 14px;
        color: #ffb30f;
    }
}



/* 自定义颜色 */
.intake {
    background-color: #00db77;
}

/* 进风 */
.usage {
    background-color: #ffb30f;
}

/* 用风 */
.return {
    background-color: #dd212f;
}

/* 回风 */
.fan {
    background-color: #0D00A4;
}

/* 局部通风机 */

:v-deep(.el-button .el-icon) {
    height: 0.5em;
    width: 0.5em;
}

.norem-box {
    /* padding: 20px;
    background-color: #000; */
    color: white;
    margin: 20px;
    display: flex;


    .norem-btn {
        /* postcss-pxtorem-disable */
        display: flex;
        justify-content: center;
        align-items: center;
        width: 55px;
        height: 25px;
        font-size: 12px;
        border-radius: 0;
        cursor: pointer;
        background-color: rgba(0, 255, 255, 0.6);
        margin: 0.5px;
        color: white;

        &:hover {
            background-color: rgba(0, 255, 255, 0.8);
        }
    }

    .norem-btn1 {
        width: auto;

        &:hover {
            background-color: rgba(0, 255, 255, 0.6);
        }

        .norem-btn1-1 {
            background-color: rgba(255, 255, 255, 0.8);
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: #fff;
            display: flex;
            justify-items: center;
            align-items: center;

            &:hover {
                background-color: rgba(255, 255, 255, 1.0);
            }
        }
    }
}

.scene {
    width: 100vw;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
}
</style>