<template>
    <div class="fixed left-0 bottom-0  z-99 w-410px h-full bg-[#262626] opacity-90">
        <span class="text-white text-30px">{{ title }}</span><br />
        <span class="text-white text-30px">{{ data.TunID }}</span>
        <div v-for="(v, i) in data.NodeList" :key="v.x" class="text-white text-18px line-height-40px">
            <span class="text-[#00bcf2]">坐标{{ i + 1 }}:</span>&nbsp;
            <span class="text-[#fff59d]">X: </span><span>{{ v.x }}</span>&nbsp;
            <span class="text-[#fff59d]">Y: </span><span>{{ v.z }}</span>&nbsp;
            <span class="text-[#fff59d]">Z: </span><span>{{ v.y }}</span>&nbsp;
        </div>
        <div v-for="(v, i) in data.distanceList" :key="v.x" class="text-white text-18px line-height-40px">
            <span class="text-[#ff8c00]">距离{{ i + 1 }}:</span>&nbsp;<span class="text-[#ede7f6]">{{ v
                }}</span>&nbsp;
            <span class="text-[#ede7f6]">坐标{{ i + 1 }}->坐标{{ i + 2 }}</span>&nbsp;
        </div>
    </div>

</template>
<script setup>
import { ref, watch } from 'vue';

const props = defineProps(['currentTun'])
// 监听鼠标位置 创建悬浮框
const title = ref()
const data = ref({})
watch(() => props.currentTun, (val) => {
    if (val) {
        const { NodeList, distanceList, tunName, TunID } = val;
        data.value.NodeList = NodeList;
        data.value.distanceList = distanceList;
        data.value.TunID = TunID;
        title.value = tunName;
    }
})


</script>
<style lang="scss" scoped></style>