import { ref, reactive } from "vue";
// 导入辅助坐标轴
import { setMapRepeat, createAGrid, addTunLable } from "./tunUtils";
import { createTun } from './tun.js';
import { modelClick, modelHover, dbModelClick } from "@/three/utils/event";
import { useThree } from "@/three/useThree";
import { creartTrapezoidShapeGeo, createCircleShapeGeo, createArchShapeGeo, recreate } from "./tunShape";
import { useThereVentilation } from '@/store/thereVentilation';
// import 
import { moveToCamera } from "@/three/cameraControls";

const store = useThereVentilation()
const endCamera = [-4000, 5000, 4000]
const { scene, THREE, findMesh, renderer, css3Renderer, changeCurrentMesh, mountRender, limitCameraVerticalAngle, restCamera, cameraControls, camera, addOutlinePass1, addOutlinePass, ani, stopRender, outlinePass } = useThree({ endCamera, isCameraAni: false, isAxes: false, isDepthBuffer: true });
export { cameraControls, endCamera, mountRender, restCamera, addOutlinePass1, addOutlinePass, ani, stopRender };



// 添加经纬格
scene.add(createAGrid())
// 初始化配置
outlinePass.edgeThickness = 4.0;
outlinePass.edgeGlow = 2.0;
outlinePass.edgeStrength = 4.0;
outlinePass.pulsePeriod = 3.0;

// 获取接口数据
import { roadwayList } from '@/https/encapsulation/threeDimensional'

const tunNameList = [] // 巷道名称 和 需要展示的相关数据
const tunNodeList = [] //对应巷道名称 每条巷道节点的数据
let tubeMeshArr = []; // 存储巷道三维物体数据
let tubeTextureArr = []; // 存储拱形巷道纹理数据
let pointMeshArr = []; // 存储三维巷道连接小球数据
const tubePointsAll = [] // 存储全部巷道点位信息 基于相对坐标生成的数组信息
let spriteMeshArr = [] // 存储精灵标签物体数组
// 相对坐标点
const relativePoint = [38413339, 616 + 60.5, 4004542] // 其他点基于这个点生成相对位置
// 创建loading加载

// setTimeout(() => {
//     loading.close()
// }, 2000)
store.$patch({ loadingShow: true })
export const tunInit = function () {
    roadwayList().then(({ Result }) => {
        console.log(Result, 'Result');
        if (!Result) return
        let len = Result.length;
        for (let i = 0; i < len; i++) {
            const [x1, y1, z1] = relativePoint
            const { TunName, TunID, Nodes, Ventflow, S, Q, V, H } = Result[i];
            tunNameList.push({ TunName, TunID, Ventflow, S, Q, V, H })
            const nodeLen = Nodes.length;
            let tubePoints = []
            const tubeOther = []
            for (let j = 0; j < nodeLen; j++) {
                const { NodeID, NodeName, x, y, z } = Nodes[j]; // 数据里z代表y
                const n = 2
                const x2 = (x - x1) * n;
                const y2 = (z - y1) * n;
                const z2 = (y - z1) * n;
                tubePoints.push(new THREE.Vector3(x2, y2, z2))
                tubeOther.push({ NodeID, NodeName, NodePoint: [x2, y2, z2] })
            }
            tunNodeList.push(tubeOther); //存储节点数据信息
            tubePointsAll.push(tubePoints);
        }

        tubePointsAll.forEach((v, i) => {
            const { TunID, Ventflow, TunName } = tunNameList.at(i)
            const { meshArr, roadArr, sphereArr } = createTun(scene, v, TunID, Ventflow, 'arch', TunName);
            tubeMeshArr.push(...meshArr); tubeTextureArr.push(...roadArr); pointMeshArr.push(...sphereArr)
        })
    }).then(() => {
        spriteMeshArr = addTunLable(scene, tubePointsAll, tunNameList,numberLevel) // 添加标签
        // addMeshOutlineShine()

        // 去除重复的节点
        pointMeshArr = checkRepet('point', pointMeshArr);

        pointMeshArr.forEach(v => {
            scene.add(v);
        })

        setTimeout(() => {
            store.$patch({ loadingShow: false })
        }, 1200)
    })
}
tunInit();
// 检差巷道中是否存在重复绘制情况
function checkRepet(field, orginalArr) {
    const newPointMeshArr = [];
    const pointVec3Arr = orginalArr.map((v, i) => {
        if (!v[field]) return null;
        if (v[field] instanceof Array) {
            let str = ``;
            v[field].length && v[field].forEach((v2) => {
                const { x, y, z } = v2;
                str += `${x}${y}${z}-`
            })
            return str
        } else {
            const { x, y, z } = v[field];
            return `${x}${y}${z}`
        }
    })
    pointVec3Arr.forEach((item, index) => {
        if (pointVec3Arr.indexOf(item) === index) {
            newPointMeshArr.push(orginalArr[index]);
        }
    });
    return newPointMeshArr;
}




// 鼠标悬浮事件
const shineMeshList = ref([])// 全部高亮列表
const moveMesh = ref()// 当前悬浮的物体高亮
const clickMesh = ref()// 当前点击的物体高亮
const currentEvent = ref('')

// 物体检测
/*单击*/
modelClick(tubeTextureArr, camera, (currentMesh, event) => {

    if (currentMesh) {
        const mesh = currentMesh.object.parent.children.length == 1 ? currentMesh.object.parent : currentMesh.object
        commonEvent('click', mesh, event)
    }
})

/*悬浮*/
modelHover(tubeTextureArr, camera, (currentMesh, event) => {
    if (currentMesh) {
        const mesh = currentMesh.object.parent.children.length == 1 ? currentMesh.object.parent : currentMesh.object
        commonEvent('mousemove', mesh, event)
    } else {
        commonEvent('mousemove', '', event)
    }
})
/*双击*/
dbModelClick(tubeTextureArr, camera, (currentMesh, event) => {
    // cameraControls.moveTo(0, 10, 0, true)
    moveToCamera(cameraControls, 0, [0, 10, 0]);
    cameraControls.fitToBox(currentMesh.object, true);
})

// 存储鼠标坐标
export const currentTun = ref({})
//监听鼠标点击事件
function commonEvent(type, currentMesh, event) {
    currentEvent.value = type
    const shineList = [...shineMeshList.value]
    if (currentMesh) {
        if ((shineMeshList.value.findIndex(v => v.tunName == currentMesh.tunName)) != -1) { return; } // 高亮显示的一组物体不做处理
        const { tunName } = currentMesh;
        if (tunName) {
            const { H, Q, S, V } = tunNameList.find(v => v.TunName == tunName);
            currentTun.value = { x: event.x, y: event.y, H, Q, S, V, tunName, isShow: false }
        }

        switch (type) {
            case 'click':
                currentTun.value.isShow = false;
                clickMesh.value = currentMesh;
                break;
            case 'mousemove':
                currentTun.value.isShow = true;
                // moveMesh.value = currentMesh;
                break;
        }
        if (moveMesh.value && clickMesh.value && moveMesh.value.mid == clickMesh.value.mid) {
            moveMesh.value = null
        }
        clickMesh.value && shineList.push(clickMesh.value)
        moveMesh.value && shineList.push(moveMesh.value)
        addOutlinePass(shineList)
    } else {
        currentTun.value = { isShow: false }
        if (type === 'click') {
            clickMesh.value = null
            moveMesh.value = null
            addOutlinePass(shineList)
        }
    }
}

/**
 * 监测分析交互事件
 */
// 按名称查找物体进行高亮显示
export function addMeshOutlineShine(nameList = [], type) {
    // shineMeshList.value = []
    // tubeTextureArr.forEach(mesh => {
    //     if (nameList.includes(mesh.tunName)) {
    //         shineMeshList.value.push(mesh)
    //     }
    // })
    // addOutlinePass(shineMeshList.value)
}

// 修改巷道信息
function modifyTunInformation(color) {
    tubeMeshArr.forEach(v => {
        const meshLine = v.children[0]
        if (color) {
            v.material.color = new THREE.Color(color);
            meshLine.material.color = new THREE.Color(color);
        }
    })
}

// 显示或者隐藏标签信息
function showOrHideLabels(isShow = true) {
    if (!spriteMeshArr.length) return;
    spriteMeshArr.forEach(mesh => {
        mesh.visible = isShow;
    })
}

// 飞跃到物体附近
export function flyToMesh(mid = '10#横贯') {
    const mesh = tubeTextureArr.find(v => v.tunName.includes(mid))
    if (!mesh) return;
    const [point1, point2] = mesh.points;
    const { x: x1, y: y1, z: z1 } = point1;
    const { x: x2, y: y2, z: z2 } = point2;
    const [x, y, z] = [(x1 + x2) / 2, (y1 + y2) / 2, (z1 + z2) / 2]
    const mesh1 = mesh.parent.children.length == 1 ? mesh.parent : mesh
    // shineMeshList.value.push(mesh1)
    resetEffect()
    addOutlinePass1([mesh1])
    setTimeout(() => {
        moveToCamera(cameraControls, [10, 50, 10], [x, y, z])
    }, 1000)
}

// 重置效果
export function resetEffect() {
    addOutlinePass([])
    addOutlinePass1([])
    restCamera()
}

// 导入glb模型
// const gltfLoader = new GLTFLoader();
// gltfLoader.load("./model/通风1.glb", (gltf) => {
//     console.log(gltf, 'gltf');
//     gltf.scene.position.set(50, 20, 50)
//     const mLight = new THREE.DirectionalLight(0xffffff, 1)
//     mLight.position.set(50, 30, 50)
//     scene.add(gltf.scene)
//     scene.add(mLight)
// })


// 渲染函数
ani((...args) => {
    const [time, camera, delta] = args;
    const { x, y, z } = camera.rotation
    // 将弧度转换为度数
    // let yRotate = (180 / Math.PI) * y * 2;
    // console.log(yRotate, 'yRotate');
    // znzDom.style.transform = `rotate(${yRotate}deg)`
    // 在这里检查相机的缩放，并限制最小缩放值
    // const minZoom = 0.5;
    // console.log(camera.zoom, 'camera.zoom');
    // if (camera.zoom < minZoom) {
    //     camera.zoom = minZoom;
    //     camera.updateProjectionMatrix();
    // }

})




// 当前巷道形状
const tunShape = ref('circle')
// 修改巷道粗细方法
export function changeTun(radius1) {
    // 修改巷道粗细
    tubeMeshArr.forEach((v, i) => {
        const radius = radius1 ?? v.radius;
        v.children[0].geometry = new THREE.SphereGeometry(radius + 0.12, 32, 32);
        if (tunShape.value == 'circle') {
            let geo = createCircleShapeGeo(v.points, radius)
            v.geometry = geo
            recreate(tubeTextureArr[i], tubeTextureArr[i].points, radius + 0.6, tunShape.value)
        } else if (tunShape.value == 'arch') {
            let geo = createArchShapeGeo(v.points, radius)
            v.geometry = geo
            recreate(tubeTextureArr[i], tubeTextureArr[i].points, radius + 0.6, tunShape.value)
        } else {
            let geo = creartTrapezoidShapeGeo(v.points, radius)
            v.geometry = geo
            recreate(tubeTextureArr[i], tubeTextureArr[i].points, radius + 0.8, tunShape.value == 'trapezoid' ? 'arch' : tunShape.value)
        }
    })
}
// 修改管道形状
export function changeShape(shape) {
    tunShape.value = shape;
    switch (shape) {
        case 'circle':
            changeTun()
            break;
        case 'arch':
            changeTun()
        case 'trapezoid':
            changeTun()
            break;
    }
}