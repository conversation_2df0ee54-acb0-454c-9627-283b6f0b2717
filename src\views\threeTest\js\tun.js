import * as THREE from 'three'
import { textureLoader } from '@/three/utils/loader'
import { initMatcapThree } from '@/three/effect/matcap'
import { deepCloneMaterial } from '@/three/utils/utils'
import gsap from "gsap";
import { creartTrapezoidShapeGeo, createCircleShapeGeo, createArchShapeGeo, recreate } from "./tunShape";
import { initRoadFlow, initAirFlow } from '@/three/effect/flow';
import { setMapRepeat } from "./tunUtils";
// import { TubeGeometry } from '@/three/mesh/TubeGeometry';
import { TubePath } from '@/three/mesh/TubePath';
// 巷道生成类
class TubeLine {
    constructor(linePoints = [[0, 0, 0], [1, 1, 1]], option = { color: 0xffffff, speed: -1, radius: 1, mapPath: '' }) {
        let { radius, mapPath, color, speed } = option
        const threeLinePoints = []
        linePoints.forEach((v) => {
            if (v instanceof THREE.Vector3) {
                threeLinePoints.push(v)
            } else {
                threeLinePoints.push(new THREE.Vector3(...v))
            }
        })
        // 创建曲线
        this.lineCurve = new THREE.CatmullRomCurve3(threeLinePoints);
        // 根据曲线生成管道几何体

        this.geometry = new THREE.TubeGeometry(
            this.lineCurve,
            1
            ,
            radius,
            18,
            false
        );


        if (mapPath) {
            this.texture = textureLoader().load(mapPath);
            this.texture.repeat.set(4, 4);
            this.texture.wrapS = THREE.RepeatWrapping;
            this.texture.wrapT = THREE.MirroredRepeatWrapping;
        }

        // 使用烘培纹理
        const mat = initMatcapThree({ path: './textures/444.png', color })
        // 金属贴图
        this.material = mat
        mat.side = THREE.DoubleSide;
        mat.map = this.texture;
        mat.side = THREE.DoubleSide;

        // 创建物体
        this.mesh = new THREE.Mesh(this.geometry, this.material);
        this.mesh.points = linePoints;
        // 创建一个小球
        this.sphereMaterial = deepCloneMaterial(this.material)
        this.sphereMaterial.map = null
        const sphereGeometry = new THREE.SphereGeometry(radius, 36, 36)
        this.sphereMesh = new THREE.Mesh(sphereGeometry, this.sphereMaterial)
        this.sphereMesh.position.set(...threeLinePoints[1])

        this.mesh.add(this.sphereMesh)
        // 通过偏移纹理贴图位置 来实现动画效果
        // gsap.to(this.texture.offset, {
        //     x: speed,
        //     duration: 3,
        //     repeat: -1,
        //     ease: "none",
        // });
    }
}

// 创建巷道
export function createTun(scene, tubePoints = [], id, Ventflow, shape, TunName) {
    const color = Ventflow == 1 ? "#00db77" : (Ventflow == -1 ? "#dd212f" : "#ffb30f")
    // let path = Ventflow == 1 ? "./textures/jt2-1.png" : (Ventflow == -1 ? "./textures/jt2-2.png" : "./textures/jt2-2.png")

    const radius = 8;

    let meshArr = [];
    let sphereArr = [];
    const len = tubePoints.length;
    if (len <= 1) return { meshArr, sphereArr };

    const linePoint = tubePoints
    const { tubeMesh } = tun(linePoint, { ventflow: Ventflow, color: color, radius: radius, tunName: TunName })
    tubeMesh.mid = id;









    meshArr.push(tubeMesh);
    // scene.add(tubeMesh);
    // 添加连接物体
    const { x, y, z } = linePoint.at(-1)
    const { sphere } = createPoint({ x, y, z, color: color, radius: radius + 0.2 })
    sphere.name = TunName;
    sphereArr.push(sphere);

    // }

    return { meshArr, sphereArr }
}

export function createPoint(options = { x, y, z, color: 0xff0000, radius }) {
    const { x, y, z, radius, color } = options;
    // 使用烘培纹理
    const mat = initMatcapThree({ path: './textures/444.png', color })
    // const mat = new THREE.MeshBasicMaterial({ color })
    // 金属贴图
    mat.side = THREE.DoubleSide;
    mat.color = new THREE.Color(color);
    const geometry = new THREE.SphereGeometry(radius, 32, 32);
    const sphere = new THREE.Mesh(geometry, mat ?? new THREE.MeshBasicMaterial({ color: 'red' }));
    sphere.position.set(x, y + 1, z);
    sphere.point = new THREE.Vector3(x, y, z);

    function remove() {
        meshRemove(sphere);
    }
    return { sphere, mat, geometry, remove };
}

export function tun(linePoints = [], { ventflow = 1, color = 0xffffff, radius = 1, tunName = '' }) {
    let path = ventflow == 1 ? "./textures/jt_green.png" : (ventflow == -1 ? "./textures/jt_red.png" : "./textures/jt_yellow.png")
    let texture = path ? textureLoader().load(path) : '';
    if (texture) {
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.MirroredRepeatWrapping;
        texture.needsUpdate = true
    }

    // 使用烘培纹理
    const mat = initMatcapThree({ path: './textures/444.png', color })
    // 金属贴图
    mat.side = THREE.DoubleSide;
    mat.transparent = true;


    // CatmullRomCurve3创建一条平滑的三维样条曲线
    let curve = new THREE.CatmullRomCurve3(linePoints) // 曲线路径

    // 创建管道
    let tubeGeometry = new THREE.TubeGeometry(curve, 10, radius, 8, false);
    let tubeMesh = new THREE.Mesh(tubeGeometry, mat);
    tubeMesh.points = linePoints;
    tubeMesh.tunName = tunName;
    function roadAnimate() {
        // 一定要在此函数中调用
        if (texture) texture.offset.x -= 0.01
    }



    return { tubeMesh, roadAnimate, tubeGeometry }
}



export function createTun2(scene, tubePoints = [], id, Ventflow, shape, TunName) {
    const color = Ventflow == 1 ? "#00db77" : (Ventflow == -1 ? "#dd212f" : "#ffb30f")
    let path = Ventflow == 1 ? "./textures/jt2-1.png" : (Ventflow == -1 ? "./textures/jt2-2.png" : "./textures/jt2-2.png")

    const radius = 8
    let meshArr = []; // 存储巷道三维物体数据
    let roadArr = []; // 存储拱形巷道纹理数据
    let sphereArr = []; // 存储创建的节点
    const len = tubePoints.length;
    if (len <= 1) return { meshArr, roadArr, sphereArr };


    for (let i = 0; i < len - 1; i++) {
        const points = [tubePoints[i], tubePoints[i + 1]]
        const { x, y, z } = tubePoints[i], { x: x1, y: y1, z: z1 } = tubePoints[i + 1]
        const point1 = new THREE.Vector3(x, y, z)
        const point2 = new THREE.Vector3(x1, y1, z1)
        let { material } = new TubeLine(points, { mapPath: './textures/decorate.png', color, radius: 5, speed: -1, direction: '正' });
        material.map = null

        let geo = shape == 'arch' ? createArchShapeGeo(points, radius) : (shape == 'circle' ? createCircleShapeGeo(points, radius) : creartTrapezoidShapeGeo(points, radius))
        const tubeMesh = new THREE.Mesh(geo, material);
        tubeMesh.points = points;
        tubeMesh.radius = radius;
        // 添加连接物体
        const geometry = new THREE.SphereGeometry(radius + 0.12, 32, 32);
        const sphere = new THREE.Mesh(geometry, material);
        sphere.position.set(x1, y1, z1)
        sphere.point = point2;
        sphereArr.push(sphere);

        // tubeMesh.add(sphere);
        scene.add(tubeMesh);
        meshArr.push(tubeMesh);

        // 添加道路气体流向
        const points1 = [point1, point2]

        const { roadMesh } = initRoadFlow(points1, { path, radius: shape == 'trapezoid' ? radius + 0.8 : radius + 0.6 })
        roadMesh.mid = id + i;
        roadMesh.points = points;
        roadMesh.tunName = TunName;
        recreate(roadMesh, points1, shape == 'trapezoid' ? radius + 0.8 : radius + 0.6, shape == 'trapezoid' ? 'arch' : shape)
        roadMesh.material.map.repeat.set(8, 3)
        setMapRepeat(roadMesh, points)
        gsap.to(roadMesh.material.map.offset, {
            x: 1,
            duration: 5,
            repeat: -1,
            ease: "none",
        });
        scene.add(roadMesh);
        roadArr.push(roadMesh);

    }
    return { meshArr, roadArr, sphereArr }
}