
import { <PERSON>rid<PERSON>elper } from 'three';
import { createCss3DText, createCss3DSprite } from '@/three/css3DRender';
// 设置x方向的贴图重复数量
export function setMapRepeat(mesh, points) {
    let distance = 0;
    for (let i = 0; i < points.length - 1; i++) {
        distance += calculateDistance(points[i], points[i + 1]);
    }


    if (distance < 200) {
        mesh.material.map.repeat.set(2, 3);
    } else if (distance > 200 && distance < 400) {
        mesh.material.map.repeat.set(3, 3);
    } else if (distance > 400 && distance < 600) {
        mesh.material.map.repeat.set(5, 3);
    } else if (distance > 600 && distance < 800) {
        mesh.material.map.repeat.set(7, 3);
    } else if (distance > 800 && distance < 1000) {
        mesh.material.map.repeat.set(9, 3);
    } else {
        mesh.material.map.repeat.set(12, 3);
    }
}
export function distanceBetweenPoints(point1, point2) {
    const dx = point2.x - point1.x;
    const dy = point2.y - point1.y;
    const dz = point2.z - point1.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
}


// canvas纹理
export function createCanvas(text) {
    const canvas = document.createElement("canvas");
    const arr = text.split(""); //分割为单独字符串
    let num = 0;
    const reg = /[\u4e00-\u9fa5]/;
    for (let i = 0; i < arr.length; i++) {
        if (reg.test(arr[i])) { //判断是不是汉字
            num += 1;
        } else {
            num += 0.5; //英文字母或数字累加0.5
        }
    }
    // 根据字符串符号类型和数量、文字font-size大小来设置canvas画布宽高度
    const h = 120; //根据渲染像素大小设置，过大性能差，过小不清晰
    const w = h + num * 32;
    canvas.width = w;
    canvas.height = h;
    const h1 = h * 0.8;
    const c = canvas.getContext('2d');
    // 定义轮廓颜色，黑色半透明
    c.fillStyle = "rgba(0,255,255,0.6)";
    // 绘制矩形
    c.fillRect(0, 0, w, h1); // 填充矩形
    c.fill();
    // 绘制箭头
    c.beginPath();
    const h2 = h - h1;
    c.moveTo(w / 2 - h2, h1);
    c.lineTo(w / 2 + h2, h1);
    c.lineTo(w / 2, h * 0.9);
    c.fill();
    // 文字
    c.beginPath();
    c.translate(w / 2, h1 / 2);
    c.fillStyle = "#ffffff"; //文本填充颜色
    c.font = "bold 32px 宋体"; //字体样式设置
    c.textBaseline = "middle"; //文本与fillText定义的纵坐标
    c.textAlign = "center"; //文本居中(以fillText定义的横坐标)
    c.fillText(text, 0, 0);
    return canvas;
}

// 创建经纬格
export function createAGrid({ divisions = 20, color1 = '#81d4fa', color2 = '', scale = 1.8, xSize = 0, zSize = 0, xMin = 0, zMin = 0 }) {
    const size = 20000 * scale;
    const gridHelper = new GridHelper(size, divisions, color1);

    for (var i = 0; i <= divisions; i++) {
        const [x, y, z] = [(-size / 2 - (size / divisions / 2)), 1, (size / 2) - i * (size / divisions)]
        const [x1, y1, z1] = [(size / 2) - i * (size / divisions), 1, (-size / 2 - (size / divisions / 2))]
        const textX = i * (zSize / divisions) + xMin
        const textZ = i * (xSize / divisions) + zMin
        gridHelper.add(createCss3DText([-x, y, z], Math.round(textX), 5))
        gridHelper.add(createCss3DText([x1, y1, z1], Math.round(textZ), 5))
    }
    return gridHelper
}

// 添加巷道标签
export function addTunLable(scene, tunPointsAll, tunNameList) {
    const tunNameArr = [] // 去重
    const spriteMeshArr = []
    tunPointsAll.forEach((val, i) => {
        const { TunName } = tunNameList.at(i)
        const len = val.length
        if (len <= 1) return;
        // 添加标注
        const [x1, y1, z1] = val[0]
        const [x2, y2, z2] = val[len - 1]
        const [x, y, z] = [(x1 + x2) / 2, (y1 + y2) / 2, (z1 + z2) / 2];
        // 创建标签的Sprite
        if (!tunNameArr.includes(TunName)) {
            tunNameArr.push(TunName)

            // 添加精灵标注
            const sprite = createCss3DSprite('./textures/data_label.png', TunName, 1, `
            <div class="data_label_box">
                <div class="data_label">
                    <span>${TunName}</span>
                </div>
            </div>
            `)

            sprite.position.set(x, y, z);// 设置标签的位置
            scene.add(sprite);
            spriteMeshArr.push(sprite);
        }
    })
    return spriteMeshArr;
}