<template>
    <div class="canvasBox" ref="sceneDiv"></div>

    <CenterTitle title="全矿预览"></CenterTitle>
    <!-- 悬浮框 -->
    <TunInfo v-show="isLeftShow" :currentTun="currentTun" />
    <div class="fixed right-20px bottom-50%">
        <div>
            <el-button type="warning" color="#ea2465" size="default" @click="isShowLabel">
                <div v-if="isShow">隐藏标签</div>
                <div v-else>显示标签</div>
            </el-button>
        </div>
        <div class="mt-10px">
            <el-button type="warning" color="#ea2465" size="default" @click="isLeftShow = !isLeftShow">
                <div v-if="isLeftShow">隐藏左侧边栏</div>
                <div v-else>显示左侧边栏</div>
            </el-button>
        </div>

    </div>

</template>

<script setup>
import { ref, onMounted, watch, onUnmounted, watchEffect, provide } from 'vue'
import CenterTitle from '@/components/common/CenterTitle.vue';
import TunInfo from './components/tunInfo/tunInfo.vue'
import { onBeforeRouteLeave } from 'vue-router';
// 控制侧边栏显示
const isLeftShow = ref(true)
const sceneDiv = ref(null)
const znzDom = ref()
function getZnzDom(dom) {
    znzDom.value = dom
}
onBeforeRouteLeave(() => {
    stopRender();
    dblRemove();
    hoverRemove();
    clickRemove();
})

onMounted(() => {
    mountRender(sceneDiv.value, znzDom.value)
    // mitt.on('quitLogin', () => {
    //     stopRender();
    // })
});


// 修改管道粗细
const radius = ref(5)
function changeTun1(num) {
    radius.value += num;
    radius.value = radius.value < 3 ? 5 : (radius.value > 9 ? 5 : radius.value);
    tunInit.then(() => {
        changeTun(radius.value)
    })
}

//切换相机视角
function cameraAngle(direction) {
    const [x, y, z] = endCamera;
    const DEG90 = Math.PI * 0.5;
    const DEG180 = Math.PI;
    const DEG315 = Math.PI * 1.75;
    function angle(num = 1) {
        return Math.PI * num;
    }
    switch (direction) {
        case 'up':
            cameraControls.rotateTo(-DEG90, 0, true);
            break;
        case 'front':
            cameraControls.rotateTo(DEG315, angle(0.48), true);
            break;
        case 'side':
            cameraControls.rotateTo(angle(1.65), angle(0.25), true);

            break;
    }
}

// 导入辅助坐标轴
import { setMapRepeat, createAGrid, addTunLable } from "./js/tunUtils";
import { createTun } from './js/tun.js';
import { modelClick, modelHover, dbModelClick } from "@/three/utils/event";
import { useThree } from "@/three/useThree";
import { creartTrapezoidShapeGeo, createCircleShapeGeo, createArchShapeGeo, recreate } from "./js/tunShape";
import { useThereVentilation } from '@/store/thereVentilation';
import { moveToCamera } from "@/three/cameraControls";
import { textureLoader } from '@/three/utils/loader';

const store = useThereVentilation()
const endCamera = [-4000, 5000, 4000]

const { scene, THREE, findMesh, renderer, css3Renderer, gsap, changeCurrentMesh, mountRender, limitCameraVerticalAngle, restCamera, cameraControls, camera, addOutlinePass1, addOutlinePass, ani, stopRender, outlinePass } = useThree({ endCamera, isCameraAni: false, isAxes: false, isDepthBuffer: true, isComposer: true })

cameraControls.infinityDolly = true
// 初始化配置
outlinePass.edgeThickness = 4.0;
outlinePass.edgeGlow = 2.0;
outlinePass.edgeStrength = 4.0;
outlinePass.pulsePeriod = 3.0;

// 获取接口数据
import { roadwayList } from '@/https/encapsulation/threeDimensional'



const tunNameList = [] // 巷道名称 和 需要展示的相关数据
let tubeMeshArr = []; // 存储巷道三维物体数据
let pointMeshArr = []; // 存储三维巷道连接小球数据
const tubePointsAll = [] // 存储全部巷道点位信息 基于相对坐标生成的数组信息
let spriteMeshArr = [] // 存储精灵标签物体数组
const meshGroup = new THREE.Group();


// 最大 最小坐标组
const maxPosition = [0, 0, 0]
const minPosition = [0, 0, 0]

store.$patch({ loadingShow: true })
const tunInit = function () {
    roadwayList().then(({ Result }) => {
        //console.log(Result, 'Result');
        if (!Result) return

        let len = Result.length;

        let arr = [];

        for (let i = 0; i < len; i++) {
            const { TunName, TunID, Nodes, Ventflow, S, Q, V, H } = Result[i];

            const nodeLen = Nodes.length;
            let tubePoints = []
            for (let j = 0; j < nodeLen; j++) {
                const { NodeID, NodeName, x, y, z } = Nodes[j]; // 数据里z代表
                tubePoints.push(new THREE.Vector3(x, y, z))
                const { x: x1, y: y1, z: z1 } = { x, y, z };
                const [maxX, maxY, maxZ] = maxPosition;
                if (x1 > maxX) {
                    maxPosition[0] = x1;
                } else {
                    minPosition[0] = x1;
                }
                if (z1 > maxY) {
                    maxPosition[1] = z1;
                } else {
                    minPosition[1] = z1;
                }
                if (y1 > maxZ) {
                    maxPosition[2] = y1;
                } else {
                    minPosition[2] = y1;
                }
            }
            arr.push(tubePoints);
            const distanceList = [];
            for (var k = 0; k < tubePoints.length - 1; k++) {
                const point1 = tubePoints[k];
                const point2 = tubePoints[k + 1];
                distanceList.push(distanceBetweenPoints(point1, point2));
            }
            // console.log(distanceList, 'distance');
            tunNameList.push({ TunName, TunID, Ventflow, S, Q, V, H, NodeList: tubePoints, distanceList })
        }

        const { x, y, z } = arr[Math.floor(len / 2)].at(0);

        for (let i = 0, len = arr.length; i < len; i++) {
            const item = arr[i];
            const newArr = []
            for (let j = 0, len2 = item.length; j < len2; j++) {
                const { x: x1, y: y1, z: z1 } = item[j];
                const n = 2 // 放大系数
                const x2 = (x1 - x) * n;
                const y2 = (z1 - z + (Math.abs(z - minPosition[1]))) * n;
                const z2 = (y1 - y) * n;
                newArr.push(new THREE.Vector3(x2, y2, z2))
            }
            tubePointsAll.push(newArr);
        }
        for (var i = 0; len = tubePointsAll.length, i < len; i++) {
            const item = tubePointsAll[i];
            const { TunID, Ventflow, TunName } = tunNameList[i]
            const { meshArr, sphereArr } = createTun(scene, item, TunID, Ventflow, 'circle', TunName);
            tubeMeshArr.push(...meshArr); pointMeshArr.push(...sphereArr)
            meshGroup.add(...meshArr);
        }



    }).then(() => {
        spriteMeshArr = addTunLable(scene, tubePointsAll, tunNameList) // 添加标签
        // addMeshOutlineShine()

        scene.add(meshGroup);
        // 去除重复的节点
        pointMeshArr = checkRepet('point', pointMeshArr);

        // 将连接点位转为 InstancedMesh 格式物体
        const firstMesh = pointMeshArr[0];
        const instanceMesh = new THREE.InstancedMesh(firstMesh.geometry, firstMesh.material, pointMeshArr.length);
        const matrix = new THREE.Matrix4();
        for (let i = 0; i < pointMeshArr.length; i++) {
            const item = pointMeshArr[i];
            const { x, y, z } = item.point;
            matrix.setPosition(x, y + 1, z);
            instanceMesh.setMatrixAt(i, matrix);
            instanceMesh.setColorAt(i, item.material.color);
        }
        scene.add(instanceMesh);


        setTimeout(() => {
            store.$patch({ loadingShow: false })
        }, 1200)


        // 添加经纬格
        const [maxX, maxY, maxZ] = maxPosition;
        const [minX, minY, minZ] = minPosition;
        scene.add(createAGrid({ xSize: maxX - minX, zSize: maxZ - minZ, xMin: minX, zMin: minZ, scale: 1.8 }))
    })
}
function distanceBetweenPoints(point1, point2) {
    const dx = point2.x - point1.x;
    const dy = point2.y - point1.y;
    const dz = point2.z - point1.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
}

tunInit();
// 检差巷道中是否存在重复绘制情况
function checkRepet(field, orginalArr) {
    const newPointMeshArr = [];
    const pointVec3Arr = orginalArr.map((v, i) => {
        if (!v[field]) return null;
        if (v[field] instanceof Array) {
            let str = ``;
            v[field].length && v[field].forEach((v2) => {
                const { x, y, z } = v2;
                str += `${x}${y}${z}-`
            })
            return str
        } else {
            const { x, y, z } = v[field];
            return `${x}${y}${z}`
        }
    })
    pointVec3Arr.forEach((item, index) => {
        if (pointVec3Arr.indexOf(item) === index) {
            newPointMeshArr.push(orginalArr[index]);
        }
    });
    return newPointMeshArr;
}

const isShow = ref(true);
// 是否显示标签名称
function isShowLabel() {
    isShow.value = !isShow.value;
    spriteMeshArr.forEach(v => {
        v.visible = isShow.value;
    })
}


// 鼠标悬浮事件
const shineMeshList = ref([])// 全部高亮列表
const moveMesh = ref()// 当前悬浮的物体高亮
const clickMesh = ref()// 当前点击的物体高亮
const currentEvent = ref('')

// 物体检测
/*单击*/
const { clickRemove } = modelClick(tubeMeshArr, camera, (currentMesh, event) => {
    if (currentMesh) {
        const mesh = currentMesh.object.parent.children.length == 1 ? currentMesh.object.parent : currentMesh.object
        commonEvent('click', mesh, event)
    }
})

/*悬浮*/
const { hoverRemove } = modelHover(tubeMeshArr, camera, (currentMesh, event) => {
    if (currentMesh) {
        const mesh = currentMesh.object.parent.children.length == 1 ? currentMesh.object.parent : currentMesh.object
        commonEvent('mousemove', mesh, event)
    } else {
        commonEvent('mousemove', '', event)
    }
})
/*双击*/
const { dblRemove } = dbModelClick(tubeMeshArr, camera, (currentMesh, event) => {
    // cameraControls.moveTo(0, 10, 0, true)
    moveToCamera(cameraControls, 0, [0, 10, 0]);
    cameraControls.fitToBox(currentMesh.object, true);
})

// 存储鼠标坐标
const currentTun = ref({})
//监听鼠标点击事件
function commonEvent(type, currentMesh, event) {
    currentEvent.value = type
    const shineList = [...shineMeshList.value]
    if (currentMesh && type == 'click') {
        if ((shineMeshList.value.findIndex(v => v.tunName == currentMesh.tunName)) != -1) { return; } // 高亮显示的一组物体不做处理
        const { tunName, mid } = currentMesh;
        if (mid) {
            const { NodeList, distanceList, TunID } = tunNameList.find(v => v.TunID == mid);
            currentTun.value = { x: event.x, y: event.y, NodeList, distanceList, TunID, tunName }
        }

        switch (type) {
            case 'click':
                clickMesh.value = currentMesh;
                break;
            case 'mousemove':
                moveMesh.value = currentMesh;
                break;
        }
        if (moveMesh.value && clickMesh.value && moveMesh.value.mid == clickMesh.value.mid) {
            moveMesh.value = null
        }
        clickMesh.value && shineList.push(clickMesh.value)
        moveMesh.value && shineList.push(moveMesh.value)
        addOutlinePass(shineList)
    } else {
        if (type === 'click') {
            clickMesh.value = null
            moveMesh.value = null
            addOutlinePass(shineList)
        }
    }
}

provide('flyToMesh', flyToMesh);
provide('resetEffect', resetEffect);
provide('addMeshOutline', { addMeshOutlineShine, addOutlinePass, addOutlinePass1 });

// 按名称查找物体进行高亮显示
function addMeshOutlineShine(nameList = [], type) {
    // shineMeshList.value = []
    // tubeMeshArr.forEach(mesh => {
    //     if (nameList.includes(mesh.tunName)) {
    //         shineMeshList.value.push(mesh)
    //     }
    // })
    // addOutlinePass(shineMeshList.value)
}

// 飞跃到物体附近
function flyToMesh(mid = '10#横贯') {
    const mesh = tubeMeshArr.find(v => v.tunName.includes(mid))
    if (!mesh) return;
    const [point1, point2] = mesh.points;
    const { x: x1, y: y1, z: z1 } = point1;
    const { x: x2, y: y2, z: z2 } = point2;
    const [x, y, z] = [(x1 + x2) / 2, (y1 + y2) / 2, (z1 + z2) / 2]
    const mesh1 = mesh.parent.children.length == 1 ? mesh.parent : mesh
    // shineMeshList.value.push(mesh1)
    resetEffect()
    addOutlinePass1([mesh1])
    setTimeout(() => {
        moveToCamera(cameraControls, [10, 50, 10], [x, y, z])
    }, 1000)
}

// 重置效果
function resetEffect() {
    addOutlinePass([])
    addOutlinePass1([])
    restCamera()
}



// 渲染函数
ani((...args) => {
    const [time, camera, delta] = args;
    const { x, y, z } = camera.rotation

})



// 当前巷道形状
const tunShape = ref('circle')
// 修改巷道粗细方法
function changeTun(radius1) {
    // 修改巷道粗细
    tubeMeshArr.forEach((v, i) => {
        const radius = radius1 ?? v.radius;
        v.children[0].geometry = new THREE.SphereGeometry(radius + 0.12, 32, 32);
        if (tunShape.value == 'circle') {
            let geo = createCircleShapeGeo(v.points, radius)
            v.geometry = geo
            recreate(tubeMeshArr[i], tubeMeshArr[i].points, radius + 0.6, tunShape.value)
        } else if (tunShape.value == 'arch') {
            let geo = createArchShapeGeo(v.points, radius)
            v.geometry = geo
            recreate(tubeMeshArr[i], tubeMeshArr[i].points, radius + 0.6, tunShape.value)
        } else {
            let geo = creartTrapezoidShapeGeo(v.points, radius)
            v.geometry = geo
            recreate(tubeMeshArr[i], tubeMeshArr[i].points, radius + 0.8, tunShape.value == 'trapezoid' ? 'arch' : tunShape.value)
        }
    })
}
// 修改管道形状
function changeShape(shape) {
    tunShape.value = shape;
    switch (shape) {
        case 'circle':
            changeTun()
            break;
        case 'arch':
            changeTun()
        case 'trapezoid':
            changeTun()
            break;
    }
}






</script>

<style lang="scss" scoped>
:v-deep(.el-button .el-icon) {
    height: 0.5em;
    width: 0.5em;
}

.norem-box {
    /* padding: 20px;
    background-color: #000; */
    color: white;
    margin: 20px;
    display: flex;


    .norem-btn {
        /* postcss-pxtorem-disable */
        display: flex;
        justify-content: center;
        align-items: center;
        width: 55px;
        height: 25px;
        font-size: 12px;
        border-radius: 0;
        cursor: pointer;
        background-color: rgba(0, 255, 255, 0.6);
        margin: 0.5px;
        color: white;

        &:hover {
            background-color: rgba(0, 255, 255, 0.8);
        }
    }

    .norem-btn1 {
        width: auto;

        &:hover {
            background-color: rgba(0, 255, 255, 0.6);
        }

        .norem-btn1-1 {
            background-color: rgba(255, 255, 255, 0.8);
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: #fff;
            display: flex;
            justify-items: center;
            align-items: center;

            &:hover {
                background-color: rgba(255, 255, 255, 1.0);
            }
        }
    }
}

.scene {
    width: 100vw;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
}
</style>