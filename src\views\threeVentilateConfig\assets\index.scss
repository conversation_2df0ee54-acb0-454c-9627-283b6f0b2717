// 定义橘黄色主题的颜色变量
$primary-color: #CC9900; // 暗黄色
$secondary-color: #996600; // 深暗黄色
$background-color: #F5F5DC; // 浅米色背景（偏暗）
$text-color: #333; // 深灰色文字
$hover-color: #FFCC00; // 暗金色（用于悬停效果）
$border-color: #CC9900; // 暗黄色边框
$scrollbar-color: #996600; // 深暗黄色滚动条
$table-header-bg: #453F37; // 表格头部背景
$table-header-text: #FFF; // 表格头部文字颜色
$table-row-bg: #F5F5DC; // 表格行背景
$table-row-text: #333; // 表格行文字颜色
$table-hover-bg: #FFCC00; // 表格行悬停背景
$collapse-header-bg: #CC9900; // 折叠面板头部背景
$collapse-content-bg: #F5F5DC; // 折叠面板内容背景

.configBox {
    input {
        background-color: white;

        &:hover {
            border-color: $hover-color;
        }

        &:focus {
            border-color: $hover-color;
        }
    }

    .el-table-v2__header-row {
        background: $table-header-bg;
    }

    .el-table-v2__header-cell {
        color: $table-header-text;
        background: $table-header-bg;
    }

    .el-table-v2__row {
        background: $table-row-bg;
    }

    .el-table-v2__row-cell {
        width: 100%;
        background: $table-row-bg;
    }

    .el-scrollbar__thumb {
        background-color: $scrollbar-color;
        opacity: 0.8;
    }

    // table
    .el-table th.el-table__cell {
        background: $table-header-bg !important;
        color: $table-header-text;
        text-align: center;
        height: 10rem !important;
        font-size: 14rem;
        border-bottom: 1px solid $border-color !important;
    }

    .el-table--border .el-table__inner-wrapper::after,
    .el-table--border::after,
    .el-table--border::before,
    .el-table__inner-wrapper::before {
        background-color: transparent !important;
    }

    .el-table td.el-table__cell {
        height: 15rem !important;
        color: $table-row-text;
        background: $table-row-bg !important;
        text-align: center;
        font-size: 14rem;
        border-bottom: 1rem solid $border-color !important;
    }

    .el-table__body-wrapper {
        background-color: $background-color;
    }

    .el-table tbody tr:hover td {
        background-color: $table-hover-bg !important;
    }

    // 折叠面板
    .el-collapse-item,
    .el-collapse,
    .el-collapse-item__content,
    .el-collapse-item__wrap {
        border: none !important;
        padding: none !important;
    }

    .el-collapse-item__arrow {
        color: $text-color !important;
    }

    // list列表
    .el-collapse-item__header {
        background-color: $collapse-header-bg !important;
        width: 408rem !important;
        height: 50rem !important;
        background-image: url('/src/views/thereVentilation/assets/img/list_bg.png') !important;
        background-size: 408rem 48rem;
        border: none !important;
    }

    .el-collapse-item__content {
        padding-top: 5px !important;
        padding-bottom: 5px !important;
    }

    .el-collapse-item__wrap {
        background-color: $collapse-content-bg !important;
        width: 408rem !important;
    }

    // 修改数据为空时 展示的文字
    .el-table__empty-text {
        color: $text-color;
        position: relative;

        &::before {
            content: "无数据";
            position: absolute;
            width: 100%;
            height: 100%;
            left: 50%;
            transform: translateX(-50%);
            font-weight: 400;
            color: $text-color;
            z-index: 9;
        }
    }
}