<template>
    <div v-show="resultBoxShow"
        class="sbbd_header fixed w-800px h-300px bg-[#144763] rounded-20px top-100px left-50% translate-x-[-400px]  p-25px">
        <div class="flex justify-between w-full">
            <div class=" text-center text-20px text-white">{{ title }}</div>
            <el-icon size="20rem" color="#ff9800" class="mt-[-10px] opacity-75 :hover:opacity-100 cursor-pointer"
                @click="resultBoxShow = false">
                <Close />
            </el-icon>
        </div>
        <div class="my-20px" border="solid 1px #ff980088">
        </div>
        <div class="h-200px text-white relative rounded-20px bg-#15233666">
            <div v-show="resultBoxLoading" class="z-9 absolute top-0 left-0 w-full h-full" v-loading></div>
            <div class="p-20px h-full w-full">{{ resultData }}</div>
        </div>
    </div>
</template>
<script setup>
import { watch, ref } from 'vue';
import { getStructural, geVentilationCalculation,getHardRoute } from '@/https/encapsulation/ventilationCalculation';
import { Close } from '@element-plus/icons-vue';
const resultBoxShow = defineModel(false)
const props = defineProps(['handlerFunc', 'handleStructuralQuery', 'schemeId'])
const emit = defineEmits(['structuralQuery', 'solutionResult','hardRoute'])
watch(() => props.handlerFunc, (val) => {
    console.log(val, 'valvalva');

    if (val == '结构') {
        structuralQuery();
    } else if(val == '困难路线') {
       hardRoute();
    } else{
       
          solutionResult();
    }
})
// watch(() => props.handleStructuralQuery, (val) => {
//     if (val) {
//         structuralQuery();
//     }
// })

let resultBoxLoading = ref(true);
let resultData = ref()
const title = ref('结构查询');
function resetVar() {
    resultData.value = ''
    resultBoxShow.value = true;
    resultBoxLoading.value = true;
}

// 结构查询
async function structuralQuery() {
    resultBoxShow.value = true;
    resetVar();
    title.value = '结构查询';
    console.log(props.schemeId, 'props.schemeId');
    const { data } = await getStructural(props.schemeId);
    resultBoxLoading.value = false;
    resultData.value = data.Result ?? data.Msg;
}
// 通风解算
async function solutionResult() {
    resultBoxShow.value = true;
    resetVar();
    title.value = '通风解算';
    const { data } = await geVentilationCalculation(props.schemeId);
    resultBoxLoading.value = false;
    resultData.value = data.Result ?? data.Msg;
}
//困难路线
async function hardRoute() {
    resultBoxShow.value = true;
    resetVar();
    title.value = '困难路线'; 
    const { data } = await getHardRoute(props.schemeId);
    resultBoxLoading.value = false;
    resultData.value = data.Result?? data.Msg;
}
</script>
<style lang="scss"></style>
<style lang="scss" scoped></style>