<template>
    <div class="configBox">
        <div class="canvasBox " ref="sceneDiv"></div>
        <!-- 右边侧边栏 -->
        <Toolbar :resetEffect="resetEffect" :changeSpriteLabel="changeSpriteLabel" @click="stopEvent"
            @mousemove="stopEvent" :cameraAngle="cameraAngle">
        </Toolbar>
        <!-- 操作菜单 -->
        <div @click="stopEvent" @mousemove="stopEvent"
            class="btn_menu fixed top-300px left-10px flex flex-col h-300px justify-around items-center">
            <div> <el-button dark color="#453F38" size="large" class="text-center" @click=" operateTun(1)">
                    <img class="btn_img" :src="getImg('tunEdit')" />&nbsp;巷道配置</el-button></div>
            <div><el-button dark color="#453F38" size="large" class="text-center" @click=" operateTun(2)">
                    <img class="btn_img" :src="getImg('deviceBinding')" />&nbsp;设备绑定</el-button></div>
            <div><el-button dark color="#453F38" size="large" class="text-center" @click=" operateTun(3)">
                    <img class="btn_img" :src="getImg('deviceBinding')" />&nbsp;三区分布</el-button></div>
            <div><el-button dark color="#453F38" size="large" class="text-center" @click=" operateTun(4)">
                    <img class="btn_img" :src="getImg('deviceBinding')" />&nbsp;实时解算</el-button></div>
        </div>

        <!-- 表单 -->
        <!-- 巷道属性修改 -->
        <el-dialog @click="stopEvent" @mousemove="stopEvent" title="巷道属性修改" v-model="tunEditShow" width="40%"
            style="height: 550rem;position: relative;">
            <el-form class="mt-20px mr-10px" :model="tunEditInfo" ref="form" :rules="rules" label-width="80px"
                :inline="false" size="normal">
                <el-form-item label="巷道名称" prop="TunName">
                    <el-input v-model="tunEditInfo.TunName" clearable></el-input>
                </el-form-item>
                <el-form-item label="用风类型" prop="Ventflow">
                    <el-select v-model="tunEditInfo.Ventflow">
                        <el-option :value="1" label="进风"></el-option>
                        <el-option :value="0" label="用风"></el-option>
                        <el-option :value="-1" label="回风"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="宽度" prop="Width">
                    <el-input v-model="tunEditInfo.Width" clearable></el-input>
                </el-form-item>
                <el-form-item label="风速" prop="Width">
                    <el-input v-model="tunEditInfo.V" clearable></el-input>
                </el-form-item>
                <el-form-item label="面积" prop="Width">
                    <el-input v-model="tunEditInfo.S" clearable></el-input>
                </el-form-item>
            </el-form>

            <div class="absolute bottom-40px right-30px ">
                <el-button @click="tunEditShow = false">取消</el-button>
                <el-button type="primary" @click="saveTunEdit">保存</el-button>
            </div>
        </el-dialog>
        <!-- 设备绑定 -->
        <div @click="stopEvent" @mousemove="stopEvent" class="sbbd top-20%" v-show="deviceBindingShow" fixed right-10px>

            <div my-20px class="sbbd_header h-20px flex justify-between items-center">
                <div class=" text-20px font-bold text-[#fff] ">设备绑定 </div>
                <div class="mt-[-8px]">
                    <el-icon @click="() => { deviceBindingReset(); deviceBindingShow = false }" size="20rem"
                        class=" cursor-pointer">
                        <Close />
                    </el-icon>
                </div>
            </div>

            <div border="2px solid #2DA8EB" h-130px relative rounded-20px my-15px p-20px pt-30px>
                <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                    <div class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                        选中信息
                    </div>
                </div>
                <el-form label-width="auto">
                    <el-form-item label="巷道名称：">
                        <el-input disabled v-model="deviceBindingTun.originPoints.TunName" size="normal"></el-input>
                    </el-form-item>
                    <el-form-item label="选中坐标：">
                        <el-space>
                            <el-input disabled v-model="auxiliaryPoint.originPoint.x" size="normal"></el-input>
                            <el-input disabled v-model="auxiliaryPoint.originPoint.y" size="normal"></el-input>
                            <el-input disabled v-model="auxiliaryPoint.originPoint.z" size="normal"></el-input>
                        </el-space>
                    </el-form-item>
                </el-form>
            </div>
            <div>
                <el-space>
                    <el-button type="primary" size="default" @click="getDevice(1)">传感器</el-button>
                    <el-button type="primary" size="default" @click="getDevice(2)">主通风机</el-button>
                    <!-- <el-button type="primary" size="default" @click="getDevice(3)">局部通风机</el-button> -->
                    <el-button type="primary" size="default" @click="getDevice(4)">风门</el-button>
                    <el-button type="primary" size="default" @click="getDevice(5)">风窗</el-button>
                </el-space>
            </div>
            <div class="pl-10px">
                <el-scrollbar height="500rem">
                    <div v-for="(v, i) in deviceList" :key="i" class="mt-15px">
                        <el-space>
                            <div v-show="currentDeviceID == 1" class="flex justify-between items-center">
                                <div mx-10px text-left class="text-white">{{ v.DevCode }}</div>
                                <div mx-20px text-left class="text-white w-100px">{{ v.DevTypeName }}</div>
                            </div>
                            <div v-show="currentDeviceID != 1 && currentDeviceID != 0"
                                class="flex justify-between items-center w-100px">
                                <span class="text-white ">
                                    {{ typeof v.DevName != 'undefined' ? v.DevName : v.devName }}</span>
                            </div>
                            <div class="flex justify-around items-center">
                                <el-button :type="v.state ? 'warning ' : 'success'" size="small"
                                    @click="event => handleDeviceBind(v, i, event)">
                                    {{ v.state ? '更新绑定' : '未绑定' }}
                                </el-button>
                                <el-button v-show="v.state" type="danger" size="small"
                                    @click="event => handleDeviceBindDel(v, i, event)">
                                    删除绑定
                                </el-button>
                                <el-button v-show="v.state" type="primary" size="small"
                                    @click="event => devicePositioning(v.ID, cameraControls, cameraFlyToMesh)">
                                    <el-icon size="16rem" color="#fff">
                                        <Location />
                                    </el-icon>
                                </el-button>
                            </div>
                        </el-space>
                    </div>
                </el-scrollbar>
            </div>

        </div>
        <!-- 三区分布 -->
        <div @click="stopEvent" @mousemove="stopEvent"
            class=" fixed bottom-20px left-20px w-35% h-300px bg-[#1B5471] p-20px" rounded-20px
            border="2px solid #2DA8EB" v-show="threeZoneDistributionShow">
            <div class="text-20px font-bold text-[#fff] my-5px">三区分布设置 </div>
            <el-form class="mt-20px mr-10px" :model="threeZoneDistributionInfo" ref="form" label-width="80px"
                :inline="false" size="normal">
                <el-form-item :label="index = 0 ? '起始点' : '结束点'" v-for="(item, index) in threeZoneDistributionInfo"
                    :key="item.NodeID">
                    <el-space>
                        <el-input disabled v-model="item.NodeName"></el-input>
                        <el-input disabled v-model="item.x"></el-input>
                        <el-input disabled v-model="item.y"></el-input>
                        <el-input disabled v-model="item.z"></el-input>
                        <el-icon v-if="item.state" size="20rem" color="#ea2465" class="mt-8px">
                            <Lock />
                        </el-icon>
                        <el-icon v-else size="20rem" color="#40c057" class="mt-8px">
                            <Unlock />
                        </el-icon>
                    </el-space>
                </el-form-item>
            </el-form>

            <div>
                <span class="absolute bottom-40px right-30px ">
                    <el-button @click="cancelThreeZoneDistribution">取消</el-button>
                    <el-button type="primary" @click="saveThreeZoneDistribution">保存</el-button>
                </span>
            </div>
        </div>
        <!-- 实时解算 -->
        <div @click="stopEvent" v-show="drawerShow"
            class="fixed bottom-0 left-0 right-0 w-100% h-auto bg-#142337 p-10px">
            <div class="flex my-5px w-800px items-center">
                <div class=" w-80px line-height-25px text-#ffc107">
                    巷道搜索:
                </div>
                <div class="flex w-200px mr-12px">
                    <el-select style="width:100%" v-model="searchTun" clearable="" filterable placeholder="搜索巷道名称">
                        <el-option v-for="item in data" :key="item.TunID" :label="item.TunName ?? '-'"
                            :value="item.TunID" />
                    </el-select>
                </div>
                <el-button type="primary" size="default" @click="structuralQuery">结构查询</el-button>
                 <el-button type="primary" size="default" @click="hardRoute">困难路线</el-button>
                <el-button type="primary" size="default" @click="solutionResult">网络解算</el-button>
                <el-popover :width="600" placement="top-start" trigger="click" popper-style=" padding: 20px;">
                    <template #reference>
                        <el-button type="primary" size="default" @click="filterShow = !filterShow">筛选表格</el-button>
                    </template>
                   
                    <template #default>
                        <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">
                            全选
                        </el-checkbox>
                        <el-checkbox-group v-model="checkedCities" @change="handleCheckedCitiesChange">
                            <el-checkbox v-show="i > 6" v-for="(v, i) in columns" :key="v.key" :label="v.title"
                                :value="v.key">
                                {{ v.title }}
                            </el-checkbox>
                        </el-checkbox-group>
                        <div class="absolute right-50px bottom-10px">
                            <el-button type="primary" size="small" @click="confirmFilter">确定</el-button>
                        </div>

                    </template>
                </el-popover>

            </div>
            <!-- 列表 -->
            <div ref="resizableDiv" class="resizable" :style="{ height: height + 'rem' }">
                <div style="height:100%;">
                    <el-auto-resizer style="height: 100%;">
                        <template #default="{ height, width }">
                            <el-table-v2 :row-height="30" border align="center" @column-sort="onSort"
                                v-model:sort-state="sortState" :columns="columns2" :data="data" :width="width"
                                :height="height" fixed>
                            </el-table-v2>
                        </template>
                    </el-auto-resizer>
                </div>
                <div class="resizer" @mousedown="startResize"></div>
            </div>

            <div style="flex: auto" class="  absolute right-20px bottom-10px">
                <el-button @click="cancelTunUpdate">取消</el-button>
                <el-button type="primary" @click="saveTunUpdate">保存</el-button>
            </div>
        </div>
        <StructuredQuery v-model="resultBoxShow" :handlerFunc="handlerFunc"></StructuredQuery>
        <!-- <div v-show="resultBoxShow"
            class="fixed w-800px h-300px bg-[#144763] rounded-20px top-100px left-50% translate-x-[-400px]  p-25px">
            <div class="flex justify-between w-full">
                <div class=" text-center text-20px text-white">{{ title }}</div>
                <el-icon size="20rem" color="#ff9800" class="opacity-75 :hover:opacity-100 cursor-pointer"
                    @click="resultBoxShow = false">
                    <Close />
                </el-icon>
            </div>
            <div class="my-20px" border="solid 1px #ff980088">
            </div>
            <div class="h-200px text-white relative rounded-20px bg-#15233666">
                <div v-show="resultBoxLoading" class="z-9 absolute top-0 left-0 w-full h-full" v-loading></div>
                <div class="p-20px h-full w-full">{{ resultData }}</div>
            </div>
        </div> -->

    </div>
</template>
<script lang="jsx" setup>
import { ref, onMounted, watch, onUnmounted, watchEffect, onBeforeUnmount, toRaw, h, render } from 'vue'
import { useThree } from '@/three/useThree';
import { onBeforeRouteLeave, useRouter } from 'vue-router';
import { Lock, Unlock, Close, Location } from '@element-plus/icons-vue';
import Toolbar from '../thereVentilation/components/toolbar/toolbar.vue';
import { ElInput } from 'element-plus'
import StructuredQuery from './components/structuredQuery.vue';


// div拉伸
// 定义 div 的初始高度和宽度
const height = ref(350); // 初始高度
const width = ref(300); // 固定宽度，不可调整

// 获取 div 的 DOM 引用
const resizableDiv = ref(null);

// 用于标记是否正在调整大小
let isResizing = false;

// 记录初始鼠标位置和初始高度
let startY = 0;
let startHeight = 0;

// 开始调整大小
const startResize = (e) => {
    console.log(e,'开始调整大小');
    e.preventDefault(); // 防止默认行为
    isResizing = true;
    startY = e.clientY; // 记录初始鼠标位置
    startHeight = height.value; // 记录初始高度
    window.addEventListener('mousemove', resize);
    window.addEventListener('mouseup', stopResize);
};

// 调整高度
const resize = (e) => {
    if (isResizing) {
        // 计算鼠标移动的差值
        const deltaY = startY - e.clientY; // 向上拉为正，向下拉为负
        // 计算新的高度
        const newHeight = startHeight + deltaY;
        // 限制最小高度（可选）
        if (newHeight > 280 && newHeight < 800) {
            height.value = newHeight;
        }
    }
};

// 停止调整大小
const stopResize = () => {
    isResizing = false;
    window.removeEventListener('mousemove', resize);
    window.removeEventListener('mouseup', stopResize);
};

// 组件卸载时清理事件监听器
onUnmounted(() => {
    window.removeEventListener('mousemove', resize);
    window.removeEventListener('mouseup', stopResize);
});

// 获取图片的方法
function getImg(name, type = 'png') {
    return new URL(`../tunEdit/assets/img/${name}.${type}`, import.meta.url)
}
const sceneDiv = ref(null)
onMounted(() => {
    mountRender(sceneDiv.value)
});
onBeforeRouteLeave(() => {
    dataReset();
    stopRender();
})

// 导入辅助坐标轴
import { calculateDistance, computeTunLenght, createSpriteLabel, positionTransform } from "@/views/thereVentilation/js/tunUtils.js";
// 获取接口数据
import { meshPickup } from '@/three/raycaster'
import { ElMessage } from "element-plus";
import { pinyin } from 'pinyin-pro';
import { addEvent, removeEvent, stopEvent } from '@/utils/window';
import { ApiNodeList, ApiTunList, ApiTunUpdate, ApiTunUpdate1 } from '@/https/encapsulation/threeDimensional';
import { cameraFlyToMesh, moveToCamera } from '@/three/cameraControls.js'
import { createTun, tun, createPoint, createLine } from '@/views/thereVentilation/js/tun.js';
import { tunInit, getTunList, tunLabelShowByCamera, createOriginPoint, getDeviceBindByType, devicePositioning, setTunDevice } from '../thereVentilation/js/tunInit';
import {
    tunOriginList, pointMeshArr, tunPointsAll, pointShowAll, allGroup, meshGroup, pointGroup, spriteLabelGroup, maxPosition, deviceFilterList,
    minPosition, averagePosition, showOrHideLabels, transformMaxPosition, transformMinPosition, setY, n, tunStr, apiTunList, apiNodeList, middleNode, maxNodeID
} from '../thereVentilation/js/tunInit';// 变量
import { facilityBindingRoadway, facilityUpdateBindingRoadway, facilityDelBindingRoadway, findFacilityBindingRoadwayPageList, findFacilityBindingRoadway } from '@/https/encapsulation/threeVentilateConfig';
import { ApiSecurityMonitoringList } from '@/https/encapsulation/Safety';
import { ApiMainFanList } from '@/https/encapsulation/MainFan.js';
import { ApiWindDoorList } from '@/https/encapsulation/WindDoor.js';
import { ApitWindwindowList } from '@/https/encapsulation/Windwindow.js';
import { deepClone2, getType } from '@/utils/utils';
import { TableV2SortOrder } from 'element-plus'
import { modelClick } from '@/three/utils/event';

import { ApiUseList, ApiShapeList, ApiSupportTypeList, ApiRoadwayTypeList } from '@/https/encapsulation/threeDimensional';
const endCamera = [-6792, 8180, -3620]
const { scene, mountRender, restCamera, stopRender, cameraControls, ani, camera, THREE, addOutlinePass, outlinePass } = useThree({ endCamera, isControl: true, isComposer: true, isCameraAni: false, isAxes: false, isDepthBuffer: true });;
cameraControls.dollySpeed = 0.22;
cameraControls.truckSpeed = 1.1;
camera.far = 60000
//scene.background = new THREE.Color(0x262626);
const { dataInit, dataReset, cameraAngle } = tunInit(scene, cameraControls);
dataInit(5);
const searchTun = ref('');
const currentOperateId = ref(null);
let currentRemoveEvent = null;
let cancelVarFn = null;
let drawerShow = ref(false);
let resultBoxShow = ref(false);
let handlerFunc = ref('');
const tableProperty = ref(['id', 'TunID', 'TunName', 'SnodeID', 'MnodeID', 'EnodeID', 'Ventflow', 'Place', 'Lx', 'ShoringType', 'ShapeType', 'Length', 'Width', 'Height', 'S', 'A', 'R', 'NetLine', 'QDemand', 'QCalculate', 'QMonitor', 'V', 'H', 'Q2', 'H2', 'V2', 'Q3', 'H3', 'V3', 'HWay', 'HParte', 'HRelate', 'HDynamic', 'HStatic', 'HEnergy', 'H100m', 'Vssz'])
const tableLabel = ref(['序号', '巷道ID', '巷道名称', '起始节点', '中间节点', '结束节点', '巷道类型(Ventflow)', '位置(Place)', '用途类型(Lx)', '支护类型(ShoringType)', '断面形状(ShapeType)', '长度(Length)', '宽度(Width)', '高度(Height)', '面积(S)', '摩擦阻力系数(A)', '风阻(R)', '是否解算风支(NetLine)', '需风量(QDemand)', '解算风量(QCalculate)', '监测风量(QMonitor)', '风速(V)', '阻力(H)', '按需分风(Q2)', '按需分风(H2)', '按需分风(V2)', '按需分风(Q3)', '虚拟分风(H3)', '虚拟分风(V3)', '沿途阻力(HWay)', '局部阻力(HParte)', '相对压差(HRelate)', '动压差(HDynamic)', '静压差(HStatic)', '位能差(HEnergy)', '百米阻力(H100m)', '风速传感器(Vssz)'])


const updateTunList = ref([])
watch(searchTun, (v) => {
    if (v) {
        goTunLocation(v);
        const index = data.value.findIndex(k => k.TunID == v);
        if (index >= 0 && index < data.value.length) {
            const item = data.value.splice(index, 1)[0]; // 删除该项
            data.value.unshift(item); // 将该项添加到数组开头
        }
    } else {
        data.value = deepClone2(originData.value); // 恢复原始数据
        addOutlinePass([]);
    }
})
function saveTunUpdate() {
    // 调用接口 使用promise.all批量处理 保证每一项成功 再提示
    updateTunList.value.map(v => {
        v.MnodeID = v.MnodeID ? v.MnodeID : ''
        return v
    })
    console.log(updateTunList.value, 'updateTunList.value');

    Promise.all(updateTunList.value.map(v => ApiTunUpdate1(v))).then(res => {
        const status = res.every(v => v.Status == 0);
        if (status) {
            generateData();
            ElMessage.success('巷道修改成功');
            dataReset();
            setTimeout(() => {
                dataInit();
                updateTunList.value = [];
            })
        } else {
            ElMessage.error('巷道修改失败');
        }
    })
}
function cancelTunUpdate() {
    drawerShow.value = false;
    updateTunList.value = [];
}
onMounted(v => {
})

// 生成表格列
const generateColumns = () => {
    return tableProperty.value.map((v, i) => {
        return {
            key: v,
            dataKey: v,
            title: tableLabel.value[i],
            width: i == 2 ? 200 : 100,
            hidden: i == 1 ? true : false,
            align: 'center',
            dataAligin: 'center',
            fixed: i == 2 || i == 0 ? 'left' : false,
            sortable: i > 1 ? true : false,
            cellRenderer: (args) => {
                const { rowData, rowIndex, column } = args
                if (i == 0) {
                    return h('div', { class: "cursor-pointer bg-sky-500/40  flex  items-center mx-auto w-99% text-20px    py-10px px-5px rounded-5px" }, [
                        h(Location,
                            {
                                class: "w-25px h-25px text-[#1976d2]",
                                onclick: () => {
                                    goTunLocation(rowData.TunID, 100, false)
                                }
                            },
                        ), // 使用定义图标组件
                        h('div', { class: "ml-5px w-36x" }, `${rowData[column.dataKey]}`),
                    ]);
                }
                if (v == 'NetLine' || v == 'Ventflow') {
                    const options = v == 'NetLine' ?
                        [h('option', { value: true }, '是'),
                        h('option', { value: false }, '否'),] :
                        [h('option', { value: 1 }, '进风'),
                        h('option', { value: 0 }, '用风'),
                        h('option', { value: -1 }, '回风')]
                    //选择框 只有true和false的选项
                    return h('select', {
                        class: ' mx-auto w-99% border-2px border-solid border-sky-500/80  px-5px rounded-5px cursor-pointer',
                        value: rowData[column.dataKey], // 绑定 value
                        onChange: (event) => {
                            rowData[column.dataKey] = event.target.value; // 更新数据
                            udateUpdateTunList(rowData);
                        },
                    }, options)
                }
                if (v == 'Lx' || v == 'ShapeType' || v == 'ShoringType') {
                    const selectList = v == 'Lx' ? selectDataList.value.usageTypeList : (v == 'ShapeType' ? selectDataList.value.shapeTypeList : selectDataList.value.supportTypeList);
                    const options = selectList.map(item => h('option', { value: item.TypeID }, item.TypeName));

                    //选择框 只有true和false的选项
                    return h('select', {
                        class: ' mx-auto w-99% border-2px border-solid border-sky-500/80  px-5px rounded-5px cursor-pointer',
                        value: rowData[column.dataKey], // 绑定 value
                        onChange: (event) => {
                            rowData[column.dataKey] = event.target.value; // 更新数据
                            udateUpdateTunList(rowData);
                        },
                    }, options)
                }
                return h('input', {
                    id: 'inputBox',
                    type: i == 2 || i == 4 ? '' : 'number',
                    class: 'mx-auto h-30px w-99% border-2px border-solid border-sky-500/80 py-10px px-5px rounded-5px cursor-pointer',
                    value: rowData[column.dataKey], // 绑定 value
                    onInput: (event) => {
                        rowData[column.dataKey] = event.target.value; // 更新数据
                    },
                    onChange: (event) => {
                        udateUpdateTunList(rowData);
                    }
                });
            },
        }
    })
}
// 表格更新
function udateUpdateTunList(rowData) {
    let index = -1;
    const tun = apiTunList.value.find(v => v.TunID == rowData.TunID);
    const obj = { ...tun, ...rowData };
    if (updateTunList.value.length) {
        index = updateTunList.value.findIndex(v => v.TunID == rowData.TunID);
    }
    if (index != -1) {
        updateTunList.value.splice(index, 1, obj);
    } else {
        updateTunList.value.push(obj);
    }
}
// 定位到巷道
function goTunLocation(TunID, addOffset = 100) {
    addOutlinePass([]);
    const mesh = meshGroup.children.find(v => TunID == v.mid);
    cameraFlyToMesh(cameraControls, { mesh: mesh, paddingTop: addOffset, paddingRight: addOffset, paddingBottom: addOffset, paddingLeft: addOffset });
    addOutlinePass([mesh], 0x20c997);
}
let columns = generateColumns();
let columns2 = ref(deepClone2(columns));
const data = ref([])
const originData = ref([])
const disableEvents = ref(true)
const generateData = async () => {
    await ApiTunList().then(({ Result }) => {
        apiTunList.value = Result;
        data.value = Result.map((v, rowIndex) => {
            return columns.reduce(
                (rowData, column, columnIndex) => {
                    if (column.dataKey == 'id') {
                        rowData[column.dataKey] = rowIndex + 1
                    } else {
                        rowData[column.dataKey] = columnIndex > 6 && getType(v[column.dataKey]) == 'number' ? Number(v[column.dataKey].toFixed(2)) : v[column.dataKey]
                    }
                    return rowData
                },
                {
                    parentId: null,
                }
            )
        })

        originData.value = Result.map((v, i) => ({ ...v, id: i + 1 }));
    })

}
const sortData = tableProperty.value.filter((v, i) => i > 1)
const sortState = ref(Object.fromEntries(sortData.map(key => [key, TableV2SortOrder.ASC])))
// 排序
const onSort = ({ key, order }) => {
    sortState.value[key] = order
    if (key === 'TunName') {
    // 对TunName字段按拼音首字母排序
    data.value.sort((a, b) => {
      const getFirstLetter = (str) => {
        if (!str) return '';
        const py = pinyin(str.charAt(0), { pattern: 'first' });
        return py.toUpperCase();
      };
      const letterA = getFirstLetter(a.TunName);
      const letterB = getFirstLetter(b.TunName);
      if (letterA < letterB) {
        return order === 'asc'? -1 : 1;
      }
      if (letterA > letterB) {
        return order === 'asc'? 1 : -1;
      }
      // 首字母相同则按原始字符串比较
      return order === 'asc'? a.TunName.localeCompare(b.TunName) : b.TunName.localeCompare(a.TunName);
    });
  } else {
    // 对其他字段按原逻辑排序
    data.value.sort((a, b) => order === 'asc'? a[key] - b[key] : b[key] - a[key]);
  }

}

function operateTun(num) {
    addOutlinePass([]);
    currentOperateId.value = num;
    // 清除上次触发事件
    if (currentRemoveEvent && currentRemoveEvent instanceof Function) {
        currentRemoveEvent();
    }
    if (cancelVarFn && cancelVarFn instanceof Function) {
        cancelVarFn();
    }
    let msg = ''
    switch (num) {
        case 1:
            msg = '当前处于巷道配置模式'
            const { clearEvent: clearEvent1 } = tunEdit();
            currentRemoveEvent = clearEvent1;
            break;
        case 2:
            msg = '当前处于设备绑定模式'
            const { clearEvent: clearEvent2 } = deviceBinding();
            currentRemoveEvent = clearEvent2;
            break;
        case 3:
            msg = '当前处于三区分布配置模式'
            const { clearEvent: clearEvent3 } = threeZoneDistribution();
            currentRemoveEvent = clearEvent3;
            break;
        case 4:
            drawerShow.value = true;
            generateData();
            function tunEditclick() {
                const { currentMesh } = meshPickup(event, meshGroup.children, camera, sceneDiv.value);
                if (currentMesh) {
                    searchTun.value = currentMesh.object.originPoints.TunID;
                } else {
                    searchTun.value = ''
                }
            }
            addEvent(window, 'click', tunEditclick);
            function clearEvent() {
                removeEvent(window, 'click', tunEditclick);
            }
            currentRemoveEvent = clearEvent;
            break;
    }
    if (msg) {
        ElMessage.success(msg);
    }

}
function resetVar() {
    resultData.value = ''
    resultBoxShow.value = true;
    resultBoxLoading.value = true;
}
const selectDataList = ref({
    supportTypeList: [],
    shapeTypeList: [],
    usageTypeList: [],
})

onMounted(() => {
    //支护类型
    ApiSupportTypeList().then(res => {
        if (res.Status == 0) {
            const { Result } = res;
            selectDataList.value.supportTypeList = Result;
        }
    })

    // 断面形状
    ApiShapeList().then(res => {
        if (res.Status == 0) {
            const { Result } = res;
            selectDataList.value.shapeTypeList = Result;
        }
    })
    // 用途类型
    ApiUseList().then(res => {
        if (res.Status == 0) {
            const { Result } = res;
            selectDataList.value.usageTypeList = Result;
        }
    }).then(() => {

        generateData();
    })
        const handleWheel = (event) => {
    if (!disableEvents.value) return;
    
    event.preventDefault();
    
    const currentZoom = getZoomLevel();
    
    // if (event.deltaY < 0) {
    //   cameraControls.dollyIn();
    // } else {
    //   cameraControls.dollyOut();
    // }
  };
  window.addEventListener('wheel', handleWheel, { passive: false });
  onUnmounted(() => {
    window.removeEventListener('wheel', handleWheel);
  });
})
// 结构查询
async function structuralQuery() {
    resultBoxShow.value = true;
    handlerFunc.value = '结构'
}
// 通风解算
async function solutionResult() {
    handlerFunc.value = '解算'
}

// 困难路线
async function hardRoute() {
    handlerFunc.value = '困难路线'
}
// 筛选表格
const checkAll = ref(false)
const isIndeterminate = ref(true)
const checkedCities = ref([])
const filterShow = ref(false)
const defaultList = ['id', 'TunID', 'TunName', 'SnodeID', 'MnodeID', 'EnodeID', 'Ventflow'];// 常显

const handleCheckAllChange = (val) => {
    checkedCities.value = val ? columns.map(item => item.key) : []
    isIndeterminate.value = false
}
const handleCheckedCitiesChange = (value) => {
    const checkedCount = value.length
    checkAll.value = checkedCount === columns.length
    isIndeterminate.value = checkedCount > 0 && checkedCount < columns.length
}
function confirmFilter() {
    columns2.value = columns.filter(v => [...defaultList, ...checkedCities.value].includes(v.key))
}
const labelShow = ref(true)
function changeSpriteLabel(val) {
    labelShow.value = val;
    showOrHideLabels(val, 0);
}
let distanceValue=5
function getZoomLevel() {
  const distance = camera.position.distanceTo(new THREE.Vector3(0, 0, 0));
  // 根据场景实际大小调整这些值
  const maxDistance = 10000; 
  const minDistance = 100;  
  const clampedDistance = Math.min(Math.max(distance, minDistance), maxDistance);
  const normalized = 1 + ((clampedDistance - minDistance) / (maxDistance - minDistance)) * 4;
  distanceValue = Math.round(normalized)
  console.log('123123');
  console.log(Math.round(normalized),'distance');
  return Math.round(normalized);
}
ani((...args) => {
    const [time, camera, delta] = args
    const zoomLevel = getZoomLevel();
    tunLabelShowByCamera && tunLabelShowByCamera(camera, labelShow.value);
})

// 重置效果
function resetEffect() {
    addOutlinePass([])
    restCamera()
}


/** 巷道属性修改 */
const tunEditShow = ref(false);
const tunEditInfo = ref({});
let tunEditChangeMesh = null;
function tunEdit() {
    if (currentOperateId.value == 1) {
        addOutlinePass([]);
        tunEditAddEvent();
    }
    watch(tunEditShow, val => {
        if (!val && currentOperateId.value == 1) {
            addOutlinePass([]);
            setTimeout(() => {
            }, 10);
        }

    })

    function tunEditclick(event) {
        const { currentMesh } = meshPickup(event, meshGroup.children, camera, sceneDiv.value);
        if (currentMesh) {
            tunEditInfo.value = currentMesh.object.originPoints;
            addOutlinePass([currentMesh.object], 0xf08c00);
            tunEditShow.value = true;
            tunEditChangeMesh = currentMesh.object;
        }
    }
    function clearEvent() {
        removeEvent(window, 'click', tunEditclick);
    }
    function tunEditAddEvent() {
        addEvent(window, 'click', tunEditclick);
    }
    return { clearEvent }
}
async function saveTunEdit() {
    // ElMessage.success('修改已保存');
    const id = tunEditChangeMesh.mid;
    if (!id) {
        ElMessage.error('该巷道尚未上传,无法修改其属性');
    } else {
        const obj = apiTunList.value.find(v => v.TunID == id);
        const { Ventflow, TunName } = tunEditInfo.value;
        if (!obj) return;
        const MnodeID = obj.MnodeID ?? ''
        const tunObj = { ...obj, MnodeID, ...tunEditInfo.value }
        const { Status } = await ApiTunUpdate(tunObj);
        if (Status == 0) {
            ElMessage.success('巷道修改成功');
            tunEditChangeMesh.material = tun(tunEditChangeMesh.points, { isEditMat: false, ventflow: Ventflow }).mat;
            // 替换label
            const { middlePoint: { x, y, z }, mid } = tunEditChangeMesh;
            const sprite = createSpriteLabel(x, y, z, TunName,TunID,distanceValue);
            spriteLabelGroup.remove(spriteLabelGroup.children.find(v => v.mid == mid));
            spriteLabelGroup.add(sprite);
        } else {
            ElMessage.error('巷道修改失败');
        }
    }
    tunEditShow.value = false;
}


/** 设备绑定 */
const deviceBindingShow = ref(false);

let deviceBindingTun = ref({ originPoints: { TunName: "" } });
let deviceBindingLocation = null;
let interruptMiddleLine = null;
let auxiliaryPoint = ref({ originPoint: { x: null, y: null, z: null } });
// deviceBindingShow.value = true;

// 数据获取
const deviceList = ref()
const currentDeviceID = ref(1);
let deviceForm = {
    FacilityID: '', FacilityType: '', x: '', y: '', z: '', TunID: '', NodeID: '', Remark: ''//距离
}
async function getDevice(type) {
    currentDeviceID.value = type;
    switch (type) {
        case 1:
            deviceList.value = await ApiSecurityMonitoringList().then(({ data: { Result } }) => { return Result.map(v => { return { ...v, state: false } }) });
            deviceForm.FacilityType = '安全监控'
            break;
        case 2: deviceList.value = await ApiMainFanList().then(({ Result }) => { return Result.map(v => { return { ...v.Fannerinfo, state: false } }) });
            deviceForm.FacilityType = '主通风机'
            break;
        // case 3: deviceList.value = await ApiLocalFanList().then(({ Result }) => { return Result.map(v => { return { ...v.LocalFaninfo, state: false } }) });
        //     break;
        case 4: deviceList.value = await ApiWindDoorList().then(({ Result }) => { return Result.map(v => { return { ...v.AirDoorinfo, state: false } }) });
            deviceForm.FacilityType = '自动风门'
            break;
        case 5: deviceList.value = await ApitWindwindowList().then(({ Result }) => { return Result.map(v => { return { ...v.WindWindowInfo, state: false } }) });
            deviceForm.FacilityType = '自动风窗'
            break;
    }
    getDeviceBindByType(type).then(v => {
        const FacilityIDList = deviceFilterList.value.map(v => v.FacilityID)
        // deviceList.value
        deviceList.value.forEach(v => {
            if (FacilityIDList.includes(v.ID)) {
                v.state = true;
            }
        })
    })
}
getDevice(1)
// 获取全部 已经绑定的关系


getDeviceBindByType(6)
// 绑定 更新绑定
async function handleDeviceBind(item, index, event) {
    if (typeof auxiliaryPoint.value.mid == 'undefined') {
        ElMessage.warning('你需要先设置设施放置的位置')
        return
    }
    const { state, ID } = item;
    stopEvent(event);
    let data = { FacilityID: '', FacilityType: '', x: '', y: '', z: '', TunID: '', NodeID: '', Remark: '', OperateInfo: '', RealValue: '', IOTime: '', Upflag: '' }
    deviceForm.FacilityID = ID;
    if (!state) {
        let newData = { ...data, ...deviceForm }
        const { data: { Status, Msg } } = await facilityBindingRoadway(newData);
        handle(Status, Msg);
    } else {
        const find = deviceFilterList.value.find(v => v.FacilityID == item.ID);
        let newData = { ...find, ...deviceForm }
        const { data: { Status, Msg } } = await facilityUpdateBindingRoadway(newData);
        handle(Status, Msg);
    }

    function handle(Status, Msg) {
        if (Status == 0) {
            ElMessage.success('保存成功')
            !state && (deviceList.value.at(index).state = true);
            getDeviceBindByType();
            deviceBindingReset();
            setTunDevice();
        } else {
            ElMessage.warning(Msg)
        }
    }
}

async function handleDeviceBindDel(item, index, event) {

    const { ID } = item;
    stopEvent(event);
    const find = deviceFilterList.value.find(v => v.FacilityID == ID);
    const { data: { Status, Msg } } = await facilityDelBindingRoadway(find.ID);
    handle(Status, Msg);
    function handle(Status, Msg) {
        if (Status == 0) {
            ElMessage.success('保存成功')
            deviceList.value.at(index).state = false;
            getDeviceBindByType();
            deviceBindingReset();
            setTunDevice();
        } else {
            ElMessage.warning(Msg)
        }
    }
}


function deviceBinding() {
    if (currentOperateId.value == 2) {
        addEvents();
    }

    function click(event) {
        const { currentMesh } = meshPickup(event, meshGroup.children, camera, sceneDiv.value);
        if (currentMesh) {
            // //console.log(currentMesh, 'currentMesh');
            // deviceBindingLocation = currentMesh.point;
            if (deviceBindingTun.value && deviceBindingTun.value.mid != currentMesh.mid) {
                // 恢复上一个
                deviceBindingTun.value.visible = true;
            }
            deviceBindingTun.value = currentMesh.object;
            deviceBindingTun.value.visible = false;
            if (!interruptMiddleLine) {
                interruptMiddleLine = createLine({ linePoints: deviceBindingTun.value.points })
                scene.add(interruptMiddleLine)
            } else {
                interruptMiddleLine.geometry = createLine({ linePoints: deviceBindingTun.value.points }).geometry;
            }
        }
        if (interruptMiddleLine) {
            const { currentMesh: mesh } = meshPickup(event, [interruptMiddleLine], camera, sceneDiv.value);
            if (mesh) {
                if (deviceBindingTun.value && deviceBindingLocation) {
                    const { x, y, z } = deviceBindingLocation;
                    const originPoint = createOriginPoint(x, y, z);

                    if (typeof auxiliaryPoint.value.mid != 'undefined') {
                        auxiliaryPoint.value.position.set(x, y, z);
                        auxiliaryPoint.value.point = new THREE.Vector3(x, y, z);
                        auxiliaryPoint.value.originPoint = originPoint;
                    } else {
                        const { sphere } = createPoint({ x, y, z, originPoint, isEditMat: true, color: '#ff0000' });
                        auxiliaryPoint.value = sphere;
                        pointGroup.add(sphere);
                    }

                    const points = deviceBindingTun.value.points;
                    const { Ventflow, Nodes } = deviceBindingTun.value.originPoints;
                    let minDistance = 0;
                    let num = 0;
                    // 查找距离该点最近的巷道点位
                    points.forEach((v, i) => {
                        const distance = calculateDistance(deviceBindingLocation, v);
                        i == 0 && (minDistance = distance);
                        if (distance < minDistance) {
                            minDistance = distance;
                            num = i;
                        }
                    })
                    // 计算拾取点位 跟该点位哪个距离零点较近
                    const distance1 = calculateDistance(points[num], points[0]);
                    const distance2 = calculateDistance(deviceBindingLocation, points[0]);
                    let points1, originsPoint1;
                    if (distance2 > distance1) {
                        points1 = [...points.slice(0, num + 1), deviceBindingLocation];
                        originsPoint1 = [...Nodes.slice(0, num + 1), originPoint];
                    } else {
                        points1 = [...points.slice(0, num), deviceBindingLocation];
                        originsPoint1 = [...Nodes.slice(0, num), originPoint];
                    }


                    const Length = computeTunLenght(originsPoint1);
                    deviceForm.Remark = Length;
                    deviceForm.x = originPoint.x;
                    deviceForm.y = originPoint.y;
                    deviceForm.z = originPoint.z;
                    deviceForm.TunID = deviceBindingTun.value.mid;

                    interruptMiddleLine && scene.remove(interruptMiddleLine);
                    interruptMiddleLine = null;
                    deviceBindingTun.value.visible = true;
                    deviceBindingShow.value = true;
                }
            }
        }

    }
    function mouseMove(event) {
        if (interruptMiddleLine) {
            const { currentMesh: mesh } = meshPickup(event, [interruptMiddleLine], camera, sceneDiv.value);
            if (mesh) {
                deviceBindingLocation = mesh.point;
                interruptMiddleLine.material.color = new THREE.Color(0xff0000);
            } else {
                interruptMiddleLine.material.color = new THREE.Color(0xffff00);
            }
        }
        const { currentMesh } = meshPickup(event, meshGroup.children, camera);
        if (currentMesh) {
            addOutlinePass([currentMesh.object], '#5f3dc4');
        } else {
            addOutlinePass([]);
        }
    }


    // function dblClick() {

    // }

    // 添加事件
    function addEvents() {
        addEvent(window, 'click', click);
        addEvent(window, 'mousemove', mouseMove);
        // addEvent(window, 'dblclick', dblClick);
    }

    // 移除事件
    function clearEvent() {
        removeEvent(window, 'click', click);
        removeEvent(window, 'mousemove', mouseMove);
        // removeEvent(window, 'dblclick', dblClick);
    }
    cancelVarFn = deviceBindingReset;
    return { clearEvent }
}


function saveDeviceBinding() {


    // deviceBindingReset();
}
function deviceBindingReset() {
    interruptMiddleLine && scene.remove(interruptMiddleLine);
    interruptMiddleLine = null;
    deviceBindingTun.value = { originPoints: { TunName: "" } };
    if (typeof auxiliaryPoint.value.mid != 'undefined') {
        pointGroup.remove(toRaw(auxiliaryPoint.value));
    }
    auxiliaryPoint.value = { originPoint: { x: null, y: null, z: null } };
}

/** 三区分布*/
const threeZoneDistributionShow = ref(false)
const threeZoneDistributionInfo = ref([{ NodeID: '', NodeName: '', x: null, y: null, z: null, state: false }])

function threeZoneDistribution() {
    threeZoneDistributionShow.value = true;
    if (currentOperateId.value == 3) {
        addOutlinePass([]);
        AddEvent();
    }
    watch(threeZoneDistributionShow, val => {
        if (val) {
            // clearEvent();
            ElMessage.info('依次选择起始点,双击确认使用该点');
        } else {
            if (currentOperateId.value == 3) {
                addOutlinePass([]);
                // AddEvent();
            }
        }
    })

    function click(event) {
        const { currentMesh } = meshPickup(event, pointGroup.children, camera, sceneDiv.value);
        if (currentMesh) {
            const { originPoint } = currentMesh.object;
            if (!threeZoneDistributionInfo.value.at(0).state) {
                threeZoneDistributionInfo.value.splice(0, 1, { ...originPoint, state: false });
            } else {
                threeZoneDistributionInfo.value.splice(1, 1, { ...originPoint, state: false });
            }
            addOutlinePass([currentMesh.object], 0x7048e8);
            threeZoneDistributionShow.value = true;
            console.log(threeZoneDistributionInfo, 'threeZoneDistributionInfo');
        }
    }
    function dblclick(event) {
        const { currentMesh } = meshPickup(event, pointGroup.children, camera, sceneDiv.value);
        if (currentMesh) {
            const { mid } = currentMesh.object;
            let index = (threeZoneDistributionInfo.value[0].NodeID && threeZoneDistributionInfo.value.findIndex(v => v.NodeID == mid)) ?? -1
            if (index != -1) {
                threeZoneDistributionInfo.value.at(index).state = true;
            }
        }
    }
    function clearEvent() {
        removeEvent(window, 'click', click);
        removeEvent(window, 'dblclick', dblclick);
    }
    function AddEvent() {
        addEvent(window, 'click', click);
        addEvent(window, 'dblclick', dblclick);
    }
    cancelVarFn = cancelThreeZoneDistribution;
    return { clearEvent }
}

function saveThreeZoneDistribution() {

}
function cancelThreeZoneDistribution() {
    addOutlinePass([]);
    threeZoneDistributionShow.value = false
    threeZoneDistributionInfo.value = [{ NodeID: '', NodeName: '', x: null, y: null, z: null, state: false }]
}
</script>
<style lang="scss">
.sbbd_header {
    .el-icon {
        top: 15px;

        svg {
            color: #fff;

            &:hover {
                color: #ea2465;
            }
        }

    }
}

.sbbd {
    padding: 20px;
    width: 500px;
    background-image: url('./assets/img/side.png');
    background-repeat: no-repeat;
    height: 800px;
    background-size: 500px 800px;
    top: 150px;

    .el-drawer__body {
        overflow: hidden;
    }

}
</style>
<style lang="scss">
@use './assets/index.scss';
</style>
<style lang="scss" scoped>
:deep(.el-button--primary) {
    background-color: rgba(255, 179, 15, 0.3) !important;
    border: 1px solid rgba(232, 157, 6, 0.8) !important;
}

:deep(.el-checkbox) {
    color: #eee !important;
}

:deep(.el-checkbox__label) {
    color: #eee !important;
}

.resizable {
    position: relative;
    overflow: hidden;
    margin: 10px 0 45px 0;
    /* 防止内容溢出 */
}

.content {
    padding: 10px;
}

.resizer {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 8px;
    margin: auto;
    border-radius: 20px;
    width: 30%;
    background-color: #00ffff22;
    cursor: ns-resize;

    /* 竖向拉伸光标 */
    &:hover {
        background-color: #00ffff55;
    }
}

.table-v2-inline-editing-trigger {
    border: 1px transparent dotted;
    padding: 4px;
}

.table-v2-inline-editing-trigger:hover {
    border-color: var(--el-color-primary);
}

.btn_img {
    width: 19px;
    height: 19px;
}

.btn_menu {
    :deep(.el-button--large) {
        width: 135rem;
        height: 50rem;
        /* background-image: url('../tunEdit/assets/img/btn_bg.png'); */
        background-size: 100%;
    }

    :deep(.el-button--small) {
        width: 100rem;
        height: 30rem;
        /* background-image: url('../tunEdit/assets/img/btn_bg.png'); */
        background-size: 100%;
    }
}


:deep(.el-form-item__label) {
    color: #fff;
}

:deep(.el-form-el-dialog__header) {
    margin: 10px 0 0 10px;
    color: #fff;
}

:v-deep(.el-button .el-icon) {
    height: 0.5em;
    width: 0.5em;
}

:deep(.el-input.is-disabled .el-input__inner) {
    -webkit-text-fill-color: #2962ff;
    cursor: not-allowed;
}
</style>