<template>
    <div class="relative w-100% h-100%" :class="store.$state.interactionMode == 0 ? 'cursor-grabbing' : ''">
        <div class="w-100% h-100%" ref="sceneDiv"></div>
           <!-- 悬浮框 -->
        <TunInfo :currentTun="currentTun" />
        <!-- <Toolbar :resetEffect="resetEffect" :camera-angle="initMesh?.cameraAngle"
            style="position: fixed;left: 2rem;top:40rem">
        </Toolbar> -->
        <!--  -->
        <!-- <div class="czsm_div" absolute bottom-717px right-68px v-show="currentOperateId"> -->

        <div v-show="tControlPosition" border="1px solid white"
            class=" rounded-10px absolute right-20px bottom-20px w-220px h-150px opacity-75 text-#f8f9fa font-bold bg-[#082237] flex flex-col justify-around items-center">
            <div class="w-180px text-left"><span class="font-bold font-30px">X:</span> {{ tControlPosition &&
                tControlOriginPosition.x.toFixed(5) }}</div>
            <div class="w-180px text-left"><span class="font-bold font-30px">Y:</span> {{ tControlPosition &&
                tControlOriginPosition.y.toFixed(5) }}</div>
            <div class="w-180px text-left"><span class="font-bold font-30px">Z:</span> {{ tControlPosition &&
                tControlOriginPosition.z.toFixed(5) }}</div>
        </div>
        <!-- 巷道属性修改 -->
        <el-dialog title="巷道属性修改" v-model="tunEditShow" width="40%" style="height: 550rem;position: relative;">
            <el-form class="mt-20px mr-10px" :model="tunEditInfo" ref="form" :rules="rules" label-width="80px"
                :inline="false" size="normal">
                <el-form-item label="巷道名称" prop="TunName">
                    <el-input v-model="tunEditInfo.TunName" clearable></el-input>
                </el-form-item>
                <el-form-item label="用风类型" prop="Ventflow">
                    <el-select v-model="tunEditInfo.Ventflow">
                        <el-option :value="1" label="进风"></el-option>
                        <el-option :value="0" label="用风"></el-option>
                        <el-option :value="-1" label="回风"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="宽度" prop="Width">
                    <el-input v-model="tunEditInfo.Width" clearable></el-input>
                </el-form-item>
            </el-form>

            <template #footer>
                <span class="absolute bottom-40px right-30px ">
                    <el-button @click="tunEditShow = false">取消</el-button>
                    <el-button type="primary" @click="saveTunEdit">保存</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 使用说明 -->
    </div>


</template>

<script setup>
import { ref, onMounted, toRefs, watch, onUnmounted, watchEffect, onBeforeUnmount, toRaw } from 'vue'
import { useThree } from '@/three/useThree';

import { onBeforeRouteLeave, useRouter } from 'vue-router';
import _ from 'lodash'
import { deepClone, getType } from '@/utils/utils';
import mitt from '@/utils/eventHub';
import { useTunEdit } from '@/store/tunEdit';
import { storeToRefs } from 'pinia';
const props = defineProps(['labelShow'])
const { labelShow } = toRefs(props);
// 引入悬浮框
// import TunInfo from '../thereVentilation/components/tunInfo/tunInfo.vue'
const store = useTunEdit();
const { tunStr, roadwayList, enterFormDiv, updateNodeInfo, nodeList: apiNodeList, tunList: apiTunList, addTunNodeList: nodeList, addTunNodeIDList, updateTunNodeIDList, updateTunOther, operationMode, interactionMode,
    updateTunChangIndex: changeIndex, isThree3DFullScreen, isThree2DFullScreen, updateTunIsFlag: isFlag, setNodeListShow, operationRecord, restoreRecord, updateTunNodeList: updateNodeList, interruptMovePosition, interruptChangeMesh } = storeToRefs(store);

const router = useRouter();
// 获取图片的方法
function getImg(name, type = 'png') {
    return new URL(`./assets/img/${name}.${type}`, import.meta.url)
}
const sceneDiv = ref(null)
//存放悬浮
// const currentTun = ref({})

onMounted(() => {
    mountRender(sceneDiv.value)
    // mitt.on('get2DRecentNode', (mid) => {
    //     const mesh = [...pointGroup.children, ...middlePointList].find(v => v.mid == mid);
    //     cameraControls.fitToBox(mesh, true, { paddingTop: 600, paddingRight: 600, paddingBottom: 600, paddingLeft: 600 })
    // })
    // 接收相机飞跃到节点事件
    if (isThree2DFullScreen.value == true) { return }
    mitt.on('flyToPoint', (mid) => {
        goNodeLocation(mid, 100, false)
    })
    mitt.on('flyToTun', (mid) => {
        goTunLocation(mid, 100, false)
    })
    mitt.on('moveCamera', (angle) => {
        if (angle) {
            cameraAngle(angle);
        }
    })
    mitt.on('resetCamera', (angle) => {
        refreshCamera();
    })

});

onBeforeRouteLeave((leaveGuard, currentRoute, next) => {
    if (operationRecord.value.length > 0) {
        ElMessage.warning('你有未保存的修改,请保存！')
        next(false);
        return;
    } else {
        next(true);
    }
    dataReset();
    if (leaveGuard.name == 'ventilationCalculation' || leaveGuard.name == 'tunEdit') {
        return;
    }
    stopRender();
     addEvenetThrottle = null
})



const isShow = ref(false)
watch(tControlPosition, (val) => {
    if (val) {
        isShow.value = true;
    } else {
        isShow.value = false;
    }
})

// 导入辅助坐标轴
import { positionTransform, createSpriteLabel } from "@/views/thereVentilation/js/tunUtils.js";
import { createTun, tun, createPoint, createLine } from '@/views/thereVentilation/js/tun.js';
import { TransformControls } from 'three/examples/jsm/controls/TransformControls.js';
import { deepCloneMaterial, setMeshScale } from "@/three/utils/meshUtils";
// 获取接口数据
import { meshPickup } from '@/three/raycaster'
import { ElMessage, ElMessageBox } from "element-plus";
import { addEvent, removeEvent } from '@/utils/window';
import { NodeConfig } from './js/config';
import { calculateDistance } from "@/views/thereVentilation/js/tunUtils.js";
import { ApiNodeAdd, ApiNodeDel, ApiNodeUpdate, ApiNodeList, ApiTunAdd, ApiTunDel, ApiTunUpdate, ApiTunList } from '@/https/encapsulation/threeDimensional';
import { cameraFlyToMesh, moveToCamera } from '@/three/cameraControls.js'
import { tunInit, getTunList, tunLabelShowByCamera, getRecentNodeThrottleFn, showOrHideLabels } from '@/views/thereVentilation/js/tunInit';
import { dbModelClick, modelClick, modelHover } from '@/three/utils/event';
import { throttleFn } from '@/utils/utils';
import {
    tunOriginList, pointMeshArr, tunMeshArr, tunPointsAll, pointShowAll, allGroup, meshGroup, pointGroup, middlePointList, spriteLabelGroup, maxPosition,
    minPosition, averagePosition, transformMaxPosition, transformMinPosition, setY, n, middleNode, maxNodeID
} from '../thereVentilation/js/tunInit';// 变量
import { TunConfig } from '../thereVentilation/js/config';
const endCamera = [0, 4000, 0]
const { scene, mountRender, gsap, renderer, initModelArr, meshRemove, clearMesh, setPosition, restCamera, setMeshRotation, stopRender, cameraControls, ani, camera, THREE, addOutlinePass, addOutlinePass1, outlinePass } = useThree({ endCamera, isControl: true, isComposer: true, isCameraAni: false, isAxes: false, isDepthBuffer: true });;
cameraControls.dollySpeed = 0.32;
cameraControls.truckSpeed = 1.1;
watch(labelShow, (val) => {
    showOrHideLabels(val, 0);
})
// let removeEvent = null;
// 鼠标悬浮事件
// const shineMeshList = ref([])// 全部高亮列表
// const moveMesh = ref()// 当前悬浮的物体高亮
// const clickMesh = ref()// 当前点击的物体高亮
// const currentEvent = ref('')
// const disableEvents = ref(true)
camera.far = 100000;
// 初始化配置
outlinePass.edgeThickness = 4.0;
outlinePass.edgeGlow = 2.0;
outlinePass.edgeStrength = 4.0;
outlinePass.pulsePeriod = 3.0;
// 物体检测
// let addEvenetThrottle = throttleFn(addEvent1, 1500);
// watch(disableEvents, (val) => {
//     console.log(val);
// })
//引入悬浮框
function addEvent1() {

    /*悬浮*/
    const { hoverRemove } = modelHover(meshGroup.children, camera, (currentMesh, event) => {

        if (!disableEvents.value) return
        // if (isFlag.value) return;
        if (currentMesh) {
            const mesh = currentMesh.object.parent.children.length == 1 ? currentMesh.object.parent : currentMesh.object
            commonEvent('mousemove', mesh, event)
        } else {
            commonEvent('mousemove', '', event)
        }
    })
 
    function handleRemove() {
        console.log('handle remove');
        hoverRemove();
      
    }
    // removeEvent = throttleFn(handleRemove, 10);
}


//
function commonEvent(type, currentMesh, event) {
    
    currentEvent.value = type
    const shineList = [...shineMeshList.value]
    if (currentMesh) {
        if ((shineMeshList.value.findIndex(v => v.tunName == currentMesh.tunName)) != -1) { return; } // 高亮显示的一组物体不做处理
        const { originPoints, mid } = currentMesh;
        if (mid) {
            const { H, QCalculate, S, V } = tunOriginList.find(v => v.TunID == mid);
            currentTun.value = { x: event.x, y: event.y, H, QCalculate, S, V, tunName: originPoints.TunName, isShow: false }
        }

        switch (type) {
            case 'click':
                currentTun.value.isShow = false;
                clickMesh.value = currentMesh;
                break;
            case 'mousemove':
                currentTun.value.isShow = true;
                break;
        }
        if (moveMesh.value && clickMesh.value && moveMesh.value.mid == clickMesh.value.mid) {
            moveMesh.value = null
        }
        clickMesh.value && shineList.push(clickMesh.value)
        moveMesh.value && shineList.push(moveMesh.value)
        addOutlinePass(shineList)
    } else {
        currentTun.value = { isShow: false }
        if (type === 'click') {
            clickMesh.value = null
            moveMesh.value = null
            addOutlinePass(shineList)
        }
    }
}
//时间移除 数组

let currentRemoveEvent = null;

const { dataInitLocal, dataReset, refreshCamera, cameraAngle } = tunInit(scene, cameraControls);
// dataInit();
// 监听 operationRecord
const flagOperationRecord = ref(true)
watch(operationRecord, (val) => {
    flagOperationRecord && setTimeout(() => {
        localUpdate(val);
    }, 100)
    if (val.length == 10) {
        ElMessage.warning('您已操作10次，请保存！')
    }
})
onMounted(() => {
    // 监听撤销
    mitt.on('handleRepeal', (last) => {
        flagOperationRecord.value = false;
        repealLocalUpdate(last);
    })

    //  addEvent1();
})

// 局部更新渲染

function localUpdate(arr) {
    if (arr.length > 0) {
        const last = arr[arr.length - 1];
        getType(last) == 'array' && last.forEach((v, i) => {
            handleSwitch(toRaw(v));
        })
        // 不是数组的情况
        if (getType(last) == 'object') {
            handleSwitch(toRaw(last));
        }
    }
    function handleSwitch(value) {
        // 判断data中是否有TunID这个属性，如果有，则说明是巷道
        const { type, data } = value;
        let Nodes = [], linePoints = [];

        if ((type == 4 || type == 5) && data instanceof Object && data.hasOwnProperty('TunID')) {
            // 根据 Snode Enode Mnode 查找节点
            const { SnodeID, EnodeID, MnodeID } = toRaw(data);
            const list = MnodeID ? [SnodeID, ...MnodeID.split('-').map(v => Number(v)).filter(v => v != 0), EnodeID] : [SnodeID, EnodeID]
            Nodes = list.map(k => toRaw(apiNodeList.value.find(n => n.NodeID == k)));
            linePoints = Nodes.map((k, i) => {
                if (!k) return;
                const { NodeID, x, y, z } = k;
                const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { n, middlePosition: middleNode.value });
                return { ...k, x: x1, y: y1, z: z1 }
            })
        }
        switch (type) {
            case 1:
                // 新增
                const { x: x1, y: y1, z: z1 } = data;
                const { x: x2, y: y2, z: z2 } = positionTransform(x1, y1, z1, { n, middlePosition: middleNode.value, type: 0 })
                const { sphere } = createPoint({ x: x2, y: y2, z: z2, originPoint: data, type: 1, ventflow: 0 })
                pointMeshArr.push(sphere);
                pointGroup.add(sphere);
                break;
            case 2:
                // 更新
                const { x: x3, y: y3, z: z3 } = data;
                const { x: x4, y: y4, z: z4 } = positionTransform(x3, y3, z3, { n, middlePosition: middleNode.value, type: 0 })
                const mesh2 = pointMeshArr.find(v => v.mid == data.NodeID);
                mesh2.position.copy({ x: x4, y: y4, z: z4 });
                mesh2.originPoint = data;
                // 与节点相关的巷道全部更新
                const tunList = apiTunList.value.filter(k => k.SnodeID == data.NodeID || k.EnodeID == data.NodeID || (k.MnodeID ? k.MnodeID.includes(data.NodeID) : ''));
                tunList.forEach(j => {
                    const { SnodeID, EnodeID, MnodeID } = j;
                    const list = MnodeID ? [SnodeID, ...MnodeID.split('-').map(v => Number(v)).filter(v => v != 0), EnodeID] : [SnodeID, EnodeID]
                    if (!(list.includes(data.NodeID)) || SnodeID == '1' || EnodeID == '1') return;
                    const Nodes = list.map(k => toRaw(apiNodeList.value.find(n => n.NodeID == k)));
                    if (!Nodes.every(v => v)) return;

                    const linePoints = Nodes.map((k, i) => {
                        const { NodeID, x, y, z } = k;
                        const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { n, middlePosition: middleNode.value });
                        return { ...k, x: x1, y: y1, z: z1 }
                    })
                    updateTun(linePoints, Nodes, j)
                })
                break;
            case 3:
                // 节点删除
                const point3 = pointMeshArr.find(v => v.mid == data.NodeID);
                point3.visible = false;
                // 与节点相关的巷道全部隐藏
                tunMeshArr.value.forEach((j, i) => {
                    const { SnodeID, EnodeID, MnodeID } = j.originPoints;
                    const list = [SnodeID, , MnodeID, EnodeID]
                    if (list.includes(data.NodeID)) {
                        j.visible = false;
                    };
                })
                break;
            case 4:
                let radius = TunConfig.radius;
                if (data.Lx == 41) {
                    radius = TunConfig.excavationRadius;
                }
                const { meshArr } = createTun({ tubePoints: linePoints, radius, originPoints: { Nodes, ...data } });
                if (meshArr.length > 0) {
                    tunMeshArr.value.push(...meshArr);
                    meshGroup.add(...meshArr);
                }
                break;
            case 5:
                updateTun(linePoints, Nodes, data)
                break;
            case 6:
                // 巷道删除
                const mesh = tunMeshArr.value.find(v => v.originPoints.TunID == data.TunID);
                const index = tunMeshArr.value.findIndex(v => v.originPoints.TunID == data.TunID);
                mesh && (mesh.visible = false, tunMeshArr.value.splice(index, 1));
                break;
        }
    }
}

// 撤销更新渲染

function repealLocalUpdate(value) {
    const last = toRaw(value);
    getType(last) == 'array' && last.forEach((v, i) => {
        handleSwitch(toRaw(v))
    })
    // 不是数组的情况
    if (getType(last) == 'object') {
        handleSwitch(toRaw(last))
    }
    function handleSwitch(value) {
        let { type, data, index } = value;
        let Nodes = [], linePoints = [];
        if ((type == 4 || type == 5) && data instanceof Object && data.hasOwnProperty('TunID')) {
            // 根据 Snode Enode Mnode 查找节点
            const { SnodeID, EnodeID, MnodeID } = toRaw(data);
            const list = MnodeID ? [SnodeID, ...MnodeID.split('-').map(v => Number(v)).filter(v => v != 0), EnodeID] : [SnodeID, EnodeID]
            Nodes = list.map(k => toRaw(apiNodeList.value.find(n => n.NodeID == k)));
            linePoints = Nodes.map((k, i) => {
                if (!k) return;
                const { NodeID, x, y, z } = k;
                const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { n, middlePosition: middleNode.value });
                return { ...k, x: x1, y: y1, z: z1 }
            })

        }
        switch (type) {
            case 1:
                const index3 = pointMeshArr.findIndex(v => v.mid == data.NodeID);
                pointMeshArr.splice(index3, 1);
                pointGroup.children.splice(index3, 1);
                break;
            case 2:
                const { x: x1, y: y1, z: z1 } = data;
                const { x: x2, y: y2, z: z2 } = positionTransform(x1, y1, z1, { n, middlePosition: middleNode.value, type: 0 })
                const mesh2 = pointMeshArr.find(v => v.mid == data.NodeID);
                mesh2.position.copy({ x: x2, y: y2, z: z2 });
                mesh2.originPoint = data;
                // 与节点相关的巷道全部更新
                const tunList = apiTunList.value.filter(k => k.SnodeID == data.NodeID || k.EnodeID == data.NodeID || (k.MnodeID ? k.MnodeID.includes(data.NodeID) : ''));
                tunList.forEach(j => {
                    const { SnodeID, EnodeID, MnodeID } = j;
                    const list = MnodeID ? [SnodeID, ...MnodeID.split('-').map(v => Number(v)).filter(v => v != 0), EnodeID] : [SnodeID, EnodeID]
                    if (!(list.includes(data.NodeID)) || SnodeID == '1' || EnodeID == '1') return;
                    const Nodes = list.map(k => toRaw(apiNodeList.value.find(n => n.NodeID == k)));
                    if (!Nodes.every(v => v)) return;

                    const linePoints = Nodes.map((k, i) => {
                        const { NodeID, x, y, z } = k;
                        const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { n, middlePosition: middleNode.value });
                        return { ...k, x: x1, y: y1, z: z1 }
                    })
                    updateTun(linePoints, Nodes, j)
                })
                break;
            case 3:
                // 节点删除
                const point3 = pointMeshArr.find(v => v.mid == data.NodeID);
                point3.visible = true;
                // 与节点相关的巷道全部隐藏
                tunMeshArr.value.forEach((j, i) => {
                    const { SnodeID, EnodeID, MnodeID } = j.originPoints;
                    const list = [SnodeID, , MnodeID, EnodeID]
                    if (list.includes(data.NodeID)) {
                        j.visible = true;
                    };
                })
                break;
            case 4:
                const index4 = tunMeshArr.value.findIndex(k => k.mid == data.TunID);
                const mesh4 = meshGroup.children.find(k => k.mid == data.TunID);
                if (!mesh4) return;
                mesh4.visible = false;
                mesh4.position.copy({ x: 0, y: 0, z: 0 });
                meshGroup.remove(mesh4);
                scene.remove(mesh4);
                tunMeshArr.value.splice(index4, 1);
                break;
            case 5:
                updateTun(linePoints, Nodes, data)
                break;
            case 6:
                const mesh = meshGroup.children.find(v => v.originPoints.TunID == data.TunID);
                const index = meshGroup.children.findIndex(v => v.originPoints.TunID == data.TunID);
                mesh && (mesh.visible = true, tunMeshArr.value.splice(index, 1, mesh));
                break;
            default:
                //更新 meshGroup
                meshGroup.updateMatrixWorld(true);
                meshGroup.updateMatrix();
                break;

        }
    }
    flagOperationRecord.value = true;
}
// 局部更新巷道
function updateTun(linePoints, Nodes, data) {
    const mesh5 = meshGroup.children.find(m => m.mid == data.TunID);

    const { meshArr: meshArr5 } = createTun({ tubePoints: linePoints, radius: TunConfig.radius, originPoints: { Nodes, ...data } });
    mesh5.geometry = meshArr5[0].geometry;
    mesh5.originPoints = meshArr5[0].originPoints;
    mesh5.material = meshArr5[0].material.clone();
    mesh5.material.needsUpdate = true;

}
// 监听执行渲染
watch(() => store.$state.renderFlag, (flag) => {
    if (flag > 1) {
        handleRender(false);
    } else {
        handleRender();
    }
    // 恢复相机位置
    camera.position.x = lastCamera.value.x;
    camera.position.y = lastCamera.value.y;
    camera.position.z = lastCamera.value.z;
})
// 监听操作模式变化
watch(() => store.$state.interactionMode, (num) => {
    changeEvent(num);
})

function changeEvent(val) {
    if (val) {
        clear();
        clearEffect('nodeClick'); clearEffect('tunClick');
    }
    if (val == 1) {
        nodeInteraction(sceneDiv.value);
    } else if (val == 2) {
        watch(setNodeListShow, (val) => {
            clear();
            addOutlinePass([]);
            addOutlinePass1([]);
            clearEffect('nodeClick'); clearEffect('tunClick');
            if (val) {
                nodeInteraction(sceneDiv.value);
            } else {
                tunInteraction(sceneDiv.value);
            }
        })
        watch(changeIndex, (val) => {
            clear();
            if (val != null && val != -1) {
                nodeInteraction(sceneDiv.value);
            } else {
                addOutlinePass1([]);
                clearEffect('nodeClick'); clearEffect('tunClick');
                tunInteraction(sceneDiv.value);
            }
        }, { immediate: true })
    } else {
        clear();
        clearEffect('nodeClick'); clearEffect('tunClick');
        twoAndThreeLink.value = true;
    }
}

function clear() {
    if (currentRemoveEvent && currentRemoveEvent instanceof Function) {
        currentRemoveEvent();
        tControlRemoveEvent();
    }
}

var initMesh = {};
// 处理刷新
function handleRender(isResetCamera = true) {
  
    dataReset(() => {
        currentRemoveEvent = null;
        resetVariable();
    });
    initMesh = dataInitLocal(store.$state.nodeList, store.$state.tunList, roadwayList.value, tunStr, isResetCamera);
}

// 定位到物体所在位置
function goNodeLocation(NodeID, addOffset = 100, isAddOutlinePass = true) {
    let mesh = pointGroup.children.find(v => v.mid == NodeID);
    addOutlinePass1([mesh], 0x20c997);
    cameraFlyToMesh(cameraControls, { mesh: mesh, paddingTop: addOffset, paddingRight: addOffset, paddingBottom: addOffset, paddingLeft: addOffset });
}
// 定位到巷道
function goTunLocation(TunID, addOffset = 100) {
    addOutlinePass1([]);
    const mesh = meshGroup.children.find(v => TunID == v.mid);
    cameraFlyToMesh(cameraControls, { mesh: mesh, paddingTop: addOffset, paddingRight: addOffset, paddingBottom: addOffset, paddingLeft: addOffset });
    addOutlinePass1([mesh], 0x20c997);
}



// 新代码 ☆☆☆☆☆
// 节点交互事件
// 更新节点交互事件
function nodeInteraction(el) {
    const { clickRemove } = modelClick(pointGroup.children, camera, (currentMesh) => {
        if (isThree3DFullScreen.value && enterFormDiv.value) return;
        if (interactionMode.value == 2) {
            if (setNodeListShow.value && operationMode.value == 4) {
                clearEffect('nodeClick'); clearEffect('tunClick');
                addInteraction4(currentMesh)
            } else if (operationMode.value == 5) {
                updateTunNodeClick(currentMesh);
            }
        }
        else if (interactionMode.value == 1) {
            // updateNodeInfo.value = { NodeID: '', NodeName: '', x: null, y: null, z: null, Vertex: null }
            addOutlinePass1([currentMesh.object], 0xffab00);
            const obj = currentMesh.object.originPoint;
            updateNodeInfo.value = { ...obj };
        }
    }, el)
    function removeEvent() {
        clickRemove();
    }
    currentRemoveEvent = removeEvent;
}

function addInteraction4(currentMesh) {
    const { uuid, originPoint } = currentMesh.object;
    for (var i = 0, len = nodeList.value.length; i < len; i++) {
        const item = nodeList.value[i];
        updateNodeInfo.value = { ...toRaw(originPoint) };
        if (!item.originPoint.x) {
            nodeList.value.splice(i, 1, { originPoint: toRaw(originPoint), uuid, NodeID: originPoint.NodeID });
            break;
        } else {
            (i == len - 1) && nodeList.value.splice(i, 1, { originPoint: toRaw(originPoint), uuid, NodeID: toRaw(originPoint.NodeID) }); // 仅允许替换中间点的最后一个点
        }
    }
}
// 更新巷道 节点点击事件
function updateTunNodeClick(currentMesh) {
    if (currentMesh) {
        if (changeIndex.value == null) {
            // ElMessage.warning({ message: '您需要点击左侧新增按钮或者编辑按钮才能进行操作', duration: 3000 });
            return
        }
        const { uuid, originPoint } = currentMesh.object;

        const middleFlag = updateNodeList.value.find(v => v.uuid == uuid); // 判断有没有重复添加点位
        // middleFlag && ElMessage.error('该点已被添加')
        if (middleFlag) return;
        updateNodeList.value.splice(changeIndex.value, 1, { uuid, originPoint: toRaw(originPoint), NodeID: toRaw(originPoint.NodeID), state: true });
    }
}
watch(enterFormDiv, (val) => {
    console.log(val, 'valval');
})
// 更新巷道 巷道点击事件
function tunInteraction(el) {
    const { clickRemove } = modelClick(tunMeshArr.value, camera, (currentMesh) => {
        if (isThree3DFullScreen.value && enterFormDiv.value) return;
        if (changeIndex.value == null && isFlag.value) {
            updateNodeList.value = [{ originPoint: { x: null, y: null, z: null }, uuid: '', NodeID: null, state: true }]
            const { originPoints } = currentMesh.object;
            updateTunOther.value = originPoints;
            updateNodeList.value = originPoints.Nodes.map(v => { return { originPoint: deepClone(v), uuid: '', NodeID: v.NodeID, state: true } });
        }
    }, el)
    addEvent(window, 'click', mousePickUpClick);
    addEvent(window, 'mousemove', mousePickUpMove);
    function clearEvent() {
        clickRemove();
        removeEvent(window, 'click', mousePickUpClick);
        removeEvent(window, 'mousemove', mousePickUpMove);
    }
    currentRemoveEvent = clearEvent;
}

// 监听巷道选中
watch(updateTunOther, (val) => {
    if (!val.TunID || isThree2DFullScreen.value) return;
    const mesh = meshGroup.children.find(v => v.originPoints.TunID == val.TunID);
    const padding = 200;
    cameraControls.fitToBox(mesh, true, { paddingBottom: padding, paddingLeft: padding, paddingRight: padding, paddingTop: padding });
    addOutlinePass([mesh], 0xfb8c00);
    if (operationMode.value == 7) {
        interruptClick(mesh);
    }
})
// 监听updateInfo表单变化 同步选中状态
const twoAndThreeLink = ref(true)
watch(updateNodeInfo, (val) => {
    if (val.NodeID && !isThree2DFullScreen.value) {
        const mesh = pointGroup.children.find(v => v.mid == val.NodeID);
        if (operationMode.value != 4) { goNodeLocation(val.NodeID, 200, false); }
        addOutlinePass1([mesh], 0xffab00);
    }
})
function clearEffect(type) {
    if (type == 'nodeClick') {
        updateTunOther.value = { TunName: '', Ventflow: 1, Width: 0, Level: 0 }
    } else {
        updateNodeInfo.value = { NodeID: '', NodeName: '', x: null, y: null, z: null, Vertex: null }
    }
    addOutlinePass([]);
    addOutlinePass1([]);
}
// 相机联动
watch(addTunNodeIDList, (val) => {
    if (val.length > 0) {
        addOutlinePass1([]);
        const arr = val.filter(v => getType(v) === 'number')
        const meshList = pointMeshArr.filter(v => arr.includes(v.mid));
        goNodeLocation(val.at(-1), 200, false);
        addOutlinePass1(meshList, 0xffab00);
    } else {
        addOutlinePass1([]);
    }
})
watch(updateTunNodeIDList, (val) => {
    if (val.length > 0) {
        addOutlinePass1([]);
        const arr = val.filter(v => getType(v) === 'number')
        const meshList = pointMeshArr.filter(v => arr.includes(v.mid));
        addOutlinePass1(meshList, 0x0ca678);
    } else {
        addOutlinePass1([]);
    }
})

// 监听 operationMode
watch(operationMode, (val) => {
    if (val == 7) {
        const { TunID } = updateTunOther.value
        if (TunID) {
            const mesh = meshGroup.children.find(v => v.originPoints.TunID == TunID);
            interruptClick(mesh);
        }
    } else if (val == 5 || val == 6 || val == 7) {
        setNodeListShow.value = false;
        interruptReset();
    } else if (val == 4) {
        changeIndex.value = null;
        interruptReset();
    }

})


// 控制说明面板是否显示
var tControlPosition = ref(null);
var tControlOriginPosition = ref({ x: 0, y: 0, z: 0 });
// 当前操作id
const currentOperateId = ref(null);

function operateTun(num, event) {
    addOutlinePass([]);
    currentOperateId.value = num;
    // 清除上次触发事件
    if (currentRemoveEvent && currentRemoveEvent instanceof Function) {
        currentRemoveEvent();
        tControlRemoveEvent();
    }
    let msg = ''
    switch (num) {
        case 1:
            // msg = '当前处于绘制模式'
            // const { clearEvent: clearEvent1 } = draw();
            // tControlAddEvent();
            // currentRemoveEvent = clearEvent1;
            break;
        case 2:
            // msg = '当前处于编辑模式'
            // tControlAddEvent();
            // const { clearEvent: clearEvent2 } = edit();
            // currentRemoveEvent = clearEvent2;
            break;
        case 3:
            msg = '当前处于删除模式'
            const { clearEvent: clearEvent3 } = remove();
            currentRemoveEvent = clearEvent3;
            tControlPosition.value = null;
            break;
        case 4:
            msg = '当前处于节点合并模式'
            const { clearEvent: clearEvent4 } = merge();
            currentRemoveEvent = clearEvent4;
            tControlPosition.value = null;
            break;
        case 5:
            msg = '当前处于打断巷道模式'
            // const { clearEvent: clearEvent5 } = interrupt();
            // currentRemoveEvent = clearEvent5;
            // tControlPosition.value = null;
            break;
        case 6:
            msg = '当前处于巷道配置模式'
            const { clearEvent: clearEvent6 } = tunEdit();
            currentRemoveEvent = clearEvent6;
            tControlPosition.value = null;
            event.stopPropagation();
            break;

    }
    if (msg) {
        ElMessage.success(msg);
    }

}

// 重置之前存储的变量信息
function resetVariable() {
    drawReset();
    editReset();
    removeReset();
    mergeReset();
    interruptReset();
}


/** 绘制巷道*/
// 所需变量
const newTunData = [];
const drawChangeMesh = {
    tunTempList: [], //临时存储
    pointTempList: [],
    tunList: [],
    pointList: [],
}
let tubeMesh = null;    // 生成的临时巷道
let spherePoint = null; // 被拖动的辅助节点
let drawPositionList = []; // 临时存储编辑坐标的数组

let drawNodeList = [[]];



// 撤销
function drawRepeal() {
    if (tubeMesh) {
        meshGroup.remove(tubeMesh);
        drawReset()
    } else {
        if (drawChangeMesh.tunTempList.length < 1) return;
        let drawPoint = newTunData.pop();
        const index = drawNodeList[drawPoint[2]].find(v => JSON.stringify(v) == JSON.stringify(drawPoint))
        drawNodeList[drawPoint[2]].splice(index, 1);
        meshGroup.remove(drawChangeMesh.tunTempList.pop());
        pointGroup.remove(drawChangeMesh.pointTempList.pop());
    }
}
// 重置内容
function drawReset() {
    tubeMesh = null;
    drawPositionList = [];
    spherePoint && scene.remove(spherePoint);
    tControl && tControl.detach();
    tControl && scene.remove(tControl);
    spherePoint = null;
    tControlPosition.value = null;

}

function generateUniqueThreeDigitId() {
    const timestamp = String(Date.now()).slice(-3); // 取时间戳后3位
    const random = String(Math.floor(Math.random() * 100)).padStart(2, '0'); // 2位随机数
    return Number(timestamp + random) + 10000;
}
// 将点位转为正式点位
function createOriginPoint(x, y, z) {
    const obj = positionTransform(x, y, z, { n, type: 1, middlePosition: middleNode.value })
    const originPoint = { NodeID: generateUniqueThreeDigitId(), NodeName: '', ...obj }
    return originPoint;
}
// 创建Nodes 它将被携带到巷道之中
function createTunOrigin(originPoints, options = { Ventflow: 1, TunName: '-' }) {
    const { Ventflow, TunName } = options;
    return { TunID: String(generateUniqueThreeDigitId()), TunName: TunName ?? '-', Ventflow, S: '-', Q: '-', V: '-', H: '-', Nodes: originPoints }
}
//////////////////////////////////////////////////
/** 编辑巷道*/

// 变量
let originalTunPositionList = [];//存储巷道原始点位信息 方便恢复
let originalPositionList = [];//存储节点原始点位信息 方便恢复
let editChangeMesh = {             // 被修改的物体列表
    tunList: [],
    pointList: []
}
let intersectTunNum = null; // 节点相交数
let intersectTunList = []; // 相交物体数组
let currentPoint = null; // 选中的节点
let editPosition = null; // 记录节点所在坐标


// 右键取消编辑当前节点
function editRepeal(event) {
    // 取消当前操作
    if (currentPoint && originalTunPositionList.length >= 1) {
        ctrlZ(intersectTunList, currentPoint);
        editReset()
    }
    // 取消已经保存的节点操作
    if (originalTunPositionList.length >= 1 && originalPositionList.length >= 1) {
        let arr1 = editChangeMesh.tunList.pop();
        let arr2 = editChangeMesh.pointList.pop();
        ctrlZ(arr1, arr2);
    }
    function ctrlZ(tunArr, pointMesh) {
        const arr = originalTunPositionList.at(-1)
        tunArr.forEach((v, i) => {
            v.points = arr.at(i);
            v.geometry = tun(v.points, { isEditMat: true, tunName: 'editTun', radius: 8 }).tubeMesh.geometry;
        })
        const { x, y, z } = originalPositionList.at(-1);
        pointMesh.position.set(x, y, z);
        pointMesh.point = new THREE.Vector3(x, y, z);
        originalTunPositionList.pop();
        originalPositionList.pop();
    }
}

// 重置参数
function editReset() {
    intersectTunNum = null;
    intersectTunList = [];
    currentPoint = null;
    editPosition = null;
    tControl && scene.remove(tControl);
    tControlPosition.value = null;
}

///////////////////////////////////////////////////
/** 删除*/

// 变量
const removeChangeMesh = {
    tunList: [],
    pointList: []
}
let currentDelete = null;

function remove() {
    ElMessage.success('请点击选中巷道,之后双击会删除巷道')
    addEvent(window, 'click', click);
    addEvent(window, 'dblclick', dblClick);
    function click(event) {
        const { currentMesh } = meshPickup(event, meshGroup.children, camera, sceneDiv.value);
        if (currentMesh) {
            //console.log(currentMesh.object);
            currentDelete = currentMesh.object;
            addOutlinePass([currentMesh.object]);
        }
    }
    function dblClick(event) {
        if (currentDelete) {
            const i = meshGroup.children.findIndex(v => v.mid == currentDelete.mid);
            if (i != -1) {
                removeChangeMesh.tunList.push(meshGroup.children[i]);
                meshGroup.remove(currentDelete);
            }
            removeReset();
            // 判断是否有节点所连接的巷道已经清除完
            const tubePointArr = meshGroup.children.map(v => v.points);
            const removeArr = []
            pointGroup.children.forEach((item, i) => {
                const point = JSON.stringify(item.point);
                let num = 0;
                for (var j = 0; j < tubePointArr.length; j++) {
                    const tunList = tubePointArr[j]
                    if (JSON.stringify(tunList).includes(point)) {
                        let index = JSON.stringify(tunList[0]) == point && 0;
                        index = index != 0 && JSON.stringify(tunList[tunList.length - 1]) == point && tunList.length - 1;
                        if (index == 0 || index == tunList.length - 1) {
                            num += 1;
                            break;
                        }
                    }
                }
                if (num == 0) {
                    removeArr.push(item);
                }
            })
            removeArr.forEach(v => {
                removeChangeMesh.pointList.push(v);
                pointGroup.remove(v);
            })

        }
    }



    // 移除事件
    function clearEvent() {
        removeEvent(window, 'click', click);
        removeEvent(window, 'dblclick', dblClick);
    }
    return { clearEvent }
}
// 右键取消编辑当前节点
function removeRepeal(event) {
    // 取消当前操作
    if (currentDelete) {
        removeReset();
    } else {
        // 删除撤回
        if (removeChangeMesh.tunList.length > 0) {
            const mesh = removeChangeMesh.tunList.pop();
            const point = removeChangeMesh.pointList.pop();
            pointMeshArr.push(point);
            meshGroup.add(mesh);
            pointGroup.add(point);
        }
    }
}
function removeReset() {
    currentDelete = null;
    addOutlinePass([]);
}

///////////////////////////////////////////////////
/** 巷道合并*/

// 变量
const mergeChangeMesh = {
    tunList: [], // 被操作的巷道
    pointList: [], // 被操作移除的点位
    positionList: []
}

let mergeCurrentMesh = [null, null];
let relevancyTunList = [];
let positionArr = [];
let isExecute = true;
let isSave = false;

function merge() {
    addEvent(window, 'click', click);
    addEvent(window, 'dblclick', dblClick);
    function click(event) {
        const { currentMesh } = meshPickup(event, pointGroup.children, camera, sceneDiv.value);
        let isFlag = true;
        if (currentMesh && !isSave) {
            isExecute = true;
            currentMesh.object.material = changeMat('#ff0000');
            mergeCurrentMesh.length && (mergeCurrentMesh.forEach((v, i) => {
                if (v && currentMesh.object.uuid == v.uuid) {
                    isFlag = false;
                    mergeReset();
                }
                if (!isFlag && v) {
                    v.material = changeMat('#3742fa');
                }
            }))
            if (isFlag) {
                if (!mergeCurrentMesh[0]) {
                    mergeCurrentMesh[0] = currentMesh.object;
                    ElMessage.success('1节点已被选中')
                } else if (!mergeCurrentMesh[1]) {
                    mergeCurrentMesh[1] = currentMesh.object;
                    ElMessage.success('2节点已被选中')
                } else {
                    mergeCurrentMesh[1].material = changeMat('#3742fa');
                    mergeCurrentMesh[1] = currentMesh.object;
                    ElMessage.success('2节点已切换选中')
                }
            }
            // 判断合并节点是否在同一节点上
            if (mergeCurrentMesh[0] && mergeCurrentMesh[1]) {
                mergeCurrentMesh.forEach((v, i) => {
                    positionArr.splice(i, 1, v.point);
                })
                const indexList1 = [], indexList2 = [];
                tunPointsAll.forEach((v, i) => {
                    if (JSON.stringify(v).includes(JSON.stringify(positionArr[0]))) {
                        indexList1.push(i);
                    }
                    if (JSON.stringify(v).includes(JSON.stringify(positionArr[1]))) {
                        indexList2.push(i);
                    }
                })
                if (hasCommonItems(indexList1, indexList2)) {
                    isExecute = false;
                    ElMessage.error('不允许合并同一条巷道上的节点');
                } else {
                    // 查找与节点1相关的所有巷道模型
                    meshGroup.children.forEach(v => {
                        const arr = v.points;
                        if (JSON.stringify(arr).includes(JSON.stringify(positionArr[0]))) {
                            relevancyTunList.push(v);
                        }
                    })
                }
            }

        }
    }

    function dblClick() {
        if (isExecute && mergeCurrentMesh[0] && mergeCurrentMesh[1] && relevancyTunList.length) {
            isSave = true;
            changeTun(relevancyTunList, positionArr[0], positionArr[1], mergeCurrentMesh[1]);

            pointGroup.remove(mergeCurrentMesh[0]);

            let j = pointMeshArr.findIndex(v => v.uuid === mergeCurrentMesh[0].uuid);
            pointMeshArr.splice(j, 1);
            mergeCurrentMesh.forEach(v => {
                v.material = changeMat('#3742fa');
            })
            mergeChangeMesh.tunList.push(relevancyTunList);
            mergeChangeMesh.pointList.push(mergeCurrentMesh[0]);
            mergeChangeMesh.positionList.push(positionArr);
            mergeReset();
        }
    }





    // 判断两个长度相同的数组中 是否存在相同的项
    function hasCommonItems(arr1, arr2) {
        for (let i = 0; i < arr1.length; i++) {
            if (arr2.includes(arr1[i])) {
                return true;
            }
        }
        return false;
    }


    // 移除事件
    function clearEvent() {
        removeEvent(window, 'click', click);
        removeEvent(window, 'dblclick', dblClick);
        mergeReset();
    }
    return { clearEvent }
}

// 合并巷道 将被重新赋值
function changeTun(array = [], vec3Old, vec3New, newPointMesh) {
    array.length && array.forEach(v => {
        // 将与1点相关巷道数据 节点替换成 2点节点
        let index = v.points.findIndex(v => {
            const { x, y, z } = v;
            const { x: x1, y: y1, z: z1 } = vec3Old;
            return x == x1 && y == y1 && z == z1
        });
        const { mid, originPoint } = newPointMesh;
        v.SnodeID = index == 0 ? mid : null;
        v.EnodeID = index == v.points.length - 1 ? mid : null;
        v.points.splice(index, 1, vec3New);
        v.originPoints.Nodes.splice(index, 1, originPoint);
        v.geometry = tun(v.points, { tunName: 'mergeTun' }).tubeMesh.geometry;
    })
    //console.log(array, 'mergeNum');
}
function changeMat(color = "#3742fa") {
    const { mat } = createPoint({ isEditMat: true, color, radius: 8 })
    return mat
}
// 撤销操作
function mergeRepeal() {
    if (mergeChangeMesh.tunList.length) {
        const point = mergeChangeMesh.pointList.pop();
        const tunArr = mergeChangeMesh.tunList.pop();
        const pointArr = mergeChangeMesh.positionList.pop();
        changeTun(tunArr, pointArr[1], pointArr[0], point);
        pointGroup.add(point);
        pointMeshArr.push(point);
    }
}

function mergeReset() {
    mergeCurrentMesh.length && mergeCurrentMesh.forEach(v => {
        v && (v.material = changeMat('#3742fa'))
    })
    mergeCurrentMesh = [null, null];
    relevancyTunList = [];
    positionArr = [];
    isExecute = true;
    isSave = false;
}


/** 打断巷道*/
let interruptCurrentTun = null;
let interruptMiddleLine = null;
let interruptSphere = null;
// 巷道点击事件

function interruptClick(currentMesh) {
    if (currentMesh) {
        if (interruptCurrentTun && interruptCurrentTun.mid != currentMesh.mid) {
            // 恢复上一个
            interruptCurrentTun.visible = true;
        }
        interruptCurrentTun = currentMesh;
        interruptCurrentTun.visible = false;
        if (!interruptMiddleLine) {
            interruptMiddleLine = createLine({ linePoints: interruptCurrentTun.points })
            scene.add(interruptMiddleLine)
        } else {
            interruptMiddleLine.geometry = createLine({ linePoints: interruptCurrentTun.points }).geometry;
        }
    }
}

// 鼠标在中线取点事件
function mousePickUpClick(event) {
    if (operationMode.value == 7) {
        if (interruptMiddleLine) {
            const { currentMesh: mesh } = meshPickup(event, [interruptMiddleLine], camera, sceneDiv.value);
            if (mesh) {
                const { x, y, z } = mesh.point;

                const originPoint = createOriginPoint(x, y, z);
                interruptMovePosition.value = { x: originPoint.x, y: originPoint.y, z: originPoint.z };
                const { sphere } = createPoint({ x, y, z, originPoint });

                if (!interruptSphere) {
                    interruptSphere = sphere;
                    scene.add(interruptSphere);
                } else {
                    setPosition(interruptSphere, x, y, z);
                    interruptSphere.originPoint = originPoint;
                }
                interruptSphere.point = mesh.point;

                handleChange();
            }
        }
    }
}

function mousePickUpMove(event) {
    if (operationMode.value == 7) {
        if (interruptMiddleLine) {
            const { currentMesh: mesh } = meshPickup(event, [interruptMiddleLine], camera, sceneDiv.value);
            if (mesh) {
                interruptMiddleLine.material.color = new THREE.Color(0xff0000);
            } else {
                interruptMiddleLine.material.color = new THREE.Color(0xffff00);
            }
        }
    }
}
// 点击新增节点
function handleChange() {

    if (interruptCurrentTun && interruptSphere) {
        const points = interruptCurrentTun.points;
        const { Ventflow, Nodes } = interruptCurrentTun.originPoints;
        let minDistance = 0;
        let num = 0;
        // 查找距离该点最近的巷道点位
        points.forEach((v, i) => {
            const distance = calculateDistance(interruptSphere.point, v);

            i == 0 && (minDistance = distance);
            if (distance < minDistance) {
                minDistance = distance;
                num = i;
            }
        })
        // 计算拾取点位 跟该点位哪个距离零点较近
        const distance1 = calculateDistance(points[num], points[0]);
        const distance2 = calculateDistance(interruptSphere.point, points[0]);
        let points1, points2, originsPoint1, originsPoint2;
        const originPoint = interruptSphere.originPoint;
        if (distance2 > distance1) {
            // 拆分节点 绘制两条巷道
            points1 = [...points.slice(0, num + 1), interruptSphere.point];
            points2 = [interruptSphere.point, ...points.slice(num + 1, points.length)]
            originsPoint1 = [...Nodes.slice(0, num + 1), originPoint];
            originsPoint2 = [originPoint, ...Nodes.slice(num + 1, Nodes.length)]
        } else {
            points1 = [...points.slice(0, num), interruptSphere.point];
            points2 = [interruptSphere.point, ...points.slice(num, points.length)]
            originsPoint1 = [...Nodes.slice(0, num), originPoint];
            originsPoint2 = [originPoint, ...Nodes.slice(num, Nodes.length)]
        }
        const originTun1 = createTunOrigin(originsPoint1, { Ventflow });
        const originTun2 = createTunOrigin(originsPoint2, { Ventflow });

        interruptChangeMesh.value.addTunList = [originTun1, originTun2];
        interruptChangeMesh.value.removeTun = interruptCurrentTun.originPoints;
        interruptChangeMesh.value.addNode = originPoint;


    }
}


// 撤销
function interruptRepeal() {
    if (interruptCurrentTun) {
        interruptReset();
    }
}
// 移除操作
function interruptReset() {
    interruptCurrentTun && (interruptCurrentTun.visible = true);
    interruptCurrentTun = null;
    interruptMiddleLine && scene.remove(interruptMiddleLine);
    interruptSphere && scene.remove(interruptSphere);
    interruptMiddleLine = null;
    interruptSphere = null;
}
/** 撤销操作 */
function repeal() {
    if (currentOperateId.value) {
        switch (currentOperateId.value) {
            case 1: drawRepeal(); break;
            case 2: editRepeal(); break;
            case 3: removeRepeal(); break;
            case 4: mergeRepeal(); break;
            case 5: interruptRepeal(); break;
        }
    }
}
/** 巷道属性修改 */
const tunEditShow = ref(false);
const tunEditInfo = ref({});
let tunEditChangeMesh = null;
function tunEdit() {
    if (currentOperateId.value == 6) {
        addOutlinePass([]);
        tunEditAddEvent();
    }
    watch(tunEditShow, val => {
        clearEvent();
        if (!val && currentOperateId.value == 6) {
            addOutlinePass([]);
            setTimeout(() => {
                tunEditAddEvent();
            }, 10);
        }

    })

    function tunEditclick(event) {
        const { currentMesh } = meshPickup(event, meshGroup.children, camera, sceneDiv.value);
        if (currentMesh) {
            tunEditInfo.value = currentMesh.object.originPoints;
            addOutlinePass([currentMesh.object], 0xf08c00);
            tunEditShow.value = true;
            tunEditChangeMesh = currentMesh.object;
        }
    }
    function clearEvent() {
        removeEvent(window, 'click', tunEditclick);
    }
    function tunEditAddEvent() {
        addEvent(window, 'click', tunEditclick);
    }
    return { clearEvent }
}
async function saveTunEdit() {
    // ElMessage.success('修改已保存');
    const id = tunEditChangeMesh.mid;
    if (!id) {
        ElMessage.error('该巷道尚未上传,无法修改其属性');
    } else {
        const obj = apiTunList.value.find(v => v.TunID == id);
        const { Ventflow, TunName } = tunEditInfo.value;
        if (!obj) return;
        const MnodeID = obj.MnodeID ?? ''
        const tunObj = { ...obj, MnodeID, ...tunEditInfo.value }
        const { Status } = await ApiTunUpdate(tunObj);
        if (Status == 0) {
            ElMessage.success('巷道修改成功');
            tunEditChangeMesh.material = tun(tunEditChangeMesh.points, { isEditMat: false, ventflow: Ventflow }).mat;
            // 替换label
            const { middlePoint: { x, y, z }, mid } = tunEditChangeMesh;
            const sprite = createSpriteLabel(x, y, z, TunName, mid);
            spriteLabelGroup.remove(spriteLabelGroup.children.find(v => v.mid == mid));
            spriteLabelGroup.add(sprite);
        } else {
            ElMessage.error('巷道修改失败');
        }
    }
    tunEditShow.value = false;
}






// 场景保存

async function saveScene() {
    operateTun(0);
    // 新增板块 
    // 查看编辑物体是否有编辑到新物体
    const addTunList = [...interruptChangeMesh.value.addTunList.flat(), ...drawChangeMesh.tunList].filter(v => v != undefined); // 打断巷道需要重新生成两条新巷道
    const removeTunList = [...interruptChangeMesh.value.removeTunList, ...removeChangeMesh.tunList].filter(v => v != undefined);//打断巷道时 删除原本的巷道
    const addPointList = [...interruptChangeMesh.value.pointList, ...drawChangeMesh.pointList].filter(v => v != undefined); // 在打断处生成新的连接 节点
    const removePointList = [...mergeChangeMesh.pointList].filter(v => v != undefined); // 合并时也需要删除节点
    const newPointList = [];

    if (addPointList.length) {
        createPoint().then(v => {
            createTun();
        })
    } else {
        createTun();
    }

    async function createTun() {
        addTunList.length && await Promise.allSettled(addTunList.map(async v => {
            if (removeTunList.length && removeTunList.find(k => k.uuid == v.uuid)) return;
            const editTun = editChangeMesh.tunList.find(k => k.uuid == v.uuid);
            let item = editTun ?? v;
            const tunAddObj = {
                TunName: '', Place: 0, TunState: 1, SnodeID: 0, MnodeID: '', EnodeID: 0, Ventflow: 1, Lx: 0, ShoringType: 0, ShapeType: 0, UseType: 0, Length: 0, Width: 0, Height: 0, S: 0, A: 0, R: 0, NetLine: true
            }
            const { originPoints } = item;
            const { Nodes, Length } = originPoints;

            tunAddObj.SnodeID = findId(Nodes[0]);
            tunAddObj.EnodeID = findId(Nodes[Nodes.length - 1]);
            tunAddObj.Length = Length;

            function findId(info) {
                const { NodeID, x: x1, y: y1, z: z1 } = info;
                if (NodeID) {
                    return NodeID
                } else {
                    const findNode = newPointList.find(({ x, y, z }) => { return x == x1 && y == y1 && z == z1 });
                    return findNode.NodeID;
                }
            }

            if (Nodes.length > 2) {
                tunAddObj.MnodeID = Nodes.slice(1, Nodes.length - 1).map(v => findId(v)).join('-');
            } else {
                tunAddObj.MnodeID = '';
            }
            const { Status } = await ApiTunAdd(tunAddObj);
            return Status;
        })).then((result) => {
            if (result.every(v => v.value == 0)) {
                drawChangeMesh.tunList = [] // 
                interruptChangeMesh.value.addTunList = [] //打断新增的
            }
        })
    }
    function createPoint() {
        return addPointList.length && Promise.allSettled(addPointList.map(async v => {
            if (removePointList.length && removePointList.find(k => k.uuid == v.uuid)) return;
            // let item = pointGroup.children.find(k => k.uuid == v.uuid);
            const editPoint = editChangeMesh.pointList.find(k => k.uuid == v.uuid);
            let item = editPoint ?? v;
            //console.log(item, 'item');
            if ((removeChangeMesh.pointList.length && JSON.stringify(removeChangeMesh.pointList).includes(JSON.stringify(item))) || !item) return;
            let NodeAddObj = {
                NodeID: 0, NodeName: '', Num: 0, Vertex: 1, X: 0, Y: 0, Z: 0
            }
            const { originPoint: { NodeID, x: X, y: Y, z: Z }, name } = item;
            NodeAddObj.Vertex = name == 'new0' ? 0 : 1;
            NodeAddObj = { ...NodeAddObj, NodeID, X, Y, Z }
            const { Status, Result } = await ApiNodeAdd(NodeAddObj);
            newPointList.push({ NodeID: Result, x: X, y: Y, z: Z })
            return Status;
        })).then(result => {
            if (result.every(v => v.value == 0)) {
                drawChangeMesh.pointList = []
                interruptChangeMesh.value.pointList = []
            }
        })
    }

    // 编辑
    editChangeMesh.pointList.length && Promise.allSettled(editChangeMesh.pointList.filter(v => v != undefined).map(async v => {
        if (v.name.includes('new') || !v) return;
        const { originPoint: { x: X, y: Y, z: Z } } = v;
        const obj = apiNodeList.value.find(k => k.NodeID == v.mid)
        let NodeAddObj = {
            ...obj,
            X, Y, Z
        }
        const { Status } = await ApiNodeUpdate(NodeAddObj);;
        return Status;
    })).then(result => {
        if (result.every(v => v.value == 0)) {
            editChangeMesh.pointList = []
            editChangeMesh.tunList = []
        }
    })
    // 合并物体
    const mergeTunList = mergeChangeMesh.tunList.flat().filter(v => v != undefined);
    mergeTunList.length && Promise.allSettled(mergeTunList.map(async v => {
        if (v.name.includes('new') || !v) return;
        const obj = apiTunList.value.find(k => k.TunID == v.mid)
        if (obj) {
            let tunAddObj = {
                ...obj,
            }
            tunAddObj.SnodeID = v.SnodeID ?? tunAddObj.NodeID; tunAddObj.EnodeID = v.EnodeID ?? tunAddObj.NodeID;
            const { Status } = await ApiTunUpdate(tunAddObj);
            return Status;
        } else {
            return 1;
        }

    })).then(result => {
        if (result.every(v => v.value == 0)) {
            mergeChangeMesh.tunList = []
        }
    })
    // // 删除板块
    removeTunList.length && Promise.allSettled(removeTunList.map(async v => {
        if (v.mid == '' || !v) return;
        const { Status } = await ApiTunDel(v.mid);
        const nodes = v.originPoints.Nodes;

        getTunList().then(() => {
            nodes.length && nodes.forEach(async (v, i) => {
                if (!v) return;
                const index = tunStr.value.findIndex(k => k.includes(v.NodeID));
                if (index === -1) {
                    const { Status } = await ApiNodeDel(v.NodeID);;
                    return Status;
                }
            })
        })

    })).then(result => {
        if (result.every(v => v.value == 0)) {
            interruptChangeMesh.value.removeTunList = []
            removeChangeMesh.tunList = []
            removeChangeMesh.pointList = []
        }
    })
    removePointList.length && Promise.allSettled(removePointList.map(async v => {
        if (v.name.includes('new') || !v) return;
        const { Status } = await ApiNodeDel(v.mid);;
        return Status;
    })).then(result => {
        if (result.every(v => v.value == 0)) {
            removeChangeMesh.pointList = []
            mergeChangeMesh.pointList = []
        }
    })
    return Promise.resolve();
}
function exitScene() {
    router.replace('./threeVentilate')
}

// 变换控制器
let tControl = new TransformControls(camera, renderer.domElement);
// removeEvent(tControl, 'change', tControlChange)
function tControlChange() {
    const bool = typeof tControl.object != 'undefined' && typeof tControl.object.position != 'undefined' && typeof tControl.object.position.x != 'undefined' && typeof tControl.object.position.y != 'undefined' && typeof tControl.object.position.z != 'undefined'
    // 首尾节点不相等
    if (bool) {
        const { x, y, z } = tControl.object && tControl.object.position;
        if (currentOperateId.value == 1) {
            if (drawPositionList[0] instanceof THREE.Vector3 && JSON.stringify(drawPositionList[0]) !== JSON.stringify(tControl.object.position)) {
                drawPositionList[1] = new THREE.Vector3(x, y, z);
                tControlPosition.value = { x, y, z };
                const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { n, type: 1, middlePosition: middleNode.value })
                tControlOriginPosition.value = { x: x1, y: y1, z: z1 }
            }
        } else if (currentOperateId.value == 2) {
            if (currentPoint) {
                editPosition = new THREE.Vector3(x, y, z);
                tControlPosition.value = { x, y, z };
                const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { n, type: 1, middlePosition: middleNode.value })
                tControlOriginPosition.value = { x: x1, y: y1, z: z1 }
            }
        }
    }
}

//  操作变换控制器时暂时禁用其他控制器
function tControlDraggingChanged(event) {
    if (currentOperateId.value == 1) {
        if (drawPositionList[0] instanceof THREE.Vector3) {
            cameraControls.enabled = !event.value;
        }
    } else {
        cameraControls.enabled = !event.value;
    }
}
function tControlAddEvent() {
    addEvent(tControl, 'change', tControlChange);
    addEvent(tControl, 'dragging-changed', tControlDraggingChanged);
}
function tControlRemoveEvent() {
    removeEvent(tControl, 'change', tControlChange);
    removeEvent(tControl, 'dragging-changed', tControlDraggingChanged);
}



// 动态标签 和 二三维联动
let lastCamera = ref({ x: 0, y: 0, z: 0 })
ani((...args) => {
    const [time, camera, delta] = args
    // 监听相机位置
    lastCamera.value = { x: camera.position.x, y: camera.position.y, z: camera.position.z }
    tunLabelShowByCamera && tunLabelShowByCamera(camera, labelShow.value);
    twoAndThreeLink.value && getRecentNodeThrottleFn && getRecentNodeThrottleFn(camera);
})



// 重置效果
function resetEffect() {
    addOutlinePass([])
    restCamera()
}









</script>
<style lang="scss">
@use './assets/index.scss';
</style>
<style lang="scss" scoped>
.czsm_div {
    width: 271rem;
    height: 171rem;
    background-image: url('./assets/img/czsm.png');
    background-size: 100%;
}

.right_btn {
    width: 90rem;
    height: 30rem;
    background-image: url('./assets/img/back_save_bg.png');
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    margin-top: 10px;
    font-size: 15px;
    cursor: pointer;
}

.btn_img {
    width: 16px;
    height: 16px;
}

.btn_menu {
    :deep(.el-button--large) {
        width: 135rem;
        height: 50rem;
        background-image: url('./assets/img/btn_bg.png');
        /* background-size: cover; */
        background-size: 100%;
    }

    :deep(.el-button--small) {
        width: 100rem;
        height: 30rem;
        background-image: url('./assets/img/btn_bg.png');
        background-size: 100%;
    }
}


:deep(.el-form-item__label) {
    color: #fff;
}

:deep(.el-form-el-dialog__header) {
    margin: 10px 0 0 10px;
    color: #fff;
}


body {
    overflow: hidden;
}

:v-deep(.el-button .el-icon) {
    height: 0.5em;
    width: 0.5em;
}

.norem-box {
    /* padding: 20px;
    background-color: #000; */
    color: white;
    margin: 20px;
    display: flex;


    .norem-btn {
        /* postcss-pxtorem-disable */
        display: flex;
        justify-content: center;
        align-items: center;
        width: 55px;
        height: 25px;
        font-size: 12px;
        border-radius: 0;
        cursor: pointer;
        background-color: rgba(0, 255, 255, 0.6);
        margin: 0.5px;
        color: white;

        &:hover {
            background-color: rgba(0, 255, 255, 0.8);
        }
    }

    .norem-btn1 {
        width: auto;

        &:hover {
            background-color: rgba(0, 255, 255, 0.6);
        }

        .norem-btn1-1 {
            background-color: rgba(255, 255, 255, 0.8);
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: #fff;
            display: flex;
            justify-items: center;
            align-items: center;

            &:hover {
                background-color: rgba(255, 255, 255, 1.0);
            }
        }
    }
}

.scene {
    width: 100vw;
    height: 100vh;
    position: absolute;
    left: 0;
    top: 0;
}
</style>