.valuePanelBox {
    .introduce_text {
        height: 24px;
        font-size: 15px;
        font-family: Source <PERSON>s CN;
        font-weight: 400;
        color: #41F5F1;
        line-height: 24px;
    }

    // 重置原样式

    // table
    .el-table th.el-table__cell {
        background: #021a27;
        color: #76F7FF;
        text-align: center;
        font-size: 14rem;
        // border-bottom: none ;
        border-bottom: 1px solid #11AEEE !important;
    }

    .el-table--border .el-table__inner-wrapper::after,
    .el-table--border::after,
    .el-table--border::before,
    .el-table__inner-wrapper::before {
        background-color: transparent;
    }


    .el-table td.el-table__cell {
        height: 40rem;
        color: #fff;
        background: #021a27;
        text-align: center;
        font-size: 14rem;
        // border-bottom: none ;
        // border-radius: 2px;
        border-bottom: 1rem solid #11AEEE;
    }

    .el-table__body-wrapper {
        background-color: #0A2642;
    }

    .el-table tbody tr:hover td {
        background-color: #1864ab !important;
    }

    .el-table__body tr.current-row>td.el-table__cell {
        background-color: #1864ab;
    }

    // :root {
    //     --el-table-current-row-bg-color: #262626;
    // }

    // 折叠面板
    .el-collapse-item,
    .el-collapse,
    .el-collapse-item__content,
    .el-collapse-item__wrap {
        border: none;
        padding: none;
    }


    .el-collapse-item__arrow {
        color: #EFF7F9;
    }

    // list列表
    .el-collapse-item__header {
        background-color: #0A273E99;
        width: 408rem;
        height: 50rem;
        background-image: url('/src/views/thereVentilation/assets/img/list_bg.png');
        background-size: 408rem 48rem;
        border: none;

    }

    .el-collapse-item__content {
        padding-top: 5px;
        padding-bottom: 5px;
    }

    .el-collapse-item__wrap {
        background-color: #0A273E99;
        width: 408rem;
    }

    // 修改数据为空时 展示的文字
    .el-table__empty-text {
        color: #0D2641;
        position: relative;


        &::before {
            content: "无数据";
            position: absolute;
            width: 100%;
            height: 100%;
            left: 50%;
            transform: translateX(-50%);
            font-weight: 400;
            color: #eee;
            z-index: 9;
        }
    }

    .el-popover.el-popper {
        background: #094e74 !important;
        border: 1rem solid #094e74 !important;
    }
}