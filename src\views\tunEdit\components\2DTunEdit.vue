<template>
    <div class="w-100% h-100% relative" :class="store.$state.interactionMode == 0 ? 'cursor-grabbing' : ''">
        <div class="w-100% h-100%  bg-[#262626]" ref="sceneDiv" id="sceneDiv2"></div>
    </div>
</template>
<script setup>
import { watch, watchEffect, reactive, ref, toRaw, onUnmounted, onMounted,nextTick } from 'vue';
import { useThree } from '@/three/useThree';
import { moveToCamera, restrictCameraVerticalDirection } from '@/three/cameraControls.js'
import { ElMessage } from 'element-plus';
import { deepClone, getType } from '@/utils/utils';
import { dbModelClick, modelClick } from '@/three/utils/event';
import { positionTransform } from "@/views/thereVentilation/js/tunUtils.js";
import { ApiNodeAdd, ApiNodeDel, ApiNodeUpdate, ApiNodeList, ApiTunAdd, ApiTunDel, ApiTunUpdate, ApiTunList, roadwayList, ApiNodeCheckDouble, ApiNodeDistance } from '@/https/encapsulation/threeDimensional';
import { computeTunLenght } from "@/views/thereVentilation/js/tunUtils.js";
import { addEvent, removeEvent, stopEvent } from "@/utils/window";
import { createLine2Mat, createLine2 } from '@/three/mesh/Line2Mesh';
import { canvasTexture, canvasTexture2 } from '@/three/material/canvasTexture';
import { onBeforeRouteLeave, useRouter } from 'vue-router';
import mitt from '@/utils/eventHub';
import { calculateDistance } from "@/views/thereVentilation/js/tunUtils.js";
import { meshPickup } from '@/three/raycaster'
import { useTunEdit } from '@/store/tunEdit';
import { storeToRefs } from 'pinia';
// import { BatchedMesh } from 'three/examples/jsm/objects/';
// 对于使用模块化导入的项目

const store = useTunEdit();
const { tunStr, interactionMode, roadwayList: tunLinkList } = storeToRefs(store);
const { updateNodeInfo, enterFormDiv, operationMode, addTunNodeList: nodeList, addTunNodeIDList,
    updateTunNodeList: updateNodeList, updateTunChangIndex: changeIndex, updateTunIsFlag: isFlag,
    updateTunOther, updateTunNodeIDList, isThree2DFullScreen, isThree3DFullScreen, operationRecord, interruptMovePosition, interruptChangeMesh, setNodeListShow } = storeToRefs(store);
const emit = defineEmits(['newData', 'stopRender']);
const sceneDiv = ref(null);
const operateID = ref(1); // 控制操作方式
const apiNodeList = ref([]); // 节点列表
const apiTunList = ref([]); // 巷道列表

const needNodeList = ref([]) //需要展示的点位
const searchPoint = ref(null)
const formShow = ref(false)

watch(searchPoint, (val) => {
    if (val) {
        goNodeLocation(val);
    }
})
watchEffect(() => {
    apiNodeList.value = store.$state.nodeList;
    apiTunList.value = store.$state.tunList;;
})


onBeforeRouteLeave((leaveGuard) => {
    closeImportNode(true);
    if (leaveGuard.name == 'ventilationCalculation' || leaveGuard.name == 'tunEdit') {
        return;
    }
    stopRender();
})

// 选择框列表
const radioCurrent = ref(-1);
watch(radioCurrent, (val) => {
    changeNeedNodeList(val);
})

function changeNeedNodeList(state) {
    console.log(state,'changeNeedNodeList')
    if (state) {
        if (state == 1) {
            needNodeList.value = apiNodeList.value.filter(v => tunStr.value.findIndex(k => k.includes(v.NodeID)) == -1);
        }
        else if (state == 2) {
            needNodeList.value = apiNodeList.value.filter(v => v.Vertex == 0);
        }
        else {
            needNodeList.value = apiNodeList.value
        }
    }

}

// 获取所有巷道信息 
async function getTunList() {
    console.log('获取所有巷道信息')
    const { Result } = await ApiTunList();
    apiTunList.value = Result ?? [];
    tunStr.value = ``;

    tunStr.value = Result.map(({ SnodeID, EnodeID, MnodeID }) => {
        return MnodeID ? [SnodeID, ...MnodeID.split('-').map(v => Number(v)), EnodeID] : [SnodeID, EnodeID]
    })
    return Result;
}

let lineMeshArr = [];
let circleMeshArr = [];
let newPointList = ref([]);
let miningAreaList = ref([])

let global = ref() // 全局管家
var maxNodeID = ref(0);
let middleNode = ref([0, 0, 0]);
let maxPosition = [0, 0, 0]
let minPosition = [0, 0, 0]
let transformMaxPosition = [0, 0, 0]
let transformMinPosition = [0, 0, 0]


import { OrthographicCamera } from 'three'
import { createCss3DObj } from '@/three/css3DRender';
import { createTunLine, tun } from '@/views/thereVentilation/js/tun';
import { Line2 } from 'three/examples/jsm/lines/Line2';
import { meshRemove } from '@/three/initThree1';
// 创建正交相机
// const myCamera = new OrthographicCamera(window.innerWidth * - 2, window.innerWidth * 2, window.innerHeight * 2, window.innerHeight * - 2, 1, 60000);
//threejs init
const { mountRender, cameraControls, scene, camera, renderer, THREE, cameraFlyToMesh, addOutlinePass, addOutlinePass1, setMeshRotation, setMeshScale, setPosition, ani, stopRender } = useThree({ endCamera: [0, 5000, 0], isCameraAni: false, isControl: true, isAxes: false, isDepthBuffer: true })
cameraControls.minDistance = 60;
cameraControls.maxDistance = 35000;
camera.far = 60000;
let group = new THREE.Group();
// group.rotateX(Math.PI);
scene.add(group);
const lineMat = createLine2Mat(0xffb30f, 6)
const line2Mat = createLine2Mat(0x00db77, 6)
const line3Mat = createLine2Mat(0xdd212f, 6)
const updateLineMat = createLine2Mat(0x1890ff, 6)
const removeLineMat = createLine2Mat(0xff0000, 6)
const searchLineMat = createLine2Mat(0x20c997, 6)
const netLineMat = createLine2Mat(0xeeeeee, 6)


global.value = { lineMat, group, stopRender, THREE, camera, scene, addOutlinePass, addOutlinePass1, cameraControls, cameraFlyToMesh, addOutlinePass1 };
// 场景放大倍数
let n = 3;
let grid = null;

var setY = 0;
function resetCamera() {
    moveToCamera(cameraControls, 0, [0, setY * n, 0], [0, 0, 0]);
}

// 控制三维页
//判断距离最近的节点 并更新联动相机状态
let cameraPosition = { x: null, y: null, z: null }

function getRecentNode(camera) {
    console.log(camera,'camera')
    const { x, y, z } = camera.position;

    const { x: x1, y: y1, z: z1 } = cameraPosition;
    if (x == x1 && y == y1 && z == z1) {
        return null;
    }
    cameraPosition = { x, y, z };

    if (!circleMeshArr.length) return;
    let minDistance = setY * 2;
    let zjMesh = null;
    circleMeshArr.forEach((mesh, i) => {
        if (mesh instanceof THREE.Mesh) {
            const point = mesh.position;

            const distance = new THREE.Vector3(x, y, z).distanceTo(point);
            console.log(mesh.position, distance, 'distance');

            if (distance < minDistance) {
                minDistance = distance;
                zjMesh = mesh
            }
        }

    })
    return zjMesh ? zjMesh.mid : null;
}
// function getRecentNodeResult(mid) {
//     if (mid) {
//         mitt.emit('get2DRecentNode', mid);
//     }
// }
// let getRecentNodeThrottleFn = throttleFn(getRecentNode, 500, getRecentNodeResult) // 节流 1m判断一次

let lastCamera = ref({ x: 0, y: 0, z: 0 })
ani((time, camera, delta, requestAnimationId) => {
    // 监听相机位置
    lastCamera.value = { x: camera.position.x, y: camera.position.y, z: camera.position.z }
    // getRecentNodeThrottleFn(camera)
});
const flag = ref(false) // 标识
// 禁用联动
const linkageState = ref(true)

onMounted(() => {
    const el = document.querySelector('#sceneDiv2');
    mountRender(el);
    // 跟随3D的相机变化
    // mitt.on('get3DRecentNodeEdit', (mid) => {
    //     goNodeLocation(mid, 100, false)
    // })
    // 接收相机飞跃到节点事件

    if (isThree3DFullScreen.value == true) { return }
    mitt.on('flyToPoint', (mid) => {
        goNodeLocation(mid, 100, true)
    })
    mitt.on('flyToTun', (mid) => {
        goTunLocation(mid, 100, false)
    })
    mitt.on('resetCamera', () => {
        resetCamera()
    })

})

watch(operationMode, val => {
    mousePickUpMove(val);
})

watch(interactionMode, (val) => {
    changeEvent(val);
})

function changeEvent(val) {

    if (val) {
        global.value.clickRemove && global.value.clickRemove();
        global.value.removeTunClick && global.value.removeTunClick();
        clearEffect('nodeClick'); clearEffect('tunClick');
    }
    if (val == 1) {
        addInteraction2(sceneDiv.value);
    } else if (val == 2) {
        watch(setNodeListShow, (val) => {
            clear();
            addOutlinePass([]);
            addOutlinePass1([]);
            clearEffect('nodeClick'); clearEffect('tunClick');
            if (val) {
                addInteraction2(sceneDiv.value);
            } else {
                addInteraction5(sceneDiv.value);
            }
        })
        watch(changeIndex, (val) => {
            clear();
            if (val != null && val != -1) {
                addInteraction2(sceneDiv.value);
            } else {
                addOutlinePass1([]);
                clearEffect('nodeClick'); clearEffect('tunClick');
                addInteraction5(sceneDiv.value);
            }
        }, { immediate: true })
    } else {
        clear();
    }
}

function clear() {
    global.value.clickRemove && global.value.clickRemove();
    global.value.removeTunClick && global.value.removeTunClick();
}
// 监听 operationRecord
const flagOperationRecord = ref(true)
watch(operationRecord, (val) => {
    flagOperationRecord && setTimeout(() => {
        localUpdate(val);
    }, 100)
})
onMounted(() => {
    // 监听撤销
    mitt.on('handleRepeal', (last) => {
        flagOperationRecord.value = false;
        repealLocalUpdate(last);
    })
})

// 局部更新渲染
function localUpdate(arr) {
    console.log('erwei局部')
    if (arr.length > 0) {
        let last = arr[arr.length - 1];

        getType(last) == 'array' && last.forEach((v, i) => {
            const { type, data, index } = toRaw(v);
            // 判断data中是否有TunID这个属性，如果有，则说明是巷道
            let Nodes = [], linePoints = [];

            if ((type == 4 || type == 5) && data instanceof Object && data.hasOwnProperty('TunID')) {
                // 根据 Snode Enode Mnode 查找节点
                const { SnodeID, EnodeID, MnodeID } = toRaw(data);
                const list = MnodeID ? [SnodeID, ...MnodeID.split('-').map(v => Number(v)).filter(v => v != 0), EnodeID] : [SnodeID, EnodeID]
                Nodes = list.map(k => toRaw(store.$state.nodeList.find(n => n.NodeID == k)));
                linePoints = Nodes.map((k, i) => {
                    if (!k) return;
                    const { NodeID, x, y, z } = k;
                    const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { middlePosition: middleNode.value });
                    return { ...k, x: x1, y: y1, z: z1 }
                })
            }

            switch (type) {
                case 1:
                    // 新增
                    const { x: x1, y: y1, z: z1 } = data;
                    const { x: x2, y: y2, z: z2 } = positionTransform(x1, y1, z1, { middlePosition: middleNode.value })
                    let circle = createCicle({ text: data.NodeID, THREE, position: { x: x2, y: y2, z: z2 }, originPoint: data, n });
                    group.add(circle);
                    break;
                case 3:
                    // 节点删除
                    const point3 = circleMeshArr.find(v => v.mid == data.NodeID);
                    point3.visible = false;
                    // 与节点相关的巷道全部隐藏
                    lineMeshArr.forEach((j, i) => {
                        const { SnodeID, EnodeID, MnodeID } = j.originPoints;
                        const list = [SnodeID, , MnodeID, EnodeID]
                        if (list.includes(data.NodeID)) {
                            j.visible = false;
                        };
                    })
                    break;
                case 4:
                    let lineMesh = createLine({ THREE, linePoints, n, el: sceneDiv.value, netLine: data.NetLine, originPoints: { Nodes, ...data } })
                    lineMeshArr.push(lineMesh);
                    group.add(lineMesh);
                    break;
                case 6:
                    // 巷道删除
                    const mesh = lineMeshArr.find(v => v.originPoints.TunID == data.TunID);
                    const index = lineMeshArr.findIndex(v => v.originPoints.TunID == data.TunID);
                    mesh && (mesh.visible = false, lineMeshArr.splice(index, 1));
                    break;
            }
        })
        // 不是数组的情况
        if (getType(last) == 'object') {
            const { type, data, index } = last;
            let Nodes = [], linePoints = [];

            if ((type == 4 || type == 5) && data instanceof Object && data.hasOwnProperty('TunID')) {
                // 根据 Snode Enode Mnode 查找节点
                const { SnodeID, EnodeID, MnodeID } = toRaw(data);
                const list = MnodeID ? [SnodeID, ...MnodeID.split('-').map(v => Number(v)).filter(v => v != 0), EnodeID] : [SnodeID, EnodeID]
                Nodes = list.map(k => toRaw(store.$state.nodeList.find(n => n.NodeID == k)));
                linePoints = Nodes.map((k, i) => {
                    if (!k) return;
                    const { NodeID, x, y, z } = k;
                    const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { middlePosition: middleNode.value });
                    return { ...k, x: x1, y: y1, z: z1 }
                })
            }
            switch (type) {
                case 2:
                    // 更新
                    const { x: x1, y: y1, z: z1 } = data;
                    const { x: x2, y: y2, z: z2 } = positionTransform(x1, y1, z1, { middlePosition: middleNode.value })
                    let circle = createCicle({ text: data.NodeID, THREE: THREE, position: { x: x2, y: y2, z: z2 }, originPoint: data, n });
                    const mesh2 = circleMeshArr.find(v => v.mid == data.NodeID);
                    mesh2.position.copy(circle.position);
                    mesh2.originPoint = data;
                    // 与节点相关的巷道全部更新
                    const tunList = store.$state.tunList.filter(k => k.SnodeID == data.NodeID || k.EnodeID == data.NodeID || (k.MnodeID ? k.MnodeID.includes(data.NodeID) : ''));

                    tunList.forEach(j => {
                        const { SnodeID, EnodeID, MnodeID } = toRaw(j);
                        const list = MnodeID ? [SnodeID, ...MnodeID.split('-').map(v => Number(v)).filter(v => v != 0), EnodeID] : [SnodeID, EnodeID]
                        if (!(list.includes(data.NodeID)) || SnodeID == '1' || EnodeID == '1') return;
                        const Nodes = list.map(k => toRaw(store.$state.nodeList.find(n => n.NodeID == k)));
                        if (!Nodes.every(v => v)) return;


                        const linePoints = Nodes.map((k, i) => {
                            const { NodeID, x, y, z } = k;
                            const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { middlePosition: middleNode.value });
                            return { ...k, x: x1, y: y1, z: z1 }
                        })
                        updateTun(linePoints, Nodes, j)
                    })
                    break;
                case 4:
                    // 巷道新增 
                    let lineMesh = createLine({ THREE, linePoints, n, el: sceneDiv.value, netLine: data.NetLine, originPoints: { Nodes, ...data } })
                    lineMeshArr.push(lineMesh);
                    group.add(lineMesh);
                    break;
                case 5:
                    updateTun(linePoints, Nodes, data)
                    break;
                case 6:
                    // 巷道删除
                    const mesh = lineMeshArr.find(v => v.originPoints.TunID == data.TunID);
                    const index = lineMeshArr.findIndex(v => v.originPoints.TunID == data.TunID);
                    mesh && (mesh.visible = false, lineMeshArr.splice(index, 1));
                    break;
            }
        }
    }
}

// 撤销更新渲染
function repealLocalUpdate(value) {
    const last = toRaw(value);
    getType(last) == 'array' && last.forEach((v, i) => {
        const { type, data, index } = toRaw(v);
        // 判断data中是否有TunID这个属性，如果有，则说明是巷道
        let Nodes = [], linePoints = [];

        if ((type == 4 || type == 5) && data instanceof Object && data.hasOwnProperty('TunID')) {
            // 根据 Snode Enode Mnode 查找节点
            const { SnodeID, EnodeID, MnodeID } = toRaw(data);
            const list = MnodeID ? [SnodeID, ...MnodeID.split('-').map(v => Number(v)).filter(v => v != 0), EnodeID] : [SnodeID, EnodeID]
            Nodes = list.map(k => toRaw(store.$state.nodeList.find(n => n.NodeID == k)));
            linePoints = Nodes.map((k, i) => {
                if (!k) return;
                const { NodeID, x, y, z } = k;
                const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { middlePosition: middleNode.value });
                return { ...k, x: x1, y: y1, z: z1 }
            })
        }

        switch (type) {
            case 1:
                const index1 = circleMeshArr.findIndex(v => v.mid == data.NodeID);
                circleMeshArr.splice(index1, 1);
                group.children.splice(index1, 1);
                break;
            case 3:
                const point3 = circleMeshArr.find(v => v.mid == data.NodeID);
                point3.visible = true;
                // 与节点相关的巷道全部显示
                lineMeshArr.forEach((j, i) => {
                    const { SnodeID, EnodeID, MnodeID } = j.originPoints;
                    const list = [SnodeID, , MnodeID, EnodeID]
                    if (list.includes(data.NodeID)) {
                        j.visible = true;
                    };
                })
                break;
            case 4:
                const index4 = lineMeshArr.findIndex(v => v.mid == data.TunID);
                lineMeshArr.splice(index4, 1);
                group.children.splice(index4, 1);
                break;
            case 6:
                //   巷道删除
                const mesh = group.children.find(v => v.originPoints.TunID == data.TunID);
                const index = group.children.findIndex(v => v.originPoints.TunID == data.TunID);
                mesh && (mesh.visible = true, lineMeshArr.splice(index, 1, mesh));
                break;
        }
    })
    // 不是数组的情况
    if (getType(last) == 'object') {
        const { type, data, index } = last;
        let Nodes = [], linePoints = [];

        if ((type == 4 || type == 5) && data instanceof Object && data.hasOwnProperty('TunID')) {
            // 根据 Snode Enode Mnode 查找节点
            const { SnodeID, EnodeID, MnodeID } = toRaw(data);
            const list = MnodeID ? [SnodeID, ...MnodeID.split('-').map(v => Number(v)).filter(v => v != 0), EnodeID] : [SnodeID, EnodeID]
            Nodes = list.map(k => toRaw(store.$state.nodeList.find(n => n.NodeID == k)));
            linePoints = Nodes.map((k, i) => {
                if (!k) return;
                const { NodeID, x, y, z } = k;
                const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { middlePosition: middleNode.value });
                return { ...k, x: x1, y: y1, z: z1 }
            })
        }
        switch (type) {
            case 2:
                // 更新
                const { x: x1, y: y1, z: z1 } = data;
                const { x: x2, y: y2, z: z2 } = positionTransform(x1, y1, z1, { middlePosition: middleNode.value })
                let circle = createCicle({ text: data.NodeID, THREE: THREE, position: { x: x2, y: y2, z: z2 }, originPoint: data, n });
                const mesh2 = circleMeshArr.find(v => v.mid == data.NodeID);
                mesh2.position.copy(circle.position);
                mesh2.originPoint = data;
                // 与节点相关的巷道全部更新
                const tunList = store.$state.tunList.filter(k => k.SnodeID == data.NodeID || k.EnodeID == data.NodeID || (k.MnodeID ? k.MnodeID.includes(data.NodeID) : ''));

                tunList.forEach(j => {
                    const { SnodeID, EnodeID, MnodeID } = toRaw(j);
                    const list = MnodeID ? [SnodeID, ...MnodeID.split('-').map(v => Number(v)).filter(v => v != 0), EnodeID] : [SnodeID, EnodeID]
                    if (!(list.includes(data.NodeID)) || SnodeID == '1' || EnodeID == '1') return;
                    const Nodes = list.map(k => toRaw(store.$state.nodeList.find(n => n.NodeID == k)));
                    if (!Nodes.every(v => v)) return;


                    const linePoints = Nodes.map((k, i) => {
                        const { NodeID, x, y, z } = k;
                        const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { middlePosition: middleNode.value });
                        return { ...k, x: x1, y: y1, z: z1 }
                    })
                    updateTun(linePoints, Nodes, j)
                })
                break;
            case 4:
                const index4 = lineMeshArr.findIndex(v => v.mid == data.TunID);
                lineMeshArr.splice(index4, 1);
                group.children.splice(index4, 1);
                break;
            case 5:
                updateTun(linePoints, Nodes, data)
                break;
            case 6:
                //   巷道删除
                const mesh = group.children.find(v => v.mid == data.TunID);
                const index = group.children.findIndex(v => v.mid == data.TunID);
                mesh && (mesh.visible = true, lineMeshArr.splice(index, 1, mesh));
                break;
        }
    }
    flagOperationRecord.value = true;
}
// 局部更新巷道
function updateTun(linePoints, Nodes, data) {
    console.log(linePoints,'linePoints')
    const mesh5 = lineMeshArr.find(m => m.mid == data.TunID);
    if (!(linePoints.every(v => v))) return;
    let lineMesh = createLine({ THREE, linePoints, n, el: sceneDiv.value, netLine: data.NetLine, originPoints: { Nodes, ...data } })
    if (!mesh5) return;
    mesh5.geometry = lineMesh.geometry;
    mesh5.originPoints = lineMesh.originPoints;
}
// 保存之后统一刷新
function openImportNode() {
    global.value.addOutlinePass([]);
    global.value.addOutlinePass1([]);
    let vec3 = null;
    apiNodeList.value.forEach(({ x, y, z, NodeID }) => {
        const [maxX, maxY, maxZ] = maxPosition;
        x > maxX ? maxPosition[0] = x : minPosition[0] = x;
        y > maxY ? maxPosition[1] = y : minPosition[1] = y;
        z > maxZ ? maxPosition[2] = z : minPosition[2] = z;
    })
    const NodeList = Result.map(v => v.NodeID);
    newPointList.value = newPointList.value.filter(v => NodeList.includes(v.originPoint.NodeID))

    const [x1, y1, z1] = maxPosition;
    const [x2, y2, z2] = minPosition;
    // vec3 = Result.length == 1 ? Result[0] : ({ x: (x1 + x2) / 2, y: (y1 + y2) / 2, z: (z1 + z2) / 2 });
    vec3 = Result.length == 1 ? Result[0] : (Result[Math.floor(Result.length / 2)] ?? { x: 0, y: 0, z: 0 });

    const { x, y, z } = vec3 ?? { x: 0, y: 0, z: 0 }
    middleNode.value = [x, y, z];
    // threejs init
    setY = middleNode.value[2] + 5000;

    if (!flag.value) {
        moveToCamera(cameraControls, 0, [0, setY * n, 0], [0, 0, 0]);
        flag.value = true;
    }

    grid = new THREE.GridHelper(((setY + 10000) * 6), 20);
    // group.position.y
    grid.renderOrder = 1;

    // setPosition(grid, ((averagePosition[0] - middleNode.value[0])) * n, -2, 0);
    scene.add(grid);
    restrictCameraVerticalDirection(cameraControls, 0, 0);
    cameraControls.minAzimuthAngle = 0;
    cameraControls.maxAzimuthAngle = 0;

    roadwayList().then(({ Result }) => {
        if (!Result || !Result.length) return
        Result.forEach(v => {
            // 渲染线段
            const newNodes = v.Nodes.map(k => {
                const { x, y, z } = k;
                const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { middlePosition: middleNode.value });
                return { ...k, x: x1, y: y1, z: z1 }
            })
            if (v.Nodes.length > 1) {
                const linePoints = newNodes.map(v => { return { x: v.x, y: v.y, z: v.z } })
                let lineMesh = createLine({ THREE, linePoints, n, el: sceneDiv.value })
                lineMesh.mid = v.TunID;
                lineMesh.originPoints = v;
                group.add(lineMesh);
            }
        })
    })
    let zMax = 0, zMin = 0;
    getTunList().then(v => {
        changeNeedNodeList(radioCurrent.value);
        if (!apiNodeList.value.length) return
        apiNodeList.value.forEach((v, i) => {
            // if (v.Vertex == 0 || !tunStr.includes(v.NodeID)) {

            const { x, y, z, NodeID } = v;
            if (NodeID > maxNodeID.value) {
                maxNodeID.value = NodeID;
            }


            const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { middlePosition: middleNode.value })

            const [maxX, maxY, maxZ] = transformMaxPosition;
            x1 > maxX ? transformMaxPosition[0] = x1 : transformMinPosition[0] = x1;
            y1 > maxY ? transformMaxPosition[1] = y1 : transformMinPosition[1] = y1;
            if (z1 > zMax) {
                zMax = z1;
            }
            if (z1 < zMin) {
                zMin = z1;
            }

            //test 采区
            if (i == 500) {
                // let mesh = createMiningArea({ text: 'test001', scale: 5, position: { x: x1, y: y1, z: z1 }, originPoint: v, n })
                // miningAreaList.value.push(mesh);
                // group.add(mesh);
            }
            //
            let circle = createCicle({ text: NodeID, THREE: THREE, position: { x: x1, y: y1, z: z1 }, originPoint: v, n });
            group.add(circle);
            // }
        })
        transformMaxPosition[2] = zMax; transformMinPosition[2] = zMin;

        const zPy = (Math.abs(zMax) + Math.abs(zMin)) / 2
        group.position.z = -zPy + zMin / 2;

    })

    changeEvent(operateID.value);

}
// 直接渲染巷道的方法
// 参数：tuns - 巷道数据列表（格式同 tunLinkList）
function renderTuns(tuns) {
  if (!tuns || !tuns.length) {
    console.warn('没有可渲染的巷道数据');
    return;
  }

  // 清除现有巷道（可选，根据需求决定是否保留原有巷道）
  // lineMeshArr.forEach(mesh => group.remove(mesh));
  // lineMeshArr = [];

  tuns.forEach(tun => {
    // 过滤无效巷道（节点不足）
    if (!tun.Nodes || tun.Nodes.length < 2) {
      console.warn(`跳过无效巷道（节点不足）：${tun.TunID}`);
      return;
    }

    // 过滤无效节点（排除 null/undefined）
    const validNodes = tun.Nodes.filter(Boolean);
    if (validNodes.length < 2) {
      console.warn(`巷道节点无效：${tun.TunID}`);
      return;
    }

    // 转换节点坐标（基于当前中间点校准）
    const transformedNodes = validNodes.map(node => {
      const { x, y, z } = node;
      const { x: x1, y: y1, z: z1 } = positionTransform(
        x, 
        y, 
        z, 
        { middlePosition: middleNode.value }
      );
      return { ...node, x: x1, y: y1, z: z1 };
    });

    // 提取线条坐标点
    const linePoints = transformedNodes.map(node => ({
      x: node.x,
      y: node.y,
      z: node.z
    }));

    // 创建巷道线条
    const lineMesh = createLine({
      originPoints: tun,
      THREE: THREE,
      linePoints: linePoints,
      n: n, // 场景放大倍数
      el: sceneDiv.value,
      netLine: tun.NetLine // 网络线标识（如果有）
    });

    // 存储巷道标识，方便后续操作（如定位、更新）
    lineMesh.mid = tun.TunID;
    lineMesh.originPoints = tun;

    // 添加到场景
    group.add(lineMesh);
    lineMeshArr.push(lineMesh);
  });

  console.log(`成功渲染 ${tuns.length} 条巷道`);
}
// 提取二维初始化逻辑为公共方法
function init2DScene() {
  // 将原mounted中的初始化代码迁移到这里
  // 例如：创建场景、相机、渲染巷道和节点等
  mountRender(sceneDiv.value); // 假设已有挂载渲染的方法
     // 主动加载巷道数据（若依赖接口）
  const loadData = async () => {
    await ApiTunList(); // 假设加载巷道数据的接口
    // 等待 store 数据更新
    await nextTick();
    // 确保数据就绪后渲染
    if (apiTunList.value.length && apiNodeList.value.length) {
      handleRender();
    }
  };
  loadData();

  tunLinkList.value.forEach(v => {
  if (!v.Nodes || v.Nodes.length < 2) {
    console.warn('跳过无效巷道（节点不足）：', v.TunID);
    return;
  }
  // 过滤无效节点（如 null/undefined）
  const validNodes = v.Nodes.filter(Boolean);
  if (validNodes.length < 2) {
    console.warn('巷道节点无效：', v.TunID);
    return;
  }
  // 继续渲染逻辑...
});
 

}

// 暴露方法给父组件调用
defineExpose({
  init2DScene,
   handleRender,
   
});
// 监听执行渲染
watch(() => store.$state.renderFlag, (flag) => {
    if (flag > 1) {
        handleRender(false);
    } else {
        handleRender();
    }
    // 恢复相机位置
    camera.position.x = lastCamera.value.x;
    camera.position.y = lastCamera.value.y;
    camera.position.z = lastCamera.value.z;
})


// 处理渲染事件
function handleRender(isResetCamera = true) {
    closeImportNode();
    global.value.addOutlinePass([]);
    global.value.addOutlinePass1([]);
    let vec3 = null;
    const nodeList = store.$state.nodeList;
    store.$state.nodeList.forEach(({ x, y, z, NodeID }) => {
        const [maxX, maxY, maxZ] = maxPosition;
        x > maxX ? maxPosition[0] = x : minPosition[0] = x;
        y > maxY ? maxPosition[1] = y : minPosition[1] = y;
        z > maxZ ? maxPosition[2] = z : minPosition[2] = z;
    })
    const NodeList = nodeList.map(v => v.NodeID);
    newPointList.value = newPointList.value.filter(v => NodeList.includes(v.originPoint.NodeID))

    const [x1, y1, z1] = maxPosition;
    const [x2, y2, z2] = minPosition;
    vec3 = nodeList[0];
    // vec3 = nodeList.length == 1 ? Result[0] : (nodeList[Math.floor(nodeList.length / 2)] ?? { x: 0, y: 0, z: 0 });

    const { x, y, z } = vec3 ?? { x: 0, y: 0, z: 0 }
    middleNode.value = [x, y, z];
    // threejs init
    setY = middleNode.value[2] + 5000;

    if (isResetCamera) {
        moveToCamera(cameraControls, 0, [0, setY * n, 0], [0, 0, 0]);
    }

    grid = new THREE.GridHelper(((setY + 10000) * n * 2), 20);
    // group.position.y
    grid.renderOrder = 1;

    // setPosition(grid, ((averagePosition[0] - middleNode.value[0])) * n, -2, 0);
    scene.add(grid);
    restrictCameraVerticalDirection(cameraControls, 0, 0);
    cameraControls.minAzimuthAngle = 0;
    cameraControls.maxAzimuthAngle = 0;


    if (!tunLinkList.value || !tunLinkList.value.length) return
    tunLinkList.value.forEach(v => {
        if (v.Nodes.length < 1) return
        // 渲染线段
        const newNodes = v.Nodes.filter(v => v).map(k => {
            const { x, y, z } = k;
            const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { middlePosition: middleNode.value });
            return { ...k, x: x1, y: y1, z: z1 }
        })

        if (v.Nodes.length > 1) {
            const linePoints = newNodes.filter(v => JSON.stringify(v) != '[]')
            if (linePoints.length) {
                let lineMesh = createLine({ originPoints: v, THREE, linePoints, n, el: sceneDiv.value, netLine: v.NetLine })
                lineMesh.mid = v.TunID;
                lineMesh.originPoints = v;
                group.add(lineMesh);
            }
        }
    })


    let zMax = 0, zMin = 0;

    changeNeedNodeList(radioCurrent.value);
    if (!store.$state.nodeList.length) return
    store.$state.nodeList.forEach((v, i) => {
        const { x, y, z, NodeID } = v;
        if (NodeID > maxNodeID.value) {
            maxNodeID.value = NodeID;
        }
        const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { middlePosition: middleNode.value })
        const [maxX, maxY, maxZ] = transformMaxPosition;
        x1 > maxX ? transformMaxPosition[0] = x1 : transformMinPosition[0] = x1;
        y1 > maxY ? transformMaxPosition[1] = y1 : transformMinPosition[1] = y1;
        if (z1 > zMax) {
            zMax = z1;
        }
        if (z1 < zMin) {
            zMin = z1;
        }

        //test 采区
        if (i == 500) {

            // let mesh = createMiningArea({ text: 'test001', scale: 5, position: { x: x1, y: y1, z: z1 }, originPoint: v, n })
            // miningAreaList.value.push(mesh);
            // group.add(mesh);
        }
        //
        let circle = createCicle({ text: NodeID, THREE: THREE, position: { x: x1, y: y1, z: z1 }, originPoint: v, n });
        group.add(circle);
        // }
    })
    transformMaxPosition[2] = zMax; transformMinPosition[2] = zMin;

    const zPy = (Math.abs(zMax) + Math.abs(zMin)) / 2
    group.position.z = -zPy + zMin / 2;

    // 开启事件
    changeEvent(interactionMode.value);
}


//
// 监听updateInfo表单变化 同步选中状态
watch(updateNodeInfo, (val) => {
    if (val.NodeID && !isThree3DFullScreen.value) {
        clearEffect('nodeClick');
        const mesh = circleMeshArr.find(v => v.mid == val.NodeID);
        addOutlinePass1([mesh], 0xe91e63);
        if (operationMode.value == 4) return;
        goNodeLocation(val.NodeID, 300, false);
    }
})
function clearEffect(type) {
    
    if (type == 'nodeClick') {
        updateTunMesh && (updateTunMesh.material = lineMat, updateTunMesh = null)
        updateTunOther.value = {
            TunName: '',
            Ventflow: 1,
            Width: 0
        }
    } else {
        updateNodeInfo.value = { NodeID: '', NodeName: '', x: null, y: null, z: null, Vertex: null }
    }
    addOutlinePass([]);
    addOutlinePass1([]);
}
function closeImportNode(isExit = false) {
    addCancel();
    updateCancel();
    removeCancel();
    tunCancel();
    updateTunCancel();
    ImportCancel();

    lineMeshArr = [];
    circleMeshArr.length && circleMeshArr.forEach(v => {
        // 清除关联的 DOM 元素
        if (v && v.children[0] && v.children[0].element.parentNode) {
            v.children[0].element.parentNode.removeChild(v.children[0].element);
        }
    })
    circleMeshArr = [];
    middleNode.value = [0, 0, 0];
    apiTunList.value = [];
    apiNodeList.value = [];


    grid && scene.remove(grid);
    global.value.clickRemove && global.value.clickRemove();
    global.value.dblRemove && global.value.dblRemove();
    global.value.repealRemove && global.value.repealRemove();
    global.value.removeTunClick && global.value.removeTunClick();
    group.children.forEach(v => {
        if (v instanceof Line2) {
            meshRemove(v, true); return;
        }
        meshRemove(v);

    })
    group.remove(...group.children);

    if (isExit) {
        // global.value.stopRender();
        // global.value = null;
        newPointList.value = [];
        resetLocationMesh();
        locationMesh = null;
        sceneDiv.value = null;
    }
}

// 批量导点 表单
const pointList = ref([{ name: '', x: null, y: null, z: null }])
// 新增节点



// 取消节点保存
const addCancel = () => {
    addOutlinePass([])
    addOutlinePass1([])
    pointList.value = [{ name: '', x: null, y: null, z: null }];
}

var locationMesh = null;
// 定位到物体所在位置
function goNodeLocation(NodeID, addOffset = 100, isAddOutlinePass = true) {
    let mesh = circleMeshArr.find(v => v.mid == NodeID);
    if (!mesh) return;
    resetLocationMesh(mesh);
    isAddOutlinePass && addOutlinePass1([mesh]);
    cameraFlyToMesh(cameraControls, { mesh: mesh, paddingTop: addOffset, paddingRight: addOffset, paddingBottom: addOffset, paddingLeft: addOffset });
}
// 还原locationMesh 设置新的mesh
function resetLocationMesh(mesh) {
    locationMesh && locationMesh.position.set(locationMesh.position.x, 2, locationMesh.position.z);
    const styles = {
        zIndex: 1,
        backgroundColor: '#0097A4'
    };
    locationMesh && Object.assign(locationMesh.children[0].element.style, styles);
    if (!mesh) return;
    const { position: { x, y, z } } = mesh;
    // 设置
    mesh.position.set(x, 5, z);
    const styles2 = {
        zIndex: 9,
        backgroundColor: '#f44336'
    };
    mesh && Object.assign(mesh.children[0].element.style, styles2);
    locationMesh = mesh;
}
// 定位到物体所在位置
const currentTun = ref(null)
function goTunLocation(TunID, addOffset = 100, isAddOutlinePass = true) {
    if (currentTun.value) {
        currentTun.value.material = lineMat;
    }
    const mesh = lineMeshArr.find(v => TunID == v.mid);
    mesh.material = searchLineMat;
    currentTun.value = mesh;
    cameraFlyToMesh(cameraControls, { mesh: mesh, paddingTop: addOffset, paddingRight: addOffset, paddingBottom: addOffset, paddingLeft: addOffset });

}

// 更新节点
let changePoint2 = [] //统计被改变的点位数据
let findPoint2 = [] //统计已经添加过查找的数据


// 更新节点交互事件

function addInteraction2(el) {
    const { clickRemove } = modelClick(circleMeshArr, camera, (currentMesh) => {
        if (isThree2DFullScreen.value && enterFormDiv.value) return;
        if (interactionMode.value == 2) {
            if (operationMode.value == 4) {
                clearEffect('nodeClick'); clearEffect('tunClick');
                addInteraction4(currentMesh)
            } else if (operationMode.value == 5) {
                updateTunNodeClick(currentMesh);
            }
        }
        else if (interactionMode.value == 1) {
            // updateNodeInfo.value = { NodeID: '', NodeName: '', x: null, y: null, z: null, Vertex: null }
            addOutlinePass1([currentMesh.object], 0xffab00);
            console.log(currentMesh.object);

            const obj = currentMesh.object.originPoint;
            checkCircleShelter(changePoint2, findPoint2, currentMesh.object);
            updateNodeInfo.value = { ...obj };
        }
    }, el)
    global.value.clickRemove = clickRemove;
}

function updateCancel() {
    addOutlinePass([])
    addOutlinePass1([])
    updateNodeInfo.value = { NodeID: '', NodeName: '', x: null, y: null, z: null, Vertex: null };
    findPoint2 = []
    changePoint2 = [];
}

// 删除节点
const removeNodeInfo = ref({ NodeName: '', NodeID: '', Vertex: null });
let removeNodeMesh = null;
let changePoint4 = [];
let findPoint4 = [];

function removeCancel() {
    addOutlinePass([])
    addOutlinePass1([])
    removeNodeInfo.value = { NodeName: '', NodeID: '', Vertex: null };
    removeNodeMesh = null;
    changePoint4 = [];
    findPoint4 = [];
}




// 设置节点关系
const tunOther = ref({
    TunName: '',
    Ventflow: 1,
    Width: 0
})


function tunCancel() {
    addOutlinePass([])
    addOutlinePass1([])
    nodeList.value = [{ originPoint: { x: null, y: null, z: null }, uuid: '' }];
    tunOther.value = {
        TunName: '',
        Ventflow: null,
        Width: 0
    }
}

// addMiddlePoint
function addMiddlePoint(index) {

    const newPoint = { originPoint: { x: null, y: null, z: null }, uuid: '' }
    if (index != null) {
        nodeList.value.splice(index + 1, 0, newPoint);
    } else {
        nodeList.value.push(newPoint);
    }
}



//
var changePoint = [] //统计被改变的点位数据
var findPoint = [] //统计已经添加过查找的数据


// 设置节点关系交互事件
function isSimilar(num1, num2, threshold) {
    return Math.abs(num1 - num2) <= threshold;
}

// 设置节点关系点击事件
function addInteraction4(currentMesh) {
    const { uuid, originPoint } = currentMesh.object;

    const middleFlag = nodeList.value.find(v => v.uuid == uuid); // 判断有没有重复添加点位
    middleFlag && ElMessage.error('该点已被添加')
    if (middleFlag) return;
    checkCircleShelter(findPoint, changePoint, currentMesh.object);

    for (var i = 0, len = nodeList.value.length; i < len; i++) {
        const item = nodeList.value[i];
        updateNodeInfo.value = { ...toRaw(originPoint) };
        if (!item.originPoint.x) {
            nodeList.value.splice(i, 1, { originPoint: toRaw(originPoint), uuid, NodeID: originPoint.NodeID });
            break;
        } else {
            (i == len - 1) && nodeList.value.splice(i, 1, { originPoint: toRaw(originPoint), uuid, NodeID: toRaw(originPoint.NodeID) }); // 仅允许替换中间点的最后一个点
        }
    }
    // const uuidList = nodeList.value.map(v => v.uuid);
    // global.value.addOutlinePass(circleMeshArr.filter(v => uuidList.includes(v.uuid)), 0x0ca678);
}

watch(addTunNodeIDList, (val) => {
    if (val.length > 0) {
        addOutlinePass([]);
        addOutlinePass1([]);
        const arr = val.filter(v => getType(v) === 'number')
        const meshList = circleMeshArr.filter(v => arr.includes(v.mid));
        // goNodeLocation(val.at(-1), 200, false);
        addOutlinePass(meshList, 0x0ca678);
    } else {
        addOutlinePass([]);
    }
})

function setNodeDblclick() {
    if (!nodeList.value.at(-1).originPoint.x) return;
    addMiddlePoint();
}
function dblRemove() {
    removeEvent(window, 'dblclick', setNodeDblclick)
}

// 更新节点关系
var findPoint3 = [], changePoint3 = [];
var updateTunMesh = null
// 取消操作
function updateTunCancel() {
    addOutlinePass([])
    addOutlinePass1([])
    updateNodeList.value = [{ originPoint: { x: null, y: null, z: null }, uuid: '', NodeID: null, state: true }]
    updateTunOther.value = { TunName: '', Ventflow: null, Width: 0, Level: 0 }
    findPoint3 = [];
    changePoint3 = [];
    updateTunMesh && (updateTunMesh.material = lineMat);
    updateTunMesh = null;
    changeIndex.value = null;
    isFlag.value = true;
}


// 更新巷道 巷道点击事件
function addInteraction5(el) {
    const { clickRemove } = modelClick(lineMeshArr, camera, (currentMesh) => {
        if (isThree2DFullScreen.value && enterFormDiv.value) return;
        const { originPoints } = currentMesh.object;
        updateTunOther.value = originPoints;
        updateNodeList.value = [{ originPoint: { x: null, y: null, z: null }, uuid: '', NodeID: null, state: true }]
        updateTunMesh && (updateTunMesh.material = lineMat, updateTunMesh = null)
        currentMesh.object.material = updateLineMat;
        updateNodeList.value = originPoints.Nodes.map(v => { return { originPoint: deepClone(v), uuid: '', NodeID: v.NodeID, state: true } });

        updateTunMesh = currentMesh.object;
    }, el)
    // addEvent(window, 'mousemove', mousePickUpMove);
    function clearEvent() {
        clickRemove();
        // removeEvent(window, 'mousemove', mousePickUpMove);
    }
    global.value.removeTunClick = clearEvent;
}

// 更新巷道 节点点击事件
function updateTunNodeClick(currentMesh) {
    if (currentMesh) {
        if (changeIndex.value == null) { ElMessage.warning({ message: '您需要点击左侧新增按钮或者编辑按钮才能进行操作', duration: 3000 }); return }
        const { uuid, originPoint } = currentMesh.object;

        const middleFlag = updateNodeList.value.find(v => v.uuid == uuid); // 判断有没有重复添加点位
        middleFlag && ElMessage.error('该点已被添加')
        if (middleFlag) return;
        checkCircleShelter(findPoint3, changePoint3, currentMesh.object);
        updateNodeList.value.splice(changeIndex.value, 1, { uuid, originPoint: toRaw(originPoint), NodeID: toRaw(originPoint.NodeID), state: true });
    }
}
watch(updateTunNodeIDList, (val) => {
    if (val.length > 0) {
        addOutlinePass([]);
        const arr = val.filter(v => getType(v) === 'number')
        const meshList = circleMeshArr.filter(v => arr.includes(v.mid));
        addOutlinePass(meshList, 0x0ca678);
    } else {
        addOutlinePass([]);
    }
})
watch(updateTunOther, (val) => {
    if (!val.hasOwnProperty('TunID') || isThree3DFullScreen.value) return;
    updateTunMesh && (updateTunMesh.material = lineMat, updateTunMesh = null)
    const mesh = lineMeshArr.find(v => v.originPoints.TunID == val.TunID);
    if (mesh) {
        clearEffect('tunClick')
        mesh.material = updateLineMat
        const padding = 200;
        addOutlinePass1([mesh], 0x0ca678);
        cameraControls.fitToBox(mesh, true, { paddingBottom: padding, paddingLeft: padding, paddingRight: padding, paddingTop: padding });
        updateTunMesh = mesh;
    }
})

// 打断巷道
// 鼠标在中线取点事件
let interruptSphere = null;
function interactionClick(mesh) {
    if (mesh) {
        const { x, y, z } = mesh.point;
        const originPoint = createOriginPoint(x, y, z);
        interruptMovePosition.value = { x: originPoint.x, y: originPoint.y, z: originPoint.z };
        let sphere = createCicle({ text: originPoint.NodeID, THREE: THREE, position: { x, y, z }, originPoint, n })

        if (!interruptSphere) {
            interruptSphere = sphere;
            scene.add(sphere);
        } else {
            setPosition(interruptSphere, x, y, z);
            interruptSphere.originPoint = originPoint;
        }
        interruptSphere.point = mesh.point;

        handleChange(mesh.object);
    }
}



function mousePickUpMove(val) {
    if (val == 5 || val == 6 || val == 7) {
        setNodeListShow.value = false;
    } else if (val == 4) {
        changeIndex.value = null;
    }
}
// 点击新增节点
function handleChange(interruptCurrentTun) {
    if (interruptCurrentTun && interruptSphere) {
        const points = interruptCurrentTun.points;

        const { Ventflow, Nodes } = interruptCurrentTun.originPoints;
        let minDistance = 0;
        let num = 0;
        // 查找距离该点最近的巷道点位
        points.forEach((v, i) => {
            const distance = calculateDistance(interruptSphere.point, v);
            i == 0 && (minDistance = distance);
            if (distance < minDistance) {
                minDistance = distance;
                num = i;
            }
        })
        // 计算拾取点位 跟该点位哪个距离零点较近
        const distance1 = calculateDistance(points[num], points[0]);
        const distance2 = calculateDistance(interruptSphere.point, points[0]);
        let points1, points2, originsPoint1, originsPoint2;
        const originPoint = interruptSphere.originPoint;
        if (distance2 > distance1) {
            // 拆分节点 绘制两条巷道
            points1 = [...points.slice(0, num + 1), interruptSphere.point];
            points2 = [interruptSphere.point, ...points.slice(num + 1, points.length)]
            originsPoint1 = [...Nodes.slice(0, num + 1), originPoint];
            originsPoint2 = [originPoint, ...Nodes.slice(num + 1, Nodes.length)]
        } else {
            points1 = [...points.slice(0, num), interruptSphere.point];
            points2 = [interruptSphere.point, ...points.slice(num, points.length)]
            originsPoint1 = [...Nodes.slice(0, num), originPoint];
            originsPoint2 = [originPoint, ...Nodes.slice(num, Nodes.length)]
        }
        const originTun1 = createTunOrigin(originsPoint1, { Ventflow });
        const originTun2 = createTunOrigin(originsPoint2, { Ventflow });


        interruptChangeMesh.value.addTunList = [originTun1, originTun2];
        interruptChangeMesh.value.removeTun = interruptCurrentTun.originPoints;
        interruptChangeMesh.value.addNode = originPoint;
    }
}
// 生成id
function generateUniqueThreeDigitId() {
    const timestamp = Date.now();
    const timestampPart = parseInt(timestamp.toString().slice(-3));
    const randomPart = Math.floor(Math.random() * 100);
    return (timestampPart + randomPart) % 1000;
}
// 将点位转为正式点位
function createOriginPoint(x, y, z) {
    const obj = positionTransform(x, y, z, { n, type: 1, middlePosition: middleNode.value })
    const originPoint = { NodeID: generateUniqueThreeDigitId() + '*', NodeName: '', ...obj }
    return originPoint;
}
// 创建Nodes 它将被携带到巷道之中
function createTunOrigin(originPoints, options = { Ventflow: 1, TunName: '-' }) {
    const { Ventflow, TunName } = options;
    return { TunID: String(generateUniqueThreeDigitId()), TunName: TunName ?? '-', Ventflow, S: '-', Q: '-', V: '-', H: '-', Nodes: originPoints }
}

// 导入节点
const tableData = ref([])
const tableOriginData = ref([])
const tableHeader = ref(['X', 'Y', 'Z']);




//
function getImportData(val) {
    tableData.value = val.map(v => { return { ...v, x: v.X, y: v.Y, z: v.Z, state: 1, Vertex: 1 } });
    tableOriginData.value = val.map(v => { return { ...v, x: v.X, y: v.Y, z: v.z, state: 1, Vertex: 1 } });
    checkDouble(val).then(v => {
        if (!tableData.value.every(v => v.state == 1)) ElMessage.error('存在重复点，请及时处理')
    })
}

// 监测重复点的操作
function checkDouble(val) {
    const obj = { NodeID: -1, NodeName: '', Num: 0, Vertex: 1, x: 0, y: 0, z: 0 }
    const params = val.map(({ X, Y, Z }) => { return { ...obj, x: X, y: Y, z: Z } })
    return params.length && ApiNodeCheckDouble(params).then(({ Result }) => {
        if (!Result || !Result.length) return [];
        Result.forEach((v, i) => {
            const { x, y, z, NodeID, Vertex } = v;
            const index = tableData.value.findIndex(({ X, Y, Z }) => X == x && Y == y && Z == z);
            const lastIndex = tableData.value.findLastIndex(({ X, Y, Z }) => X == x && Y == y && Z == z);

            if (index != -1) {
                // tableData.value.splice(index, 1, { ...v, X: x, Y: y, Z: z, state: 1 });
                // index != lastIndex && lastIndex != -1 && tableData.value.splice(lastIndex, 1, { ...v, X: x, Y: y, Z: z, state: 1 });
                tableData.value.splice(index, 1, { ...tableData.value[index], state: 0 })
                index != lastIndex && lastIndex != -1 && tableData.value.splice(lastIndex, 1, { ...tableData.value[lastIndex], state: 0 })
                tableOriginData.value.splice(index, 1, { ...tableOriginData.value[index], state: 0 })
                index != lastIndex && lastIndex != -1 && tableOriginData.value.splice(lastIndex, 1, { ...tableOriginData.value[lastIndex], state: 0 })
            }
        })

        return Result;
    })
}
// 设置搜索范围 默认为5
const range = ref(5)
const intersectionPointList = ref([])
const intersectionIsUse = ref() // id array
// 重复点数组信息
const duplicatePointList = ref([])
const duplicateIsUse = ref()
function ImportCancel() {
    addOutlinePass([])
    addOutlinePass1([])
    tableData.value = [];
    tableData.value = []
    tableOriginData.value = []
    range.value = 5
    intersectionPointList.value = []
    intersectionIsUse.value = null // id array
    // 重复点数组信息
    duplicatePointList.value = []
    duplicateIsUse.value = null
}

// 采区配置
const miningAreaForm = ref([{ name: '', x: null, y: null, z: null }])
function addMiningArea() {
    miningAreaForm.value.push({ name: '', x: null, y: null, z: null });
}
function removeMiningArea(index) {
    miningAreaForm.value.splice(index, 1);
}


const miningAreaSubmit = () => {
    const newPoints = []
    let flag = true;
    for (var i = 0; i < miningAreaForm.value.length; i++) {
        const v = miningAreaForm.value[i]
        const { x, y, z, name } = v;
        if (JSON.stringify(parseFloat(x)) != 'null' && JSON.stringify(parseFloat(y)) != 'null' && JSON.stringify(parseFloat(z)) != 'null') {
            const bol1 = getType(parseFloat(x));
            const bol2 = getType(parseFloat(y));
            const bol3 = getType(parseFloat(z));
            flag = bol1 == 'number' && bol2 == 'number' && bol3 == 'number';
            if (!flag) {
                ElMessage.error('请保证您输入的是一个数字类型')
                break;
            } else {
                maxNodeID.value = ++maxNodeID.value;
                const vec3 = positionTransform(x, y, z, { middlePosition: middleNode.value })
                const point = { point: vec3, originPoint: { NodeID: maxNodeID.value, NodeName: name, x, y, z, state: 'new' } };
                newPoints.push(point);
            }
        } else {
            flag = false;
            ElMessage.error('请保证您输入的是一个数字类型')
            break;
        }
    }
    if (flag) {
        miningAreaList.value.push(...newPoints);
        newPoints.forEach(({ point, originPoint }) => {
            let mesh = createMiningArea({ text: originPoint.NodeName, scale: 5, position: point, originPoint, n })
            group.add(mesh);
        })

        // newPoints.length && Promise.allSettled(newPoints.map(async item => {
        //     let NodeAddObj = {
        //         NodeID: 0, NodeName: '', Num: 0, Vertex: 1, X: 0, Y: 0, Z: 0
        //     }
        //     const { originPoint: { NodeID, NodeName, x: X, y: Y, z: Z } } = item;
        //     NodeAddObj = { ...NodeAddObj, NodeName, NodeID, X, Y, Z }
        //     const { Status } = await ApiNodeAdd(NodeAddObj);
        //     return Status;
        // })).then(result => {
        //     if (result.every(v => v.value == 0)) {
        //         ElMessage.success('保存成功')
        //         newPointList.value.push(...newPoints);
        //         closeImportNode();
        //         openImportNode();
        //         addCancel();
        //     }
        // })
    }
}

// 取消节点保存
const addMiningAreaCancel = () => {
    addOutlinePass([])
    addOutlinePass1([])
    miningAreaForm.value = [{ name: '', x: null, y: null, z: null }];
}





// utils
// 节点避让

function checkCircleShelter(pointArr, changePointArr, mesh) {
    let threshold = 5;
    const { point, originPoint, uuid, position: { x, y, z } } = mesh;
    resetLocationMesh(mesh);

    if (findPoint.findIndex(k => k.uuid == uuid) == -1 && changePoint.findIndex(k => k.uuid == uuid) == -1
        && changePoint2.findIndex(k => k.uuid == uuid) == -1 && (findPoint2.findIndex(k => k.uuid == uuid) == -1)
        && findPoint3.findIndex(k => k.uuid == uuid) == -1 && changePoint3.findIndex(k => k.uuid == uuid) == -1
        && findPoint4.findIndex(k => k.uuid == uuid) == -1 && changePoint4.findIndex(k => k.uuid == uuid) == -1) {
        const findArr = circleMeshArr.filter(v => isSimilar(v.originPoint.x, originPoint.x, threshold) && isSimilar(v.originPoint.y, originPoint.y, threshold));
        if (findArr.length > 1) {
            findArr.forEach((v, i) => {
                if (v.uuid == uuid) {
                    pointArr.push(v)
                    // v.material.color = new global.value.THREE.Color(0xffc078);
                }
                else {
                    if (changePointArr.length == 0 || changePointArr.findIndex(k => k.uuid == v.uuid) == -1) {
                        const { x, y, z } = v.position;
                        v.position.set(x + (avoidRange) * (i - 1), y, z + avoidRange)
                        // v.material.color = new global.value.THREE.Color(0x63e6be);
                        changePointArr.push(v);
                    }
                }
            })
        }
    }
    locationMesh = mesh;
}

// 创建 巷道线
function createLine({ THREE, linePoints = [], color = 0xffff00, n = 1, netLine = true, type = 0, el, originPoints = {} }) {
    const arr1 = linePoints.map((item) => {
        const { x, y, z } = item;
        // return x1 < z1 ? new THREE.Vector3(x * n, 0, z * n) : new THREE.Vector3(z * n, 0, x * n);
        return [x * n, 0, z * n]
    }).flat();
    const mat = originPoints.Ventflow == 1 ? line2Mat : (originPoints.Ventflow == -1 ? line3Mat : lineMat)
    const lineMesh = createLine2(arr1, netLine ? mat : netLineMat)
    // let newArr = linePoints.map(({ x, y, z }) => {
    //     return new THREE.Vector3(x * n, 0, z * n);
    // });
    // const lineMesh = createTunLine(newArr, { radius: 30 }).tubeMesh;



    lineMesh.points = linePoints;
    JSON.stringify(originPoints) != '{}' && (lineMesh.originPoints = originPoints, lineMesh.mid = originPoints.NodeID);
    lineMesh.renderOrder = 2;
    lineMeshArr.push(lineMesh);
    return lineMesh;
}

const avoidRange = 30;
const geometry = new THREE.CircleGeometry(avoidRange, 10);
const circleMat = new THREE.MeshBasicMaterial({ color: 0x0097A4 });
// 创建巷道节点
function createCicle({ bgColor, color, text, THREE, position = { x: 0, y: 0, z: 0 }, n = 1, originPoint }) {
    const circle = new THREE.Mesh(geometry, circleMat);
    const { x, y, z } = position;
    circle.position.set(x * n, 2, z * n);
    // circle.position.set(x * 2, 5, z * 2)
    circle.rotation.x = -Math.PI / 2;
    circle.point = position instanceof THREE.Vector3 ? position : new THREE.Vector3(x, y, z);
    circle.originPoint = originPoint;
    circle.mid = originPoint.NodeID;
    circleMeshArr.push(circle);

    const sprite = createCss3DObj({
        dom: `
            <div style="width:60px;height:60px" class="flex  bg-[#0097A4] rounded-50%  text-[#ffff] justify-center items-center  text-[22px] ">
                ${text}
            </div>
        ` })
    circle.add(sprite);
    // var { texture } = canvasTexture(THREE, text,
    //     {
    //         fontsize: 110,
    //         borderColor: { r: 255, g: 0, b: 0, a: 1.0 },/* 边框黑色 */
    //         color: color ?? { r: 255, g: 255, b: 255, a: 1.0 },
    //         backgroundColor: bgColor ?? { r: 0, g: 151, b: 164, a: 1.0 }/* 背景颜色 */
    //     });
    // circle.material.map = texture;
    // circle.renderOrder = 3;
    // circle.material.transparent = true;
    // circle.material.opacity = 0.9;
    return circle;
}
// 创建采区
const miningAreaGeo = new THREE.PlaneGeometry(15, 12, 8, 8);
function createMiningArea({ text, position = { x: 0, y: 0, z: 0 }, scale = 1, n = 1, originPoint }) {
    const mat = new THREE.MeshBasicMaterial({ map: canvasTexture2(text ?? '采区工作面').texture });
    const mesh = new THREE.Mesh(miningAreaGeo, mat)
    const { x, y, z } = position;
    mesh.position.set(x * n, 2, z * n)
    mesh.rotation.x = -Math.PI / 2;
    setMeshScale(mesh, scale);
    mesh.point = position instanceof THREE.Vector3 ? position : new THREE.Vector3(x, y, z);
    mesh.originPoint = originPoint;
    mesh.mid = originPoint.NodeID;
    return mesh;
}



</script>

<style lang="scss">
@use '../assets/index.scss';
</style>

<style lang="scss" scoped>
.btn_menu {
    :deep(.el-button--large) {
        width: 135rem;
        height: 50rem;
        background-image: url('../assets/img/btn_bg.png');
        /* background-size: cover; */
        background-size: 100%;
    }

    :deep(.el-button--small) {
        width: 100rem;
        height: 30rem;
        background-image: url('../assets/img/btn_bg.png');
        background-size: 100%;
    }

    .btn_img {
        width: 16px;
        height: 16px;
    }
}

:deep(.el-button--small) {
    width: 100rem;
    height: 30rem;
    background-image: none;
    background-size: 100%;
}

:deep(.el-form-item__label) {
    color: #fff;
}

:deep(.el-dialog__header) {
    margin-left: 50px !important;
    position: relative;
}

:deep(.el-dialog__title) {
    position: absolute;
    top: 45px
}

:deep(.el-text) {
    color: #fff;
    font-size: 25px;
    margin: auto;
}

:deep(.el-input.is-disabled .el-input__inner) {
    -webkit-text-fill-color: #2962ff;
    cursor: not-allowed;
}

:deep(.el-radio.is-bordered.el-radio--large .el-radio__label) {
    color: #eee;
}

// 悬浮框
:deep(.el-alert--success.is-dark) {
    background-color: #1864ab;
}

.css3d-object {
    transform-style: preserve-3d;
    backface-visibility: hidden;
    transform: translateZ(0);
    /* 强制创建新的层叠上下文 */
    will-change: transform;
    /* 提示浏览器优化 */
    isolation: isolate;
    /* 防止混合 */
}
</style>