<template>
    <div class="setNode">
        <!-- 弹窗 -->
        <el-dialog style=" overflow:hidden" title="批量绘点" :fullscreen="true" v-model="isDialogShow">

            <div class="flex">
                <div class="mt-100px w-35%">
                    <div>
                        <!-- 点位搜索 -->
                        <div>
                            <div border="2px solid #20c997" relative mt-10px rounded-20px ml-20px p-20px pt-30px>
                                <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                                    <div
                                        class="w-120px h-25px text-center line-height-25px text-#20c997 font-bold bg-[#082237]">
                                        点位搜索
                                    </div>
                                </div>
                                <div class="flex mt-5px">
                                    <el-select style="width: 250rem;" v-model="searchPoint" filterable
                                        placeholder="搜索历史点">
                                        <el-option v-for="item in needNodeList" :key="item.NodeID"
                                            :label="`巷道ID:${item.NodeID}${item.NodeName && item.NodeName != 'null' ? `--名称:${item.NodeName}` : ''}`"
                                            :value="item.NodeID" />
                                    </el-select>
                                    <!-- 筛选框 -->
                                    <el-space ml-10px>
                                        <label>筛选:</label>
                                        <el-radio-group v-model="radioCurrent">
                                            <el-radio value="1" size="large" border>孤岛点</el-radio>
                                            <el-radio value="2" size="large" border>联络点</el-radio>
                                            <el-radio value="3" size="large" border>全部</el-radio>
                                        </el-radio-group>
                                    </el-space>
                                </div>

                            </div>

                        </div>
                        <div class="ml-20px mb-10px mt-20px">
                            <el-space>
                                <el-tag class="cursor-pointer" effect="dark" size="large" type="primary"
                                    @click="operateID = 1">新增节点</el-tag>
                                <el-tag class="cursor-pointer" effect="dark" size="large" type="warning"
                                    @click="operateID = 2">更新节点</el-tag>
                                <el-tag class="cursor-pointer" effect="dark" size="large" type="danger"
                                    @click="operateID = 3">删除节点</el-tag>
                                <el-tag class="cursor-pointer" effect="dark" size="large" type="primary"
                                    @click="operateID = 4">设置节点关系</el-tag>
                                <el-tag class="cursor-pointer" effect="dark" size="large" type="warning"
                                    @click="operateID = 5">更新节点关系</el-tag>
                                <el-tag class="cursor-pointer" effect="dark" size="large" type="danger"
                                    @click="operateID = 6">删除节点关系</el-tag>
                                <el-tag class="cursor-pointer" effect="dark" size="large" type="primary"
                                    @click="operateID = 7">节点导入</el-tag>
                            </el-space>
                        </div>


                        <!-- 新增节点 -->
                        <div v-show="operateID == 1" class="mt-20px">
                            <el-form label-width="auto">
                                <div border="2px solid #2DA8EB" relative rounded-20px ml-20px p-20px pt-30px>
                                    <div absolute
                                        class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                                        <div
                                            class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                                            新增节点
                                        </div>
                                    </div>
                                    <el-scrollbar height="310rem" class="flex justify-center">
                                        <el-form-item v-for="(item, index) in pointList" :key="index">
                                            <el-row :gutter="20" justify="left" align="center">
                                                <el-col :span="1">
                                                </el-col>
                                                <label class="text-[#fff] text-18px">名称:</label>
                                                <el-col :span="5">
                                                    <el-input v-model.trim="item.name" />
                                                </el-col>
                                                <label class="text-[#fff] text-18px">x:</label>
                                                <el-col :span="4">
                                                    <el-input v-model.trim="item.x" />
                                                </el-col>
                                                <label class="text-[#fff] text-18px">y:</label>
                                                <el-col :span="4">
                                                    <el-input v-model.trim="item.y" />
                                                </el-col>
                                                <label class="text-[#fff] text-18px">z:</label>
                                                <el-col :span="4">
                                                    <el-input v-model.trim="item.z" />
                                                </el-col>
                                                <el-col :span="1" v-show="index == pointList.length - 1">
                                                    <el-icon size="20rem" color="#40c057" class="mt-8px"
                                                        @click="addPoint">
                                                        <CirclePlus />
                                                    </el-icon>
                                                </el-col>
                                                <el-col :span="1" v-show="pointList.length != 1">
                                                    <el-icon size="20rem" color="#ea2465" class="mt-8px"
                                                        @click="removePoint(index)">
                                                        <Remove />
                                                    </el-icon>
                                                </el-col>
                                            </el-row>
                                        </el-form-item>
                                    </el-scrollbar>
                                    <div class="w-92% mt-30px  flex justify-end">
                                        <el-button type="primary" @click="onSubmit">保存</el-button>
                                        <el-button @click="addCancel">取消</el-button>
                                    </div>
                                </div>
                            </el-form>

                            <!-- 新增节点列表 -->
                            <div border="2px solid #2DA8EB" relative rounded-20px mt-15px ml-20px p-20px pt-30px>
                                <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                                    <div
                                        class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                                        新增节点列表
                                    </div>
                                </div>
                                <el-scrollbar height="200rem">
                                    <div class="ml-40px my-5px" v-for="(item, index) in newPointAll" :key="item.NodeID">
                                        <span>节点名称：</span><span>{{ item.originPoint.NodeID }}</span>&nbsp;
                                        <el-button type=" danger" size="default"
                                            @click="goNodeLocation(item.originPoint.NodeID)">定位</el-button>
                                    </div>
                                </el-scrollbar>

                            </div>

                        </div>
                        <!-- 更新节点 -->
                        <div v-show="operateID == 2">
                            <div border="2px solid #2DA8EB" h-400px relative rounded-20px mt-25px ml-20px p-20px
                                pt-30px>
                                <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                                    <div
                                        class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                                        更新节点
                                    </div>
                                </div>
                                <el-form :model="updateNodeInfo" label-width="auto">
                                    <el-form-item label="节点名称：">
                                        <el-input v-model="updateNodeInfo.NodeName" placeholder="请输入修改名称"
                                            size="normal"></el-input>
                                    </el-form-item>
                                    <el-form-item label="节点坐标：">
                                        <el-space>
                                            <el-input v-model="updateNodeInfo.x" size="normal"></el-input>
                                            <el-input v-model="updateNodeInfo.y" size="normal"></el-input>
                                            <el-input v-model="updateNodeInfo.z" size="normal"></el-input>
                                        </el-space>
                                    </el-form-item>
                                </el-form>
                                <div class="w-92% mt-30px absolute right-8% bottom-20px flex justify-end">
                                    <el-button type="primary" @click="updateSave">保存</el-button>
                                    <el-button type="primary" @click="updateCancel">取消</el-button>
                                </div>
                            </div>
                        </div>
                        <!-- 删除节点 -->
                        <div v-show="operateID == 3">
                            <div border="2px solid #2DA8EB" h-400px relative rounded-20px mt-25px ml-20px p-20px
                                pt-30px>
                                <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                                    <div
                                        class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                                        删除节点
                                    </div>
                                </div>
                                <el-form :model="removeNodeInfo" label-width="auto">
                                    <el-form-item label="节点ID：">
                                        <el-input :value="removeNodeInfo.NodeID" disabled />
                                    </el-form-item>
                                    <el-form-item label="节点名称：">
                                        <el-input :value="removeNodeInfo.NodeName" disabled />
                                    </el-form-item>
                                    <el-form-item label="节点状态：">
                                        <el-input
                                            :value="removeNodeInfo.Vertex != null ? (removeNodeInfo.Vertex == 0 ? '起始点' : '中间点') : ''"
                                            disabled />
                                    </el-form-item>
                                </el-form>
                                <div class="w-92% mt-30px absolute right-8% bottom-20px flex justify-end">
                                    <el-button type="primary" @click="removeSave">保存</el-button>
                                    <el-button type="primary" @click="removeCancel">取消</el-button>
                                </div>
                            </div>
                        </div>
                        <!-- 设置节点关系 -->
                        <div v-show="operateID == 4">
                            <el-form :model="form" label-width="auto" style="margin-left: 10rem;">
                                <div border="2px solid #2DA8EB" h-700rem relative rounded-20px mt-25px ml-20px p-20px
                                    pt-30px>
                                    <div absolute
                                        class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                                        <div
                                            class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                                            设置节点关系
                                        </div>
                                    </div>
                                    <el-form-item class="mb-5px">
                                        <div class="h-150rem w-100% rounded-15px p-20px bg-[#1864ab] text-[#ffff]">
                                            <h3>使用说明</h3>
                                            <div>
                                                <span>1、点击选中右侧节点,可以使用上述点位搜索功能进行查找，会直接定位到目标</span> <br />
                                                <span>2、点位右侧的三个图标可以定位、新增和删除节点</span> <br />
                                                <span class=" font-500 text-[#ffa39e]">3、新增巷道必须要选中两个节点才能进行添加</span>
                                            </div>
                                        </div>
                                    </el-form-item>

                                    <el-form-item label="巷道名称" prop="TunName">
                                        <el-input v-model="tunOther.TunName" clearable></el-input>
                                    </el-form-item>
                                    <el-form-item label="用风类型" prop="Ventflow">
                                        <el-select v-model="tunOther.Ventflow">
                                            <el-option :value="1" label="进风"></el-option>
                                            <el-option :value="0" label="用风"></el-option>
                                            <el-option :value="-1" label="回风"></el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="宽度" prop="Width">
                                        <el-input v-model="tunOther.Width" clearable></el-input>
                                    </el-form-item>
                                    <el-scrollbar height="280rem">

                                        <el-form-item v-for="( item, index) in nodeList" :label="'坐标' + (index + 1)"
                                            :key="index">
                                            <el-row :gutter="20" justify="left" align="center">
                                                <el-col :span="1">
                                                </el-col>
                                                <label class=" text-18px">ID:</label>
                                                <el-col :span="4">
                                                    <el-input disabled v-model.trim="item.originPoint.NodeID" />
                                                </el-col>
                                                <label class=" text-18px">x:</label>
                                                <el-col :span="4">
                                                    <el-input disabled v-model.trim="item.originPoint.x" />
                                                </el-col>
                                                <label class=" text-18px">y:</label>
                                                <el-col :span="4">
                                                    <el-input disabled v-model.trim="item.originPoint.y" />
                                                </el-col>
                                                <label class=" text-18px">z:</label>
                                                <el-col :span="4">
                                                    <el-input disabled v-model.trim="item.originPoint.z" />
                                                </el-col>
                                                <el-col :span="1">
                                                    <el-icon size="20rem" color="#4263eb" class="mt-8px cursor-pointer"
                                                        @click="goNodeLocation(item.originPoint.NodeID)">
                                                        <Location />
                                                    </el-icon>
                                                </el-col>
                                                <el-col :span="1">
                                                    <el-icon size="20rem" color="#66bb6a" class="mt-8px cursor-pointer"
                                                        @click="addMiddlePoint(index < nodeList.length - 1 ? index : null)">
                                                        <CirclePlus />
                                                    </el-icon>
                                                </el-col>
                                                <el-col :span="1">
                                                    <el-icon size="20rem" color="#ea2465" class="mt-8px"
                                                        @click="removeMiddlePoint(index)">
                                                        <Remove />
                                                    </el-icon>
                                                </el-col>

                                            </el-row>
                                        </el-form-item>
                                    </el-scrollbar>

                                    <div class="w-92% mt-30px absolute right-8% bottom-20px flex justify-end">
                                        <el-button type="primary" @click="onSubmitSet">保存</el-button>
                                        <el-button type="primary" @click="updateTunCancel">取消</el-button>
                                    </div>
                                </div>
                            </el-form>
                        </div>
                        <!-- 更新节点关系 -->
                        <div v-show="operateID == 5">
                            <el-form :model="form" label-width="auto" style="margin-left: 10rem;">
                                <div border="2px solid #2DA8EB" h-700rem relative rounded-20px mt-25px ml-20px p-20px
                                    pt-30px>
                                    <div absolute
                                        class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                                        <div
                                            class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                                            更新节点关系
                                        </div>
                                    </div>
                                    <el-form-item class="mb-5px">
                                        <div class="h-150rem w-100% rounded-15px p-20px bg-[#1864ab] text-[#ffff]">
                                            <h3>使用说明</h3>
                                            <div>
                                                <span>1、点击右侧线段进行选中,在未进行第二步时可以自由切换巷道线段</span> <br />
                                                <span class="text-[#ffa39e]">2、需要点击编辑或者新增按钮才能进行节点点击,之后巷道点击将被锁定</span>
                                                <br />
                                                <span class="text-[#ffa39e]">3、想要切换其他巷道线段可以点击取消当前操作</span> <br />
                                            </div>
                                        </div>
                                    </el-form-item>

                                    <el-form-item label="巷道名称" prop="TunName">
                                        <el-input v-model="updateTunOther.TunName" clearable></el-input>
                                    </el-form-item>
                                    <el-form-item label="用风类型" prop="Ventflow">
                                        <el-select v-model="updateTunOther.Ventflow">
                                            <el-option :value="1" label="进风"></el-option>
                                            <el-option :value="0" label="用风"></el-option>
                                            <el-option :value="-1" label="回风"></el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="宽度" prop="Width">
                                        <el-input v-model="updateTunOther.Width" clearable></el-input>
                                    </el-form-item>
                                    <el-scrollbar height="280rem">

                                        <el-form-item v-for="( item, index) in updateNodeList"
                                            :label="'坐标' + (index + 1)" :key="index">
                                            <el-row :gutter="10" justify="left" align="center">
                                                <el-col :span="0.5">
                                                </el-col>
                                                <label class=" text-18px">ID:</label>
                                                <el-col :span="4">
                                                    <el-input disabled v-model.trim="item.originPoint.NodeID" />
                                                </el-col>
                                                <label class=" text-18px">x:</label>
                                                <el-col :span="4">
                                                    <el-input disabled v-model.trim="item.originPoint.x" />
                                                </el-col>
                                                <label class=" text-18px">y:</label>
                                                <el-col :span="4">
                                                    <el-input disabled v-model.trim="item.originPoint.y" />
                                                </el-col>
                                                <label class=" text-18px">z:</label>
                                                <el-col :span="4">
                                                    <el-input disabled v-model.trim="item.originPoint.z" />
                                                </el-col>
                                                <el-col :span="1">
                                                    <el-icon size="20rem" color="#4263eb" class="mt-8px cursor-pointer"
                                                        @click="goNodeLocation(item.originPoint.NodeID)">
                                                        <Location />
                                                    </el-icon>
                                                </el-col>
                                                <el-col :span="1">
                                                    <el-icon size="20rem" color="#66bb6a" class="mt-8px cursor-pointer"
                                                        @click="updateAddPoint(index < updateNodeList.length - 1 ? index : null)">
                                                        <CirclePlus />
                                                    </el-icon>
                                                </el-col>
                                                <el-col :span="1">
                                                    <el-icon size="20rem" color="#ea2465" class="mt-8px"
                                                        @click="updateRemovePoint(index)">
                                                        <Remove />
                                                    </el-icon>
                                                </el-col>
                                                <el-col :span="1">
                                                    <el-icon size="20rem" color="#ECBF7D" class="mt-8px"
                                                        @click="updateEditPoint(index)">
                                                        <Edit />
                                                    </el-icon>
                                                </el-col>

                                            </el-row>
                                        </el-form-item>
                                    </el-scrollbar>

                                    <div class="w-92% mt-30px absolute right-8% bottom-20px flex justify-end">
                                        <el-button type="primary" @click="updateOnSubmit">保存</el-button>
                                        <el-button type="primary" @click="updateTunCancel">取消</el-button>
                                    </div>
                                </div>
                            </el-form>
                        </div>
                        <!-- 删除节点关系 -->
                        <div v-show="operateID == 6">
                            <el-form :model="form" label-width="auto" style="margin-left: 10rem;">
                                <div border="2px solid #2DA8EB" h-700rem relative rounded-20px mt-25px ml-20px p-20px
                                    pt-30px>
                                    <div absolute
                                        class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                                        <div
                                            class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                                            删除节点关系
                                        </div>
                                    </div>
                                    <el-form-item class="mb-5px">
                                        <div
                                            class="mb-5px h-150rem w-100% rounded-15px p-20px bg-[#1864ab] text-[#ffff]">
                                            <h3>使用说明</h3>
                                            <div>
                                                <span>1、点击选中右侧巷道进行选中</span> <br />
                                                <span class=" font-500 text-[#ffa39e]">3、点击鼠标右键进行撤销操作</span> <br />
                                            </div>
                                        </div>

                                    </el-form-item>
                                    <div class="w-92% mt-30px absolute right-8% bottom-20px flex justify-end">
                                        <el-button type="primary" @click="removeTunSave">保存</el-button>
                                        <el-button type="primary" @click="removeTunCancel">取消</el-button>
                                    </div>
                                </div>
                            </el-form>
                        </div>
                        <!-- 节点导入 -->
                        <div v-show="operateID == 7" calss="nodeImport">
                            <el-form :model="form" label-width="auto" style="margin-left: 10rem;">
                                <div border="2px solid #2DA8EB" h-700rem relative rounded-20px mt-25px ml-20px p-20px
                                    pt-30px>
                                    <div absolute
                                        class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                                        <div
                                            class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                                            节点导入
                                        </div>
                                    </div>
                                    <UploadExcel @result="getImportData"></UploadExcel>

                                </div>

                            </el-form>



                        </div>
                    </div>
                </div>



                <div class="mt-100px w-60% h-82vh m-auto bg-[#262626]" ref="sceneDiv" id="sceneDiv2"></div>
                <div class="fixed top-50% right-20px">
                    <div class="w-50px h-50px cursor-pointer" @click="restCamera"><img class=" w-full h-full"
                            src="../../thereVentilation/assets/img/scene_fw.png"></div>

                </div>
            </div>

        </el-dialog>
    </div>
</template>
<script setup>
import { watch, reactive, ref, toRaw, onUnmounted, onMounted } from 'vue';
import { useThree } from '@/three/useThree';
import { moveToCamera, restrictCameraVerticalDirection } from '@/three/cameraControls.js'
import { CirclePlus, Remove, Location, Edit, Search, Check, Close, CloseBold, Select } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { getType } from '@/utils/utils';
import { dbModelClick, modelClick } from '@/three/utils/event';
import { positionTransform } from "@/views/thereVentilation/js/tunUtils.js";
import { ApiNodeAdd, ApiNodeDel, ApiNodeUpdate, ApiNodeList, ApiTunAdd, ApiTunDel, ApiTunUpdate, ApiTunList, roadwayList, ApiNodeCheckDouble, ApiNodeDistance } from '@/https/encapsulation/threeDimensional';
import { computeTunLenght } from "@/views/thereVentilation/js/tunUtils.js";
import { addEvent, removeEvent } from "@/utils/window";
import UploadExcel from '@/components/common/testUpload/testUpload.vue'
const emit = defineEmits(['newData', 'stopRender']);
const sceneDiv = ref(null);
const operateID = ref(7); // 控制操作方式

const isDialogShow = defineModel("isDialogShow", { type: Boolean, default: true })
const props = defineProps({
    needData: { type: Object, default: {} },
    needFn: { type: Object, default: {} }
})

const apiTunList = ref([]);
const apiNodeList = ref([]);
let tunStr = ref(``);
const needNodeList = ref([]) //需要展示的点位
const searchPoint = ref(null)

watch(searchPoint, (val) => {
    if (val) {
        goNodeLocation(val);
    }
})

// 选择框列表
const radioCurrent = ref(-1);
watch(radioCurrent, (val) => {
    changeNeedNodeList(val);
})

function changeNeedNodeList(state) {
    if (state) {
        if (state == 1) {
            needNodeList.value = apiNodeList.value.filter(v => !tunStr.includes(v.NodeID));
        }
        else if (state == 2) {
            needNodeList.value = apiNodeList.value.filter(v => v.Vertex == 0);
        }
        else {
            needNodeList.value = apiNodeList.value.filter(v => v.Vertex == 0 || !tunStr.includes(v.NodeID));;
        }
    }

}

// 获取所有巷道信息 
async function getTunList() {
    const { Result } = await ApiTunList();
    console.log(Result, 'Result');
    apiTunList.value = Result ?? [];
    tunStr = ``;
    Result.forEach(v => {
        const { SnodeID, EnodeID, MnodeID } = v;
        tunStr += MnodeID ? `${SnodeID}-${MnodeID}-${EnodeID}-` : `${SnodeID}-${EnodeID}-`
    })
    return Result;
}
// 获取所有节点信息 
async function getNodeList() {
    const { Result } = await ApiNodeList();
    apiNodeList.value = Result ?? [];
    // setScene(Result);

    return Result;
}



// getNodeList();

// 
const { createOriginPoint, createTunOrigin } = props.needFn;

let lineMeshArr = [];
let circleMeshArr = [];
let newPointAll = ref([]);
var newPointList = [];//新增节点列表
let global = ref() // 全局管家
var maxNodeID = ref(0);
let middleNode = ref([0, 0, 0]);
let maxPosition = []
let minPosition = []

//threejs init
const { mountRender, cameraControls, scene, camera, renderer, THREE, cameraFlyToMesh, addOutlinePass, addOutlinePass1, setMeshRotation, setMeshScale, setPosition, ani, stopRender } = useThree({ endCamera: [0, 10000, 0], isCameraAni: false, isControl: true, isAxes: false, isDepthBuffer: true })
let group = new THREE.Group();
// setMeshRotation(group, { y: Math.PI })
scene.add(group);
const lineMat = new THREE.LineBasicMaterial();
global.value = { lineMat, group, stopRender, THREE, camera, scene, addOutlinePass, addOutlinePass1, cameraControls, cameraFlyToMesh, addOutlinePass1 };
scene.background = new THREE.Color(0x262626)
// 场景放大系数
let n = 2.8

// 记录上次操作的 相机位置
const lastCameraPosition = ref({ x: null, y: null, z: null })
var setY = 0;
function restCamera() {
    moveToCamera(cameraControls, 0, [0, setY * n, 0], [0, 0, 0]);
}

ani((time, camera, delta, requestAnimationId) => {
    const { x, y, z } = camera.position;
    lastCameraPosition.value = { x, y, z };
    // console.log(requestAnimationId, 'requestAnimationId');
});
const flag = ref(false) // 标识

watch(isDialogShow, (val) => {
    if (val) {
        openImportNode();
        setTimeout(() => {
            const el = document.querySelector('#sceneDiv2');
            mountRender(el);
        })

    } else {
        operateID.value = 1;
        emit('newData', { maxNodeID2: toRaw(maxNodeID.value) })
        emit('stopRender', stopRender)
        closeImportNode(true);
    }
}, { immediate: true })

watch(operateID, val => {
    if (val && sceneDiv.value) {
        changeEvent(val);
    }
})
function changeEvent(index) {
    global.value.clickRemove && global.value.clickRemove();
    global.value.dblRemove && global.value.dblRemove();
    global.value.repealRemove && global.value.repealRemove();
    global.value.addOutlinePass([]);
    global.value.addOutlinePass1([]);
    switch (index) {
        case 1: break;
        case 2: addInteraction2(sceneDiv.value); break;
        case 3: addInteraction3(sceneDiv.value); break;
        case 4: addInteraction4(sceneDiv.value); break;
        case 5: addInteraction5(sceneDiv.value); break;
        case 6: addInteraction6(sceneDiv.value); break;
        case 7: break;
    }
}
function openImportNode() {

    global.value.addOutlinePass([]);
    global.value.addOutlinePass1([]);
    let vec3 = null;
    getNodeList().then((Result) => {
        vec3 = Result.length == 1 ? Result[0] : (Result[Math.floor(Result.length / 2)] ?? { x: 0, y: 0, z: 0 });
        const { x, y, z } = vec3 ?? { x: 0, y: 0, z: 0 }
        middleNode.value = [x, y, z];
    }).then(() => {
        // threejs init
        setY = middleNode.value[2] + 4000;


        if (!flag.value) {
            moveToCamera(cameraControls, 0, [0, setY * n, 0], [0, 0, 0]);
            flag.value = true;
        }

        const grid = new THREE.GridHelper((setY * n) * 5, 20);
        grid.renderOrder = 1;
        setPosition(grid, 0, -2, 0)
        group.add(grid);
        restrictCameraVerticalDirection(cameraControls, 0, 0);
        cameraControls.minAzimuthAngle = 0;
        cameraControls.maxAzimuthAngle = 0;

        roadwayList().then(({ Result }) => {
            if (!Result || !Result.length) return
            Result.forEach(v => {
                // 渲染线段
                const newNodes = v.Nodes.map(k => {
                    const { x, y, z } = k;
                    const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { middlePosition: middleNode.value });
                    return { ...k, x: x1, y: y1, z: z1 }
                })
                if (v.Nodes.length > 1) {
                    const linePoints = newNodes.map(v => { return { x: v.x, y: v.y, z: v.z } })
                    let lineMesh = createLine({ THREE, linePoints, n })
                    lineMesh.mid = v.TunID;
                    lineMesh.originPoints = v;
                    group.add(lineMesh);
                }
            })
        })
        getTunList().then(v => {
            changeNeedNodeList(radioCurrent.value);
            if (!apiNodeList.value.length) return
            console.log(tunStr, 'tunStr');
            apiNodeList.value.forEach(v => {
                // if (v.Vertex == 0 || !tunStr.includes(v.NodeID)) {
                const { x, y, z, NodeID } = v;
                if (NodeID > maxNodeID.value) {
                    maxNodeID.value = NodeID;
                }
                const [maxX, maxY, maxZ] = maxPosition;
                x > maxX ? maxPosition[0] = x : minPosition[0] = x;
                z > maxY ? maxPosition[1] = z : minPosition[1] = z;
                y > maxZ ? maxPosition[2] = y : minPosition[2] = y;
                const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { middlePosition: middleNode.value })
                let circle = createCicle({ text: NodeID, THREE: THREE, position: { x: x1, y: y1, z: z1 }, originPoint: v, n });
                group.add(circle);
                // }
            })
        })

    }).then(v => {
        changeEvent(operateID.value);
    })

}

function closeImportNode(isExit = false) {
    addCancel();
    updateCancel();
    removeCancel();
    tunCancel();
    updateTunCancel();
    removeTunCancel();
    ImportCancel();

    lineMeshArr = [];
    circleMeshArr = [];
    newPointList = [];//新增节点列表
    middleNode.value = [0, 0, 0];
    apiTunList.value = [];
    apiNodeList.value = [];
    tunStr = ``;

    global.value.clickRemove && global.value.clickRemove();
    global.value.dblRemove && global.value.dblRemove();
    global.value.repealRemove && global.value.repealRemove();
    group.remove(...group.children);

    if (isExit) {
        // global.value.stopRender();
        // global.value = null;
        newPointAll.value = [];
        locationMesh && locationMesh.position.set(locationMesh.position.x, 2, locationMesh.position.y);
        locationMesh = null;
    }
}


function createLine({ THREE, linePoints = [], isEditMat, color = 0xffff00, n = 1, type = 0 }) {
    const [x1, y1, z1] = maxPosition;
    const arr = type == 0 ? linePoints.map((item) => {
        const { x, y, z } = item;
        // return x1 < z1 ? new THREE.Vector3(x * n, 0, z * n) : new THREE.Vector3(z * n, 0, x * n);
        return new THREE.Vector3(x * n, 0, z * n)
    }) : linePoints;
    const mat = isEditMat ? new THREE.LineBasicMaterial() : global.value.lineMat;
    mat.color = new THREE.Color(color);
    // let curve = new THREE.CatmullRomCurve3(arr) // 曲线路径
    // const points = curve.getPoints(arr.length * 2);
    const geometry = new THREE.BufferGeometry().setFromPoints(arr);
    const lineMesh = new THREE.Line(geometry, global.value.lineMat);
    lineMesh.points = linePoints;
    lineMesh.renderOrder = 2;
    lineMeshArr.push(lineMesh);

    return lineMesh;
}
import { createCss3DText, createCss3DSprite, createCss3DObj } from '@/three/css3DRender';
const geometry = new THREE.CircleGeometry(20, 20);
function createCicle({ bgColor, color, text, THREE, position = { x: 0, y: 0, z: 0 }, n = 1, originPoint }) {
    const circleMat = new THREE.MeshBasicMaterial({ color: 0xffffff });
    const circle = new THREE.Mesh(geometry, circleMat);
    const { x, y, z } = position;
    const [x1, y1, z1] = maxPosition;
    // x1 < z1 ? circle.position.set(x * n, 2, z * n) : circle.position.set(z * n, 2, x * n);
    circle.position.set(x * n, 2, z * n)
    circle.rotation.x = -Math.PI / 2;
    circle.point = position instanceof THREE.Vector3 ? position : new THREE.Vector3(x, y, z);
    circle.originPoint = originPoint;
    circle.mid = originPoint.NodeID;
    circleMeshArr.push(circle);

    var { texture } = makeTextSprite(THREE, text,
        {
            fontsize: 110,
            borderColor: { r: 255, g: 0, b: 0, a: 1.0 },/* 边框黑色 */
            color: color ?? { r: 255, g: 255, b: 255, a: 1.0 },
            backgroundColor: bgColor ?? { r: 0, g: 151, b: 164, a: 1.0 }/* 背景颜色 */
        });
    circle.material.map = texture;
    circle.renderOrder = 3;
    // circle.material.transparent = true;
    // circle.material.opacity = 0.9;
    return circle;
}


// 批量导点 表单

// const pointList = ref([{ x: null, y: null, z: null }])
const pointList = ref([{ name: '', x: null, y: null, z: null }])
function addPoint() {
    pointList.value.push({ name: '', x: null, y: null, z: null });
}
function removePoint(index) {
    pointList.value.splice(index, 1);
}

var newCircleList = []
// 新增节点
const onSubmit = () => {
    const newPoints = []
    let flag = true;
    for (var i = 0; i < pointList.value.length; i++) {
        const v = pointList.value[i]
        const { x, y, z, name } = v;
        // console.log(JSON.stringify(parseFloat(v.x)), parseFloat(v.y), parseFloat(v.z));
        if (JSON.stringify(parseFloat(x)) != 'null' && JSON.stringify(parseFloat(y)) != 'null' && JSON.stringify(parseFloat(z)) != 'null') {
            const bol1 = getType(parseFloat(x));
            const bol2 = getType(parseFloat(y));
            const bol3 = getType(parseFloat(z));
            flag = bol1 == 'number' && bol2 == 'number' && bol3 == 'number';
            if (!flag) {
                ElMessage.error('请保证您输入的是一个数字类型')
                break;
            } else {
                maxNodeID.value = ++maxNodeID.value;
                const vec3 = positionTransform(x, y, z, { middlePosition: middleNode.value })
                const point = { point: vec3, originPoint: { NodeID: maxNodeID.value, NodeName: name, x, y, z, state: 'new' } };
                newPoints.push(point);
            }
        } else {
            flag = false;
            ElMessage.error('请保证您输入的是一个数字类型')
            break;
        }
    }
    if (flag) {
        newPoints.length && Promise.allSettled(newPoints.map(async item => {
            let NodeAddObj = {
                NodeID: 0, NodeName: '', Num: 0, Vertex: 1, X: 0, Y: 0, Z: 0
            }
            const { originPoint: { NodeID, NodeName, x: X, y: Y, z: Z } } = item;
            NodeAddObj = { ...NodeAddObj, NodeName, NodeID, X, Y, Z }
            const { Status } = await ApiNodeAdd(NodeAddObj);
            return Status;
        })).then(result => {
            if (result.every(v => v.value == 0)) {
                ElMessage.success('保存成功')
                newPointAll.value.push(...newPoints);
                closeImportNode();
                openImportNode();
                addCancel();
            }
        })
    }
}
// 取消节点保存
const addCancel = () => {
    addOutlinePass([])
    addOutlinePass1([])
    newCircleList = [];
    pointList.value = [{ name: '', x: null, y: null, z: null }];
}

var locationMesh = null;
// 定位到节点所在位置
function goNodeLocation(NodeID) {
    if (!NodeID) { ElMessage.warning('NodeID 不存在'); return };
    locationMesh && locationMesh.position.set(locationMesh.position.x, 2, locationMesh.position.z);
    let mesh = group.children.find(v => v.mid == NodeID);
    const { x, y, z } = mesh.position;
    global.value.addOutlinePass1([mesh]);
    mesh.position.set(x, 5, z);
    global.value.cameraFlyToMesh(global.value.cameraControls, { mesh: mesh, paddingTop: 100, paddingRight: 100, paddingBottom: 100, paddingLeft: 100 });
    locationMesh = mesh;
}

// 更新节点
const updateNodeInfo = ref({ NodeName: '', x: null, y: null, z: null });
let changePoint2 = [] //统计被改变的点位数据
let findPoint2 = [] //统计已经添加过查找的数据


// 更新节点交互事件
function addInteraction2(el) {
    let threshold = 5;
    const { clickRemove } = modelClick(circleMeshArr, global.value.camera, (currentMesh) => {
        const { point, originPoint, uuid, position: { x, y, z } } = currentMesh.object;
        locationMesh && locationMesh.position.set(locationMesh.position.x, 2, locationMesh.position.z);
        currentMesh.object.position.set(x, 5, z);
        if (findPoint.findIndex(k => k.uuid == uuid) == -1 && changePoint.findIndex(k => k.uuid == uuid) == -1
            && changePoint2.findIndex(k => k.uuid == uuid) == -1 && (findPoint2.findIndex(k => k.uuid == uuid) == -1)
            && findPoint3.findIndex(k => k.uuid == uuid) == -1 && changePoint3.findIndex(k => k.uuid == uuid) == -1) {
            const findArr = circleMeshArr.filter(v => isSimilar(v.originPoint.x, originPoint.x, threshold) && isSimilar(v.originPoint.y, originPoint.y, threshold));
            if (findArr.length > 1) {
                findArr.forEach((v, i) => {
                    if (v.uuid == uuid) {
                        findPoint2.push(v)
                        v.material.color = new global.value.THREE.Color(0xffc078);
                    }
                    else {
                        if (changePoint2.length == 0 || changePoint2.findIndex(k => k.uuid == v.uuid) == -1) {
                            const { x, y, z } = v.position;
                            v.position.set(x + 40 * (i - 1), y, z + 40)
                            v.material.color = new global.value.THREE.Color(0x63e6be);
                            changePoint2.push(v);
                        }
                    }
                })
            }
        }
        locationMesh = currentMesh.object;
        global.value.addOutlinePass1([currentMesh.object], 0xffab00);
        updateNodeInfo.value = currentMesh.object.originPoint;

    }, el)
    global.value.clickRemove = clickRemove;
}
async function updateSave() {
    const { x: X, y: Y, z: Z, NodeName, NodeID } = updateNodeInfo.value;
    const obj = apiNodeList.value.find(k => k.NodeID == NodeID)
    if (obj) {
        let NodeAddObj = {
            ...obj, X, Y, Z, NodeName
        }
        const { Status } = await ApiNodeUpdate(NodeAddObj);
        Status == 0 ? ElMessage.success('保存成功') : ElMessage.error('保存失败')
        Status == 0 && (closeImportNode(), openImportNode(), updateCancel());
        updateNodeInfo.value = { NodeName: '', x: null, y: null, z: null };
    }

}
function updateCancel() {
    addOutlinePass([])
    addOutlinePass1([])
    updateNodeInfo.value = { NodeName: '', x: null, y: null, z: null };
    findPoint2 = []
    changePoint2 = [];
}

// 删除节点
const removeNodeInfo = ref({ NodeName: '', NodeID: '', Vertex: null });
let removeNodeMesh = null;
// 更新节点交互事件
function addInteraction3(el) {
    const { clickRemove } = modelClick(circleMeshArr, global.value.camera, (currentMesh) => {
        global.value.addOutlinePass1([currentMesh.object], 0xffab00);
        removeNodeInfo.value = currentMesh.object.originPoint;
        removeNodeMesh = currentMesh.object;
    }, el)
    global.value.clickRemove = clickRemove;
}
async function removeSave() {
    const { NodeID, Vertex } = removeNodeInfo.value;
    if (tunStr.includes(NodeID)) {
        ElMessage.warning('起始点需要在关联巷道删除完后才能删除')
        return;
    }
    if (NodeID) {
        const { Status } = await ApiNodeDel(NodeID);
        Status == 0 ? ElMessage.success('保存成功') : ElMessage.error('保存失败')
        if (Status == 0) {
            // getNodeList();
            closeImportNode(); openImportNode();
            removeCancel();
        }
    }
}
function removeCancel() {
    addOutlinePass([])
    addOutlinePass1([])
    removeNodeInfo.value = { NodeName: '', NodeID: '', Vertex: null };
    removeNodeMesh = null;
}




// 设置节点关系
const nodeList = ref([{ originPoint: { x: null, y: null, z: null }, uuid: '' }]);
const tunOther = ref({
    TunName: '',
    Ventflow: 1,
    Width: 0
})

async function onSubmitSet() {
    const arr = [];
    let flag = true;
    for (var i = 0; i < nodeList.value.length; i++) {
        const v = nodeList.value[i]

        if (!v.point) {
            const { x, y, z } = v.originPoint;
            if (JSON.stringify(parseFloat(x)) != 'null' && JSON.stringify(parseFloat(y)) != 'null' && JSON.stringify(parseFloat(z)) != 'null') {
                const bol1 = getType(parseFloat(x));
                const bol2 = getType(parseFloat(y));
                const bol3 = getType(parseFloat(z));
                flag = bol1 == 'number' && bol2 == 'number' && bol3 == 'number';
                if (!flag) {
                    ElMessage.error('请保证您输入的是一个数字类型')
                    break;
                } else {
                    maxNodeID.value = ++maxNodeID.value;
                    const vec3 = positionTransform(x, y, z, { middlePosition: middleNode.value })
                    const point = { point: vec3, originPoint: { NodeID: maxNodeID.value, NodeName: '' + maxNodeID.value, x, y, z } };
                    arr.push(point);
                }
            } else {
                flag = false;
                ElMessage.error('请保证您输入的是一个数字类型')
                break;
            }
        } else {
            arr.push(v);
        }

    }
    if (flag) {
        const Nodes = arr.map(v => toRaw(v.originPoint));
        submitTunAdd(Nodes, tunCancel);
    }
}

async function submitTunAdd(arr, reset = () => { }) {
    const Nodes = arr;
    const tunAddObj = {
        Place: 0, TunState: 1, SnodeID: 0, MnodeID: '', EnodeID: 0, Lx: 0, ShoringType: 0, ShapeType: 0, UseType: 0, Length: 0, Width: 0, Height: 0, S: 0, A: 0, R: 0, NetLine: true,
        ...tunOther.value,
    }
    tunAddObj.SnodeID = Nodes[0].NodeID; tunAddObj.EnodeID = Nodes[Nodes.length - 1].NodeID;
    tunAddObj.Length = computeTunLenght(Nodes);

    if (Nodes.length > 2) {
        tunAddObj.MnodeID = Nodes.slice(1, Nodes.length - 1).map(v => v.NodeID).join('-');
    } else {
        tunAddObj.MnodeID = '';
    }


    if (arr[0].NodeID && arr[0].Vertex == 1) {
        const { x, y, z, Number, NodeName } = arr[0];
        let NodeAddObj = {
            ...arr[0], NodeName: NodeName ?? '', Num: Number, Vertex: 0, X: x, Y: y, Z: z
        }
        const { Status } = await ApiNodeUpdate(NodeAddObj);
    }
    if (arr.at(-1).NodeID && arr.at(-1).Vertex == 1) {
        const { x, y, z, Number, NodeName } = arr.at(-1);
        let NodeAddObj = { ...arr.at(-1), NodeName: NodeName ?? '', Num: Number, Vertex: 0, X: x, Y: y, Z: z }
        const { Status } = await ApiNodeUpdate(NodeAddObj);
    }

    const { Status } = await ApiTunAdd(tunAddObj);
    Status == 0 ? (closeImportNode(), openImportNode(), ElMessage.success('保存成功'), reset()) : ElMessage.error('保存失败')
}

function tunCancel() {
    addOutlinePass([])
    addOutlinePass1([])
    nodeList.value = [{ originPoint: { x: null, y: null, z: null }, uuid: '' }];
    tunOther.value = {
        TunName: '',
        Ventflow: 1,
        Width: 0
    }
}
// addMiddlePoint
function addMiddlePoint(index) {

    const newPoint = { originPoint: { x: null, y: null, z: null }, uuid: '' }
    if (index != null) {
        nodeList.value.splice(index + 1, 0, newPoint);
    } else {
        nodeList.value.push(newPoint);
    }
}
// 移除
function removeMiddlePoint(index) {
    if (index == 0) {
        if (nodeList.value[0].originPoint.x == null) {
            ElMessage.warning('该项已被清空,请勿重复操作')
            return;
        }
        nodeList.value.splice(0, 1, { originPoint: { x: null, y: null, z: null }, uuid: '' });
    } else {
        nodeList.value.splice(index, 1);
    }
}


//
var changePoint = [] //统计被改变的点位数据
var findPoint = [] //统计已经添加过查找的数据


// 设置节点关系交互事件
function isSimilar(num1, num2, threshold) {
    return Math.abs(num1 - num2) <= threshold;
}
let threshold = 5; // 搜索范围 让被遮挡的点显示出来
function addInteraction4(el) {
    const { clickRemove } = modelClick(circleMeshArr, global.value.camera, (currentMesh) => {
        if (currentMesh) {
            const { point, originPoint, uuid, position: { x, y, z } } = currentMesh.object;
            locationMesh && locationMesh.position.set(locationMesh.position.x, 2, locationMesh.position.z);
            currentMesh.object.position.set(x, 5, z)
            const middleFlag = nodeList.value.find(v => v.uuid == uuid); // 判断有没有重复添加点位
            middleFlag && ElMessage.error('该点已被添加')
            if (middleFlag) return;
            if (findPoint.findIndex(k => k.uuid == uuid) == -1 && changePoint.findIndex(k => k.uuid == uuid) == -1
                && changePoint2.findIndex(k => k.uuid == uuid) == -1 && (findPoint2.findIndex(k => k.uuid == uuid) == -1)
                && findPoint3.findIndex(k => k.uuid == uuid) == -1 && changePoint3.findIndex(k => k.uuid == uuid) == -1) {
                const findArr = circleMeshArr.filter(v => isSimilar(v.originPoint.x, originPoint.x, threshold) && isSimilar(v.originPoint.y, originPoint.y, threshold));
                if (findArr.length > 1) {
                    findArr.forEach((v, i) => {
                        if (v.uuid == uuid) {
                            findPoint.push(v)
                            v.material.color = new global.value.THREE.Color(0xffc078);
                        }
                        else {
                            if (changePoint.length == 0 || changePoint.findIndex(k => k.uuid == v.uuid) == -1) {
                                const { x, y, z } = v.position;
                                v.position.set(x + 40 * (i - 1), y, z + 40)
                                v.material.color = new global.value.THREE.Color(0x63e6be);
                                changePoint.push(v);
                            }
                        }
                    })
                }
            }
            for (var i = 0, len = nodeList.value.length; i < len; i++) {
                const item = nodeList.value[i];
                if (!item.originPoint.x) {
                    nodeList.value.splice(i, 1, currentMesh.object);
                    break;
                } else {
                    (i == len - 1) && nodeList.value.splice(i, 1, currentMesh.object); // 仅允许替换中间点的最后一个点
                }
            }
            global.value.addOutlinePass(nodeList.value.filter(v => v.uuid), 0x0ca678);
            locationMesh = currentMesh.object;
        }
    }, el);

    // 双击保存当前节点
    addEvent(window, 'dblclick', setNodeDblclick)
    global.value.clickRemove = clickRemove;
    global.value.dblRemove = dblRemove;
    function removeEvent() {
        clickRemove();
        dblRemove();
    }
    return removeEvent;
}
function setNodeDblclick() {
    if (!nodeList.value.at(-1).originPoint.x) return;
    addMiddlePoint();
}
function dblRemove() {
    removeEvent(window, 'dblclick', setNodeDblclick)
}
// 更新节点关系
var updateNodeList = ref([{ originPoint: { x: null, y: null, z: null }, uuid: '' }]);
const updateTunOther = ref({
    TunName: '',
    Ventflow: 1,
    Width: 0
})
var findPoint3 = [], changePoint3 = [];
let updateTunMesh = null
let changeIndex = null
let isFlag = true;
// 取消操作
function updateTunCancel() {
    addOutlinePass([])
    addOutlinePass1([])
    updateNodeList.value = [{ originPoint: { x: null, y: null, z: null }, uuid: '' }];
    updateTunOther.value = {
        TunName: '',
        Ventflow: 1,
        Width: 0
    }
    findPoint3 = [];
    changePoint3 = [];
    updateTunMesh && (updateTunMesh.material = lineMat);
    updateTunMesh = null;
    changeIndex = null;
    isFlag = true;
}
//
async function updateOnSubmit() {
    const arr = [];
    let flag = true;
    for (var i = 0; i < updateNodeList.value.length; i++) {
        const v = updateNodeList.value[i]

        if (!v.point) {
            const { x, y, z } = v.originPoint;
            if (JSON.stringify(parseFloat(x)) != 'null' && JSON.stringify(parseFloat(y)) != 'null' && JSON.stringify(parseFloat(z)) != 'null') {
                const bol1 = getType(parseFloat(x));
                const bol2 = getType(parseFloat(y));
                const bol3 = getType(parseFloat(z));
                flag = bol1 == 'number' && bol2 == 'number' && bol3 == 'number';
                if (!flag) {
                    ElMessage.error('请保证您输入的是一个数字类型')
                    break;
                }
            } else {
                flag = false;
                ElMessage.error('请保证您输入的是一个数字类型')
                break;
            }
        }
    }
    if (flag) {
        const Nodes = updateNodeList.value.map(v => toRaw(v.originPoint));
        const tunAddObj = {
            ...updateTunOther.value,
        }

        tunAddObj.SnodeID = Nodes[0].NodeID; tunAddObj.EnodeID = Nodes[Nodes.length - 1].NodeID;
        tunAddObj.Length = computeTunLenght(Nodes);

        if (Nodes.length > 2) {
            tunAddObj.MnodeID = Nodes.slice(1, Nodes.length - 1).map(v => v.NodeID).join('-');
        } else {
            tunAddObj.MnodeID = '';
        }
        console.log(tunAddObj, 'tunAddObj');
        const obj1 = apiNodeList.value.find(k => k.NodeID == updateNodeList.value.at(0).mid)
        const obj2 = apiNodeList.value.find(k => k.NodeID == updateNodeList.value.at(-1).mid)
        if (obj1 && obj1.Vertex == 1) {
            const { x, y, z, Number, NodeName } = obj1;
            let NodeAddObj = {
                ...obj1, NodeName: NodeName ?? '', Num: Number, Vertex: 0, X: x, Y: y, Z: z, Vertex: 0
            }
            const { Status } = await ApiNodeUpdate(NodeAddObj);
        }
        if (obj2 && obj2.Vertex == 1) {
            const { x, y, z, Number, NodeName } = obj2;
            let NodeAddObj = { ...obj2, NodeName: NodeName ?? '', Num: Number, Vertex: 0, X: x, Y: y, Z: z, Vertex: 0 }
            const { Status } = await ApiNodeUpdate(NodeAddObj);
        }

        const { Status } = await ApiTunUpdate(tunAddObj);
        Status == 0 ? (closeImportNode(), openImportNode(), ElMessage.success('保存成功')) : ElMessage.error('保存失败')

        updateTunCancel();
    }
}
function addInteraction5(el) {
    const { clickRemove } = modelClick(lineMeshArr, camera, (currentMesh) => {
        if (changeIndex == null && isFlag) {
            updateTunMesh && (updateTunMesh.material = lineMat)
            const { originPoints } = currentMesh.object;
            currentMesh.object.material = new THREE.LineBasicMaterial({ color: 0xfd7e14 });
            updateTunOther.value = originPoints;
            updateNodeList.value = originPoints.Nodes.map(v => { return { originPoint: v, uuid: '' } });
            updateTunMesh = currentMesh.object;
        }
    }, el)
    const { clickRemove: clickRemove2 } = modelClick(circleMeshArr, global.value.camera, (currentMesh) => {
        if (currentMesh) {
            if (changeIndex == null) { ElMessage.warning({ message: '您需要点击左侧新增按钮或者编辑按钮才能进行操作', duration: 3000 }); return }
            const { point, originPoint, uuid, position: { x, y, z } } = currentMesh.object;
            locationMesh && locationMesh.position.set(locationMesh.position.x, 2, locationMesh.position.z);
            currentMesh.object.position.set(x, 5, z)
            const middleFlag = updateNodeList.value.find(v => v.uuid == uuid); // 判断有没有重复添加点位
            middleFlag && ElMessage.error('该点已被添加')
            if (middleFlag) return;
            if (findPoint.findIndex(k => k.uuid == uuid) == -1 && changePoint.findIndex(k => k.uuid == uuid) == -1
                && changePoint2.findIndex(k => k.uuid == uuid) == -1 && (findPoint2.findIndex(k => k.uuid == uuid) == -1)
                && findPoint3.findIndex(k => k.uuid == uuid) == -1 && changePoint3.findIndex(k => k.uuid == uuid) == -1) {
                const findArr = circleMeshArr.filter(v => isSimilar(v.originPoint.x, originPoint.x, threshold) && isSimilar(v.originPoint.y, originPoint.y, threshold));
                if (findArr.length > 1) {
                    findArr.forEach((v, i) => {
                        if (v.uuid == uuid) {
                            findPoint.push(v)
                            v.material.color = new global.value.THREE.Color(0xffc078);
                        }
                        else {
                            if (changePoint.length == 0 || changePoint.findIndex(k => k.uuid == v.uuid) == -1) {
                                const { x, y, z } = v.position;
                                v.position.set(x + 40 * (i - 1), y, z + 40)
                                v.material.color = new global.value.THREE.Color(0x63e6be);
                                changePoint.push(v);
                            }
                        }
                    })
                }
            }
            if (changeIndex === true) {
                updateNodeList.value.splice(updateNodeList.value.length - 1, 1, currentMesh.object);
            } else {
                updateNodeList.value.splice(changeIndex, 1, currentMesh.object);
            }

            global.value.addOutlinePass([currentMesh.object], 0x0ca678);
            locationMesh = currentMesh.object;
        }
    }, el);
    global.value.clickRemove = removeEvent;
    function removeEvent() {
        clickRemove();
        clickRemove2();
    }
}
// 添加
function updateAddPoint(index) {
    if (!updateAstrict()) return;
    const newPoint = { originPoint: { x: null, y: null, z: null }, uuid: '' }
    if (index != null) {
        updateNodeList.value.splice(index + 1, 0, newPoint);
    } else {
        updateNodeList.value.push(newPoint);
    }
    changeIndex = true;
}
// 移除
function updateRemovePoint(index) {
    if (index == 0) {
        if (updateNodeList.value[0].originPoint.x == null) {
            ElMessage.warning('该项已被清空,请勿重复操作')
            return;
        }
        updateNodeList.value.splice(0, 1, { originPoint: { x: null, y: null, z: null }, uuid: '' });
    } else {
        updateNodeList.value.splice(index, 1);
    }
}

// 编辑
function updateEditPoint(index) {
    if (!updateAstrict()) return;
    updateNodeList.value.splice(index, 1, { originPoint: { x: null, y: null, z: null }, uuid: '' });
    changeIndex = index;
}
// 限制
function updateAstrict() {
    let flag = true;
    for (var i = 0, len = updateNodeList.value.length; i < len; i++) {
        const item = updateNodeList.value[i];
        if (!item.originPoint.x) {
            flag = false
            break;
        }
    }
    flag == false && ElMessage.warning('存在未填充的点');
    isFlag = flag;
    return flag;
}

// 删除节点关系
let tunID = null
let tunMesh = null
function addInteraction6(el) {
    const { clickRemove } = modelClick(lineMeshArr, camera, (currentMesh) => {
        tunMesh && (tunMesh.material = lineMat);
        const { mid } = currentMesh.object;
        tunID = mid;
        tunMesh = currentMesh.object;
        currentMesh.object.material = new THREE.LineBasicMaterial({ color: 0xff0000 });
    }, el)

    global.value.clickRemove = clickRemove;
    global.value.repealRemove = removeTunCancel;
}
async function removeTunSave() {
    if (tunID) {
        const { Status } = await ApiTunDel(tunID);
        if (Status == 0) {
            ElMessage.success('删除成功')
            closeImportNode();
            openImportNode();
            tunID = null
        }
    }
}



//
function removeTunCancel() {
    tunID = null;
    tunMesh && (tunMesh.material = lineMat);
    tunMesh = null;
}

// 导入节点
const tableData = ref([])
const tableOriginData = ref([])
const tableHeader = ref(['X', 'Y', 'Z']);




//
function getImportData(val) {
    let arr = val.map(v => { return { ...v, x: v.X, y: v.Y, z: v.Z, state: 1, Vertex: 1 } });
    // console.log(arr, 'arr');
    checkDouble(val, arr).then(v => {
        console.log(arr, v);
        // if (!tableData.value.every(v => v.state == 1)) ElMessage.error('存在重复点，请及时处理')
        ImportOnSubmit(arr)
    })
}

// 监测重复点的操作
function checkDouble(val, arr) {
    const obj = { NodeID: -1, NodeName: '', Num: 0, Vertex: 1, x: 0, y: 0, z: 0 }
    const params = val.map(({ X, Y, Z }) => { return { ...obj, x: X, y: Y, z: Z } })
    return params.length && ApiNodeCheckDouble(params).then(({ Result }) => {
        if (!Result || !Result.length) return [];
        Result.forEach((v) => {
            const { x, y, z, NodeID, Vertex } = v;
            const index = arr.findIndex(({ X, Y, Z }) => X == x && Y == y && Z == z);
            const lastIndex = arr.findLastIndex(({ X, Y, Z }) => X == x && Y == y && Z == z);
            console.log(index, 'index');


            if (index != -1) {
                arr.splice(index, 1, { ...v, X: x, Y: y, Z: z, state: 1 });
                index != lastIndex && lastIndex != -1 && arr.splice(lastIndex, 1, { ...v, X: x, Y: y, Z: z, state: 1 });
            }
        })
        return arr;
    })
}

function ImportOnSubmit(arr) {
    let uploadArr = arr;
    if (!uploadArr.every(v => v.state == 1)) return ElMessage.error('存在重复点，请及时处理')
    uploadArr.length && Promise.allSettled(uploadArr.map(async (item, i) => {
        if (item.NodeID) return { Status: 0, Result: item.NodeID };
        maxNodeID.value = ++maxNodeID.value;
        let NodeAddObj = {
            NodeID: maxNodeID.value, NodeName: '', Num: 0, Vertex: 1, X: 0, Y: 0, Z: 0,
            ...item
        }
        if (i == 0 || i == uploadArr.length - 1) NodeAddObj.Vertex = 0;
        const { Status, Result } = await ApiNodeAdd(NodeAddObj);
        return { Status, Result };
    })).then((res) => {
        const StatusList = res.map(v => v.value.Status);
        const NodeIDList = res.map(v => v.value.Result);
        if (StatusList.every(v => v == 0)) {
            uploadArr = uploadArr.map((v, i) => { return { ...v, NodeID: NodeIDList[i] } })
            console.log(uploadArr, ' uploadArr');
            submitTunAdd(uploadArr, ImportCancel);
        } else {
            ElMessage.warning('保存失败')
        }
    })
}

// 设置搜索范围 默认为5
const range = ref(5)
const intersectionPointList = ref([])
const intersectionIsUse = ref() // id array
// 重复点数组信息
const duplicatePointList = ref([])
const duplicateIsUse = ref()
function ImportCancel() {
    addOutlinePass([])
    addOutlinePass1([])
    tableData.value = [];
    tableData.value = []
    tableOriginData.value = []
    range.value = 5
    intersectionPointList.value = []
    intersectionIsUse.value = null // id array
    // 重复点数组信息
    duplicatePointList.value = []
    duplicateIsUse.value = null
}
function findIntersectionPoint(row) {
    let obj = {
        NodeID: 0, NodeName: '', Number: 0, Vertex: 1, StaticForce: 0, Temperature: 0, Humidity: 0, VaporForce: 0, Density: 0,
        SchemeID: 0, range: range.value, x: row.X, y: row.Y, z: row.Z
    }
    ApiNodeDistance(obj).then((v) => {
        intersectionPointList.value = v.Result.map(v => {
            let isUse = false;
            if (intersectionIsUse.value == v.NodeID) {
                isUse = true
            }
            return { ...v, isUse }
        });
    })
}
function changeSwitch(index, row, type = "intersection") {
    if (type == 'intersection') {
        if (intersectionIsUse.value) {
            const index = intersectionPointList.value.findIndex(v => v.NodeID == intersectionIsUse.value);
            index != -1 && intersectionPointList.value.splice(index, 1, { ...intersectionPointList.value[index], isUse: false })
            intersectionIsUse.value = null;
        }
    }
    else {
        if (duplicateIsUse.value) {
            const index = duplicatePointList.value.findIndex(v => v.NodeID == duplicateIsUse.value);
            index != -1 && duplicatePointList.value.splice(index, 1, { ...duplicatePointList.value[index], isUse: false })
            duplicateIsUse.value = null;
        }
    }
    if (row.isUse) {
        tableData.value.splice(index, 1, { ...row, X: row.x, Y: row.y, Z: row.z, state: 1 });
        if (type == 'intersection') {
            intersectionIsUse.value = row.NodeID
        } else {
            duplicateIsUse.value = row.NodeID
        }
    } else {
        tableData.value.splice(index, 1, tableOriginData.value[index]);
    }
}

function getCheckDouble(row) {
    checkDouble([row]).then(Result => {
        duplicatePointList.value = Result.map(v => {
            let isUse = false;
            if (duplicateIsUse.value == v.NodeID) {
                isUse = true
            }
            return { ...v, isUse }
        });
    })
}




//
/* 创建字体精灵 */
function makeTextSprite(THREE, message, parameters) {

    if (parameters === undefined) parameters = {};

    var fontface = parameters.hasOwnProperty("fontface") ?
        parameters["fontface"] : "Arial";

    /* 字体大小 */
    var fontsize = parameters.hasOwnProperty("fontsize") ?
        parameters["fontsize"] : 18;

    /* 边框厚度 */
    var borderThickness = parameters.hasOwnProperty("borderThickness") ?
        parameters["borderThickness"] : 1;

    /* 边框颜色 */
    var borderColor = parameters.hasOwnProperty("borderColor") ?
        parameters["borderColor"] : { r: 0, g: 0, b: 0, a: 1.0 };

    /* 背景颜色 */
    var backgroundColor = parameters.hasOwnProperty("backgroundColor") ?
        parameters["backgroundColor"] : { r: 255, g: 255, b: 255, a: 1.0 };

    /* 字体颜色*/
    var fontColor = parameters.hasOwnProperty('color') ?
        parameters["color"] : { r: 0, g: 0, b: 0, a: 1.0 };

    /* 创建画布 */
    var canvas = document.createElement('canvas');
    var context = canvas.getContext('2d');

    /* 字体加粗 */
    context.font = "Bold " + fontsize + "px " + fontface;

    /* 获取文字的大小数据，高度取决于文字的大小 */
    var metrics = context.measureText(message);
    var textWidth = metrics.width;
    var textHeight = metrics.height;

    /* 背景颜色 */
    context.fillStyle = "rgba(" + backgroundColor.r + "," + backgroundColor.g + ","
        + backgroundColor.b + "," + backgroundColor.a + ")";

    /* 边框的颜色 */
    context.strokeStyle = "rgba(" + borderColor.r + "," + borderColor.g + ","
        + borderColor.b + "," + borderColor.a + ")";
    context.lineWidth = borderThickness;

    /* 绘制圆角矩形 */
    // roundRect(context, borderThickness / 2, borderThickness / 2, textWidth + borderThickness, fontsize * 1.4 + borderThickness, 6);
    // roundRect(context, borderThickness / 2, borderThickness / 2, 20000, 20000, 6);
    drawCicle(context, textWidth + borderThickness, textWidth + borderThickness, (textWidth + borderThickness) * 5);
    /* 字体颜色 */
    context.fillStyle = "rgba(" + fontColor.r + "," + fontColor.g + ","
        + fontColor.b + "," + fontColor.a + ")";
    const x = (canvas.width - textWidth) / 2;
    const y = (canvas.height - textHeight) / 2 + textHeight;
    context.fillText(message, x, fontsize + borderThickness);

    /* 画布内容用于纹理贴图 */
    var texture = new THREE.Texture(canvas);
    texture.needsUpdate = true;

    var spriteMaterial = new THREE.SpriteMaterial({ map: texture });
    var sprite = new THREE.Sprite(spriteMaterial);


    /* 缩放比例 */

    return { sprite, texture };

}
/* 绘制圆角矩形 */
function roundRect(ctx, x, y, w, h, r) {

    ctx.beginPath();
    ctx.moveTo(x + r, y);
    ctx.lineTo(x + w - r, y);
    ctx.quadraticCurveTo(x + w, y, x + w, y + r);
    ctx.lineTo(x + w, y + h - r);
    ctx.quadraticCurveTo(x + w, y + h, x + w - r, y + h);
    ctx.lineTo(x + r, y + h);
    ctx.quadraticCurveTo(x, y + h, x, y + h - r);
    ctx.lineTo(x, y + r);
    ctx.quadraticCurveTo(x, y, x + r, y);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();

}

function drawCicle(ctx, x, y, radius = 50) {
    // 绘制圆形
    ctx.beginPath();
    ctx.arc(x, y, radius, 0, 2 * Math.PI);
    ctx.fill(); // 填充圆形
    ctx.closePath();
}

</script>

<style lang="scss">
@use '../assets/index.scss';
</style>

<style lang="scss" scoped>
:deep(.el-button--small) {
    width: 100rem;
    height: 30rem;
    background-image: none;
    background-size: 100%;
}

:deep(.el-form-item__label) {
    color: #fff;
}

:deep(.el-dialog__header) {
    margin-left: 50px !important;
    position: relative;
}

:deep(.el-dialog__title) {
    position: absolute;
    top: 45px
}

:deep(.el-text) {
    color: #fff;
    font-size: 25px;
    margin: auto;
}

:deep(.el-input.is-disabled .el-input__inner) {
    -webkit-text-fill-color: #2962ff;
    cursor: not-allowed;
}

:deep(.el-radio.is-bordered.el-radio--large .el-radio__label) {
    color: #eee;
}

// 悬浮框
:deep(.el-alert--success.is-dark) {
    background-color: #1864ab;
}
</style>