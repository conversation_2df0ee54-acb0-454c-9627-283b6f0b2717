<template>
    <div class="fixed bottom-200px right-60px toolBar_box">
        <div :ref="`dom${i + 1}`" v-for="(v, i) in toolList" :key="v" class="my-20px p-6px cursor-pointer ">
            <div>
                <el-tooltip :content="v.content" placement="left" effect="customized">
                    <img :src="v.icon" class="w-30px h-30px cursor-pointer" @click="handleClick(i)">
                </el-tooltip>
            </div>
            <!-- <div v-show="i == 1">
                <el-popover popper-class="toolbar_popover" placement="left" title="图层工具箱" :width="200" trigger="click">
                    <template #default>
                        <div class="flex justify-between flex-wrap">
                            <div class="data_box_tab cursor-pointer" @click="cameraAngle('up')"><span>俯视</span></div>
                            <div class="data_box_tab cursor-pointer" @click="cameraAngle('front')"><span>正视</span></div>
                            <div class="data_box_tab cursor-pointer" @click="cameraAngle('side')"><span>侧视</span></div>
                            <div class="data_box_tab cursor-pointer w-150px" @click="isShowTunLabel(0)">
                                <span>{{ isShowLabel1 ? '隐藏巷道标签' : '显示巷道标签' }}</span>
                            </div>
                            <div class="data_box_tab cursor-pointer w-150px" @click="isShowTunLabel(1)">
                                <span>{{ isShowLabel2 ? '隐藏设施标签' : '显示设施标签' }}</span>
                            </div>
                        </div>
                    </template>
<template #reference>
                        <div v-show="i == 1">
                            <el-tooltip :content="v.content" placement="left" effect="customized">
                                <img :src="v.icon" class="w-61px h61px cursor-pointer">
                            </el-tooltip>
                        </div>
                    </template>
</el-popover>
</div> -->
        </div>
    </div>
</template>
<script setup>
import { ref, onMounted, inject } from 'vue';
import { useRouter } from 'vue-router';
import { showOrHideLabels, } from '@/views/thereVentilation/js/tunInit';// 变量
const router = useRouter();
const resetEffect = inject('resetEffect');
const props = defineProps(['cameraAngle', 'resetEffect'])

let isShowLabel1 = ref(true);
let isShowLabel2 = ref(true);

function isShowTunLabel(type) {
    if (type == 0) {
        isShowLabel1.value = !isShowLabel1.value;
        showOrHideLabels(isShowLabel1.value, type)
    } else {
        isShowLabel2.value = !isShowLabel2.value;
        showOrHideLabels(isShowLabel2.value, type)
    }

}
const toolList = ref(
    [
        {
            icon: 'fullScreen.svg',
            content: '全屏展示'
        },
        {
            icon: 'lookDown.svg',
            content: '俯视'
        },
        {
            icon: 'look.svg',
            content: '正视'
        },
        {
            icon: 'sideLook.svg',
            content: '侧视'
        },
        {
            icon: 'reset.svg',
            content: '重置相机'
        },
        
    ]
)
toolList.value = toolList.value.map(item => ({
    ...item,
    icon: new URL(`../../assets/svg/${item.icon}`, import.meta.url),
}));
const emit = defineEmits(['getZnzDom'])
// 指南针dom
const dom1 = ref()
onMounted(() => {
    emit('getZnzDom', dom1.value[0])
})
let isFullScreen = ref(false)
const handleClick = (i) => {
    switch (i) {
        case 0:
            isFullScreen.value ? exitFullscreen() : enterFullscreen();
            break;
        case 1: cameraAngle('up'); break;
        case 2: cameraAngle('front'); break;
        case 3: cameraAngle('side'); break;
        case 4: resetEffect ? resetEffect() : props.resetEffect(); break;
        // case 3: router.push('/tunEdit'); break;
        case 5: break;
        case 6: break;
    }
    // 进入全屏模式
    function enterFullscreen() {
        var element = document.documentElement;
        if (element.requestFullscreen) {
            element.requestFullscreen();
        } else if (element.mozRequestFullScreen) { // Firefox
            element.mozRequestFullScreen();
        } else if (element.webkitRequestFullscreen) { // Chrome, Safari and Opera
            element.webkitRequestFullscreen();
        } else if (element.msRequestFullscreen) { // IE/Edge
            element.msRequestFullscreen();
        }
        isFullScreen.value = true
    }

    // 退出全屏模式
    function exitFullscreen() {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.mozCancelFullScreen) { // Firefox
            document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) { // Chrome, Safari and Opera
            document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) { // IE/Edge
            document.msExitFullscreen();
        }
        isFullScreen.value = false
    }

}



</script>
<style lang="scss" scoped>
.data_box_tab_active {
    text-align: center;
    min-width: 100px;
    height: 40px;
    background-image: url('../../assets/img/btn_bg.png');
    background-size: 100% 40px;
    margin-right: 2px;
    margin-top: 5px;

    span {
        width: 63px;
        height: 40px;
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #0BDFF2;
        line-height: 40px;

    }
}


.data_box_tab {
    @extend .data_box_tab_active;

    span {
        color: #D0F2F7;
    }
}

.norem-btn1 {
    width: auto;

    .norem-btn1-1 {
        background-color: rgba(255, 255, 255, 0.8);
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #fff;
        text-align: center;
        line-height: 20px;


        &:hover {
            background-color: #0BDFF2;
        }
    }
}
</style>
<style lang="scss">
@use '../../assets/index.scss';

.toolbar_popover {
    background: none !important;
    background-image: url('@/assets/png/nav_select.png') !important;
    background-size: 100% 300px !important;
    border-radius: 0;
    border: none !important;
    color: white;
    padding: 5px;

    .el-popover__title {
        color: #76F7FF;
    }

    &.is-light .el-popper__arrow::before {
        background: #fff;
        border: 1px solid #fff;
    }

    &.is-light {
        padding: 20px;
        height: 300px;
        width: 250px !important;
    }
}
</style>