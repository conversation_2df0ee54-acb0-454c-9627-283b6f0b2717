<template>
    <div @mouseleave="() => enterFormDiv = false" @mouseover="() => enterFormDiv = true"
        class="w-100% h-100% relative p-10px bg-[#191919] text-white overflow-hidden">
        <!-- 点位搜索 -->
        <div>
            <div relative py-10px>
                <div v-if="route.name == 'ventilationCalculation'" class=" gutter-10px flex  mt-5px ">
                    <div class=" h-25px w-120px line-height-25px text-left text-#ffc107">
                        方案列表：
                    </div>
                    <div class="ml-[-40px] mr-10px">
                        <el-select style="width: 180rem;" v-model="schemeId" clearable collapse-tags
                            placeholder="选择你要加载的方案" popper-class="custom-header">
                            <el-option v-for="item in schemeList" :key="item.value" :label="item.SchemeName"
                                :value="item.ID" />
                        </el-select>
                    </div>
                    <div class="flex-1 mr-10px">
                        <el-button style="width: 100%;" type="primary" size="small"
                            @click="openDialog(1)">新增</el-button>
                    </div>
                    <div class="flex-1 mr-10px">
                        <el-button style="width: 100%;" type="warning" size="small"
                            @click="openDialog(2)">更新</el-button>
                    </div>
                    <div class="flex-1 ">
                        <el-button style="width: 100%;" type="danger" size="small" @click="openDialog(3)">删除</el-button>
                    </div>


                    <el-dialog v-model="visible1" style="width: 850rem;height: 550rem;">
                        <div class="absolute top-50px left-20px">
                            {{ currentScheme == 1 ? '新增方案' : (currentScheme == 2 ? '更新方案' : '删除方案') }}
                        </div>
                        <div class="  flex my-70px items-center text-[#fff]">
                            <span class="w-120px">方案名称：</span>
                            <el-input v-model="form.SchemeName" size="normal" clearable></el-input>
                        </div>
                        <div class="absolute right-50px bottom-30px">
                            <el-button size="small" @click="handleSchemeCancel()">
                                取消
                            </el-button>
                            <el-button size="small" type="primary" @click="handleScheme()">
                                保存
                            </el-button>
                        </div>
                    </el-dialog>
                </div>
                <div class="flex mt-20px items-center">
                    <div class=" h-25px w-80px line-height-25px text-#ffc107">
                        点位搜索：
                    </div>
                    <div class="flex items-center flex-1 ">
                        <el-select v-model="searchPoint" filterable placeholder="搜索历史点">
                            <el-option v-for="item in needNodeList" :key="item.NodeID"
                                :label="`节点:${item.NodeID}${item.NodeName && item.NodeName != 'null' ? `--名称:${item.NodeName}` : ''}`"
                                :value="item.NodeID" />
                        </el-select>
                    </div>

                    <!-- 筛选框 -->
                    <div class="flex items-center flex-1">
                        <span class="w-50px mx-5px text-#ffc107">类型:</span>
                        <el-select style="flex: auto;" v-model="radioCurrent" placeholder="请做出筛选：" filterable>
                            <el-option value="3" label="全部"></el-option>
                            <el-option value="1" label="孤岛点"></el-option>
                            <el-option value="2" label="联络点"></el-option>
                        </el-select>
                    </div>

                </div>
                <div class="flex mt-20px items-center">
                    <div class=" h-25px w-80px line-height-25px text-#ffc107">
                        巷道搜索:
                    </div>
                    <div class="flex  flex-1">
                        <el-select style="width:100%" v-model="searchTun" filterable placeholder="搜索巷道名称">
                            <el-option v-for="item in apiTunList" :key="item.TunID" :label="item.TunName ?? '-'"
                                :value="item.TunID" />
                        </el-select>
                    </div>
                </div>
                <!-- <div class="flex mt-20px items-center">
                    <el-button @click="structuralQuery">结构查询</el-button>
                    <StructuredQuery v-model="resultBoxShow" :handleStructuralQuery="handleStructuralQuery">
                    </StructuredQuery>
                </div> -->
            </div>
        </div>
        <div class="mt-10px w-full h-1px  bg-#ffc107 ">
        </div>
        <div class=" mt-10px">
            <div class="flex overflow-hidden  h-50px">
                <div v-for="(v, i) in svgList"
                    class="flex items-center justify-center  cursor-pointer hover:border-solid hover:border-1px hover:border-sky-500/80"
                    :class="svgCurrentIndex == i ? 'bg-sky-500/30' : ''"
                    @click="i != 0 && i != 1 && i != 2 ? svgCurrentIndex = i : ''"
                    v-show="interactionMode == 1 || interactionMode == 2">
                    <div class="w-full p-10px h-full w-47px h-47px" v-if="i == 0 && route.name == 'tunEdit'">
                        <UploadExcel v-show="restoreRecord.length == 0" class="w-100%" @result="getImportData">
                            <template #icon>
                                <el-tooltip :content="v.content" placement="top" effect="customized">
                                    <img :src="v.icon" class="w-25px h-25px">
                                </el-tooltip>
                            </template>
                        </UploadExcel>
                        <div v-show="restoreRecord.length >= 1" @click="nodeImportRestrict">
                            <el-tooltip :content="v.content" placement="top" effect="customized">
                                <img :src="v.icon" class="w-25px h-25px">
                            </el-tooltip>
                        </div>
                    </div>
                    <div class="w-full p-10px h-full w-47px h-47px" v-else-if="i == 1" @click="save">
                        <el-tooltip :content="v.content" placement="top" effect="customized">
                            <img :src="v.icon" class="w-25px h-25px">
                        </el-tooltip>
                    </div>
                    <div class="w-full p-10px h-full w-47px h-47px" v-else-if="i == 2" @click="repeal">
                        <el-tooltip :content="v.content" placement="top" effect="customized">
                            <img :src="v.icon" class="w-25px h-25px">
                        </el-tooltip>
                    </div>
                    <div v-else-if="i > 2 && (interactionMode == 1 ? i < 6 : interactionMode == 2 ? i >= 6 : false)"
                        @click="changeMode(v)">
                        <div class="w-full p-10px h-full w-47px h-47px ">
                            <el-tooltip :content="v.content" placement="top" effect="customized">
                                <img :src="v.icon" class="w-25px h-25px">
                            </el-tooltip>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="h-730px mt--40px custom-form">
            <div v-show="store.$state.operationMode == 0"
                class="mt-20px h-650rem flex justify-center items-center font-bold  text-20px text-#888888">
                当前处于漫游模式
            </div>
            <!-- 新增节点 -->
            <div v-show="store.$state.operationMode == 1" class="mt-20px">
                <el-form label-width="auto" label-position="left">
                    <div border="2px solid transparent" class="h-650rem relative rounded-20px pt-30px overflow-hidden">
                        <!-- <div absolute class="w-full flex justify-center top--12.5px  translate-x-[-30px]">
                        <div class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                            新增节点
                        </div>
                    </div> -->
                        <el-scrollbar height="500rem" class="flex justify-center ">
                            <div v-for="(item, index) in pointList" :key="index"
                                class=" pb-9px border-b-2 border-solid border-sky-500/40">
                                <div class="flex items-center">
                                    <div flex class="items-center">
                                        <span class="text-17px w-70px text-right">名称：</span>
                                        <el-input style="width: 245rem;" v-model.trim="item.name" />
                                    </div>
                                    <div v-show="pointList.length != 1">
                                        <el-icon size="32rem" color="#ea2465"
                                            class="mt-8px p-5px cursor-pointer hover:border-solid hover:border-1px hover:border-sky-500/80"
                                            @click="removePoint(index)">
                                            <Remove />
                                        </el-icon>
                                    </div>
                                    <div v-show="index == pointList.length - 1">
                                        <el-icon size="32rem" color="#40c057"
                                            class="mt-8px  p-5px cursor-pointer hover:border-solid hover:border-1px hover:border-sky-500/80"
                                            @click="addPoint">
                                            <CirclePlus />
                                        </el-icon>
                                    </div>
                                </div>


                                <el-space alignment="center" class="my-10px">
                                    <div flex class="items-center"><span
                                            class=" w-70px text-16px text-right">X：</span><el-input
                                            style="width: 120rem;" v-model="item.x" />
                                    </div>
                                    <div flex class="items-center"><span class="text-16px" w-30px>Y：</span><el-input
                                            v-model="item.y" />
                                    </div>
                                    <div flex class="items-center"><span class="text-16px" w-30px>Z：</span><el-input
                                            v-model="item.z" />
                                    </div>
                                </el-space>

                            </div>
                        </el-scrollbar>
                        <div class="w-92% mt-30px fixed right-10px  bottom-10px  flex justify-end">
                            <el-button @click="addCancel">取消</el-button>
                            <el-button type="primary" @click="onSubmit">确认</el-button>
                        </div>
                    </div>
                </el-form>

                <!-- 新增节点列表 -->
                <!-- <div border="2px solid transparent" relative rounded-20px mt-15px  pt-30px>
                <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                    <div class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                        新增节点列表
                    </div>
                </div>
                <el-scrollbar height="200rem">
                    <div class="ml-40px my-5px" v-for="(item, index) in newPointList" :key="item.NodeID">
                        <span>节点名称：</span><span>{{ item.originPoint.NodeID }}</span>&nbsp;
                        <el-button type=" danger" size="default"
                            @click="emitCameraFlyToPoint(item.originPoint.NodeID)">定位</el-button>
                    </div>
                </el-scrollbar>

            </div> -->

            </div>
            <!-- 更新节点 -->
            <div v-show="store.$state.operationMode == 2">
                <div border="2px solid transparent" h-650rem relative rounded-20px mt-25px pt-30px>
                    <!-- <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                    <div class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                        更新节点
                    </div>
                </div> -->
                    <el-form label-width="auto" label-position="left">
                        <el-form-item label="节点标识：">
                            <el-input disabled v-model="updateNodeInfo.NodeID" size="normal"></el-input>
                        </el-form-item>
                        <el-form-item label="节点名称：">
                            <el-input v-model="updateNodeInfo.NodeName" placeholder="请输入修改名称" size="normal"></el-input>
                        </el-form-item>
                        <el-form-item label="节点坐标：">
                            <el-space>
                                <el-input v-model="updateNodeInfo.x" size="normal"></el-input>
                                <el-input v-model="updateNodeInfo.y" size="normal"></el-input>
                                <el-input v-model="updateNodeInfo.z" size="normal"></el-input>
                            </el-space>
                        </el-form-item>
                    </el-form>
                    <div class="w-92% mt-30px fixed right-10px  bottom-10px  flex justify-end">
                        <el-button @click="updateCancel">取消</el-button>
                        <el-button type="primary" @click="updateSave">确认</el-button>
                    </div>
                </div>
            </div>
            <!-- 删除节点 -->
            <div v-show="store.$state.operationMode == 3">
                <div border="2px solid transparent" h-650rem relative rounded-20px mt-25px pt-30px>
                    <!-- <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                    <div class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                        删除节点
                    </div>
                </div> -->
                    <el-form :model="updateNodeInfo" label-width="auto" label-position="left">
                        <el-form-item label="节点标识：">
                            <el-input :value="updateNodeInfo.NodeID" disabled />
                        </el-form-item>
                        <el-form-item label="节点名称：">
                            <el-input :value="updateNodeInfo.NodeName" disabled />
                        </el-form-item>
                        <el-form-item label="节点状态：">
                            <el-input
                                :value="updateNodeInfo.Vertex != null ? (updateNodeInfo.Vertex == 0 ? '起始点' : '中间点') : ''"
                                disabled />
                        </el-form-item>
                    </el-form>
                    <div class="w-92% mt-30px fixed right-10px  bottom-10px  flex justify-end">
                        <el-button @click="removeCancel">取消</el-button>
                        <el-button type="primary" @click="removeSave">确认</el-button>
                    </div>
                </div>
            </div>
            <!-- 设置节点关系 -->
            <div v-show="store.$state.operationMode == 4">
                <div class="mt-50rem bg-[#000000] w-135rem">
                    <el-tooltip content="添加巷道" placement="top" effect="customized">
                        <el-icon size="45rem"
                            class=" p-10px cursor-pointer hover:border-solid hover:border-1px hover:border-sky-500/80"
                            :class="setNodeMenu == 0 ? 'bg-sky-500/30' : ''" @click="setNodeMenu = 0">
                            <svg t="1742195759279" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                xmlns="http://www.w3.org/2000/svg" p-id="4646" width="200" height="200">
                                <path
                                    d="M515.095273 2.257455C313.320727 2.257455 146.152727 134.749091 90.065455 317.277091v702.208h845.684363V317.277091h0.302546C880.756364 134.749091 715.962182 2.257455 516.561455 2.257455h-1.466182zM234.402909 952.832v2.117818H164.770909V457.797818l75.706182 102.632727c-2.210909 5.818182-4.235636 11.752727-6.097455 17.757091v374.644364zM164.770909 317.253818h1.070546c51.386182-150.807273 184.971636-255.069091 347.810909-255.069091h1.442909c161.28 0 294.912 105.239273 345.669818 255.069091h0.279273v29.021091l-93.137455 125.765818c-49.733818-88.715636-142.941091-148.456727-251.764364-148.456727h-0.977454c-110.661818 0-205.544727 60.346182-255.813818 149.806545l-94.580364-128.232727v-27.880727z m142.242909 635.578182V531.642182h0.698182c33.954909-99.607273 98.932364-145.175273 206.498909-145.175273h0.954182c106.589091 0 171.566545 46.219636 205.102545 145.175273l0.186182 421.189818H307.013818z m486.050909 0V578.187636h0.209455c-1.954909-6.446545-4.119273-12.8-6.493091-19.037091l74.24-100.282181v496.081454h-67.956364v-2.117818z"
                                    fill="#00ffff" p-id="4647"></path>
                            </svg>
                        </el-icon>
                    </el-tooltip>
                    <el-tooltip content="添加分支" placement="top" effect="customized">
                        <el-icon size="45rem"
                            class=" p-10px cursor-pointer hover:border-solid hover:border-1px hover:border-sky-500/80"
                            :class="setNodeMenu == 1 ? 'bg-sky-500/30' : ''" @click="setNodeMenu = 1">
                            <svg t="1741941164843" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                xmlns="http://www.w3.org/2000/svg" p-id="3119" width="200" height="200">
                                <path
                                    d="M303.146667 648.96A128.042667 128.042667 0 1 1 213.333333 647.253333V376.746667a128.042667 128.042667 0 1 1 85.333334 0V512c35.669333-26.794667 79.957333-42.666667 128-42.666667h170.666666a128.042667 128.042667 0 0 0 123.52-94.293333 128.042667 128.042667 0 1 1 86.698667 2.730667A213.418667 213.418667 0 0 1 597.333333 554.666667h-170.666666a128.042667 128.042667 0 0 0-123.52 94.293333zM256 725.333333a42.666667 42.666667 0 1 0 0 85.333334 42.666667 42.666667 0 0 0 0-85.333334zM256 213.333333a42.666667 42.666667 0 1 0 0 85.333334 42.666667 42.666667 0 0 0 0-85.333334z m512 0a42.666667 42.666667 0 1 0 0 85.333334 42.666667 42.666667 0 0 0 0-85.333334z"
                                    fill="#00ffff" p-id="3120"></path>
                            </svg>
                        </el-icon>
                    </el-tooltip>
                    <el-tooltip content="局部通风机" placement="top" effect="customized">
                        <el-icon size="45rem"
                            class=" p-10px cursor-pointer hover:border-solid hover:border-1px hover:border-sky-500/80"
                            :class="setNodeMenu == 2 ? 'bg-sky-500/30' : ''" @click="setNodeMenu = 2">
                            <svg t="1742195439884" class="icon" viewBox="0 0 1158 1024" version="1.1"
                                xmlns="http://www.w3.org/2000/svg" p-id="3508" width="200" height="200">
                                <path d="M856.14743 23.257679h279.257678v977.484642h-279.257678z" p-id="3509"
                                    fill="#00ffff">
                                </path>
                                <path
                                    d="M-0.082768 0h1158.745555v46.515357h-1158.745555z m0 977.484643h1158.745555v46.515357h-1158.745555z"
                                    p-id="3510" fill="#00ffff"></path>
                                <path
                                    d="M832.889751 0h46.515357v1024h-46.515357z m279.257679 0h46.515357v1024h-46.515357z"
                                    p-id="3511" fill="#00ffff"></path>
                                <path
                                    d="M512 418.886518v-186.226964l279.257679 186.226964v186.226964l-279.257679 186.226964v-186.226964H-0.082768v-186.226964h512z"
                                    p-id="3512" fill="#00ffff"></path>
                            </svg>
                        </el-icon>
                    </el-tooltip>
                </div>
                <el-form class=" mt--40px" label-width="90rem" label-position="left">
                    <div v-show="setNodeMenu == 0" border="2px solid transparent" h-650rem relative rounded-20px mt-25px
                        pt-30px>
                        <el-form-item label="巷道名称:" prop="TunName">
                            <el-input v-model="tunOther.TunName" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="用风类型:" prop="Ventflow">
                            <el-select v-model="tunOther.Ventflow">
                                <el-option :value="1" label="进风"></el-option>
                                <el-option :value="-1" label="回风"></el-option>
                                <el-option :value="0" label="用风"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item prop="Width">
                            <div class="w-90rem absolute ml-[-90rem] ">
                                宽&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;度:
                            </div>
                            <el-input class="ml--15px " style="width: 100%;flex:1" v-model="tunOther.Width"
                                clearable></el-input>
                        </el-form-item>

                        <el-tooltip content="添加点" placement="top" effect="customized">
                            <el-icon size="32rem" color="#66bb6a"
                                class="p-5px ml-70px  cursor-pointer hover:border-solid hover:border-1px hover:border-sky-500/80 "
                                @click="addMiddlePoint()">
                                <CirclePlus />
                            </el-icon>
                        </el-tooltip>

                        <el-scrollbar height="350rem" v-show="setNodeListShow && setNodeMenu == 0">
                            <el-form-item class=" py-9px border-b-2 border-solid border-sky-500/40" v-for="(item, index)
                                in nodeList" :label="'坐标' + (index + 1) + ':'" :key="index">
                                <div class="w-80% pr-40px flex justify-between">
                                    <div class="text-18px">ID:&nbsp;<span class="text-#52c41a text-15px">
                                            {{ item.originPoint.NodeID ?? '-' }}</span></div>
                                    <el-space alignment="center">
                                        <el-icon size="32rem" color="#ea2465"
                                            class="p-5px mx-5px  cursor-pointer hover:border-solid hover:border-1px hover:border-sky-500/80"
                                            @click="removeMiddlePoint(index)">
                                            <Remove />
                                        </el-icon>
                                    </el-space>
                                </div>
                                <div class="my-10px w-80% flex justify-between">
                                    <div class="text-18px">X:&nbsp;<span class="text-#52c41a text-15px">
                                            {{ item.originPoint.x ? item.originPoint?.x.toFixed(2) : '-' }}</span>
                                    </div>
                                    <div class="text-18px">Y:&nbsp;<span class="text-#52c41a text-15px">
                                            {{ item.originPoint.y ? item.originPoint?.y.toFixed(2) : '-' }}</span>
                                    </div>
                                    <div class="text-18px">Z:&nbsp;<span class="text-#52c41a text-15px">
                                            {{ item.originPoint.z ? item.originPoint?.z.toFixed(2) : '-' }}</span>
                                    </div>
                                </div>
                                <el-col :span="2">
                                </el-col>

                            </el-form-item>
                        </el-scrollbar>

                        <div class="w-92% mt-30px fixed right-10px  bottom-10px  flex justify-end">
                            <el-button @click="updateTunCancel(), tunCancel()">取消</el-button>
                            <el-button type="primary" @click="onSubmitSet">确认</el-button>
                        </div>
                    </div>
                    <div v-show="setNodeMenu == 1" border="2px solid transparent" h-650rem relative rounded-20px mt-25px
                        pt-30px>
                        <el-form-item :required="true" label="巷道名称:">
                            <el-input v-model="tunOther.TunName" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="分支类型:" :required="true">
                            <el-select placeholder="" v-model="branchType">
                                <el-option :value="1" label="风机分支"></el-option>
                                <el-option :value="2" label="漏风分支"></el-option>
                                <el-option :value="3" label="平衡分支"></el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item label="选择一点:" :required="true">
                            <div class="text-18px flex w-full">ID:&nbsp;
                                <el-input style="width: 250rem;" v-model="updateNodeInfo.NodeID" disabled
                                    :placeholder="!branchType ? '巷道名称和分支类型必须先填' : '请拾取一个点'" size="normal"
                                    clearable></el-input>
                            </div>
                        </el-form-item>
                        <div class="w-92% mt-30px fixed right-10px  bottom-10px  flex justify-end">
                            <el-button @click="cancelSubmitBranch">取消</el-button>
                            <el-button type="primary" @click="submitBranch">确认</el-button>
                        </div>
                    </div>
                    <div v-show="setNodeMenu == 2" border="2px solid transparent" h-650rem relative rounded-20px mt-25px
                        pt-30px>
                        <el-form-item :required="true" label="巷道名称:">
                            <el-input v-model="tunOther.TunName" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="用风类型:" :required="true">
                            <el-select v-model="updateTunOther.Ventflow">
                                <el-option :value="1" label="进风"></el-option>
                                <el-option :value="0" label="用风"></el-option>
                                <el-option :value="-1" label="回风"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item :required="true" label="分支类型:">
                            <el-input style="width: 100%;" disabled value="掘进工作面"></el-input>
                        </el-form-item>
                        <el-tooltip content="添加点" placement="top" effect="customized">
                            <el-icon size="32rem" color="#66bb6a"
                                class="p-5px ml-70px  cursor-pointer hover:border-solid hover:border-1px hover:border-sky-500/80 "
                                @click="addMiddlePoint()">
                                <CirclePlus />
                            </el-icon>
                        </el-tooltip>
                        <el-scrollbar height="350rem" v-show="setNodeListShow && setNodeMenu == 2">
                            <el-form-item class=" py-9px border-b-2 border-solid border-sky-500/40" v-for="(item, index)
                                in nodeList" :label="'坐标' + (index + 1) + ':'" :key="index">
                                <div class="w-80% pr-40px flex justify-between">
                                    <div class="text-18px">ID:&nbsp;<span class="text-#52c41a text-15px">
                                            {{ item.originPoint.NodeID ?? '-' }}</span></div>
                                    <el-space alignment="center">
                                        <el-icon size="32rem" color="#ea2465"
                                            class="p-5px mx-5px  cursor-pointer hover:border-solid hover:border-1px hover:border-sky-500/80"
                                            @click="removeMiddlePoint(index)">
                                            <Remove />
                                        </el-icon>
                                    </el-space>
                                </div>
                                <div class="my-10px w-80% flex justify-between">
                                    <div class="text-18px">X:&nbsp;<span class="text-#52c41a text-15px">
                                            {{ item.originPoint.x ? item.originPoint?.x.toFixed(2) : '-' }}</span>
                                    </div>
                                    <div class="text-18px">Y:&nbsp;<span class="text-#52c41a text-15px">
                                            {{ item.originPoint.y ? item.originPoint?.y.toFixed(2) : '-' }}</span>
                                    </div>
                                    <div class="text-18px">Z:&nbsp;<span class="text-#52c41a text-15px">
                                            {{ item.originPoint.z ? item.originPoint?.z.toFixed(2) : '-' }}</span>
                                    </div>
                                </div>
                                <el-col :span="2">
                                </el-col>

                            </el-form-item>
                        </el-scrollbar>
                        <div class="w-92% mt-30px fixed right-10px  bottom-10px  flex justify-end">
                            <el-button @click="updateTunCancel(), tunCancel()">取消</el-button>
                            <el-button type="primary" @click="onSubmitSet">确认</el-button>
                        </div>
                    </div>
                </el-form>
            </div>
            <!-- 更新节点关系 -->
            <div v-show="store.$state.operationMode == 5">
                <el-form class="" :model="form" label-width="90rem" label-position="left">
                    <div border="2px solid transparent" h-650rem relative rounded-20px mt-25px pt-30px>
                        <!-- <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                        <div class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                            更新巷道
                        </div>
                    </div> -->
                        <el-form-item label="巷道名称：" prop="TunName">
                            <el-input v-model="updateTunOther.TunName" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="用风类型：" prop="Ventflow">
                            <div class="flex w-full">
                                <div class="w-150px">
                                    <el-select v-model="updateTunOther.Ventflow">
                                        <el-option :value="1" label="进风"></el-option>
                                        <el-option :value="0" label="用风"></el-option>
                                        <el-option :value="-1" label="回风"></el-option>
                                    </el-select>
                                </div>
                                <div class="flex-1 flex justify-around">
                                    <div class="w-130px ml-15px">用途类型：</div>
                                    <el-select v-model="updateTunOther.Lx">
                                        <el-option v-show="filterUsageTypeList.includes(item.TypeName)"
                                            v-for="item in selectDataList.usageTypeList" :key="item.value"
                                            :label="item.TypeName" :value="item.TypeID"></el-option>
                                    </el-select>
                                </div>
                            </div>
                        </el-form-item>
                        <el-form-item label="位置：" prop="Place">
                            <div class="flex w-full">
                                <div class="w-150px">
                                    <el-input style="width:100%" v-model="updateTunOther.Place" clearable></el-input>
                                </div>
                                <div class="flex-1 flex justify-around">
                                    <div class="w-130px ml-15px">是否解算：</div>
                                    <el-select v-model="updateTunOther.NetLine">
                                        <el-option :value="true" label="是"></el-option>
                                        <el-option :value="false" label="否"></el-option>
                                    </el-select>
                                </div>
                            </div>
                        </el-form-item>
                        <el-form-item label="断面形状：" prop="Place">
                            <div class="flex w-full">
                                <div class="w-150px">
                                    <el-select style="width:100%" v-model="updateTunOther.ShapeType">
                                        <el-option v-for="item in selectDataList.shapeTypeList" :key="item.value"
                                            :label="item.TypeName" :value="item.TypeID"></el-option>
                                    </el-select>
                                </div>

                                <div class="flex-1 flex justify-around">
                                    <div class="w-130px ml-15px">支护类型：</div>
                                    <el-select v-model="updateTunOther.ShoringType">
                                        <el-option v-for="item in selectDataList.supportTypeList" :key="item.value"
                                            :label="item.TypeName" :value="item.TypeID"></el-option>
                                    </el-select>
                                </div>
                            </div>
                        </el-form-item>
                        <el-form-item label="面积：" prop="S">
                            <div class="flex w-full">
                                <div class="w-150px">
                                    <el-input style="width:100%" v-model="updateTunOther.S" clearable></el-input>
                                </div>

                                <div class="flex-1 flex justify-around">
                                    <div class="w-130px ml-15px">需风量：</div>
                                    <el-input style="width:100%" v-model="updateTunOther.QDemand" clearable></el-input>
                                </div>
                            </div>
                        </el-form-item>
                        <el-form-item label="摩擦系数：" prop="S">
                            <div class="flex w-full">
                                <div class="w-150px">
                                    <el-input style="width:100%" v-model="updateTunOther.A" clearable></el-input>
                                </div>
                                <div class="flex-1 flex justify-around">
                                    <div class="w-130px ml-15px">风阻：</div>
                                    <el-input style="width:100%" v-model="updateTunOther.R" clearable></el-input>
                                </div>
                            </div>
                        </el-form-item>
                        <el-form-item label="标签层级：" prop="Width">
                            <div class="flex w-full">
                                <div class="w-150px">
                                    <el-select style="width:100%" v-model="updateTunOther.Level">
                                        <el-option v-for="item in 9" :key="item" :label="item" :value="item">
                                        </el-option>
                                    </el-select>
                                </div>
                                <div class="flex-1 flex justify-around">
                                    <div class="w-130px ml-15px">长度：</div>
                                    <el-input disabled style="width:100%" v-model="updateTunOther.Length"
                                        clearable></el-input>
                                </div>
                            </div>

                        </el-form-item>
                        <el-form-item label="风量：" prop="S">
                            <div class="flex w-full">
                                <div class="w-150px">
                                    <el-input disabled style="width:100%" :value="formatFixed(updateTunOther.QCalculate)"  clearable></el-input>
                                </div>
                                <div class="flex-1 flex justify-around">
                                    <div class="w-130px ml-15px">风速：</div>
                                    <el-input disabled style="width:100%" :value="formatFixed(updateTunOther.V)" clearable></el-input>
                                </div>
                            </div>
                        </el-form-item>
                        <el-form-item label="风流反向：">
                            <el-radio-group v-model="isReverse" @click="updateNodeList.reverse()">
                                <el-radio :value="true" size="large">正向</el-radio>
                                <el-radio :value="false" size="large">反向</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-scrollbar height="340rem">
                            <el-form-item @click="changeUpdateNodeList(index)"
                                :class="changeIndex == index ? 'bg-sky-500/10' : ''"
                                class=" hover:bg-sky-500/10 cursor-pointer   border-b-2 border-solid border-sky-500/40"
                                v-for="(item, index) in
                                    updateNodeList" :key="index">
                                <div class="w-90rem absolute ml-[-90rem] ">
                                    坐&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;标{{ + (index + 1) + ':' }}
                                </div>
                                <div class=" w-100% pr-40px flex justify-between">
                                    <div class="text-15px">ID:&nbsp;<span class="text-#52c41a text-15px">
                                            {{ item.originPoint.NodeID ?? '-' }}</span></div>
                                    <div></div>
                                    <el-space alignment="center">
                                        <el-icon size="32rem" color="#66bb6a"
                                            class="p-5px cursor-pointer cursor-pointer hover:border-solid hover:border-1px hover:border-sky-500/80 "
                                            @click="updateAddPoint(index)">
                                            <CirclePlus />
                                        </el-icon>
                                        <el-icon size="32rem" color="#ea2465"
                                            class="mt-3px  p-5px cursor-pointer cursor-pointer hover:border-solid hover:border-1px hover:border-sky-500/80 "
                                            @click="updateRemovePoint(index)">
                                            <Remove />
                                        </el-icon>
                                        <div ml-2 v-show="index !== 0 && index !== updateNodeList.length - 1">
                                            <el-switch v-model="item.state" class="ml-2" inline-prompt size="small"
                                                style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
                                                active-text="连接" inactive-text="打断" />
                                        </div>
                                    </el-space>
                                </div>
                                <div class="mt-10px w-80% flex justify-between">
                                    <div class="text-18px">X:&nbsp;<span class="text-#52c41a text-15px">
                                            {{ item.originPoint.x ? item.originPoint?.x.toFixed(2) : '-' }}</span></div>
                                    <div class="text-18px">Y:&nbsp;<span class="text-#52c41a text-15px">
                                            {{ item.originPoint.y ? item.originPoint?.y.toFixed(2) : '-' }}</span></div>
                                    <div class="text-18px">Z:&nbsp;<span class="text-#52c41a text-15px">
                                            {{ item.originPoint.z ? item.originPoint?.z.toFixed(2) : '-' }}</span></div>
                                </div>
                            </el-form-item>
                        </el-scrollbar>

                        <div class="mt-30px fixed right-10px  bottom-10px  flex justify-end">
                            <el-button @click="updateTunCancel">取消</el-button>
                            <el-button type="primary" @click="updateOnSubmit">确认</el-button>
                        </div>
                    </div>
                </el-form>
            </div>
            <!-- 删除节点关系 -->
            <div v-show="store.$state.operationMode == 6">
                <el-form :model="form" label-width="auto" label-position="left" style="margin-left: 10rem;">
                    <div border="2px solid transparent" h-650rem relative rounded-20px mt-25px pt-30px>
                        <!-- <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                        <div class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                            删除巷道
                        </div>
                    </div> -->
                        <el-form-item class="mb-5px">
                            <div class="mb-5px h-150rem w-100% rounded-15px  text-[#ffff]">
                                <el-form :model="updateTunOther" label-width="auto" label-position="left">
                                    <el-form-item label="巷道名称：">
                                        <el-input :value="updateTunOther.TunName ?? '-'" disabled />
                                    </el-form-item>
                                </el-form>
                            </div>

                        </el-form-item>
                        <div class="w-92% mt-30px fixed right-10px  bottom-10px   flex justify-end">
                            <el-button @click="removeTunCancel">取消</el-button>
                            <el-button type="primary" @click="removeTunSave">确认</el-button>
                        </div>
                    </div>
                </el-form>
            </div>
            <!-- 打断巷道 -->
            <div v-show="store.$state.operationMode == 7">
                <el-form :model="form" label-width="auto" label-position="left" style="margin-left: 10rem;">
                    <div border="2px solid transparent" h-650rem relative rounded-20px mt-25px pt-30px>
                        <!-- <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                        <div class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                            打断巷道
                        </div>
                    </div> -->
                        <el-form-item class="mb-5px">
                            <div class="mb-5px h-500rem w-100% rounded-15px  text-[#ffff]">
                                <el-form :model="updateTunOther" label-width="auto" label-position="left">
                                    <el-form-item label="巷道名称：">
                                        <el-input :value="updateTunOther.TunName" disabled />
                                    </el-form-item>
                                    <el-form-item class="my-15px" label="截点坐标：">
                                        <div class="flex justify-between w-300px">
                                            <div>
                                                <div class="text-18px">X:&nbsp;<span class="text-#52c41a text-15px">
                                                        {{ interruptMovePosition.x == -Infinity ? '-'
                                                            : interruptMovePosition.x.toFixed(2) }}</span></div>
                                            </div>
                                            <div>
                                                <div class="text-18px">Y:&nbsp;<span class="text-#52c41a text-15px">
                                                        {{ interruptMovePosition.y == -Infinity ? '-'
                                                            : interruptMovePosition.y.toFixed(2) }}</span></div>
                                            </div>
                                            <div>
                                                <div class="text-18px">Z:&nbsp;<span class="text-#52c41a text-15px">
                                                        {{ interruptMovePosition.z == -Infinity ? '-'
                                                            : interruptMovePosition.z.toFixed(2) }}</span></div>
                                            </div>
                                        </div>

                                    </el-form-item>
                                </el-form>
                            </div>

                        </el-form-item>
                        <div class="w-92% mt-30px fixed right-10px  bottom-10px  flex justify-end">
                            <el-button @click="interruptTunCancel">取消</el-button>
                            <el-button type="primary" @click="interruptTunSave">确认</el-button>
                        </div>
                    </div>
                </el-form>
            </div>
            <!-- 节点导入 -->
            <el-dialog v-model="importDataDialog" :lock-scroll="false" top="80rem" width="800rem"
                style="height: 680rem;" :draggable="true" :close-on-click-modal="false" :show-close="false">
                <div class="absolute text-18px font-bold top-40px left-40px">
                    节点导入
                </div>
                <div class="absolute text-18px font-bold top-30px right-20px">
                    <el-icon size="25rem" class="hover:text-sky-500 cursor-pointer" @click="importDataDialog = false">
                        <Close />
                    </el-icon>
                </div>
                <div calss="nodeImport">
                    <el-form :model="form" label-width="auto" label-position="left">
                        <div border="2px solid transparent"
                            class=" w-100% h-50rem relative rounded-20px mt-25px  pt-30px">
                            <div flex flex-col mt-5px mb-30px>
                                <el-space alignment="center" class="color-#ECBF7D text-15px font-bold">
                                    <!-- 设置查询范围 -->
                                    <span>设置交点查询范围：</span>
                                    <el-input-number v-model="range" :max="20" :min="2" controls-position="right" />
                                </el-space>
                            </div>
                            <el-table :data="tableData" highlight-current-row style="width: 100%;height: 400rem;">
                                <el-table-column v-show="tableData.length > 0" label="点位状态" type="index" min-width="65">
                                    <template #default="scope">
                                        <el-icon v-show="scope.row.state" color="#66bb6a" size="20rem">
                                            <Check />
                                        </el-icon>
                                        <el-popover placement="right" width="400rem" trigger="click">
                                            <template #reference>
                                                <el-icon class="cursor-pointer" @click="getCheckDouble(scope.row)"
                                                    v-show="!scope.row.state" color="#ea2465" size="20rem">
                                                    <Close />
                                                </el-icon>
                                            </template>
                                            <el-table :data="duplicatePointList" style="width: 400rem;">
                                                <el-table-column min-width="70" prop="NodeID" label="重复点ID" />
                                                <el-table-column min-width="50" label="定位">
                                                    <template #default="scope">
                                                        <el-icon size="20rem" color="#ECBF7D"
                                                            class="mt-8px cursor-pointer"
                                                            @click="emitCameraFlyToPoint(scope.row.NodeID)">
                                                            <Location />
                                                        </el-icon>
                                                    </template>
                                                </el-table-column>
                                                <el-table-column min-width="50" label="使用该节点">
                                                    <template #default="scope2">
                                                        <el-switch
                                                            @change="changeSwitch(scope.$index, scope2.row, 'checkDouble')"
                                                            v-model="scope2.row.isUse" class="mb-2" />
                                                    </template>
                                                </el-table-column>
                                            </el-table>
                                        </el-popover>

                                    </template>
                                </el-table-column>
                                <el-table-column v-show="tableData.length > 0" v-for="(item, index) of tableHeader"
                                    :key="item" :prop="tableHeader[index]" :label="tableHeader[index]" min-width="90" />
                                <el-table-column v-show="tableData.length > 0" label="交点查询" type="index" min-width="65">
                                    <template #default="scope">
                                        <div class="el-popover-box">
                                            <el-popover placement="left" width="400rem" trigger="click">
                                                <template #reference>
                                                    <el-icon class="cursor-pointer"
                                                        @click="findIntersectionPoint(scope.row)" size="20rem"
                                                        color="#ECBF7D">
                                                        <Search />
                                                    </el-icon>
                                                </template>
                                                <el-table :data="intersectionPointList" style="width: 400rem;">
                                                    <el-table-column min-width="70" prop="NodeID" label="交点ID" />
                                                    <el-table-column min-width="50" label="定位">
                                                        <template #default="scope">
                                                            <el-icon size="20rem" color="#ECBF7D"
                                                                class="mt-8px cursor-pointer"
                                                                @click="emitCameraFlyToPoint(scope.row.NodeID)">
                                                                <Location />
                                                            </el-icon>
                                                        </template>
                                                    </el-table-column>
                                                    <el-table-column min-width="50" label="使用该节点">
                                                        <template #default="scope2">
                                                            <el-switch @change="changeSwitch(scope.$index, scope2.row)"
                                                                v-model="scope2.row.isUse" class="mb-2" />
                                                        </template>
                                                    </el-table-column>
                                                </el-table>
                                            </el-popover>
                                        </div>

                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </el-form>
                    <div class="w-92% mt-30px absolute right-10px  bottom-20px  flex justify-end">
                        <el-button @click="ImportCancel">取消</el-button>
                        <el-button type="primary" @click="ImportOnSubmit">确认</el-button>
                    </div>
                </div>
            </el-dialog>


            <!-- 采区配置 -->
            <div v-show="store.$state.operationMode == 9" class="mt-20px">
                <el-form label-width="auto" label-position="left">
                    <div border="2px solid #ff8c00" relative rounded-20px pt-30px>
                        <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                            <div
                                class="w-120px h-25px text-center line-height-25px text-#ff8c00 font-bold bg-[#082237]">
                                新增采区
                            </div>
                        </div>
                        <el-scrollbar height="310rem" class="flex justify-center">
                            <el-form-item v-for="(item, index) in miningAreaForm" :key="index" label="名称：">
                                <el-row :gutter="20" justify="left" align="center">
                                    <el-col :span="1">
                                    </el-col>
                                    <el-col :span="5">
                                        <el-input v-model.trim="item.name" />
                                    </el-col>
                                    <label class="text-[#fff] text-18px">x:</label>
                                    <el-col :span="4">
                                        <el-input v-model.trim="item.x" />
                                    </el-col>
                                    <label class="text-[#fff] text-18px">y:</label>
                                    <el-col :span="4">
                                        <el-input v-model.trim="item.y" />
                                    </el-col>
                                    <label class="text-[#fff] text-18px">z:</label>
                                    <el-col :span="4">
                                        <el-input v-model.trim="item.z" />
                                    </el-col>
                                    <el-col :span="1" v-show="index == pointList.length - 1">
                                        <el-icon size="20rem" color="#40c057" class="mt-8px" @click="addMiningArea">
                                            <CirclePlus />
                                        </el-icon>
                                    </el-col>
                                    <el-col :span="1" v-show="pointList.length != 1">
                                        <el-icon size="20rem" color="#ea2465" class="mt-8px"
                                            @click="removeMiningArea(index)">
                                            <Remove />
                                        </el-icon>
                                    </el-col>
                                </el-row>
                            </el-form-item>
                        </el-scrollbar>
                        <div class="w-92% mt-30px  flex justify-end">
                            <el-button @click="addMiningAreaCancel">取消</el-button>
                            <el-button type="primary" @click="miningAreaSubmit">确认</el-button>
                        </div>
                    </div>
                </el-form>

                <!-- 新增采区列表 -->
                <div border="2px solid #ff8c00" relative rounded-20px mt-15px pt-30px>
                    <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                        <div class="w-120px h-25px text-center line-height-25px text-#ff8c00 font-bold bg-[#082237]">
                            新增采区列表
                        </div>
                    </div>
                    <el-scrollbar height="200rem">
                        <div class="ml-40px my-5px" v-for="(item, index) in miningAreaList" :key="item.id">
                            <span>采区名称：</span><span>{{ item.originPoint.NodeName }}</span>&nbsp;
                            <el-button type=" danger" size="default"
                                @click="emitCameraFlyToPoint(item.originPoint.NodeID)">定位</el-button>
                        </div>
                    </el-scrollbar>

                </div>

            </div>
        </div>

    </div>
</template>
<script setup>
import { watch, reactive, ref, toRaw, onUnmounted, onMounted } from 'vue';
import { useThree } from '@/three/useThree';
import { CirclePlus, Remove, Location, Edit, Search, Check, Close, CloseBold, Select } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { deepClone, deepClone2, getType, getUuid } from '@/utils/utils';
import { ApiNodeAdd, ApiNodeDel, ApiNodeUpdate, ApiNodeList, ApiTunAdd, ApiTunDel, ApiTunUpdate, ApiTunList, roadwayList, ApiNodeCheckDouble, ApiNodeDistance } from '@/https/encapsulation/threeDimensional';
import { getSchemeNodeList, getSchemeTunList, addScheme, delScheme, getSchemeList, updateScheme, saveSchemeBatchingUpdate } from '@/https/encapsulation/ventilationCalculation';
import { saveBatchingUpdate } from '@/https/api';
import { computeTunLenght } from "@/views/thereVentilation/js/tunUtils.js";
import UploadExcel from '@/components/common/uploadExcel/index.vue'
import { createLine2Mat, createLine2 } from '@/three/mesh/Line2Mesh';
import mitt from '@/utils/eventHub';
import { useTunEdit } from '@/store/tunEdit';
import { storeToRefs } from 'pinia';
import { onBeforeRouteLeave, useRouter, useRoute } from 'vue-router';
import { ApiUseList, ApiShapeList, ApiSupportTypeList } from '@/https/encapsulation/threeDimensional';
import StructuredQuery from '@/views/threeVentilateConfig/components/structuredQuery.vue';
import { stopEvent } from '@/utils/window';

const isReverse = ref(true)
const resultBoxShow = ref(false)
const handleStructuralQuery = ref(false)
function structuralQuery() {
    resultBoxShow.value = true;
    handleStructuralQuery.value = true;
}
watch(resultBoxShow, (val) => {
    handleStructuralQuery.value = val;
})
const router = useRouter();
const route = useRoute();
const store = useTunEdit()
const { operationMode, interactionMode, enterFormDiv } = storeToRefs(store);
// 数据字典
const selectDataList = ref({
    supportTypeList: [],
    shapeTypeList: [],
    usageTypeList: [],
})
const filterUsageTypeList = ['一般巷道', '风门', '硐室', '回采工作面', '掘进工作面', '进风井', '回风井']
onMounted(() => {
    //支护类型
    ApiSupportTypeList().then(res => {
        if (res.Status == 0) {
            const { Result } = res;
            selectDataList.value.supportTypeList = Result;
        }
    })

    // 断面形状
    ApiShapeList().then(res => {
        if (res.Status == 0) {
            const { Result } = res;
            selectDataList.value.shapeTypeList = Result;
            // console.log(selectDataList.value.shapeTypeList, 'shapeTypeListshapeTypeListshapeTypeListshapeTypeListshapeTypeListshapeTypeListshapeTypeListshapeTypeList');
        }
    })
    // 用途类型
    ApiUseList().then(res => {
        if (res.Status == 0) {
            const { Result } = res;
            selectDataList.value.usageTypeList = Result;
        }
    })
})

const formatFixed = (val) => {
  if (val === null || val === undefined) return ''; // 空值处理
  const num = Number(val);                          // 转为数字
  return isNaN(num) ? '' : num.toFixed(2);          // 非数字返回空，否则保留两位小数
};
// 离开当前路径之前需要做的事情
onBeforeRouteLeave(() => {
    store.resetVar(true);
})
const { tunStr, nodeList: apiNodeList, tunList: apiTunList, updateNodeInfo, roadwayList: list,
    addTunNodeList: nodeList, updateTunNodeList: updateNodeList, updateTunChangIndex: changeIndex,
    updateTunIsFlag: isFlag, updateTunOther, schemeId, interruptMovePosition, interruptChangeMesh, setNodeListShow } = storeToRefs(store);
// 获取所有巷道信息 
async function getTunList() {
    let Result = null;
    if (schemeId.value) {
        Result = (await getSchemeTunList(schemeId.value)).data.Result ?? [];
    } else {
        Result = (await ApiTunList(schemeId.value)).Result ?? [];
    }
    store.$patch({ tunList: Result });
}
// 获取所有节点信息 
async function getNodeList() {
    let Result = null;
    if (schemeId.value) {
        Result = (await getSchemeNodeList(schemeId.value)).data.Result ?? [];
     
    } else {
        Result = (await ApiNodeList(schemeId.value)).Result ?? [];
    }
    store.$patch({ nodeList: Result });

}
// 获取巷道连接关系
async function getRoadwayList() {
    const { Result } = await roadwayList();
    store.$patch({ roadwayList: Result });
}
onMounted(() => {
    if (route.name == 'ventilationCalculation') {
        getScheme()
    } else {
        schemeId.value = null;
        dataRefresh();
    }
})
watch(route, (val) => {
    svgCurrentIndex.value = -1;
    interactionMode.value = 0;
    operationMode.value = 0;
    if (val.name == 'ventilationCalculation') {
        getScheme()
    } else {
        schemeId.value = null;
        dataRefresh();
    }
})
// 监听当前操作模式
watch(operationMode, (val) => {
    if (val == 5 || val == 6 || val == 7) {
        tunCancel();
    } else if (val == 4) {
        // updateTunOther.value = { TunID: '', TunName: '', Ventflow: 1, Width: 0, Level: 0, Lx: 0 }
        // updateNodeList.value = [{ originPoint: { x: null, y: null, z: null }, uuid: '', NodeID: null, state: true }]
    }
})
// 监听当前交互模式
watch(() => store.$state.interactionMode, (num) => {
    if (num == 1) {
        operationMode.value = 2;
        svgCurrentIndex.value = 4;
    } else if (num == 2) {
        operationMode.value = 5;
        svgCurrentIndex.value = 7;
    } else {
        operationMode.value = 0;
        svgCurrentIndex.value = -1;
    }
})

function changeMode(v, i) {
    store.$patch({ operationMode: v.id })
}

// 触发更新
function emitUpdate(isAllUpdate = false) {
    const arr = store.operationRecord.map(v => {
        if (getType(v) == 'array') {
            return v.map(k => {
                const { type, data } = k;
                return type == 6 ? { type: 6, data: { TunID: data.TunID } } : (type == 3 ? { type: 3, data: { NodeID: data.NodeID } } : k)
            })
        } else {
            const { type, data } = v;
            return type == 6 ? { type, data: { TunID: data.TunID } } : (type == 3 ? { type, data: { NodeID: data.NodeID } } : v)
        }
    })
    store.$patch(state => {
        state.operationRecord = arr;
    })
    isAllUpdate && store.$patch({ renderFlag: ++store.$state.renderFlag });
    // store.$patch({ renderFlag: ++store.$state.renderFlag });
}

// 发送相机飞跃到节点的事件
function emitCameraFlyToPoint(mid) {
    mitt.emit('flyToPoint', mid);
}
// 发送相机飞跃到巷道的事件
function emitCameraFlyToTun(mid) {
    mitt.emit('flyToTun', mid);
}

// 切换方案
const schemeList = ref([])
async function getScheme() {
    const { data: { Result } } = await getSchemeList()
    schemeList.value = Result;
    if (Result.length) {
        schemeId.value = Result[0].ID
        store.$patch({
            schemeId: Result[0].ID
        })
    }
}

const visible1 = ref(false)
const currentScheme = ref(0);
function openDialog(type) {
    visible1.value = true
    currentScheme.value = type
}
const form1 = ref({ SchemeName: '' })
const form = ref({ ID: '', SchemeName: '' })

function handleSchemeCancel() {
    visible1.value = false
}

async function handleScheme() {
    const type = currentScheme.value;
    switch (type) {
        // let data ={}
        case 1:
            const { data: { Status: Status1 } } = await addScheme(form.value)
            handle(Status1)
            form.value = { ID: '', SchemeName: '' }
            break;
        case 2:
            const { data: { Status: Status2 } } = await updateScheme(form.value)
            form.value = { ID: '', SchemeName: '' }
            handle(Status2)
            break;
        case 3:
            const { data: { Status: Status3 } } = await delScheme(form.value.ID)
            form.value = { ID: '', SchemeName: '' }
            handle(Status3)
            break;
    }
    function handle(Status, Msg) {
        if (Status == 0) {
            ElMessage.success('保存成功')
            handleSchemeCancel();
            getScheme();
        } else {
            ElMessage.warning(Msg)
        }
    }
}

watch(schemeId, (val) => {
    if (val) {
        store.$patch({
            schemeId: val
        })
        form.value = deepClone2(toRaw(schemeList.value.find(v => v.ID == val)));
        dataRefresh();
    } else {
        form.value = deepClone2(toRaw(schemeList.value.find(v => v.ID == val)));
        dataRefresh();
        form.value = { ID: '', SchemeName: '' }
    }
})


const addIcon = new URL('../assets/svg/添加.svg', import.meta.url).href;
const deleteIcon = new URL('../assets/svg/删除.svg', import.meta.url).href;
const updateIcon = new URL('../assets/svg/更新.svg', import.meta.url).href;
const repealIcon = new URL('../assets/svg/撤销.svg', import.meta.url).href;
const saveIcon = new URL('../assets/svg/保存.svg', import.meta.url).href;
const breakIcon = new URL('../assets/svg/打断.svg', import.meta.url).href;
const drawPointIcon = new URL('../assets/svg/drawPoint.svg', import.meta.url).href;
const editPointIcon = new URL('../assets/svg/editPoint.svg', import.meta.url).href;
const removePointIcon = new URL('../assets/svg/removePoint.svg', import.meta.url).href;
const drawLineIcon = new URL('../assets/svg/drawLine.svg', import.meta.url).href;
const editlineIcon = new URL('../assets/svg/editline.svg', import.meta.url).href;
const removeLineIcon = new URL('../assets/svg/removeLine.svg', import.meta.url).href;
const importPoint = new URL('../assets/svg/import.svg', import.meta.url).href;
const toggleIcon = new URL('../assets/svg/toggle.svg', import.meta.url).href;



const svgList = [
    { content: '节点导入', icon: importPoint, id: 8 },
    { content: '保存', icon: saveIcon },
    { content: '撤销', icon: repealIcon },
    { content: '新增节点', icon: drawPointIcon, id: 1 },
    { content: '更新节点', icon: editPointIcon, id: 2 },
    { content: '删除节点', icon: removePointIcon, id: 3 },
    { content: '新增巷道', icon: drawLineIcon, id: 4 },
    { content: '更新巷道', icon: editlineIcon, id: 5 },
    { content: '删除巷道', icon: removeLineIcon, id: 6 },
    { content: '打断巷道', icon: breakIcon, id: 7 },
    { content: '切换', icon: toggleIcon, id: 9 },

]
const svgCurrentIndex = ref(-1)




// 撤销操作 还原
const { operationRecord, restoreRecord } = storeToRefs(store);

function repeal() {
    if (operationRecord.value.length) {
        const last = restoreRecord.value.pop();
        console.log(last, 'last');

        operationRecord.value.pop();

        getType(last) == 'array' && last.forEach(({ type, data, index }) => {
            switch (type) {
                case 1:
                    // 撤销新增
                    apiNodeList.value = apiNodeList.value.filter(v => v.NodeID != data.NodeID);
                    break;
                case 3:
                    // 撤销节点删除
                    store.$patch((state) => {
                        state.nodeList = [...state.nodeList, data]
                    });
                    break;
                case 4:
                    // 撤销巷道新增
                    store.$patch({ tunList: apiTunList.value.filter(v => v.TunID != data.TunID) });
                    break;
                case 6:
                    // 撤销巷道删除
                    store.$patch((state) => {
                        state.tunList = [...state.tunList, data]
                    });
                    break;
            }
        })
        // 不是数组的情况
        if (getType(last) == 'object') {
            const { type, data, index } = last;
            switch (type) {
                case 1:
                    // 撤销新增
                    store.$patch({ nodeList: apiNodeList.value.filter(v => v.NodeID != data.NodeID) });
                    break;
                case 2:
                    // 撤销更新
                    store.$patch({ nodeList: apiNodeList.value.map(v => v.NodeID == data.NodeID ? data : v) });
                    break;
                case 3:
                    // 撤销节点删除
                    store.$patch((state) => {
                        state.nodeList = [...state.nodeList, data]
                    });
                    break;
                case 4:
                    // 撤销巷道新增
                    store.$patch({ tunList: apiTunList.value.filter(v => v.TunID != data.TunID) });
                    break;
                case 5:
                    // 撤销巷道新增
                    store.$patch({ tunList: apiTunList.value.map(v => v.TunID == data.TunID ? data : v) });
                    break;
                case 6:
                    // 撤销巷道删除
                    store.$patch((state) => {
                        state.tunList = [...state.tunList, data]
                    });
                    break;
            }
        }
        // store.updateRoadwayList();
        emitUpdate();
        // 执行撤销操作
        mitt.emit('handleRepeal', last);
    }
}

const index = ref(0);
async function save() {
    if (store.$state.operationRecord.length == 0) return;
    const arr = store.$state.operationRecord.map((v, i) => {
        if (getType(v) == 'array') {
            return v.map(k => { return { index: ++index.value, type: k.type, data: k.data } })
        } else {
            return { index: ++index.value, type: v.type, data: v.data }
        }
    })
    // 需风预测页面调用 saveSchemeBatchingUpdate
    const { data } = schemeId.value ? await saveSchemeBatchingUpdate(schemeId.value, arr) : await saveBatchingUpdate(arr);
    if (data.Status == 0) {
        ElMessage.success('保存成功')
    } else {
        ElMessage.warning(data.Msg)
    }
    dataRefresh();
}
// 刷新数据 
function dataRefresh() {
    index.value = 0;
    getTunList();
    getNodeList()
    store.resetVar();
    setTimeout(() => {
        changeNeedNodeList(radioCurrent.value == '全部' ? 3 : radioCurrent.value);
        emitUpdate(true);
    }, 600);
}

// 重置变量信息
const needNodeList = ref([]) //需要展示的点位
const searchPoint = ref(null)
const searchTun = ref(null);

watch(searchPoint, (val) => {
    if (val) {
        updateNodeInfo.value = apiNodeList.value.find(v => v.NodeID == val);
        emitCameraFlyToPoint(val);
    }
})
watch(searchTun, (val) => {
    if (val) {
        emitCameraFlyToTun(val);
    }
})


// 选择框列表
const radioCurrent = ref('全部');
watch(radioCurrent, (val) => {
    changeNeedNodeList(val);
})
function changeNeedNodeList(state) {
    if (state) {
        if (state == 1) {
            needNodeList.value = apiNodeList.value.filter(v => tunStr.value.findIndex(k => k.includes(v.NodeID)) == -1);
        }
        else if (state == 2) {
            needNodeList.value = apiNodeList.value.filter(v => v.Vertex == 0);
        }
        else {
            needNodeList.value = apiNodeList.value
        }
    }
}

// 新增节点 ☆☆
const onSubmit = () => {
    let flag = true;
    for (var i = 0; i < pointList.value.length; i++) {
        const v = pointList.value[i]
        const { x, y, z, name } = v;
        // console.log(JSON.stringify(parseFloat(v.x)), parseFloat(v.y), parseFloat(v.z));
        if (JSON.stringify(parseFloat(x)) != 'null' && JSON.stringify(parseFloat(y)) != 'null' && JSON.stringify(parseFloat(z)) != 'null') {
            const bol1 = getType(parseFloat(x));
            const bol2 = getType(parseFloat(y));
            const bol3 = getType(parseFloat(z));
            flag = bol1 == 'number' && bol2 == 'number' && bol3 == 'number';
            if (!flag) {
                ElMessage.error('请保证您输入的是一个数字类型')
                break;
            }
        } else {
            flag = false;
            ElMessage.error('请保证您输入的是一个数字类型')
            break;
        }
    }
    if (flag) {
        let NodeAddArr = [];
        if (pointList.value.length == 0) return;
        pointList.value.length && pointList.value.forEach((v, i) => {
            let NodeAddObj = {
                NodeID: generateUniqueThreeDigitId(), NodeName: '', Num: 0, Vertex: 1
            }
            const { name: NodeName, x: X, y: Y, z: Z } = v;
            const { x, y, z } = v;
            // NodeAddObj = { ...NodeAddObj, NodeName, x: parseFloat(x), y: parseFloat(y), z: parseFloat(z), X: parseFloat(X), Y: parseFloat(Y), Z: parseFloat(Z) }
            NodeAddObj = { ...NodeAddObj, NodeName, x: parseFloat(x), y: parseFloat(y), z: parseFloat(z) }
            apiNodeList.value.push(NodeAddObj);
            NodeAddArr.push({ type: 1, data: NodeAddObj });
            addCancel();
        })
        store.$patch((state) => {
            state.restoreRecord = [...state.restoreRecord, NodeAddArr];
            state.operationRecord = [...state.operationRecord, NodeAddArr];
        })
        emitUpdate();
        addCancel();
    }
}

// 取消节点保存
const addCancel = () => {
    pointList.value = [{ name: '', x: null, y: null, z: null }];
}

// 更新节点
function updateSave() {
    const { x, y, z, NodeName, NodeID } = updateNodeInfo.value;
    let flag = true;
    // console.log(JSON.stringify(parseFloat(v.x)), parseFloat(v.y), parseFloat(v.z));
    if (JSON.stringify(parseFloat(x)) != 'null' && JSON.stringify(parseFloat(y)) != 'null' && JSON.stringify(parseFloat(z)) != 'null') {
        const bol1 = getType(parseFloat(x));
        const bol2 = getType(parseFloat(y));
        const bol3 = getType(parseFloat(z));
        flag = bol1 == 'number' && bol2 == 'number' && bol3 == 'number';
        if (!flag) {
            ElMessage.error('请保证您输入的是一个数字类型')
            return;
        }
    } else {
        flag = false;
        ElMessage.error('请保证您输入的是一个数字类型')
        return;
    }
    if (flag) {
        const obj = apiNodeList.value.find(k => k.NodeID == NodeID)
        if (obj) {
            let NodeAddObj = {
                ...obj, ...updateNodeInfo.value, x: parseFloat(x), y: parseFloat(y), z: parseFloat(z)
            }
            console.log(NodeAddObj);

            store.$patch((state) => {
                state.restoreRecord = [...state.restoreRecord, { type: 2, data: obj }];
                state.operationRecord = [...state.operationRecord, { type: 2, data: NodeAddObj }];
                // 查询被更新的节点id
                const index = state.nodeList.findIndex(v => v.NodeID == NodeID);
                // 执行更新
                state.nodeList.splice(index, 1, NodeAddObj);
            })
            emitUpdate();
            updateCancel();
        }
    }

}
function updateCancel() {
    updateNodeInfo.value = { NodeID: '', NodeName: '', x: null, y: null, z: null, Vertex: null };
}

// 删除节点
const removeNodeInfo = ref({ NodeName: '', NodeID: '', Vertex: null });
let removeNodeMesh = null;
let changePoint4 = [];
let findPoint4 = [];
// 删除节点交互事件
function removeSave() {
    const { NodeID } = updateNodeInfo.value;

    if (!NodeID) {
        console.log(NodeID, '空点');
        return;
    }
    // 所执行操作
    const arr = []
    store.$patch((state) => {
        // 查询被更新的节点id
        const index = state.nodeList.findIndex(v => v.NodeID == NodeID);
        // 执行删除
        state.nodeList.splice(index, 1);
        arr.push({ type: 3, data: toRaw(updateNodeInfo.value) })
        const delArr = []
        tunStr.value.forEach((v, i) => {
            if (v.includes(NodeID))
                delArr.push(i)
        })
        delArr.forEach(v => {
            const list = state.tunList.splice(v, 1).map(v => toRaw(v));
            arr.push({ type: 6, data: list[0] })
        })

        state.restoreRecord = [...state.restoreRecord, arr];
        state.operationRecord = [...state.operationRecord, arr];
    })

    emitUpdate();
    updateCancel();
}
function removeCancel() {
    removeNodeInfo.value = { NodeName: '', NodeID: '', Vertex: null };
    removeNodeMesh = null;
    changePoint4 = [];
    findPoint4 = [];
}




// 设置节点关系
const tunOther = ref({
    TunName: '',
    Ventflow: 1,
    Width: 0,
    Lx: 0
})
const setNodeMenu = ref(0);
const branchType = ref(null);
watch(branchType, (val) => {
    val ? setNodeListShow.value = true : setNodeListShow.value = false;
})
watch(setNodeMenu, (val) => {
    updateTunCancel(); tunCancel(); cancelSubmitBranch();
    if (val == 2) {
        tunOther.value.Lx = 41;
    } else {
        tunOther.value.Lx = 0;
    }
})

// 提交分支
function submitBranch() {
    if (tunOther.value.TunName == '') {
        ElMessage({
            message: '请输入巷道名称',
            type: 'warning',
        })
        return
    }
    const nodeID = updateNodeInfo.value.NodeID;
    const obj = branchType.value == 1 ? { SnodeID: nodeID, EnodeID: 1, Lx: 40 } : (branchType.value == 2 ? { SnodeID: 1, EnodeID: nodeID, Lx: 43, R: 100, QDemand: 5 } : { SnodeID: 1, EnodeID: nodeID, Lx: 20, R: 0.0001 });
    submitTunAdd([], { ...tunOther.value, ...obj }, 3);

}
function cancelSubmitBranch() {
    setNodeListShow.value = false;
    branchType.value = null;
    updateNodeInfo.value = { NodeID: '', NodeName: '', x: null, y: null, z: null, Vertex: null };
    tunOther.value = {
        TunName: '',
        Ventflow: 1,
        Width: 0,
        Lx: 0
    }
}

// 提交节点关系

async function onSubmitSet() {
    if (tunOther.value.TunName == '') {
        ElMessage({
            message: '请输入巷道名称',
            type: 'warning',
        })
        return
    }
    const Nodes = nodeList.value.map(v => toRaw(v.originPoint));
    Nodes.splice(0, 1, { ...Nodes[0], Vertex: 0 });
    Nodes.splice(Nodes.length - 1, 1, { ...Nodes.at(-1), Vertex: 0 });
    submitTunAdd(Nodes, tunOther.value, 0);
    setNodeListShow.value = false;
}


function generateUniqueThreeDigitId() {
    const timestamp = String(Date.now()).slice(-3); // 取时间戳后3位
    const random = String(Math.floor(Math.random() * 100)).padStart(2, '0'); // 2位随机数
    return Number(timestamp + random) + 10000;
}
let lastId = -1000; // 从-1000开始，确保是四位负数
function generateUniqueFourDigitNegativeId() {
    // 如果已经达到最小值，可以抛出错误或重置（但重置可能导致重复）
    if (lastId <= -99999) {
        throw new Error("No more unique IDs available");
    }
    const newId = lastId - 1;
    lastId = newId;
    return newId;
}
// 巷道提交
function submitTunAdd(arr = [], formContent, type = 0) {
    const Nodes = arr;
    const tunAddObj = {
        Place: 0, TunState: 1, SnodeID: 0, MnodeID: '', EnodeID: 0, Lx: 0, ShoringType: 0, ShapeType: 0, UseType: 0, Length: 0, Width: 0, Height: 0, S: 0, A: 0, R: 0, NetLine: true, Ventflow: 1, R: 0, QDemand: 0,
        ...formContent, TunID: formContent.TunID ?? String(generateUniqueFourDigitNegativeId())
    }
    Nodes.length && (tunAddObj.SnodeID = Nodes[0].NodeID, tunAddObj.EnodeID = Nodes[Nodes.length - 1].NodeID)
    tunAddObj.Length = computeTunLenght(Nodes);

    if (Nodes.length > 2) {
        tunAddObj.MnodeID = Nodes.slice(1, Nodes.length - 1).map(v => v.NodeID).join('-');
    } else {
        tunAddObj.MnodeID = '';
    }
    if (type == 0 || type == 3) {
        const obj = { type: 4, data: tunAddObj };
        store.$patch((state) => {
            state.restoreRecord = [...state.restoreRecord, obj];
            state.operationRecord = [...state.operationRecord, obj];
            state.tunList = [...state.tunList, tunAddObj]
        })
        emitUpdate();
        type == 0 ? tunCancel() : cancelSubmitBranch();
    } else {
        return tunAddObj;
    }
}

function tunCancel() {
    setNodeListShow.value = false;
    nodeList.value = [{ originPoint: { x: null, y: null, z: null }, uuid: '' }];
    tunOther.value = {
        TunName: '',
        Ventflow: 1,
        Width: 0,
        Lx: 0,
    }
}
// addMiddlePoint
function addMiddlePoint() {
    // 判断是否存在未填充的点
    if (setNodeListShow.value) {
        if (nodeList.value.some(v => v.originPoint.x == null)) {
            ElMessage.warning('请先拾取当前节点')
            return;
        }
    }
    if (nodeList.value.length == 1 && nodeList.value[0].uuid == '') {
        setNodeListShow.value = true;
        updateTunOther.value = { TunID: '', TunName: '', Ventflow: 1, Width: 0, Level: 0, Lx: 0 }
          console.log(updateNodeList.value,'节点关系')
        updateNodeList.value = [{ originPoint: { x: null, y: null, z: null }, uuid: '', NodeID: null, state: true }]
    } else {
        const newPoint = { originPoint: { x: null, y: null, z: null }, uuid: '' };
        nodeList.value.push(newPoint);
    }
}
// 移除
function removeMiddlePoint(index) {
    if (index == 0) {
        if (nodeList.value[0].originPoint.x == null) {
            ElMessage.warning('该项已被清空,请勿重复操作')
            return;
        }
        nodeList.value.splice(0, 1, { originPoint: { x: null, y: null, z: null }, uuid: '' });
    } else {
        nodeList.value.splice(index, 1);
    }
}






// 更新节点关系
function updateOnSubmit() {
    const Nodes = updateNodeList.value.map(v => toRaw(v.originPoint));
    const { TunID: tunID, TunName, Ventflow } = updateTunOther.value;
    console.log(updateNodeList.value,'更新节点关系')

    const index = apiTunList.value.findIndex(v => v.TunID == tunID);
    if (!updateNodeList.value.every(v => v.state == true)) {

        const sliceIndex = []
        const sliceList = []
        updateNodeList.value.forEach((v, i) => {
            if (!v.state) {
                sliceIndex.push(i)
            }
        })
        for (let index = 0; index < sliceIndex.length; index++) {
            const sliceI = sliceIndex[index];
            let arr = []
            if (index == 0) {
                arr = Nodes.slice(0, sliceI + 1)
            }
            else {
                const sliceFirst = sliceIndex[index - 1];
                arr = Nodes.slice(sliceFirst, sliceI + 1)
            }
            sliceList.push(arr);
        }
        // 添加最后一段
        sliceList.push(Nodes.slice(sliceIndex.at(-1), Nodes.length));
        // 新增打断巷道 删除原来巷道
        const arr = [];
        const map = new Map();
        sliceList.forEach(v => {
            const tunObj = submitTunAdd(v, { TunName, Ventflow }, 1)
            store.$patch((state) => {
                state.tunList = [...state.tunList, tunObj]
            })
            if (map.has(tunObj.TunID)) {
                return;
            }
            arr.push({ type: 4, data: tunObj })
            map.set(tunObj.TunID, tunObj);
        })
        arr.push({ type: 6, data: updateTunOther.value })

        store.$patch((state) => {
            state.tunList.splice(index, 1)
        })
        store.$patch((state) => {
            state.restoreRecord = [...state.restoreRecord, arr]
            state.operationRecord = [...state.operationRecord, arr]
        })
    } else {
        const obj = submitTunAdd(Nodes, updateTunOther.value, 1);
        const originTun = apiTunList.value.find(v => v.TunID == updateTunOther.value.TunID);
        const obj1 = deepClone(obj)
        delete obj1.Nodes;

        store.$patch((state) => {
            state.restoreRecord = [...state.restoreRecord, { type: 5, data: originTun }]
            state.operationRecord = [...state.operationRecord, { type: 5, data: obj1 }]
            state.tunList.splice(index, 1, obj);
        })
    }
    emitUpdate();
    updateTunCancel();
}
// 取消操作
function updateTunCancel() {
    updateNodeList.value = [{ originPoint: { x: null, y: null, z: null }, uuid: '', NodeID: null, state: true }];
    updateTunOther.value = { TunName: '', Ventflow: 1, Width: 0, Level: 0 }
    changeIndex.value = null;
    isFlag.value = true;
}


// 添加
function updateAddPoint(index) {
    if (!updateAstrict()) return;
    const newPoint = { originPoint: { x: null, y: null, z: null }, NodeID: null, uuid: '', state: true }
    if (index != null) {
        updateNodeList.value.splice(index + 1, 0, newPoint);
    } else {
        updateNodeList.value.push(newPoint);
    }
    setTimeout(() => {
        changeIndex.value = index + 1;
    })
}
// 移除
function updateRemovePoint(index) {
    if (updateNodeList.value.length == 1) {
        updateNodeList.value.splice(0, 1, { originPoint: { x: null, y: null, z: null }, NodeID: null, uuid: '', state: true });
        ElMessage.warning('最后一项会进行保留')
        return;
    }
    updateNodeList.value.splice(index, 1);
}

// 编辑
function updateEditPoint(index) {
    if (!updateAstrict()) return;
    updateNodeList.value.splice(index, 1, { originPoint: { x: null, y: null, z: null }, NodeID: null, uuid: '', state: true });
    changeIndex.value = index;
}
//
function changeUpdateNodeList(index) {
    console.log(index);

    changeIndex.value = index;
}

// 限制
function updateAstrict() {
    let flag = true;
    for (var i = 0, len = updateNodeList.value.length; i < len; i++) {
        const item = updateNodeList.value[i];
        if (!item.originPoint.x) {
            flag = false
            break;
        }
    }
    flag == false && ElMessage.warning('存在未填充的点');
    isFlag.value = flag;
    return flag;
}

// 删除节点关系
function removeTunSave() {

    const { TunID } = updateTunOther.value;
    const index = apiTunList.value.findIndex(v => v.TunID == TunID);
    if (TunID) {
        const obj = { type: 6, data: updateTunOther.value };
        store.$patch((state) => {
            state.restoreRecord = [...state.restoreRecord, obj]
            state.operationRecord = [...state.operationRecord, obj]
        })
        store.$patch((state) => {
            state.tunList.splice(index, 1);
        })
        emitUpdate();
        removeTunCancel();
    }
}

//
function removeTunCancel() {
    updateNodeList.value = [{ originPoint: { x: null, y: null, z: null }, uuid: '', NodeID: null, state: true }];
}


// 打断巷道
function interruptTunSave() {
    if (interruptChangeMesh.value.addTunList.length > 1) {
        const { addTunList, addNode, removeTun } = interruptChangeMesh.value;
        const arr = []
        const newTunList = addTunList.map(v => {
            const obj = submitTunAdd(v.Nodes, () => { }, 1)
            return obj
        })
        arr.push({ type: 1, data: addNode });
        arr.push(...newTunList.map(v => { return { type: 4, data: v } }))
        arr.push({ type: 6, data: removeTun });

        store.$patch(state => {
            state.restoreRecord = [...state.restoreRecord, arr]
            state.operationRecord = [...state.operationRecord, arr]
            state.tunList = [...state.tunList, ...newTunList]
            state.nodeList = [...state.nodeList, addNode]
            const index = apiTunList.value.findIndex(v => v.TunID == removeTun.TunID)
            state.tunList.splice(index, 1);
        })
        emitUpdate();
        interruptTunCancel();
    }

}
function interruptTunCancel() {
    updateTunOther.value = { TunName: '', Ventflow: 1, Width: 0, Level: 0 }
    interruptMovePosition.value = { x: -Infinity, y: -Infinity, z: -Infinity }
    interruptChangeMesh.value = {
        removeTun: null,
        addNode: null,
        addTunList: []
    }
}

// 导入节点
const tableData = ref([])
const tableOriginData = ref([])
const tableHeader = ref(['X', 'Y', 'Z'])
const importDataDialog = ref(false);

watch(tableData, v => {
    if (v.length >= 1) {
        importDataDialog.value = true;
    } else {
        importDataDialog.value = false;
    }
})
// 节点导入之前做的一些限制
function nodeImportRestrict() {
    if (restoreRecord.value.length > 0) {
        ElMessage.warning('存在未保存的修改，请先保存')
    }
}
function getImportData(val) {
    tableData.value = val.map(v => { return { ...v, x: v.X, y: v.Y, z: v.Z, state: 1, Vertex: 1 } });
    tableOriginData.value = val.map(v => { return { ...v, x: v.X, y: v.Y, z: v.z, state: 1, Vertex: 1 } });
    checkDouble(val).then(v => {
        if (!tableData.value.every(v => v.state == 1)) ElMessage.error('存在重复点，请及时处理')
    })
}

// 监测重复点的操作
function checkDouble(val) {
    const obj = { NodeID: -1, NodeName: '', Num: 0, Vertex: 1, x: 0, y: 0, z: 0 }
    const params = val.map(({ X, Y, Z }) => { return { ...obj, x: X, y: Y, z: Z } })
    return params.length && ApiNodeCheckDouble(params).then(({ Result }) => {
        if (!Result || !Result.length) return [];
        Result.forEach((v, i) => {
            const { x, y, z, NodeID, Vertex } = v;
            const index = tableData.value.findIndex(({ X, Y, Z }) => X == x && Y == y && Z == z);
            const lastIndex = tableData.value.findLastIndex(({ X, Y, Z }) => X == x && Y == y && Z == z);

            if (index != -1) {
                // tableData.value.splice(index, 1, { ...v, X: x, Y: y, Z: z, state: 1 });
                // index != lastIndex && lastIndex != -1 && tableData.value.splice(lastIndex, 1, { ...v, X: x, Y: y, Z: z, state: 1 });
                tableData.value.splice(index, 1, { ...tableData.value[index], state: 0 })
                index != lastIndex && lastIndex != -1 && tableData.value.splice(lastIndex, 1, { ...tableData.value[lastIndex], state: 0 })
                tableOriginData.value.splice(index, 1, { ...tableOriginData.value[index], state: 0 })
                index != lastIndex && lastIndex != -1 && tableOriginData.value.splice(lastIndex, 1, { ...tableOriginData.value[lastIndex], state: 0 })
            }
        })

        return Result;
    })
}


let miningAreaList = ref([])
// 批量导点 表单
const pointList = ref([{ name: '', x: null, y: null, z: null }])
function addPoint() {
    pointList.value.push({ name: '', x: null, y: null, z: null });
}
function removePoint(index) {
    pointList.value.splice(index, 1);
}
// 批量导点
function ImportOnSubmit() {
    if (!tableData.value.every(v => v.state == 1)) return ElMessage.error('存在重复点，请及时处理')
    tableData.value.length && Promise.allSettled(tableData.value.map(async (item, i) => {
        if (item.NodeID) return { Status: 0, Result: item.NodeID };
        let NodeAddObj = {
            NodeID: generateUniqueThreeDigitId(), NodeName: '', Num: 0, Vertex: 1, X: 0, Y: 0, Z: 0,
            ...item
        }
        if (i == 0 || i == tableData.value.length - 1) NodeAddObj.Vertex = 0;
        const { Status, Result } = await ApiNodeAdd(NodeAddObj);
        return { Status, Result };
    })).then(async (res) => {
        const StatusList = res.map(v => v.value.Status);
        const NodeIDList = res.map(v => v.value.Result);
        if (StatusList.every(v => v == 0)) {
            tableData.value = tableData.value.map((v, i) => { return { ...v, NodeID: NodeIDList[i] } })
            const obj = submitTunAdd(tableData.value, {}, 1);
            const { Status } = await ApiTunAdd(obj);
            if (Status == 0) {
                ImportCancel();
                dataRefresh();
                ElMessage.warning('保存成功');
            }
        } else {
            ElMessage.warning('保存失败')
        }
    })
}

// 设置搜索范围 默认为5
const range = ref(5)
const intersectionPointList = ref([])
const intersectionIsUse = ref() // id array
// 重复点数组信息
const duplicatePointList = ref([])
const duplicateIsUse = ref()
function ImportCancel() {
    tableData.value = [];
    tableOriginData.value = []
    range.value = 5
    intersectionPointList.value = []
    intersectionIsUse.value = null // id array
    // 重复点数组信息
    duplicatePointList.value = []
    duplicateIsUse.value = null
}

function findIntersectionPoint(row) {
    let obj = {
        NodeID: 0, NodeName: '', Number: 0, Vertex: 1, StaticForce: 0, Temperature: 0, Humidity: 0, VaporForce: 0, Density: 0,
        SchemeID: 0, range: range.value, x: row.X, y: row.Y, z: row.Z
    }
    ApiNodeDistance(obj).then((v) => {
        intersectionPointList.value = v.Result.map(v => {
            let isUse = false;
            if (intersectionIsUse.value == v.NodeID) {
                isUse = true
            }
            return { ...v, isUse }
        });
    })
}
function changeSwitch(index, row, type = "intersection") {
    if (type == 'intersection') {
        if (intersectionIsUse.value) {
            const index = intersectionPointList.value.findIndex(v => v.NodeID == intersectionIsUse.value);
            index != -1 && intersectionPointList.value.splice(index, 1, { ...intersectionPointList.value[index], isUse: false })
            intersectionIsUse.value = null;
        }
    }
    else {
        if (duplicateIsUse.value) {
            const index = duplicatePointList.value.findIndex(v => v.NodeID == duplicateIsUse.value);
            index != -1 && duplicatePointList.value.splice(index, 1, { ...duplicatePointList.value[index], isUse: false })
            duplicateIsUse.value = null;
        }
    }
    if (row.isUse) {
        tableData.value.splice(index, 1, { ...row, X: row.x, Y: row.y, Z: row.z, state: 1 });
        if (type == 'intersection') {
            intersectionIsUse.value = row.NodeID
        } else {
            duplicateIsUse.value = row.NodeID
        }
    } else {
        tableData.value.splice(index, 1, tableOriginData.value[index]);
    }
}

function getCheckDouble(row) {
    checkDouble([row]).then(Result => {
        duplicatePointList.value = Result.map(v => {
            let isUse = false;
            if (duplicateIsUse.value == v.NodeID) {
                isUse = true
            }
            return { ...v, isUse }
        });
    })
}



</script>

<style lang="scss">
@use '../assets/index.scss';

.custom-form {
    .el-form-item__content {
        margin-left: -15px;
    }

    .el-form-item {
        margin-bottom: 8rem;
    }
}
</style>

<style lang="scss" scoped>
:deep(.el-button+.el-button) {
    background-color: rgba(255, 179, 15, 0.3) !important;
    border: 1px solid rgba(232, 157, 6, 0.8) !important;
}

:deep(.el-button.is-disabled, .el-button.is-disabled:hover) {
    cursor: default;
}

:deep(.el-input.is-disabled .el-input__inner) {
    cursor: default !important;
}

.shadow {
    box-shadow: 0 0 20px 5px rgba(0, 123, 255, 0.7);
}

.btn_menu {
    :deep(.el-button--large) {
        width: 135rem;
        height: 50rem;
        background-image: url('../assets/img/btn_bg.png');
        /* background-size: cover; */
        background-size: 100%;
    }

    :deep(.el-button--small) {
        width: 100rem;
        height: 30rem;
        background-image: url('../assets/img/btn_bg.png');
        background-size: 100%;
    }

    .btn_img {
        width: 16px;
        height: 16px;
    }
}

:deep(.el-button--small) {
    width: 100rem;
    height: 30rem;
    background-image: none;
    background-size: 100%;
}

:deep(.el-form-item__label) {
    color: #fff;
}

:deep(.el-dialog__header) {
    margin-left: 50px !important;
    position: relative;
}

:deep(.el-dialog__title) {
    position: absolute;
    top: 45px
}

:deep(.el-text) {
    color: #fff;
    font-size: 25px;
    margin: auto;
}

:deep(.el-input.is-disabled .el-input__inner) {
    -webkit-text-fill-color: #2962ff99;
    cursor: not-allowed;
}

:deep(.el-radio.is-bordered.el-radio--large .el-radio__label) {
    color: #eee;
}

:deep(.el-radio.is-bordered.el-radio--small .el-radio__label) {
    color: #eee;
}

// 悬浮框
:deep(.el-alert--success.is-dark) {
    background-color: #1864ab;
}
</style>