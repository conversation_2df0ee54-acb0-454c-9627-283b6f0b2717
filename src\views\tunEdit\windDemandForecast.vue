<template>
    <div class="h-100vh p-2px pt-100px bg-[#191919] relative grid grid-rows-2 grid-cols-100 gap-5px">
        <FourAngel style="background-color: #191919;" p-2px class="h-full  row-span-2 col-span-4">
            <div class="flex items-center  justify-center flex-col">
                <div class="w-68px h-68px  p-20px flex items-center justify-center cursor-pointer hover:border-solid hover:border-3px hover:border-sky-500/80"
                    v-for="(item, index) in svgList" :class="svgCurrentIndex == index ? 'bg-sky-500/30' : ''"
                    :key="index" @click="() => { svgCurrentIndex = index; changeOperationMode(index) }">
                    <el-tooltip :content="item.content" placement="left" effect="customized">
                        <img :src="item.icon" alt="" class="svg w-32px h-32px">
                    </el-tooltip>
                </div>
                <div :ref="`dom${i + 1}`" v-for="(v, i) in toolList" :key="v"
                    class="w-68px h-68px  flex items-center justify-center p-20px cursor-pointer hover:border-solid hover:border-3px hover:border-sky-500/80"
                    @click="handleClick(i)">
                    <div v-if="i < toolList.length - 3">
                        <el-tooltip :content="v.content" placement="left" effect="customized">
                            <img :src="v.icon" class="w-32px h-32px cursor-pointer">
                        </el-tooltip>
                    </div>
                    <div v-else-if="v.icon == ''">
                        <div v-show="labelShow" @click="labelShow = false">
                            <el-icon size="32rem" color="#ff9800">
                                <View />
                            </el-icon>
                        </div>
                        <div v-show="!labelShow" @click="labelShow = true">
                            <el-icon size="32rem" color="#ff9800">
                                <Hide />
                            </el-icon>
                        </div>
                    </div>
                    <div v-else-if="i == toolList.length - 1 || i == toolList.length - 2"
                        @click="structuralQuery(v.content)">
                        <el-tooltip :content="v.content" placement="left" effect="customized">
                            <img :src="v.icon" class="w-36px h-36px cursor-pointer">
                        </el-tooltip>
                    </div>
                </div>
            </div>
        </FourAngel>

        <div class="row-span-2 h-full col-span-96 grid grid-rows-2 grid-cols-80 gap-5px">
            <FourAngel v-show="!isThree2DFullScreen" style="" p-2px
                :class="isThree3DFullScreen ? 'row-span-2 col-span-80' : 'col-span-61'" class="grid-rows-2 relative">
                <ThreeDTunEdit :label-show="labelShow">
                </ThreeDTunEdit>
                <!-- 全屏按钮 -->
                <img src="./assets/svg/fullScreen.svg" @click="isThree3DFullScreen = !isThree3DFullScreen"
                    class="opacity-60 hover:opacity-100 cursor-pointer absolute bottom-0 left-0 w-25px h-25px z-99"></img>
            </FourAngel>
            <FourAngel v-if="route.name == 'tunEdit'" v-show="!isThree3DFullScreen" p-2px class=" relative"
                :class="isThree2DFullScreen ? 'row-span-2 col-span-80' : 'col-span-61'">
                <TwoDTunEdit></TwoDTunEdit>
                <!-- 全屏按钮 -->
                <img src="./assets/svg/fullScreen.svg" @click="isThree2DFullScreen = !isThree2DFullScreen"
                    class="opacity-60 hover:opacity-100 cursor-pointer absolute bottom-0 left-0 w-25px h-25px z-99"></img>
            </FourAngel>
            <FourAngel v-else v-show="!isThree3DFullScreen" p-2px class="col-span-31 "
                :class="isThree2DFullScreen ? 'row-span-2 col-span-80' : 'col-span-31'">
                <TwoDTunEdit></TwoDTunEdit>
            </FourAngel>
            <FourAngel v-if="route.name == 'ventilationCalculation'" v-show="!isThree3DFullScreen" p-2px
                class="col-span-30">
                <VentilationNetworkVentilationCalculation></VentilationNetworkVentilationCalculation>
            </FourAngel>
        </div>



    </div>
    <div class="z-1 absolute opacity-90   right-0 top-100px  h-full w-514px">
        <FourAngel style="" p-2px class="">
            <ValuePanel></ValuePanel>
        </FourAngel>
    </div>

    <StructuredQuery v-model="resultBoxShow" :handlerFunc="handlerFunc" :schemeId="store.$state.schemeId"
        :handleStructuralQuery="handleStructuralQuery">
    </StructuredQuery>
</template>
<script setup>
import TwoDTunEdit from './components/2DTunEdit.vue'
import ThreeDTunEdit from './3DTunEdit.vue'
import FourAngel from '@/components/common/fourAngel/index.vue';
import ValuePanel from './components/valuePanel.vue';
import { ref, watch } from 'vue';
import { useTunEdit } from '@/store/tunEdit';
import mitt from '@/utils/eventHub';
import VentilationNetworkVentilationCalculation from '../ventilationNetwork/ventilationNetworkVentilationCalculation.vue';
import StructuredQuery from '@/views/threeVentilateConfig/components/structuredQuery.vue';

import { useRouter, useRoute } from 'vue-router';
import { View, Hide } from '@element-plus/icons-vue';
import { storeToRefs } from 'pinia';
import { icon } from 'leaflet';
const route = useRoute();
const store = useTunEdit();
const { schemeId, enterFormDiv, isThree3DFullScreen, isThree2DFullScreen } = storeToRefs(store);

// 引入svg
const checkPointIcon = new URL('./assets/svg/selectPoint.svg', import.meta.url).href;
const checkLineIcon = new URL('./assets/svg/selectLine.svg', import.meta.url).href;
const roamIcon = new URL('./assets/svg/drag.svg', import.meta.url).href;

// 操作模式
const interactionMode = ref(1);
function changeOperationMode(index) {
    interactionMode.value = index;
    store.$patch(() => {
        store.interactionMode = index;
    })
}

const svgCurrentIndex = ref(0);
watch(route, (val) => {
    svgCurrentIndex.value = 0;
})
const svgList = [
    { icon: roamIcon, content: '漫游模式' },
    { icon: checkPointIcon, content: '节点选择' },
    { icon: checkLineIcon, content: '巷道选择' },
]
const svgCurrentIndex1 = ref(-1);
const labelShow = ref(true)
const toolList = ref(
    [
        {
            icon: 'fullScreen.svg',
            content: '全屏展示'
        },
        {
            icon: 'lookDown.svg',
            content: '俯视'
        },
        {
            icon: 'look.svg',
            content: '正视'
        },
        {
            icon: 'sideLook.svg',
            content: '侧视'
        },
        {
            icon: 'reset.svg',
            content: '重置相机'
        },
        {
            icon: '',
            content: ''
        },
        {
            icon: 'structuralQuery.svg',
            content: '结构查询'
        },
        {
            icon: 'ventilationSolution.svg',
            content: '通风解算'
        },

    ]
)
toolList.value = toolList.value.map(item => ({
    ...item,
    icon: item.content ? new URL(`./assets/svg/${item.icon}`, import.meta.url) : '',
}));
let isFullScreen = ref(false)
const handleClick = (i) => {
    switch (i) {
        case 0:
            isFullScreen.value ? exitFullscreen() : enterFullscreen();
            break;
        case 1: mitt.emit('moveCamera', 'up'); break;
        case 2: mitt.emit('moveCamera', 'front'); break;
        case 3: mitt.emit('moveCamera', 'side'); break;
        case 4: mitt.emit('resetCamera', true); break;
        // case 3: router.push('/tunEdit'); break;
        case 5: break;
        case 6: break;
    }
    // 进入全屏模式
    function enterFullscreen() {
        var element = document.documentElement;
        if (element.requestFullscreen) {
            element.requestFullscreen();
        } else if (element.mozRequestFullScreen) { // Firefox
            element.mozRequestFullScreen();
        } else if (element.webkitRequestFullscreen) { // Chrome, Safari and Opera
            element.webkitRequestFullscreen();
        } else if (element.msRequestFullscreen) { // IE/Edge
            element.msRequestFullscreen();
        }
        isFullScreen.value = true
    }

    // 退出全屏模式
    function exitFullscreen() {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.mozCancelFullScreen) { // Firefox
            document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) { // Chrome, Safari and Opera
            document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) { // IE/Edge
            document.msExitFullscreen();
        }
        isFullScreen.value = false
    }
}
const resultBoxShow = ref(false)
const handleStructuralQuery = ref(false)
const handlerFunc = ref('')
function structuralQuery(text) {
    if (text == '结构查询') {
        handlerFunc.value = '结构'
    } else {
        handlerFunc.value = '解算'
    }
    resultBoxShow.value = true;
    setTimeout(() => {
        handlerFunc.value = ''
    }, 1000)
}
watch(resultBoxShow, (val) => {
    if (val == false) {
        handlerFunc.value = ''
    }
})
</script>
<style lang="scss" scoped>
.svg {
    fill: #00ffff;
}
</style>