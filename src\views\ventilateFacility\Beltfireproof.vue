<template>
  <!-- 防火在线 -->

  <div class="">
    <!-- 基本信息 -->
    <DataBox class="top-90px left-24px boxOnline_belt" title="皮带1操作箱1-关">
      <template #mainFan>
        <div class="flex boxOnline_basic">
          <div class="box1_div ml-5px">
            <div v-for="(ba, i) in Onlinedata.data" :key="i">
              <div class="flex flex-row ml-10px pl-47px pt-20px">
                <img src="./assets/img/circle_belt.png" class="w-15px h-15px" />
                <p class="text-14px text-center pl-4px">{{ ba.name }}</p>
              </div>
            </div>
          </div>

          <!-- 按钮 -->
          <div class="boxOnline_button">
            <button class="btn">喷洒</button>
            <button class="btn">复位</button>
          </div>
        </div>
      </template>
    </DataBox>

    <!-- 综合评价 -->
    <DataBox class="top-290px left-24px boxOnline_belt" title="皮带1操作箱2-关">
      <template #mainFan>
        <div class="flex boxOnline_basic">
          <div class="box1_div ml-5px">
            <div v-for="(ba, i) in Onlinedata.data" :key="i">
              <div class="flex flex-row ml-10px pl-47px pt-20px">
                <img src="./assets/img/circle_belt.png" class="w-15px h-15px" />
                <p class="text-14px text-center pl-4px">{{ ba.name }}</p>
              </div>
            </div>
          </div>

          <!-- 按钮 -->
          <div class="boxOnline_button">
            <button class="btn">喷洒</button>
            <button class="btn">复位</button>
          </div>
        </div>
      </template>
    </DataBox>

    <!-- 监测信息 -->
    <DataBox class="top-490px left-24px boxOnline_belt" title="皮带1操作箱3-开">
      <template #mainFan>
      <div class="flex boxOnline_basic">
          <div class="box1_div ml-5px">
            <div v-for="(ba, i) in Onlinedata.data" :key="i">
              <div class="flex flex-row ml-10px pl-47px pt-20px">
                <img src="./assets/img/circle_belt.png" class="w-15px h-15px" />
                <p class="text-14px text-center pl-4px">{{ ba.name }}</p>
              </div>
            </div>
          </div>

          <!-- 按钮 -->
          <div class="boxOnline_button">
            <button class="btn">喷洒</button>
            <button class="btn">复位</button>
          </div>
        </div>
      </template>
    </DataBox>
    <DataBox class="top-695px left-24px boxOnline_belt" title="皮带1操作箱4-开">
      <template #mainFan>
        <div class="flex boxOnline_basic">
          <div class="box1_div ml-5px">
            <div v-for="(ba, i) in Onlinedata.data" :key="i">
              <div class="flex flex-row ml-10px pl-47px pt-20px">
                <img src="./assets/img/circle_belt.png" class="w-15px h-15px" />
                <p class="text-14px text-center pl-4px">{{ ba.name }}</p>
              </div>
            </div>
          </div>

          <!-- 按钮 -->
          <div class="boxOnline_button">
            <button class="btn">喷洒</button>
            <button class="btn">复位</button>
          </div>
        </div>
      </template>
    </DataBox>
    <!-- 中间模型图片 -->
    <div class="sanFan">
      <img
        src="./assets/img/beltbackground.png"
        class="w-1300px h-500px absolute top-280px left-340px"
      />
    </div>

    <!-- 中间列表 -->
     <div class="san_pic">
<div class="temperature-list">
    <table class="temp-table">
      <tbody>
        <tr 
          v-for="(row, rowIndex) in temperatureData" 
          :key="rowIndex"
        >
          <td 
            v-for="(cell, cellIndex) in row" 
            :key="cellIndex"
            :class="{
              title: cellIndex % 2 === 0, // 奇数索引（0 开始）作为标题列
              value: cellIndex % 2 === 1 // 偶数索引作为数值列
            }"
          >
            {{ cell }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
     </div>
     <!-- 右边 -->
       <DataBox class="top-140px right-24px boxOnline_belt" title="皮带1操作箱5-开">
      <template #mainFan>
        <div class="flex boxOnline_basic">
          <div class="box1_div ml-5px">
            <div v-for="(ba, i) in Onlinedata.data" :key="i">
              <div class="flex flex-row ml-10px pl-47px pt-20px">
                <img src="./assets/img/circle_belt.png" class="w-15px h-15px" />
                <p class="text-14px text-center pl-4px">{{ ba.name }}</p>
              </div>
            </div>
          </div>

          <!-- 按钮 -->
          <div class="boxOnline_button">
            <button class="btn">喷洒</button>
            <button class="btn">复位</button>
          </div>
        </div>
      </template>
    </DataBox>

    <!-- 综合评价 -->
    <DataBox class="top-340px right-24px boxOnline_belt" title="皮带1操作箱6-关">
      <template #mainFan>
        <div class="flex boxOnline_basic">
          <div class="box1_div ml-5px">
            <div v-for="(ba, i) in Onlinedata.data" :key="i">
              <div class="flex flex-row ml-10px pl-47px pt-20px">
                <img src="./assets/img/circle_belt.png" class="w-15px h-15px" />
                <p class="text-14px text-center pl-4px">{{ ba.name }}</p>
              </div>
            </div>
          </div>

          <!-- 按钮 -->
          <div class="boxOnline_button">
            <button class="btn">喷洒</button>
            <button class="btn">复位</button>
          </div>
        </div>
      </template>
    </DataBox>

    <!-- 监测信息 -->
    <DataBox class="top-540px right-24px boxOnline_belt" title="皮带1操作箱7-关">
      <template #mainFan>
      <div class="flex boxOnline_basic">
          <div class="box1_div ml-5px">
            <div v-for="(ba, i) in Onlinedata.data" :key="i">
              <div class="flex flex-row ml-10px pl-47px pt-20px">
                <img src="./assets/img/circle_belt.png" class="w-15px h-15px" />
                <p class="text-14px text-center pl-4px">{{ ba.name }}</p>
              </div>
            </div>
          </div>

          <!-- 按钮 -->
          <div class="boxOnline_button">
            <button class="btn">喷洒</button>
            <button class="btn">复位</button>
          </div>
        </div>
      </template>
    </DataBox>
    
    
  </div>
</template>
<script setup>
import { ref, onMounted, watch, watchEffect } from "vue";
import DataBox from "@/components/common/DataBox.vue";
import { onBeforeRouteLeave } from "vue-router";


// 路由离开页面时触发
onBeforeRouteLeave((to, from, next) => {
  window.removeEventListener("wheel", () => {});
  next();
});
// 模拟温度数据，实际可从接口获取
const temperatureData = ref([
  ['10m处温度', '36.83°C', '20m处温度', '27.93°C', '30m处温度', '28.34°C', '40m处温度', '28.14°C'],
  ['50m处温度', '28.3°C', '60m处温度', '28.2°C', '70m处温度', '28.35°C', '80m处温度', '30.43°C'],
  ['90m处温度', '25°C', '100m处温度', '24.5°C', '110m处温度', '24.97°C', '120m处温度', '24.75°C'],
  ['130m处温度', '25.02°C', '140m处温度', '25.51°C', '150m处温度', '25.98°C', '160m处温度', '27.26°C']
]);
// 基础信息
const Onlinedata = ref({
  data: [
    {
      name: "超温警报",
    },
    {
      name: "急停",
    },
    {
      name: "超温预警",
    },
    {
      name: "自动",
    },
  ],
});


</script>
<style lang="scss" scoped>
@use "./mainFan/assets/index.scss";

.boxOnline_basic {
  display: flex;
  flex-direction: column;

  // justify-content: space-between;
  color: #fff;
  font-size: 12px;

  .box1_div {
    display: flex;
    flex-wrap: wrap;

    // background-image: url(./assets/img/basic_backgroud.png);
  }
}
.boxOnline_button {
  display: flex;
  flex-direction: row;
  margin-top: 15px;
  margin-left: 10px;
}
 /* 按钮通用样式 */
    .btn {
      /* 按钮背景色，根据需求调整 */
      background-color: #2E2D2C; 
      color: #F9AE10; /* 文字颜色 */
      border: 2px solid #A2813B; /* 边框颜色 */
      //上右下左
      padding: 5px 45px 5px 45px;
      border-radius: 4px; /* 圆角，可按需调整 */
      cursor: pointer; /* 鼠标悬浮变手型 */
      font-size: 14px; /* 文字大小 */
      position: relative;
      overflow: hidden;
      margin-left: 20px;
    }
.san_pic{
  position: absolute;
  // background-color: #fff;
  // width: 1500px;
  // height: 270px;
  top: 693px;
 left: 403px;

}

.temperature-list {
  width: fit-content;
}
.temp-table {
  border-collapse: collapse;
  color: #fff;
}
.temp-table td {
  border: 1px solid #444;

  padding: 13px 30px;
}
.title {
  background-color: #333;
  color: #F7AE10;
  font-weight: normal;
  
}
.value {
  background-color: #222;
  text-align: center;
}
</style>