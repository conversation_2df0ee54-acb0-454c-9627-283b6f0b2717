<template>
  <div ref="sceneDiv3" class="canvasBox"></div>
  <div>
    <DataBox class="top-116px left-24px boxDoor" title="基本信息">
      <template #mainFan>
        <div class="door_basic">
          <div v-for="(d, i) in doorData.data" :key="i" class="door_basic_div">
            <div class="flex ml-10px">
              <img
                src="../airDoor/assets/img/cicle_aidoor.png"
                class="w-25px h-25px"
              />
              <div class="boxDoor_box1_span">
                <span class="color-#E3D8B7 airdoor_ul">{{ d.name }}</span>
                <span class="pt-2px first_span">   {{ d.name === '风门尺寸:' ? d.value + ' m' : d.value }}</span>
              </div>
            </div>
          </div>
        </div>
      </template>
    </DataBox>

    <!-- 环境监测 -->
    <DataBox
      class="top-575px left-24px boxDoor_box2"
      title="环境监测"
      :tabNameList="[]"
    >
      <template #mainFan>
        <div class="enivor_div">
          <div class="boxdiv_model">
          
            <div class="model_text">
              <div class="model_p1">压差：{{ diffpressure }}(kg/cm³)</div>
              <div class="model_p2">风速：{{ widspeed }}(m/s)</div>
              <div class="model_p3">压力：{{ pressure }}(kg/cm³)</div>
            </div>
          </div>
        </div>
      </template>
    </DataBox>

   
    <CenterTitle :title="centerTitleDesc"></CenterTitle>
    <!-- 控制面板 -->
    <DataBox class="top-116px right-25px boxDoor_box4" title="控制面板">
      <template #mainFan>
        <div class="flex justify-around mt-15px control_mb_div">
          <div></div>
          <div class="">
            <div class="mt-5px control_zt">
              <p>控制区域</p>
            </div>
            <div class="flex forItemBox mt-5px mr-50px ml-30px">
              <div
                v-for="(item, index) in forList.data"
                :key="index"
                class="control_forItem cursor-pointer"
                @click="openDialog(index)"
              >
                <div class="control_back flex pt-10px pl-20px">
                  <div class="control_back_div">
                    <div class="div_forItemBox flex pt-10px pl-10px">
                      <img :src="item.icon" />
                      <p class="pt-3px pl-10px">{{ item.name }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </DataBox>

    <!-- 视频监控 -->
    <DataBox
      class="top-490px right-25px box4"
      title="视频监控"
      :tabNameList="[]"
    >
      <template #mainFan>
        <div class="mt-30px flex items-center">
          <div
            @click="handleVideoMonitorListLeftRight('left')"
            class="cursor-pointer"
          >
            <img
              class="w-33px h-58px ml-18px"
              src="../mainFan/assets/img/switch_arrow.png"
            />
          </div>
          <div class="">
            <div class="flex justify-around w-386px transition-all delay-600">
              <div
                class="box4_camera"
                @click="handleVideoMonitorDetail(item)"
                v-for="(item, i) in videoMonitorList"
                :key="i"
              >
                <div>
                  <img
                    class="w-77px h-88px"
                    src="../mainFan/assets/img/camera.png"
                  />
                </div>
                <div class="mt-12px">
                  <span>{{ item.Name }}</span>
                </div>
              </div>
            </div>
          </div>
          <div
            @click="handleVideoMonitorListLeftRight('right')"
            class="cursor-pointer"
          >
            <img
              class="w-33px h-58px mr-18px rotate-180"
              src="../mainFan/assets/img/switch_arrow.png"
            />
          </div>
        </div>
      </template>
    </DataBox>

    <!-- 故障诊断 -->
    <DataBox class="top-740px right-25px boxDoor_box3" title="故障诊断">
      <template #mainFan>
        <div class="px-22px">
          <div>
            <div class="w-100% flex justify-around mt-5px">
              <div class="box5_tab" @click="changeBox5Tab(0)">
                <span>实时数据</span>
                <div :class="box5Tab == 0 ? 'box5_tab_active' : ''"></div>
              </div>
              <div class="box5_tab" @click="changeBox5Tab(1)">
                <span>历史报警数据</span>
                <div :class="box5Tab == 1 ? 'box5_tab_active' : ''"></div>
              </div>
            </div>
            <div
              class="w-100% h-1px mt-23px border border-solid border-[#52595F]"
            ></div>
          </div>
          <div class="flex justify-between w-100% flex-wrap" v-if="box5Tab === 0">
            <div
              v-for="(v, i) in box5Data"
              :key="v"
              :class="i > 2 ? 'mt-20px' : 'mt-15px'"
            >
              <div class="box5_data_value_bg">
                <span class="box5_data_value">{{ v.value }}</span>
              </div>
              <div class="box5_data_name"><span>连接状态</span></div>
            </div>
          </div>

          <div class="flex w-100% box5-tab1" v-if="box5Tab === 1">
                <div  class="item-box">
                  <div class="center_guz">
                    
                      <sapn>1#液压泵</sapn>
                      <span class="ml-30px">故障</span>
                   
                  </div>
                  <div class="time ml-150px">2022-12-16 03:19:46</div>
                </div>
              </div>
        </div>
      </template>
    </DataBox>
    <bottomTab @change="handleTab" :bottomTabList="bottomTabList"  :allData="allData"></bottomTab>
    <!-- 视频弹窗 -->
    <el-dialog
      draggable
      destroy-on-close
      modal-class="abc"
      :lock-scroll="false"
      v-model="dialogShow"
      :modal="false"
      :close-on-click-modal="false"
      @close="dialogShowClose"
      :title="dialogTitle"
      style="width: 850rem; height: 500rem; z-index: 50"
    >
      <!-- 视频播放 -->
      <canvas
        id="playCanvass"
        style="
          background-color: black;
          width: 670rem;
          height: 350rem;
          margin-left: 60rem;
          margin-top: 20rem;
        "
      ></canvas>
    </el-dialog>
    <!-- 视频弹窗结束 -->

    <!-- 控制表单 -->
    <el-dialog
      title="验证面板"
      :lock-scroll="false"
      modal-class="abc"
      draggable
      :modal="false"
      :close-on-click-modal="false"
      style="height: 300rem; z-index: 20"
      width="400px"
      v-model="controlDialog"
      @close="onClose"
    >
      <el-form
        class="mt-20px"
        :model="form"
        ref="form"
        :rules="rules"
        label-width="100px"
        :inline="false"
        size="normal"
      >
        <el-form-item
          label="登录密码："

          style="color: white"
          :rules="[
            {
              required: true,
              message: '请输入密码',
              trigger: 'blur',
            },
          ]"
        >
          <el-input
            v-model="password"
            type="password"
            placeholder="请输入登录密码"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer mt-50px">
          <el-button size="medium" @click="handleCancel">取 消</el-button>
          <el-button size="medium" type="primary" @click="handleOk"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
    <!-- 控制表单结束 -->
  </div>
</template>
<script setup>
import { ref, reactive, onMounted, watch, onBeforeUnmount } from "vue";
import { onBeforeRouteLeave } from "vue-router";
import DataBox from "@/components/common/DataBox.vue";
import CenterTitle from "@/components/common/CenterTitle.vue";
import { ApitWindwindowList } from "@/https/encapsulation/Windwindow";
import bottomTab from "../../../components/common/bottomTab.vue";
import { useThree } from "@/three/useThree";
import open_icon from "./assets/img/open_icon.png";
import lock_icon from "./assets/img/lock_icon.png";
import { useCommonStore } from "@/store/common";
import { ElMessage } from "element-plus";
import { controlWindWindow } from "@/https/api";

const doorList = ref([]);
const store = useCommonStore();
// 控制
const controlDialog = ref(false);
const password = ref("");
const controlForm = ref({
  id: "",
  windowId: 1,
  controlType: 1,
});
function handleOk() {
  if (password.value !== store.userInfo.password) {
    ElMessage.error("登录密码错误");
  } else {
    controlWindWindow(controlForm.value).then((res) => {
      if (res.data.Status == 0) {
        ElMessage.success("控制成功");
        controlDialog.value = false;
        handleCancel();
      }
    });
  }
}
//控制面板
function handleCancel() {
  controlDialog.value = false;
  controlForm.value = {
    id: "",
    windowId: 1,
    controlType: 1,
  };
  password.value = "";
}
function openDialog(index) {
  console.log(index, "index111111111");
  let windowId = 1;
  let controlType = 1;
  switch (index) {
    case 0:
      break;
    case 1:
      controlType = 2;
      break;
    case 2:
      windowId = 2;
      controlType = 1;
      break;
    case 3:
      windowId = 2;
      controlType = 2;
      break;
    case 4:
      windowId = 3;
      controlType = 1;
      break;
    case 5:
      windowId = 3;
      controlType = 2;
      break;
    case 6:
      windowId = 4;
      controlType = 1;
      break;
    case 7:
      windowId = 4;
      controlType = 2;
      break;
  }
  controlDialog.value = true;
  controlForm.value = {
    id: doorList.value[0].WindWindowInfo.ID, // 暂时写死
    windowId,
    controlType,
  };
  console.log(controlForm.value, "controlDialog");
}

let playCanvas = ref(null);

const startCamera = [1000, 300, 1000];
const endCamera = [400 * 0.7, 300, 600 * 0.95];
const gltfGlobal = ref();
const restMark = ref(false); // 重置标识
const zntf = ref(false);
let modelInit = ref();
const allData = reactive([]);
modelInit.value = useThree({ startCamera, endCamera });
// 数据弹窗
const isDataShow = ref({});
// 详细信息请看 three/useThree/index.js 文件
const {
  mountRender,
  ani,
  scene,
  camera,
  cameraControls,
  initModel,
  changeCurrentMesh,
  changeCurrentMeshByName,
  findMesh,
  restrictCameraVerticalDirection,
  matcapTexture,
  dbModelClick,
  executeMultipleAnimation,
  setMeshScale,
  stopRender,
  removeMeshByName,
  executeAnimation,
  restCamera,
  addAmbientLight,
} = modelInit.value;
// 相机重置函数
// function handleRestCamera() {
//     restCamera(() => {
//         isShow.value = false
//         restMark.value = true
//     })
// }
// 添加灯光 默认场景已经添加部分灯光
// scene.add(addAmbientLight(0xffffff, 0.5))
// 限制相机垂直方向移动
// restrictCameraVerticalDirection(cameraControls, Math.PI / 6, Math.PI / 5);
// 加载模型
// initModel('./model/tfj.glb', [0, 0, 0], (gltf) => {
initModel("./model/airWindow2.glb", [0, 0, 0], (gltf) => {
  if (gltf) {
    gltfGlobal.value = gltf;
    // console.log(gltf, "局部通风机1111");
    // 移除物体模型模块
    // removeMeshByName(gltf.scene,'JBTFXT_HD1')
    // removeMeshByName(gltf.scene,'JBTFXT_HD2')
    // 缩放模型大小
    setMeshScale(gltf.scene, 40);
    // // 需要进行贴图的物体名称 后续唐森会将物体名称进行修改 方便操作
    // const matName = ['TF_Wall_']
    // // // // 贴图函数 根据模型上物体的名称选择需要进行贴图的物体
    // matcapTexture(gltf.scene, matName, './textures/287.png')

    /**
     *  初始化动画函数 详细介绍请看 three/utils/animat.js executeMultipleAnimation的第二个参数支持传多个动画 参数必须是数组 动画数组中传入多少个动画会执行多少个动画
     *  updateMultipleAnimation更新方法 需要传入下次动画与上次动画之间的时间间隔 传0不执行
     *  对于动画和模型的对应关系 可以问唐森
     *  executeAnimation用来执行单个动画 用法与 executeMultipleAnimation类似 前者只能传入一个动画对象 非数组形式
     */

    // 风机01风扇动画
    const {
      startMultipleAnimation,
      updateMultipleAnimation,
      stopMultipleAnimation,
    } = executeMultipleAnimation(gltf.scene, [
      gltf.animations[0],
      gltf.animations[1],
    ]);
    // 风机02风扇动画
    const {
      startMultipleAnimation: startAni2,
      updateMultipleAnimation: updateAni2,
      stopMultipleAnimation: stopAni2,
    } = executeMultipleAnimation(gltf.scene, [
      gltf.animations[2],
      gltf.animations[3],
    ]);

    // 初始化风机选中  高亮
    // const mesh1 = findMesh(gltf.scene, 'JBTFXT_HD')
    // const mesh2 = findMesh(gltf.scene, 'JBTFXT_HD3')

    //  changeCurrentMesh(mesh1, { isFlyMesh: true })
    // startMultipleAnimation()
    // // 模型交互事件 双击模型 参数1:需要监测那些物体 就将哪些物体传进来 可以直接传gltf.scene 则所有物体都可被点击
    // dbModelClick([mesh1, mesh2], camera, (mesh) => {
    //     const { name } = mesh.object;
    //     fanActive.value = name == 'JBTFXT_Wall' ? 0 : 1;
    //     executeFanSelect()
    // })

    // 监听复位方法
    // 监听复位方法
    watch(restMark, (val) => {
      if (val) {
        executeFanSelect();
      }
      setTimeout(() => {
        restMark.value = false;
      }, 200);
    });
    // 切换风机 启动动画
    // function executeFanSelect() {
    //     if (fanActive.value == 0) {
    //         startMultipleAnimation()
    //         stopAni2()
    //         changeCurrentMeshByName('JBTFXT_Door_01', gltf.scene)
    //     } else {
    //         stopMultipleAnimation()
    //         startAni2()
    //         changeCurrentMeshByName('JBTFXT_HD2', gltf.scene)
    //     }
    // }

    // 渲染函数
    ani(function (...args) {
      const [time, camera, delta] = args;
      // updateAnimation(delta)
      // 更新动画
      updateMultipleAnimation(0.15);
      updateAni2(0.15);
    });
  }
}).then((gltf) => {
  zntf.value = true;
});
// 查看指定物体信息
function checkMeshData(name) {
  isShow.value = false;
  // 测试内容
  if (name == "上风门状态") {
    // 通过物体名称获取物体 并添加效果 或者显示标签 需要让那个物体高亮 就传入其对应的名称 目前物体名称没有做修改 使用其默认的名称
    // changeCurrentMeshByName('DJ_01', gltfGlobal.value.scene, {
    //     isLabel: true,
    //     // 当需要显示标签时 callback 的第一个参数可以获取到 标签的坐标位置
    //     callback: (val) => {
    //         isDataShow.value = val
    //         console.log(isDataShow.value);
    //     }
    // })
  } else {
    // changeCurrentMeshByName('DJ_02', gltfGlobal.value.scene, {
    //     isLabel: true,
    //     callback: (val) => {
    //         isDataShow.value = val
    //         console.log(isDataShow);
    //     }
    // })
  }
}

const intervalId = ref(null);
// 模型挂载
onMounted(() => {
  mountRender(sceneDiv3.value);

  //定时刷新

  intervalId.value = setInterval(() => {
    ApitWindwindowList();
  }, 20 * 1000);
  ApitWindwindowList();
});

//清除定时器，防止内存溢出
onBeforeUnmount(() => {
  clearInterval(intervalId.value);
});
//底部tab数据
const bottomTabList = ref([]);
const tabsData = ref({})
const centerTitleDesc = ref('')

// bottomTab 组件值改变事件, 根据这个值取请求接口完成业务逻辑
const handleTab = ({item:data,allData:result}) => {
  tabsData.value = data
  centerTitleDesc.value = `${data.name}`
 
  const current = result.find(item=>item.WindWindowInfo.ID === data.id);
   handleChangePageInfo(current)
  
}

// 左右切换查看更多
const handleVideoMonitorListLeftRight = (type) => {
  console.log(`${type}滚动`);
  if (videoMonitorList.value.length < 3) {
    //
  }
};

const dialogShow = ref(false);
const dialogShowClose = () => {
  player.stop();
};

const videoMonitorList = ref([]);
//视频监控列表
// 查看视频监控详情,弹窗
const handleVideoMonitorDetail = (item) => {
  console.log(item, "视频监控详情111111");
  dialogShow.value = true;

  setTimeout(() => {
    player.play({
      baseUrl: window.origin,
      canvas: document.getElementById("playCanvass"),
      //canvas: playCanvas.value, //渲染画布
      videoWidth: 670, //渲染视频宽度
      videoHeight: 350, //渲染视频高度
      codecType: item.DecodeType, //解码格式：0-h264,1-h265
      videoUrl: item.Description, //流媒体地址
      bufferLength: 0, //缓存大小
    });
  }, 50);
};
const sceneDiv3 = ref(null);
const fanActive = ref(0);
const widspeed = ref();
const diffpressure = ref();
const pressure = ref();
const cameras = reactive([]);
var player = new Player();
ApitWindwindowList().then((WindWindow) => {

  if (WindWindow.Status === 0) {
    doorList.value = WindWindow.Result;

     WindWindow.Result[0].WindWindowInfo.Cameras.forEach((item) => {
      cameras.push(item);
    });
    // 底部tab数据
    bottomTabList.value.length = 0;
    (WindWindow?.Result||[]).forEach(item=>{
      bottomTabList.value.push({
        id: item.WindWindowInfo.ID,
        name: item.WindWindowInfo.DevAddress,
        DevCode: item.WindWindowInfo.DevCode,
        Camera1: item.WindWindowInfo.Cameras,
      })
    })
    allData.push(...WindWindow.Result);
     console.log(allData.push(...WindWindow.Result),'111111kkkkkkk')
     // 默认第一个
    handleTab({
      item:bottomTabList.value[0],
      allData:WindWindow.Result
    });


    
  }
});

const handleChangePageInfo = ({WindWindowInfo,...rest})=>{
  console.log(WindWindowInfo,'风窗')
    doorData.value.data[0].value = WindWindowInfo.WindNum
    //安装位置
    doorData.value.data[1].value = WindWindowInfo.DevAddress
    //设备类型
    doorData.value.data[2].value = WindWindowInfo.DevName
    //施工时间
    doorData.value.data[3].value = WindWindowInfo.BuildTime
    //风门尺寸
    doorData.value.data[4].value = WindWindowInfo.DevSize

    //风速
    widspeed.value = rest.WindSpeed

    //  压差
    diffpressure.value = rest.DiffPressure
    // 压力
    pressure.value = rest.Pressure

    videoMonitorList.value = WindWindowInfo.Cameras;
}
// 路由离开页面时触发
onBeforeRouteLeave((to, from, next) => {
  stopRender();
  modelInit.value = null;

  try {
    player.stop();
    console.log(player.stop, "停止播放风窗");
  } catch (e) {
    console.info(e);
  }

  window.removeEventListener("wheel", () => {});
  next();
});
const doorData = ref({
  data: [
    {
      id: "1",
      icon: "damper_icon.png",
      name: "风门编号:",
      value: "",
    },
    {
      id: "2",
      icon: "az_icon.png",
      name: "安装位置:",
      value: "",
    },
    {
      id: "3",
      icon: "type_icon.png",
      name: "设备类型:",
      value: "",
    },
    // {
    //   id: "4",
    //   icon: "controul_icon.png",
    //   name: "墙体结构:",
    //   value: "",
    // },
    {
      id: "4",
      icon: "sgtime_icon.png",
      name: "施工时间:",
      value: "",
    },
    // {
    //   id: "6",
    //   icon: "screenshot_icon.png",
    //   name: "墙体厚重:",
    //   value: "",
    // },
    {
      id: "5",
      icon: "fmcc_icon.png",
      name: "风门尺寸:",
      value: "",
    },
  ],
});

// 故障监测
const box5Tab = ref(0);
function changeBox5Tab(num) {
  box5Tab.value = num;
}
const box5Data = ref([
  {
    value: "正常",
  },
]);

const twoData = doorData.value.data.map((item) => ({
  ...item,
  icon: new URL(`./assets/img/${item.icon}`, import.meta.url).href,
}));
doorData.value.data = twoData;

const forList = ref({
  data: [
    {
      icon: open_icon,
      name: "打开A窗",
    },

    {
      icon: lock_icon,
      name: "关闭A窗",
    },
    {
      icon: open_icon,
      name: "打开B窗",
    },
    {
      icon: lock_icon,
      name: "关闭B窗",
    },
    {
      icon: open_icon,
      name: "打开C窗",
    },
    {
      icon: lock_icon,
      name: "关闭C窗",
    },
    {
      icon: open_icon,
      name: "打开D窗",
    },
    {
      icon: lock_icon,
      name: "关闭D窗",
    },
  ],
});

const forData = forList.value.data.map((item) => ({
  ...item,
  icon: new URL(item.icon, import.meta.url).href,
}));
forList.value.data = forData;
</script>
<style lang="scss" scoped>
@use '../mainFan/assets/index.scss';

:deep(.el-form-item__label) {
  color: white;
}

.door_basic {
  display: flex;
  flex-direction: column;
  // justify-content: space-between;
  margin-left: 20px;
  margin-right: 20px;
  margin-top: 20px;
  padding-top: 10px;
}

.door_basic_div {
  background-color: #060d15;
  border: 1px solid #3b3e42;
  border-left-color: #ab720d;
  // background-image: url(./assets/img/basic_backgroud.png);
  width: 460px;
  height: 50px;
  background-size: 214px 80px;
  color: #fff;
  margin-top: 15px;
  text-align: center;
  padding-top: 10px;
  font-size: 16px;
}

.boxDoor_box1_span {
  display: flex;
  flex-wrap: wrap;
  padding-left: 15px;
  padding-top: 5px;
  color: #d9990f;
  // background-color: #161A1E;
  font-weight: bolder;
  text-align: left;

  .airdoor_ul {
    width: 100px;
  }
  .first_span {
    // font-size: 14px;
    padding-left: 15px;
  }
}

.enivor_div {
  margin-left: 10px;
  margin-top: 10px;
}

.boxdiv_model {
  position: relative;
  top: 10px;
  left: 55px;
  background-image: url(../airDoor/assets/img/fc-bg-airdoor.png);
  width: 350px;
  height: 340px;
  background-size: 350px 340px;
}

.model_circle {
  // .img1 {
  //   position: absolute;
  //   bottom: 2px;
  // }

  .img2 {
    position: absolute;
    bottom: 7px;
    left: -68px;
  }

  .img3 {
    position: absolute;
    top: 67px;
    left: -24px;
  }

  .img4 {
    position: absolute;
    left: -90px;
    bottom: -35px;
  }
}

.model_text {
  color: #fff;
  position: relative;

  // p {
  //   background-image: url(../airDoor/assets/img/yac_background.png);
  //   width: 133px;
  //   height: 43px;
  //   background-size: 133px 43px;
  //   padding-top: 15px;
  //   padding-left: 16px;
  // }

  .model_p1 {
    position: absolute;
    width: 200px;
    height: 32px;
    padding: 6px;
    margin: 10px;
    text-align: center;
    background-color: #161a1f;
    border: 2px solid #454442;
    // top: 10px;
    left: -22px;
  }
  .model_p1::before {
  content: "";
  position: absolute;
  width: 10px;
  height: 10px;
  top: -2px;
  left: -2px;
  border-left: 2px solid #D29613;
  border-top: 2px solid #D29613;
}
.model_p1::after {
  content: "";
  position: absolute;
  width: 10px;
  height:10px;
  bottom: -2px;
  right: -2px;
  border-bottom: 2px solid #D29613;
  border-right: 2px solid #D29613;
}
  .model_p2 {
    @extend .model_p1;
    position: absolute;
    // top: 25px;
    left: 190px;
  }

  .model_p3 {
    @extend .model_p1;
    position: absolute;
    top: 50px;
    left: 80px;
  }
}

// 验证面板代码

::v-deep .el-dialog__footer {
  margin-right: 100px;
}
::v-deep .el-button {
  padding: 25px 25px;
  font-size: 16px;
}

.dialog-footer {
  width: 100%;
  height: 30%;
}
.control_mb_div {
  color: #fff;
  display: flex;
  flex-direction: column;
}

.control_zt {
  background-image: url(./assets/img/fengj_icon_title.png);
  width: 170px;
  height: 32px;
  background-size: 170px 32px;
  margin-left: 150px;

  p {
    text-align: center;
    padding-top: 10px;
  }
}

.control_back {
  width: 116px;
  height: 39px;
  background-size: 116px 39px;
}

.forItemBox {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.div_forItemBox {
  background-image: url(./assets/img/panel_green_open.png);
  width: 116px;
  height: 38px;
  background-size: 116px 38px;

  img {
    width: 20px;
    height: 20px;
  }
}
.item-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #e3d8b7;
    padding: 10px 0;
    margin-top: 10px;

  }
  .center_guz{
      display: flex;
      flex-direction: row;
    }
.control_forItem {
  margin-left: 30px;
  margin-right: 30px;
  margin-top: 10px;
}

::v-deep .el-switch__label--right {
  color: #fff;
  opacity: 0.8;
}

:global(div.abc) {
  pointer-events: none;
}

:global(div.abc .el-dialog) {
  pointer-events: auto;
}

::v-deep .el-dialog {
  // width: 850px;
  // height: 500px;
  background: rgb(0, 0, 0) !important;
  box-shadow: rgb(255, 179, 15) 0px 0px 20px inset !important;
  border: 1px solid rgba(255, 179, 15, 0.3) !important;
  border-radius: 10px !important;
}
::v-deep .el-button+.el-button{
  background-color: rgba(255,179,15,0.3) !important;
  border: 1px solid rgba(232,157,6,0.8) !important;
}
</style>