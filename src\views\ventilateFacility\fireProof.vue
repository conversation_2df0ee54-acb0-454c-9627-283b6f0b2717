<template>
  <!-- 光纤测温 -->
  <div ref="fireDiv" class="canvasBox"></div>
  <div class="section_1 page">
    <!-- 基本信息 -->
    <DataBox class="top-116px left-24px boxs" title="基本信息">
      <template #mainFan>
        <div class="flex basic">
          <div v-for="(v, i) in basicData.data" :key="i" class="flex items-center basic_onepart">
            <div class="flex ml-10px">
              <img src="../ventilateFacility/airDoor/assets/img/cicle_aidoor.png" class="w-25px h-25px breathe_ani" />
              <div class="box5_name">
                <span class="color-#E3D8B7   airdoor_ul">{{ v.name }}</span>
                <span class="first_span">{{ v.value }}</span>
              </div>
            
            </div>
          </div>
        </div>
      </template>
    </DataBox>
    <!-- 预警报警 -->
    <DataBox class="top-540px left-24px box6" title="预警报警">
      <template #mainFan>
        <!-- 预警报警头部 -->
        <div class="temputure_onepart">
          <!-- 最高温度 -->
          <div class="temp_on">
            <div class="temp_pg">
              <img src="./assets/img/temp.png" class="float_ani temp_icon" />
              <img src="./assets/img/alarm-bg.png" class="diffuse_ani temp_bg" />
             
            </div>
            <div class="tem_font">
              <span>{{27.6}}℃</span>
              <span>最高温度</span>
            </div>
          </div>

          <!-- 位置 -->
          <div class="temp_on">
            <div class="temp_pg">
              <img src="./assets/img/locate-32.png" class="float_ani temp_icon" />
              <img src="./assets/img/alarm-bg.png" class="diffuse_ani temp_bg" />
            </div>
            <div class="tem_font">
              <span>{{100}}M</span>
              <span>位置</span>
            </div>
          </div>
        </div>
        <!-- 温湿度 -->
        <div class="flex justify-around w-100% flex-wrap mt-30px box_earlywarning">
          <div class="sData" v-for="(v, i) in bData" :key="i">
            <div class="yuj_div">
              <!--  -->
              <div class="pos_div">
                <div>
                  <img src="./assets/img/wz_icon.png" />
                  <span class="tem1_font">{{ v.ChannelPosition }}KM</span>
                </div>

                <div>
                  <img src="./assets/img/wd_icon.png" />
                  <span>{{ v.ChannelTemp}}℃</span>
                </div>
              </div>
              <!--  -->
              <!-- 时间 -->
              <div class="ml-14px mt-10px yuj_time">
                {{ v.TimeStamp }}
              </div>
              <div class="yu_warn">
                <p>处置</p>
              </div>
            </div>
          </div>
        </div>
      </template>
    </DataBox>

    <CenterTitle :title="centerTitleDesc"></CenterTitle>
    <!-- 实时监控 -->
    <DataBox class="top-116px right-25px box7" title="实时监控" >
      <template #mainFan>
        <div class="mt-10px ml-5px monitor_div">
          <div class="host">
            <!-- 主机号 -->
            <div class="host_div">
              <span class="host_span">主机号:
                <p>1号主机</p>
              </span>
            </div>
            <!-- 光纤号 -->
            <div class="host_div">
              <span class="host_span">光纤号:
                <p>1号通道</p>
              </span>
            </div>
          </div>
          <!-- 监控列表 -->
          <div class="monitor_table">
            <div class="monitor_title">
              <p class="ml-10px">位置</p>
              <p class="ml-40px">温度</p>
              <p class="ml-80px">时间</p>
              <p class="ml-100px">历史曲线</p>
            </div>
            <!-- 列表数据 -->
       <div class="tanle_bg_box">
      <div v-for="(item, index) in monitorData" :key="index" class="table_bg">
              <div class="table_content">
                <p class="w-60px pl-10px">{{ item.DevAddress }}</p>
                <p class="w-60px pl-10px">{{ item.RealValue }}℃</p>
                <p class="w-200px pl-40px">{{ formatTime(item.IOTime) }}</p>
                <div class="table_content_his"     @click="checkHistoryData(item.DevAddress)">
                  <p>查看</p>
                </div>




              </div>
            </div>
          </div>
            
          </div>
            <!-- 安全监测历史数据弹窗 -->
                <el-dialog title="历史数据" v-model="historyDialogShow" width="800rem">
                    <div v-if="historyData" class="">
                        <div class="w-90% h-446px" ref="hisEchart"></div>
                    </div>
                    <el-empty class="h-400rem" v-else description="暂无历史数据" />
                </el-dialog>
        </div>
      </template>
    </DataBox>

  
  </div>
</template>
<script setup>
import { ref,onMounted,watchEffect,watch,reactive  } from "vue";
import DataBox from "@/components/common/DataBox.vue";
import { useThree } from "@/three/useThree";
import { ApiFireprBase,ApiFireprRealValue,ApiFireprAlarm,ApiFireQx,ApiFireList } from "@/https/encapsulation/IntelligentFire"
import { onBeforeRouteLeave } from 'vue-router';
import eqump_icon from './assets/img/eqump_icon.png';
import xd_icon from './assets/img/xd_icon.png';
import length_icon from './assets/img/length_icon.png';
import sy_length_icon from './assets/img/sy_length_icon.png';
import temperature_icon from './assets/img/temperature_icon.png';
import bottomTab from "../../components/common/bottomTab.vue";
import * as echarts from 'echarts';
const startCamera = [1, 100, 8]
const endCamera = [3, 12, 10]
const gltfGlobal = ref()
const restMark = ref(false) // 重置标识
const zntf = ref(false)
let modelInit = ref()
modelInit.value = useThree({ startCamera, endCamera })
const historyData = ref()
const historyDialogShow = ref(false)
const hisEchart = ref(null)
// 详细信息请看 three/useThree/index.js 文件
const { mountRender, ani,stopRender, scene, camera, cameraControls, initModel, changeCurrentMesh, changeCurrentMeshByName, findMesh, restrictCameraVerticalDirection,
    matcapTexture, dbModelClick, executeMultipleAnimation, setMeshScale,removeMeshByName,
    executeAnimation, restCamera, addAmbientLight } = modelInit.value
// 相机重置函数
// function handleRestCamera() {
//     restCamera(() => {
//         isShow.value = false
//         restMark.value = true
//     })
// }
// 添加灯光 默认场景已经添加部分灯光 
// scene.add(addAmbientLight(0xffffff, 0.5))
// 限制相机垂直方向移动
restrictCameraVerticalDirection(cameraControls, Math.PI / 6, Math.PI / 5)
// 加载模型
// initModel('./model/tfj.glb', [0, 0, 0], (gltf) => {
initModel('./model/fanghuo.glb', [0, 0, 0], (gltf) => {
    if (gltf) {
        gltfGlobal.value = gltf;
        console.log(gltf, '局部通风机1111');
        // 移除物体模型模块
        // removeMeshByName(gltf.scene,'JBTFXT_HD1')
        // removeMeshByName(gltf.scene,'JBTFXT_HD2')
        // 缩放模型大小
        setMeshScale(gltf.scene,1.5)
        // // 需要进行贴图的物体名称 后续唐森会将物体名称进行修改 方便操作
        // const matName = ['TF_Wall_']
        // // // // // 贴图函数 根据模型上物体的名称选择需要进行贴图的物体
        // matcapTexture(gltf.scene, matName, './textures/287.png')

        /**
         *  初始化动画函数 详细介绍请看 three/utils/animat.js executeMultipleAnimation的第二个参数支持传多个动画 参数必须是数组 动画数组中传入多少个动画会执行多少个动画
         *  updateMultipleAnimation更新方法 需要传入下次动画与上次动画之间的时间间隔 传0不执行
         *  对于动画和模型的对应关系 可以问唐森
         *  executeAnimation用来执行单个动画 用法与 executeMultipleAnimation类似 前者只能传入一个动画对象 非数组形式
         */

        // 风机01风扇动画
        const { startMultipleAnimation, updateMultipleAnimation, stopMultipleAnimation } = executeMultipleAnimation(gltf.scene, [gltf.animations[0], gltf.animations[1]])
        // 风机02风扇动画
        const { startMultipleAnimation: startAni2, updateMultipleAnimation: updateAni2, stopMultipleAnimation: stopAni2 } = executeMultipleAnimation(gltf.scene, [gltf.animations[2], gltf.animations[3]])

        // 初始化风机选中  高亮
        // const mesh1 = findMesh(gltf.scene, 'JBTFXT_HD')
        // const mesh2 = findMesh(gltf.scene, 'JBTFXT_HD3')
       

        //  changeCurrentMesh(mesh1, { isFlyMesh: true })
        //  startMultipleAnimation()
        //  模型交互事件 双击模型 参数1:需要监测那些物体 就将哪些物体传进来 可以直接传gltf.scene 则所有物体都可被点击
        //   dbModelClick([mesh1, mesh2], camera, (mesh) => {
        //     const { name } = mesh.object;
        //     fanActive.value = name == 'JBTFXT_Wall' ? 0 : 1;
        //     executeFanSelect()
        // })

       
      // 监听复位方法
      watch(restMark, (val) => {
            if (val) {
                executeFanSelect()
            }
            setTimeout(() => {
                restMark.value = false
            }, 200)
        })
        // 切换风机 启动动画
        // function executeFanSelect() {
        //     if (fanActive.value == 0) {
        //         startMultipleAnimation()
        //         stopAni2()
        //         changeCurrentMeshByName('JBTFXT_Door_01', gltf.scene)
        //     } else {
        //         stopMultipleAnimation()
        //         startAni2()
        //         changeCurrentMeshByName('JBTFXT_HD2', gltf.scene)
        //     }
        // }

        // 渲染函数
        ani(function (...args) {
            const [time, camera, delta] = args
            // updateAnimation(delta)
            // 更新动画
            updateMultipleAnimation(0.15)
            updateAni2(0.15)
        })
    }

},
{
    // 初始化时就设置旋转，这样模型加载后就是水平横置的
    position:[0, 0, 0],
    scale: 2,
    rotation: { x: 0, y: -(Math.PI / 2.5), z: 0 },
    addModel: true,
  }

).then((gltf) => {
    zntf.value = true

})
// 查看指定物体信息
function checkMeshData(name) {
    isShow.value = false
    // 测试内容
    if (name == '上风门状态') {
        // 通过物体名称获取物体 并添加效果 或者显示标签 需要让那个物体高亮 就传入其对应的名称 目前物体名称没有做修改 使用其默认的名称
        // changeCurrentMeshByName('DJ_01', gltfGlobal.value.scene, {
        //     isLabel: true,
        //     // 当需要显示标签时 callback 的第一个参数可以获取到 标签的坐标位置
        //     callback: (val) => {
        //         isDataShow.value = val
        //         console.log(isDataShow.value);
        //     }
        // })
    } else {
        // changeCurrentMeshByName('DJ_02', gltfGlobal.value.scene, {
        //     isLabel: true,
        //     callback: (val) => {
        //         isDataShow.value = val
        //         console.log(isDataShow);
        //     }
        // })
    }
}


// 模型结束

// 模型挂载
onMounted(() => {
  mountRender(fireDiv.value);

});

//格式化时间截取时分
const formatTime = (timeStr) => {
  if (!timeStr) return "";
  // 截取 "HH:mm" 部分（假设格式为 "YYYY-MM-DD HH:mm:ss.xxx"）
  return timeStr.slice(0, 16); // 从索引11开始，取5个字符（如 "13:23"）
};
// 路由离开页面时触发
onBeforeRouteLeave((to, from, next) => {
  stopRender();
  modelInit.value = null;
  next();
  
});

const fireDiv = ref(null)
const fanActive = ref(0)
const centerTitleDesc = ref('光纤测温')

// bottomTab 组件值改变事件, 根据这个值取请求接口完成你的业务逻辑
// const handleTab = ({item:data}) => {
//     tabsData.value = data
//     centerTitleDesc.value = `${data.name}`

// }

const monitorData = ref({})
ApiFireList().then((res) =>{
console.log(res,'列表光纤列表')
  if(res.Status === 0){
    monitorData.value = res.Result
    
  }
})

// 动力监测-基本信息
ApiFireprBase().then((res)=>{

  
 
  if (res.Status === 0) {
    basicData.value.data[0].value = res.Result.WorkName;
    basicData.value.data[1].value = res.Result.RoadName;
    basicData.value.data[2].value = res.Result.DesignLenth;
    basicData.value.data[3].value = res.Result.ResidueLenth;
    basicData.value.data[4].value = res.Result.TempThreshold;

  }
 

})

function checkHistoryData(code) {
  
  
    ApiFireQx(code).then(res => {

        const { Result } = res;
        console.log(Result,'光纤3333')
        historyData.value = Result;
        historyDialogShow.value = true;

    }).then(() => {
        if (historyData.value) {
            renderEchart(code)

        }
    })
}
function renderEchart(code) {
 
    var myChart = echarts.init(hisEchart.value);
    var option = {
        // backgroundColor: '#123F5B',
        title: {
            text: code,
            top: "1%",
            textAlign: "left",
            left: "1%",
            textStyle: {
                color: "#38adb9",
                fontSize: 16,
                fontWeight: "600",
            },
        },
        grid: {
            left: 80,
            right: 20,
        },
        tooltip: {
            show: true,
            trigger: "axis",
            backgroundColor: "#0a2b45", // 设置背景颜色
            textStyle: {
                color: '#fff',
                fontSize: 14,
            },
            borderColor: "rgba(255, 255, 255, .16)",
            axisPointer: {
                lineStyle: {
                    color: "rgba(28, 124, 196)",
                },
            },
        },

        xAxis: [
            {
                type: "category",
                boundaryGap: false,
                axisLabel: {
                    align: "center",
                    color: "#eee",
                    textStyle: {
                        fontSize: 13,
                    },
                },
                data: historyData.value.map(v => v.IOTime.slice(0, 16))
            },
        ],

        yAxis: [
            {
                type: "value",
                name: "单位" + '℃' ,
                nameTextStyle: {
                    //y轴上方单位的颜色
                    color: "#eee",
                },

                splitLine: {
                    show: true,
                    lineStyle: {
                        color: "#eee",
                        // type: "dashed",
                    },
                },
                axisLabel: {
                    color: "#eee",
                    textStyle: {
                        fontSize: 14,
                    },
                },
                axisTick: {
                    show: false,
                },
            },
        ],
        series: [
            {
                data: historyData.value.map(v => v.RealValue),
                type: 'line',
                smooth: true,
                areaStyle: {}
            }
        ]
    };
    myChart.setOption(option);
}

// 动力监测
const basicData = ref({
  data: [
    {
      id: "1",
      name: "工作面名称:",
      value: '1613(3)工作面' ,
      icon: eqump_icon,
    },
    {
      id: "2",
      name: "巷道名称:",
      value: "1613(3)工作面",
      icon: xd_icon,
    },
    {
      id: "3",
      name: "设计长度:",
      value: "1370M",
      icon: length_icon,
    },
    {
      id: "4",
      name: "剩余长度:",
      value: "200M",
      icon: sy_length_icon,
    },
    {
      id: "5",
      name: "温度限值:",
      value: "26°C",
      icon: temperature_icon,
    },
  ],
});
const newData = basicData.value.data.map((item) => ({
  ...item,
  icon: new URL(item.icon, import.meta.url).href,
}));
basicData.value.data = newData;

//预警报警
const Position = ref();
const Temp = ref();
const bData = ref({})





</script>
<style lang="scss" scoped>
@use  './mainFan/assets/index.scss';

.basic {
  display: flex;
  flex-direction: column;
  margin-top: 2px;

  .basic_onepart {
    // background-image: url(../ventilateFacility/assets/img/jb_icon_bg.png);
    // background-size: 446px 40px;
    background-color:#060D15;
  border: 1px solid #3B3E42;
  border-left-color:#AB720D ;
    width: 446px;
    height: 50px;
    margin-top: 15px;
    margin-left: 21px;
    margin-right: 21px;
    border-radius: 5px;
  }

 

  .box5_name {
    // text-align: center;
    // vertical-align: middle;
    // width: 83px;
    display: flex;
  flex-wrap: wrap;
  padding-left: 15px;
  text-align: left;
  padding-top: 5px;
  color: #D9990F;
  // background-color: #161A1E;
  font-weight: bolder;
  .airdoor_ul{
    width: 100px;
  }

  .first_span {
  
  
    padding-left: 15px;
  }
  }

  .box5_value {
    width: 118px;
    height: 16px;
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #e98650;
    text-align: center;
    vertical-align: middle;
  }

  .box5_img {
    width: 20px;
    height: 20px;
    margin-left: 33px;
  }
}

.temputure_onepart {
  display: flex;
  justify-content: space-around;
  margin-top: 50px;
}

.temp_on {
  display: flex;
  flex-direction: row;
}

.temp_pg {
  position: relative;

  .temp_icon {
    width: 35px;
    height: 35px;
    position: absolute;
    left: 37px;
    top: 3px;
  }

  .temp_bg {
    width: 106px;
    height: 82px;
    position: absolute;
  }
}

.tem_font {
  display: flex;
  flex-direction: column;
  margin-left: 120px;
  color: #fff;
  margin-top: 20px;

  span:first-child {
    font-size: 26px;
    font-family: DIN;
    color: #ffffff;
  }

  span:nth-child(2) {
    padding-top: 10px;
    font-size: 16px;
    font-family: Source Han Sans CN;
    color: #ffffff;
    text-align: center;
  }
}
.box_earlywarning{
  overflow-y: auto;
  height: 340px;
  // justify-content: space-between;

      // 滚动条的颜色的大小
      &::-webkit-scrollbar {
        width: 2px;
    }
    &::-webkit-scrollbar-track {
        background-color:#0F3450;
    }
    &::-webkit-scrollbar-thumb {
        background: #0F3450;
        border-radius: 2px;
    }
    &::-webkit-scrollbar-thumb:hover {
        background-color: #555;
    }
}
.sData {
  width: 210px;
  height: 135px;
  background-color: #125274;
  border-radius: 5px;
  margin-left: 5px;
  margin-top: 10px;
  color: #fff;
}

.yuj_div {
  display: flex;
  flex-direction: column;
}

.pos_div {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  margin-top: 19px;
  margin-left: 14px;
  margin-right: 14px;
  font-size: 18px;
  font-family: Source Han Sans CN;
  color: #ffffff;

  img {
    width: 18px;
    height: 18px;
  }

  span {
    margin-left: 5px;
  }
}

.yu_warn {
  background-image: url(./assets/img/yuj_iconBg.png);
  width: 183.1px;
  height: 30.5px;
  background-size: 183.1px 30.5px;
  margin-left: 14px;
  margin-top: 20px;
  text-align: center;
  font-size: 16px;

  p {
    padding-top: 6px;
  }
}

.yuj_time {
  font-size: 16px;
  font-family: Source Han Sans CN;
  color: #61d5de;
}

.monitor_div {
  display: flex;
  flex-direction: column;
  margin-left: 21px;
  margin-right: 21px;
  color: #fff;

  .host {
    display: flex;
    justify-content: space-around;
    flex-direction: row;
  }

  .host_div {
    // background-image: url(./assets/img/box7_1bg.png);
    background-image: linear-gradient(90deg, rgb(226, 147, 8) 10%, rgb(40, 41, 43) 50%);
    background-size: 202px 50px;
    width: 202px;
    height: 50px;
    margin-left: 10px;

    .host_span {
      display: flex;
      flex-direction: row;
      font-size: 16px;
      font-family: Source Han Sans CN;
      margin-left: 40px;
      padding-top: 19px;
    }
  }

  // 实时监控
  .monitor_table {
    margin-top: 20px;
    font-size: 14px;
  }

  .monitor_title {
    width: 446px;
    height: 50px;
    background:rgba($color: #1C2125, $alpha: 0.5) ;
    border-radius: 6px;
    display: flex;
    flex-direction: row;
    padding-top: 15px;
    font-size: 16px;
    font-family: Source Han Sans CN;
    color: #D89B15;
    font-weight: bold;
  }
  .tanle_bg_box{
    height: 660px;
    overflow-y: auto;
     overflow-x: hidden;
     color: #D5CBAE;

        // 滚动条的颜色的大小
    &::-webkit-scrollbar {
        width: 2px;
    }
    &::-webkit-scrollbar-track {
        background-color:#555;
    }
    &::-webkit-scrollbar-thumb {
        background: #555;
        border-radius: 2px;
    }
    &::-webkit-scrollbar-thumb:hover {
        background-color: #555;
    }

  }
  .table_bg {
    width: 446px;
    height: 51px;
    
    margin-top: 10px;
  }

  .table_content {

    padding-top: 20px;
    padding-left: 5px;
    display: flex;
    flex-direction: row;
    border-bottom: 0.1px solid #9F7F1B;

    

    .table_content_his {
      
      width: 70px;
      height: 36px;
    
      margin-left: 30px;
      margin-top: -10px;

      p {
        padding-top: 10px;
        margin-left: 17px;
      }


    }
  }


}
</style>