<template>
  <!-- 防火在线 -->
  <div ref="fireDiv" class="canvasBox"></div>
  <div class="">
    <!-- 基本信息 -->
    <DataBox class="top-90px left-24px boxOnline_box1" title="基本信息">
      <template #mainFan>
        <div class="flex boxOnline_basic">
          <div v-for="(ba, i) in Onlinedata.data" :key="i" class="box1_div">
            <div class="flex pt-7px pl-10px">
              <img src="../ventilateFacility/airDoor/assets/img/cicle_aidoor.png" class="w-25px h-25px breathe_ani" />
              <div class="box1_div_title pl-10px pt-6px">
                <span class="color-#F1E4C2 online">{{ ba.name }}</span>
                <span class="pl-20px color-#EAA40E">{{ ba.value }}</span>
              </div>
            </div>
          </div>
        </div>
      </template>
    </DataBox>

    <!-- 综合评价 -->
    <DataBox class="top-410px left-24px boxOnline_box2" title="综合评价">
      <template #mainFan>
        <div class="Comprehensive_box2 mt-2px ml-15px">
          <div v-for="(com, i) in online2Data.data" :key="i" class="box2_div">
            <div class="box2_div_online">
              <div class="div_online">
                <img :src="com.icon1" class="float_ani" />
                <span class="absolute top-80px left-9px">{{
                  com.typetitle
                }}</span>
                <img src="./assets/img/box2_fire_undline.png" class="absolute top-70px left-24px w-93px h-8px" />

                <div class="div_box2_back">
                  <span>{{ com.valuename }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </DataBox>
    <CenterTitle :title="centerTitleDesc"></CenterTitle>
    <!-- 监测信息 -->
    <DataBox class="top-655px left-24px boxOnline_box3" title="监测信息">
      <template #mainFan>
        <div class="Monitoring_box3 mt-5px ml-32px">
          <div v-for="(mon, i) in messageData.data" :key="i" class="pt-10px">
            <div class="Monitoring_box3_div">
              <div class="pt-4px pl-10px flex">
                <img src="../ventilateFacility/airDoor/assets/img/cicle_aidoor.png" class="pt-2px" />

                <span class="pl-20px pt-6px  color-#E3D8B7  fire_cl">{{ mon.message }}:</span>
                <span class="ml-50px w-70px pt-6px color-#D9990F">{{
                  mon.value
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </template>
    </DataBox>

    <!-- 历史曲线 -->
    <DataBox class="top-112px right-25px boxOnline_box4" title="历史曲线">
      <template #mainFan>
        <div class="historicalCurve">
           <div 
        v-for="(chart, index) in processedChartData" 
        :key="index" 
        class="chart-item" 
        ref="chartRefs" 
      ></div>
        </div>
      </template>
    </DataBox>
    <bottomTab @change="handleTab" :bottomTabList="bottomTabList"  :allData="allData"></bottomTab>
  </div>
</template>
<script setup>
import { ref, onMounted, watch, watchEffect,reactive,computed } from "vue";
import DataBox from "@/components/common/DataBox.vue";
import { onBeforeRouteLeave } from "vue-router";
import chLine from "@/views/ventilateFacility/mainFan/components/chLine.vue";
import tempLine from "@/views/ventilateFacility/mainFan/components/tempLine.vue";
import O2Line from "@/views/ventilateFacility/mainFan/components/O2Line.vue";
import { useThree } from "@/three/useThree";
import fireOnline_icon4 from "./assets/img/fireOnline_icon4.png";
import fireOnline_icon5 from "./assets/img/fireOnline_icon5.png";
import fireOnline_icon6 from "./assets/img/fireOnline_icon6.png";
import fireOnline_icon7 from "./assets/img/fireOnline_icon7.png";
import fireOnline_co_icon from "./assets/img/fireOnline_co_icon.png";
import fireOnline_o_icon from "./assets/img/fireOnline_o_icon.png";
import fireOnline_temp_icon from "./assets/img/fireOnline_temp_icon.png";
import fireOnline_stess_icon from "./assets/img/fireOnline_stess_icon.png";
import bottomTab from "../../components/common/bottomTab.vue";
import * as echarts from 'echarts';
import dayjs from 'dayjs'; 
// 接口api
import { ApiFireproof } from "@/https/encapsulation/IntelligentFire";
import {ApiHistoricalCurve } from '@/https/encapsulation/Safety'
const startCamera = [1000, 300, 1000];
const endCamera = [400 * 0.7, 300, 600 * 0.95];
const gltfGlobal = ref();
const restMark = ref(false); // 重置标识
const zntf = ref(false);
let modelInit = ref();

modelInit.value = useThree({ startCamera, endCamera });
const centerTitleDesc = ref("1621（3）轨顺");

// 详细信息请看 three/useThree/index.js 文件
const {
  mountRender,
  ani,
  scene,
  camera,
  cameraControls,
  stopRender,
  initModel,
  changeCurrentMesh,
  changeCurrentMeshByName,
  findMesh,
  restrictCameraVerticalDirection,
  matcapTexture,
  dbModelClick,
  executeMultipleAnimation,
  setMeshScale,
  removeMeshByName,
  executeAnimation,
  restCamera,
  addAmbientLight,
} = modelInit.value;
// 相机重置函数
// function handleRestCamera() {
//     restCamera(() => {
//         isShow.value = false
//         restMark.value = true
//     })
// }
// 添加灯光 默认场景已经添加部分灯光
// scene.add(addAmbientLight(0xffffff, 0.5))
// 限制相机垂直方向移动
// restrictCameraVerticalDirection(cameraControls, Math.PI / 6, Math.PI / 5)
// 加载模型
// initModel('./model/tfj.glb', [0, 0, 0], (gltf) => {
initModel("./model/fanghuo1.glb", [0, 0, 0], (gltf) => {
  if (gltf) {
    gltfGlobal.value = gltf;
    // console.log(gltf, "局部通风机1111");
    // 移除物体模型模块
    // removeMeshByName(gltf.scene, "GS_FHJC_GD2");
    // removeMeshByName(gltf.scene,'JBTFXT_HD2')
    // 缩放模型大小
    setMeshScale(gltf.scene, 40);
    // // 需要进行贴图的物体名称 后续唐森会将物体名称进行修改 方便操作
    // const matName = ["GS_FHJC_Wall_inside"];
    // // // // 贴图函数 根据模型上物体的名称选择需要进行贴图的物体
    // matcapTexture(gltf.scene, matName, "./textures/389.png");

    /**
     *  初始化动画函数 详细介绍请看 three/utils/animat.js executeMultipleAnimation的第二个参数支持传多个动画 参数必须是数组 动画数组中传入多少个动画会执行多少个动画
     *  updateMultipleAnimation更新方法 需要传入下次动画与上次动画之间的时间间隔 传0不执行
     *  对于动画和模型的对应关系 可以问唐森
     *  executeAnimation用来执行单个动画 用法与 executeMultipleAnimation类似 前者只能传入一个动画对象 非数组形式
     */

    // 风机01风扇动画
    const {
      startMultipleAnimation,
      updateMultipleAnimation,
      stopMultipleAnimation,
    } = executeMultipleAnimation(gltf.scene, [
      gltf.animations[0],
      gltf.animations[1],
    ]);
    // 风机02风扇动画
    const {
      startMultipleAnimation: startAni2,
      updateMultipleAnimation: updateAni2,
      stopMultipleAnimation: stopAni2,
    } = executeMultipleAnimation(gltf.scene, [
      gltf.animations[2],
      gltf.animations[3],
    ]);

    // 初始化风机选中  高亮
    // const mesh1 = findMesh(gltf.scene, 'JBTFXT_HD')
    // const mesh2 = findMesh(gltf.scene, 'JBTFXT_HD3')

    //  changeCurrentMesh(mesh1, { isFlyMesh: true })
    // startMultipleAnimation()
    // // 模型交互事件 双击模型 参数1:需要监测那些物体 就将哪些物体传进来 可以直接传gltf.scene 则所有物体都可被点击
    // dbModelClick([mesh1, mesh2], camera, (mesh) => {
    //     const { name } = mesh.object;
    //     fanActive.value = name == 'JBTFXT_Wall' ? 0 : 1;
    //     executeFanSelect()
    // })

    // 监听复位方法
    // 监听复位方法
    watch(restMark, (val) => {
      if (val) {
        executeFanSelect();
      }
      setTimeout(() => {
        restMark.value = false;
      }, 200);
    });
    // 切换风机 启动动画
    // function executeFanSelect() {
    //     if (fanActive.value == 0) {
    //         startMultipleAnimation()
    //         stopAni2()
    //         changeCurrentMeshByName('JBTFXT_Door_01', gltf.scene)
    //     } else {
    //         stopMultipleAnimation()
    //         startAni2()
    //         changeCurrentMeshByName('JBTFXT_HD2', gltf.scene)
    //     }
    // }

    // 渲染函数
    ani(function (...args) {
      const [time, camera, delta] = args;
      // updateAnimation(delta)
      // 更新动画
      updateMultipleAnimation(0.15);
      updateAni2(0.15);
    });
  }
}).then((gltf) => {
  zntf.value = true;
});
// 查看指定物体信息
function checkMeshData(name) {
  isShow.value = false;
  // 测试内容
  if (name == "上风门状态") {
    // 通过物体名称获取物体 并添加效果 或者显示标签 需要让那个物体高亮 就传入其对应的名称 目前物体名称没有做修改 使用其默认的名称
    // changeCurrentMeshByName('DJ_01', gltfGlobal.value.scene, {
    //     isLabel: true,
    //     // 当需要显示标签时 callback 的第一个参数可以获取到 标签的坐标位置
    //     callback: (val) => {
    //         isDataShow.value = val
    //         console.log(isDataShow.value);
    //     }
    // })
  } else {
    // changeCurrentMeshByName('DJ_02', gltfGlobal.value.scene, {
    //     isLabel: true,
    //     callback: (val) => {
    //         isDataShow.value = val
    //         console.log(isDataShow);
    //     }
    // })
  }
}

// 模型结束

// 模型挂载
onMounted(() => {
  mountRender(fireDiv.value);
  checkHistoryData();
});
// 路由离开页面时触发
onBeforeRouteLeave((to, from, next) => {
  stopRender();
  modelInit.value = null;
  window.removeEventListener("wheel", () => { });
  next();
});
//底部
const allData = reactive([]);
const bottomTabList = ref([]);
const tabsData = ref({})
const fireDiv = ref(null);
const fanActive = ref(0);
// 接口返回的原始历史数据

// 处理后的数据（按设备分组 + 时间排序）
const chartData = ref([]);  
// ECharts 容器引用（数组，对应模板的 v-for）
const chartRefs = ref([]);  
// 3. 处理后的数据（分组 + 排序，自动响应 historyData 变化）
const processedChartData = computed(() => {
  return processChartData(historyData.value);
});
const historyData = ref([]); 

// bottomTab 组件值改变事件, 根据这个值取请求接口完成业务逻辑
const handleTab = ({item:data,allData:result}) => {
  tabsData.value = data
  centerTitleDesc.value = `${data.name}`
 
  const current = result.find(item=>item.ID === data.id);
   handleChangePageInfo(current)
}
//接口列表
ApiFireproof().then((fire) => {
// console.log(fire.Result[0].MonitInfo,'333333333333333333333333')
  if (fire.Status === 0) {
    // 基础信息
    // Onlinedata.value = fire.Result;
  
    // 底部tab数据
    bottomTabList.value.length = 0;
    (fire?.Result||[]).forEach(item=>{
      // console.log(item,'ces')
      bottomTabList.value.push({
        id: item.ID,
        name: item.DevAddress,
        DevCode: item.DevCode,
       
      })
    })
    allData.push(...fire.Result);
  
    fire?.Result.forEach((resultItem) => { 
      if (fire.Status === 0) {
      (resultItem.MonitInfo || []).forEach((monitorItem) => {
        checkHistoryData(monitorItem.DevCode);
        // const devCode = monitorItem.DevCode; // 拿到设备级的 DevCode
        // checkHistoryData(devCode);           // 调用历史曲线接口
      });
      }
    });

     // 默认第一个
    handleTab({
      item:bottomTabList.value[0],
      allData:fire.Result
    });
  }


});
const handleChangePageInfo = ({...rest})=>{
  
   messageData.value.data[0].value = rest.MonitInfo[0].RealState
   messageData.value.data[0].message = rest.MonitInfo[0].Lx
   messageData.value.data[1].value = rest.MonitInfo[1].RealState
   messageData.value.data[1].message = rest.MonitInfo[1].Lx
   messageData.value.data[2].value = rest.MonitInfo[2].RealState
   messageData.value.data[2].message = rest.MonitInfo[2].Lx
   messageData.value.data[3].value = rest.MonitInfo[3].RealState
   messageData.value.data[3].message = rest.MonitInfo[3].Lx

   Onlinedata.value.data[0].value = rest.DevAddress
   
    Onlinedata.value.data[1].value = rest.Num
   
    Onlinedata.value.data[2].value = rest.Name
 
    Onlinedata.value.data[3].value = rest.Type


    Onlinedata.value.data[4].value = rest.WallStruc

 


}
// 格式化日期为指定字符串格式
function formatDate(date, format) {
  const pad = (num) => num.toString().padStart(2, '0');
  return (
    date.getFullYear() + '-' + pad(date.getMonth() + 1) + '-' + pad(date.getDate()) + ' ' + 
    pad(date.getHours()) + ':' + pad(date.getMinutes()) + ':' + pad(date.getSeconds())
  );
}


function checkHistoryData(code) {
  const now = new Date();
  // 构造当日 00:00:00
  const startDate = new Date(now);
  startDate.setHours(0, 0, 0);
  const start = formatDate(startDate, 'YYYY-MM-DD HH:mm:ss');

  // 构造当日 23:59:59
  const endDate = new Date(now);
  endDate.setHours(23, 59, 59);
  const end = formatDate(endDate, 'YYYY-MM-DD HH:mm:ss');

  // 调用历史数据接口
  ApiHistoricalCurve({ startTime: start, endTime: end, code }).then((res) => {
    const { Result } = res.data;
    // historyData.value = Result; // 存储原始数据，触发 processedChartData 更新
     historyData.value.push(...(Result || [])); 
  });
}
function processChartData(rawData) {
  if (!rawData || rawData.length === 0) return [];

  const grouped = {};
  rawData.forEach((item) => {
    const code = item.DevCode;
    if (!grouped[code]) {
      grouped[code] = {
        devCode: code,
        devTypeName: item.DevTypeName, // 设备类型（如“乙烯”）
        times: [],
        values: [],
      };
    }
    // 收集时间和数值
    grouped[code].times.push(item.IOTime);
    grouped[code].values.push(Number(item.RealValue));
  });

  // 按时间排序（确保曲线时序正确）
  Object.values(grouped).forEach((group) => {
    // 配对时间和值，再按时间排序
    const paired = group.times.map((t, i) => [t, group.values[i]]);
    paired.sort((a, b) => new Date(a[0]) - new Date(b[0]));
    
    // 格式化时间（如 2025-07-15 17:06:25 → 07-15 17:06）
    group.formattedTimes = paired.map((p) => {
      const [date, time] = p[0].split(' ');
      return `${date.slice(5)} ${time.slice(0, 5)}`; 
    });
    group.sortedValues = paired.map((p) => p[1]); // 排序后的数值
  });

  return Object.values(grouped);
}
// 监听 processedChartData 变化，自动渲染
watch(processedChartData, (newChartData) => {
  renderCharts(newChartData);
});

function renderCharts(chartData) {
  chartData.forEach((chart, index) => {
    const dom = chartRefs.value[index]; // 取对应 DOM 容器
    if (!dom) return;

    const myChart = echarts.init(dom);
    const option = {
      title: { 
        text: `${chart.devTypeName}: ${chart.devCode}`, // 标题：乙烯: 71A11
        textStyle: { color: '#38adb9', fontSize: 16 }
      },
      xAxis: {
        type: 'category',
        data: chart.formattedTimes, // 格式化后的时间（07-15 17:06）
        axisLabel: { color: '#fff' }, 
        axisTick: {
          //刻度相关
          show: false, //是否显示刻度
          alignWithLabel: true, //是否对齐标签
        },

      },
      yAxis: [{
        type: 'value',
        axisLabel: { 
          color: '#fff',
          formatter: `{value} ${chart.Unit || ''}` // 补充单位（如 Prm）
        },
        axisTick: {
          //刻度相关
          show: false, //是否显示刻度
          alignWithLabel: true, //是否对齐标签
        },
           splitLine: {
          // 网格线
          show: false, // 是否显示，默认为true
          lineStyle: {
            color: ["#1A5A78"],
            width: 1,
            type: "solid",
          },
        },
      }],
      series: [{
        type: 'line',
        data: chart.sortedValues,
        smooth: true, // 平滑曲线
        areaStyle: {}, // 面积填充（模拟设计稿）
        itemStyle: { color: getColor(chart.devTypeName) } // 按类型配色
      }],
      tooltip: { trigger: 'axis' },
      backgroundColor: 'transparent'
    };
    myChart.setOption(option);

    // 窗口resize自适应
    window.addEventListener('resize', () => myChart.resize());
  });
}

// 辅助：按设备类型配色（可扩展）
function getColor(type) {
  const colorMap = {
    '乙烯': '#c27b44',
    '一氧化碳': '#4bb677',
    '二氧化碳': '#4bc2d2',
    // 其他设备类型...
  };
  return colorMap[type] || '#ffd700';
}
// 按设备
// 基础信息
const Onlinedata = ref({
  data: [
    {
      
     
      name: "封闭地点:",
      value: "",
    },
    {
      
     
      name: "封闭墙编号：",
      value: "",
    },
      {
    
      name: "封闭墙名称：",
      value: "",
    },
    {
    
      name: "封闭墙类型：",
      value: "",
    },
    {
    
      name: "封闭墙结构：",
      value: "",
    },


    // {
    //   icon: "fireOnline_icon3.png",
    //   name: "停采时间：",
    //   value: "2023年6月30日",
    // },
    // {
    //   icon: "fireOnline_icon3.png",
    //   name: "封闭时间：",
    //   value: "2023年7月30日",
    // },
  ],
});

const forData = Onlinedata.value.data.map((item) => ({
  ...item,
  icon: new URL(`./assets/img/${item.icon}`, import.meta.url).href,
}));
Onlinedata.value.data = forData;
// icon: air_quantity,
// 综合评价
const online2Data = ref({
  data: [
    {
      id: "1",
      icon1: fireOnline_icon4,
      typetitle: "密闭检查",
      valuename: "正常",
    },
    {
      id: "2",
      icon1: fireOnline_icon5,
      typetitle: "气体检测",
      valuename: "正常",
    },
    {
      id: "3",
      icon1: fireOnline_icon6,
      typetitle: "防火指标",
      valuename: "正常",
    },
  ],
});

const icon2Data = online2Data.value.data.map((item) => ({
  ...item,
  icon1: new URL(item.icon1, import.meta.url).href,
}));
online2Data.value.data = icon2Data;

//监测信息
const messageData = ref({
  data: [
    {
      icon3: fireOnline_icon7,
      message: "",
      value: "",
    },
    {
      icon3: fireOnline_co_icon,
      message: "",
      value: "",
    },

    {
      icon3: fireOnline_o_icon,
      message: "",
      value: "",
    },

    {
      icon3: fireOnline_temp_icon,
      message: "",
      value: "",
    },
    // {
    //   icon3: fireOnline_stess_icon,
    //   message: "压力:",
    //   value: "0.00KPa",
    // },
  ],
});

const icon3Data = messageData.value.data.map((item) => ({
  ...item,
  icon3: new URL(item.icon3, import.meta.url).href,
}));
messageData.value.data = icon3Data;
</script>
<style lang="scss" scoped>
@use './mainFan/assets/index.scss';

.boxOnline_basic {
  display: flex;
  flex-direction: column;
  // justify-content: space-between;
  color: #fff;
  font-size: 14px;

  .box1_div {
    width: 460px;
    height: 40px;
    margin-top: 10px;
    margin-left: 15px;
    margin-right: 15px;
    background-color: #060d15;
    border: 1px solid #3b3e42;
    border-left-color: #ab720d;
    // background-image: url(./assets/img/basic_backgroud.png);
  }

  .box1_div_title {
    display: flex;
    flex-direction: wrap;
    font-weight: bolder;


  }

  .online {
    width: 115px;
  }
}

.Comprehensive_box2 {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  color: #fff;

  .box2_div_online {
    display: flex;
    flex-direction: column;
    margin-top: 20px;

    .div_online {
      background-image: url(./assets/img/fireOnline_icon_background.png);
      width: 80px;
      height: 74px;
      background-size: 80px 74px;
      position: relative;

      img {
        width: 26px;
        height: 26px;
        position: absolute;
        top: 21px;
        left: 26px;
      }
    }

    .div_box2_back {
      background-image: url(./assets/img/background_box2_icon.png);
      width: 116px;
      height: 27px;
      background-size: 116px 27px;
      position: absolute;
      top: 110px;
      text-align: center;
      padding-top: 5px;
      left: -19px;
    }
  }
}

.Monitoring_box3 {
  display: flex;
  flex-direction: column;
  color: #fff;
}

.Monitoring_box3_div {

  width: 435px;
  height: 40px;
  background-color: #060D15;
  border: 1px solid #3B3E42;
  border-left-color: #AB720D;
  font-weight: bolder;
  // background-size: 421px 51px;

  img {
    width: 28px;
    height: 28px;
  }

  .fire_cl {
    width: 100px;
  }
}
.historicalCurve {
  display: flex;
  flex-direction: column;
  gap: 20px; /* 图表间距 */
}
.chart-item {
  width: 100%;
  height: 300px; /* 固定高度，或自适应 */
}
// .historicalCurve {
//   display: flex;
//   flex-direction: column;
//   margin-top: 10px;
//   color: #fff;
//   font-size: 16px;

//   .title1 {
//     background-image: url(./assets/img/group_line_background.png);
//     width: 298px;
//     height: 45px;
//     background-size: 298px 45px;
//     margin-left: 100px;
//     text-align: center;
//     padding-top: 15px;
//     margin-top: 5px;
//   }
// }
</style>