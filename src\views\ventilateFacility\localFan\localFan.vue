<template>
  <div ref="sceneDiv1" class="canvasBox"></div>
  <div v-show="zntf" ref="editCz">
    <div class="toSee" ref="referSizeRef"></div>

    <div
      class="fixed"
      :style="{
        transform: `scale(${mainSize.cs})`,
      }"
      v-for="(item, index) in ourData"
      :key="index"
    >
      <!-- 动力监测 -->
      <DataBox
        class="top-116px left-24px box1"
        title="动力监测"
        :tabNameList="['主扇', '备扇']"
        @sendFanActive="sendPower($event, index)"
        v-if="item.BoardName == '动力监测'"
        :style="{
          width: item.W + sizeUnit,
          height: item.H + sizeUnit,
          left: item.X + sizeUnit,
          top: item.Y + sizeUnit,
        }"
      >
        <template #mainFan>
          <div
            class="power-parent"
            style="position: relative; overflow: hidden"
          >
            <div
              class="power-wrapper el-carousel__item is-active is-animating"
              :class="['first-part' + index]"
              style="height: 400px"
            >
              <div class="flex justify-between">
                <div class="relative ml-74px">
                  <img
                    src="../mainFan/assets/img/dljc.png"
                    class="w-163px h-131.6px"
                  />
                  <img
                    src="../mainFan/assets/img/fan_dljj.png"
                    class="z-2 absolute w-44px h-40px top-25px left-58px turn"
                  />
                </div>
                <div class="ml-35px mt-30px">
                  <div class="fan_state_right mt-20px">
                    <span>主扇正在运行</span>
                  </div>
                </div>
              </div>
              <!-- 主扇 -->
              <div class="flex grid grid-cols-2 w-488px flex-wrap mt-15px">
                <div
                 
                  v-for="(v, i) in item.option?.list[0] || []"
                  :key="v.id"
                  class="flex items-center"
                  :class="i > 1 ? 'mt-40px' : ''"
                   @click="checkMeshData(v, 1)"
                >
                  <div class="relative">
                    <img
                      :src="bule_decorate"
                      class="diffuse_ani w-100px h-56px"
                    />
                    <img
                      :src="air_quantity"
                      class="acaleph_ani z-2 absolute w-30px h-30px top-[-5px] left-36px"
                    />
                  </div>
                  <div class="mt-[-18px]">
                    <div class="box1_value">
                      <span>{{ v.label }}</span>
                    </div>
                    <div class="box1_name">
                      <span>{{ v.value }}{{ v.Unit }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 备扇 -->
            <div
              class="power-wrapper is-animating"
              :class="['second-part' + index]"
              style="transform: translateX(500px); height: 400px"
            >
              <div class="flex justify-between">
                <div class="relative ml-74px">
                  <img
                    src="../mainFan/assets/img/dljc.png"
                    class="w-163px h-131.6px"
                  />
                  <img
                    src="../mainFan/assets/img/fan_dljj.png"
                    class="z-2 absolute w-44px h-40px top-25px left-58px turn"
                  />
                </div>
                <div class="ml-35px mt-30px">
                  <div class="fan_state_error mt-20px">
                    <span>备扇停止运行</span>
                  </div>
                </div>
              </div>

              <div class="flex justify-around w-100% flex-wrap mt-15px">
                <div
                  @click="checkMeshData(v, 2)"
                  v-for="(v, i) in item.option?.list[1]"
                  :key="v.id"
                  class="flex items-center"
                  :class="i > 1 ? 'mt-40px' : ''"
                >
                  <div class="relative">
                    <img
                      :src="bule_decorate"
                      class="diffuse_ani w-100px h-56px"
                    />
                    <img
                      :src="air_quantity"
                      class="acaleph_ani z-2 absolute w-30px h-30px top-[-5px] left-36px"
                    />
                  </div>
                  <div class="mt-[-18px]">
                    <div class="box1_value">
                      <span>{{ v.label }}</span>
                    </div>
                    <div class="box1_name">
                      <span>{{ v.value }}{{ v.Unit }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </DataBox>

      <!-- 环境监测 -->
      <DataBox
        class="top-593px left-24px box2"
        title="环境监测-1"
        :tabNameList="['主扇', '备扇']"
        @sendFanActive="sendEvion($event, index)"
        v-if="item.BoardName == '环境监测-1'"
        :style="{
          width: item.W + sizeUnit,
          height: item.H + sizeUnit,
          left: item.X + sizeUnit,
          top: item.Y + sizeUnit,
        }"
      >
        <template #mainFan>
          <!-- 环境onepart -->
          <div class="tep-parent" style="position: relative; overflow: hidden">
            <div
              class="tep-wrapper el-carousel__item is-active is-animating tem-onews"
              style="height: 400px"
            >
              <div class="environmental ml-74px mt-30px">
                <div class="envir_one diffuse_ani">
                  <img src="./assets/img/icon_hjjc.png" />
                </div>
                <div class="envir_one_ul">
                  <div class="envir_one_co float_ani">
                    <p>迎头co:0</p>
                  </div>
                  <div class="envir_one_ws float_ani">
                    <p>迎头瓦斯:0</p>
                  </div>
                  <div class="envir_one_gd float_ani">
                    <p>供电点瓦斯:0</p>
                  </div>
                  <div class="envir_one_yt float_ani">
                    <p>迎头温度:27.5</p>
                  </div>
                  <div class="envir_one_hui float_ani">
                    <p>回风巷风速:3.9</p>
                  </div>
                </div>
              </div>
            </div>

            <div
              class="tep-wrapper is-animatin tem-twows"
              style="transform: translateX(500px); height: 400px"
            >
              <div class="environmental ml-74px mt-25px">
                <div class="envir_one diffuse_ani">
                  <img src="./assets/img/icon_hjjc.png" />
                </div>
                <div class="envir_one_ul">
                  <div class="envir_one_co float_ani">
                    <p>迎头co:0</p>
                  </div>
                  <div class="envir_one_ws float_ani">
                    <p>迎头瓦斯:0</p>
                  </div>
                  <div class="envir_one_gd float_ani">
                    <p>供电点瓦斯:0</p>
                  </div>
                  <div class="envir_one_yt float_ani">
                    <p>迎头温度:27.5</p>
                  </div>
                  <div class="envir_one_hui float_ani">
                    <p>回风巷风速:3.9</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </DataBox>

      <!-- 控制面板 -->
      <DataBox
        class="top-116px right-25px box3"
        title="控制面板"
        v-if="item.BoardName == '控制面板'"
        :style="{
          width: item.W + sizeUnit,
          height: item.H + sizeUnit,
          left: item.X + sizeUnit,
          top: item.Y + sizeUnit,
        }"
      >
        <template #mainFan>
          <div
            class="control_parent"
            style="position: relative; overflow: hidden; height: 200px"
          >
            <div
              class="control_wrapper one_control el-carousel__item is-active is-animating"
              style="height: 200px"
            >
              <div
                class="flex justify-around my-15px mx-10px"
                @click="openStop"
              >
                <div class="box3_btn_bg1">
                  <div class="pt-21px pl-31px">
                    <img
                      class="w-35px h-35px"
                      src="../mainFan/assets/img/start_stop.png"
                    />
                  </div>
                  <div><span>一键启停</span></div>
                </div>
                <!-- <div class="box3_btn_bg2">
                  <div class="pt-21px pl-31px">
                    <img class="w-35px h-35px" src="../mainFan/assets/img/reverse.png" />
                  </div>
                  <div><span>一键反向</span></div>
                </div> -->
                <div class="box3_btn_bg3">
                  <div class="pt-21px pl-31px">
                    <img
                      class="w-35px h-35px"
                      src="../mainFan/assets/img/downfall.png"
                    />
                  </div>
                  <div><span>一键倒台</span></div>
                </div>
                <div class="flex flex-col justify-around">
                  <div class="box3_btn2_bg1 flex justify-around items-center">
                    <div>
                      <img
                        class="w-20px h-20px"
                        src="../mainFan/assets/img/yc_control.png"
                      />
                    </div>
                    <div><span>远程控制</span></div>
                  </div>
                  <div class="box3_btn2_bg2 flex justify-around items-center">
                    <div>
                      <img
                        class="w-20px h-20px"
                        src="../mainFan/assets/img/blade_angle.png"
                      />
                    </div>
                    <div><span>工作频率</span></div>
                  </div>
                </div>
          
              </div>

                        <!-- 新增：自动/手动/远程控制开关 -->
      <div class="flex ml-340px control-switch-group">
         <div class="switch-group" @click="openStop">
    <!-- 自动控制 -->
    <div class="switch-item" @click="toggle('auto')">
      <div class="switch-container">
        <div 
          class="switch-slider" 
          :class="{ on: states.auto }"
        ></div>
      </div>
      <span>智能调速</span>
    </div>
    </div>
    </div>
            </div>
          </div>
        </template>
      </DataBox>
      <!-- 视频监控 -->
      <DataBox
        class="top-436px right-25px box4"
        title="视频监控"
        v-if="item.BoardName == '视频监控'"
        :style="{
          width: item.W + sizeUnit,
          height: item.H + sizeUnit,
          left: item.X + sizeUnit,
          top: item.Y + sizeUnit,
        }"
      >
        <template #mainFan>
          <div class="mt-15px flex items-center">
            <div
              @click="handleVideoMonitorListLeftRight('left')"
              class="cursor-pointer"
            >
              <img
                class="w-33px h-58px ml-18px"
                src="../mainFan/assets/img/switch_arrow.png"
              />
            </div>
            <div class="">
              <div class="flex justify-around w-386px transition-all delay-600">
                <div
                  class="box4_camera"
                  @click="handleVideoMonitorDetail(item)"
                  v-for="(item, i) in videoMonitorList"
                  :key="i"
                >
                  <div>
                    <img
                      class="w-77px h-88px"
                      src="../mainFan/assets/img/camera.png"
                    />
                  </div>
                  <div class="mt-8px">
                    <span>{{ item.Name }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div
              @click="handleVideoMonitorListLeftRight('right')"
              class="cursor-pointer"
            >
              <img
                class="w-33px h-58px mr-18px rotate-180"
                src="../mainFan/assets/img/switch_arrow.png"
              />
            </div>
          </div>
        </template>
      </DataBox>
      <!-- 故障诊断 -->
      <DataBox
        class="top-695px right-25px boxError"
         title="故障诊断"
        :tabNameList="['主扇', '备扇']"
        @sendFanActive="sendFaultlc($event, index)"
       v-if="item.BoardName == '故障诊断'"
        :style="{
          width: item.W + sizeUnit,
          height: item.H + sizeUnit,
          left: item.X + sizeUnit,
          top: item.Y + sizeUnit,
        }"
      >
        <template #mainFan>
          <div
            class="fault1-parent"
            style="position: relative; overflow: hidden"
          >
            <!-- one-part -->
            <div
              class="fault1-wrapper el-carousel__item is-active is-animating"
              :class="['one-fault1' + index]"
              style="height: 400px"
            >
              <div class="px-22px">
                <div>
                  <div class="w-100% flex justify-around mt-5px">
                    <div class="box5_tab" @click="changeBox5Tab(item, 0)">
                      <span>实时数据</span>
                      <div
                        :class="item.option?.tab == 0 ? 'box5_tab_active' : ''"
                      ></div>
                    </div>
                    <div class="box5_tab" @click="changeBox5Tab(item, 1)">
                      <span>历史报警数据</span>
                      <div
                        :class="item.option?.tab == 1 ? 'box5_tab_active' : ''"
                      ></div>
                    </div>
                  </div>
                  <div
                    class="w-100% h-1px mt-23px border border-solid border-[#52595F]"
                  ></div>
                </div>
                <div
                  class="flex justify-between w-100% flex-wrap"
                  v-if="item.option?.tab === 0"
                >
                  <div
                    v-for="(v, i) in item.option?.list[0] || []"
                    :key="v.id"
                    :class="i > 2 ? 'mt-20px' : 'mt-20px'"
                    @click="checkMeshData(v, 2)"
                    class="cursor-pointer"
                  >
                    <div class="box5_data_name">
                      <span>{{ v.label }}</span>
                    </div>
                    <div class="box5_data_value_bg">
                      <span class="box5_data_value">{{ v.value }}</span>
                    </div>
                  </div>
                </div>

                <div
                  class="flex w-100% box5-tab1"
                  v-if="item.option?.tab === 1"
                >
                  <div
                    v-for="(v, i) in item.option?.list[0]"
                    :key="i"
                    class="item-box"
                  >
                    <div class="center_guz">
                      <span>1#液压泵</span>
                      <span>故障</span>
                    </div>
                    <div class="time">2022-12-16 03:19:46</div>
                  </div>
                </div>
              </div>
            </div>
            <!-- two-part -->
            <div
              class="fault1-wrapper is-animating"
              :class="['two-fault1' + index]"
              style="transform: translateX(500px); height: 400px"
            >
              <div class="px-22px">
                <div>
                  <div class="w-100% flex justify-around mt-5px">
                    <div class="box5_tab" @click="changeBox5Tab(item, 0)">
                      <span>实时数据</span>
                      <div
                        :class="item.option?.tab == 0 ? 'box5_tab_active' : ''"
                      ></div>
                    </div>
                    <div class="box5_tab" @click="changeBox5Tab(item, 1)">
                      <span>历史报警数据</span>
                      <div
                        :class="item.option?.tab == 1 ? 'box5_tab_active' : ''"
                      ></div>
                    </div>
                  </div>
                  <div
                    class="w-100% h-1px mt-23px border border-solid border-[#0BA4EE]"
                  ></div>
                </div>
                <div
                  class="flex justify-between w-100% flex-wrap"
                  v-if="item.option?.tab === 0"
                >
                  <div
                    v-for="(v, i) in item.option?.list[1]"
                    :key="v.id"
                    :class="i > 2 ? 'mt-20px' : 'mt-20px'"
                    @click="checkMeshData(v, 2)"
                    class="cursor-pointer"
                  >
                    <div class="box5_data_name">
                      <span>{{ v.label }}</span>
                    </div>
                    <div class="box5_data_value_bg">
                      <span class="box5_data_value">{{ v.value }}</span>
                    </div>
                  </div>
                </div>

                <div
                  class="flex w-100% box5-tab1"
                  v-if="item.option?.tab === 1"
                >
                  <div
                    v-for="(v, i) in item.option?.list[1]"
                    :key="i"
                    class="item-box"
                  >
                    <div class="center_guz">
                      <div class="name">1#液压泵</div>
                      <div class="val">
                        <span>故障</span>
                      </div>
                    </div>
                    <div class="time">2022-12-16 03:19:46</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </DataBox>
    </div>
    <CenterTitle :title="centerTitleDesc"></CenterTitle>
<bottomTab @change="handleTab" :bottomTabList="bottomTabList" :allData="[]"></bottomTab>

    <!--历史数据标签弹窗 -->
    <div v-show="isShow">
      <HistoryDialog :data="currentData" @labelDialogEL="getLabelDialogEL" />
    </div>

    <!-- 弹窗 -->
    <el-dialog
      v-model="dialogShow"
      draggable
      :title="dialogTitle"
      style="width: 850rem; height: 500rem"
      @close="dialogShowClose"
    >
      <!-- 视频播放 -->
      <canvas
        id="playCanvass"
         ref="playCanvas"
        style="
          background-color:black;
         
         width: 670px; height: 350px;
          margin-left: 15px;
          margin-top: 10px;
        "
      ></canvas>
    </el-dialog>

    <!-- 控制表单 -->
    <el-dialog
      title="验证面板"
      draggable
      :lock-scroll="false"
      modal-class="abc"
      style="height: 300rem"
      :modal="false"
      width="400px"
      v-model="controlDialog"
      @close="onClose"
    >
      <el-form
        class="mt-40px"
        :model="form"
        ref="form"
        :rules="rules"
        label-width="100px"
        :inline="false"
        size="normal"
      >
        <el-form-item
          label="登录密码："
          style="color: white"
          :rules="[
            {
              required: true,
              message: '请输入密码',
              trigger: 'blur',
            },
          ]"
        >
          <el-input
            type="password"
            v-model="password"
            placeholder="请输入登录密码"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer mt-50px">
          <el-button size="medium" @click="handleCancel">取 消</el-button>
          <el-button size="medium" type="primary" @click="handleOk"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import {
  ref,
  reactive,
  onMounted,
  watch,
  watchEffect,
  onBeforeUnmount,
  onUnmounted,
  nextTick 
} from "vue";
import { onBeforeRouteLeave } from "vue-router";
import DataBox from "@/components/common/DataBox.vue";
import CenterTitle from "@/components/common/CenterTitle.vue";
import HistoryDialog from "@/components/common/HistoryDialog.vue";
// localFan.glb
import bottomTab from "../../../components/common/bottomTab.vue";

import air_quantity from "../mainFan/assets/img/tianx_icon.png";
import negative_pressure from "./assets/img/negative_pressure.png";
import voltage from "./assets/img/voltage.png";
import x_fan from "./assets/img/x_fan.png";
import angular_surveying from "./assets/img/angular_surveying.png";
import transient_current from "./assets/img/transient_current.png";
import green_decorate from "./assets/img/green_decorate.png";
import yellow_decorate from "./assets/img/yellow_decorate.png";
import bule_decorate from "../mainFan/assets/img/tianx-bg.png";
import { useThree } from "@/three/useThree";
import { ApiLocalFanList,ApiStopVideo,ApiVideo,ApiPlayVideo,ApiPlayVideo1 } from "@/https/encapsulation/LocalFan";

import Hls from 'hls.js';
//页面配置页的接口
import { ApiConfigurationList } from "@/https/encapsulation/Configuration";
import { moveToCamera } from "@/three/cameraControls";
import { changeLabelPosition } from "@/hooks/useLabelData";
// 故障的字典
import { configStore } from "@/store/config";
import { storeToRefs } from "pinia";
const store = configStore();
const { devicesMap = [] } = storeToRefs(store);
const data = reactive({
  value: "100",
  value1: false,
});

const sizeUnit = ref("rem");
const mainSize = ref({
  w: 0,
  rw: 0,
  s: 1,
  cs: 1,
});
const editCz = ref(null);
const referSizeRef = ref(null);
const controlDialog = ref(false);
const centerTitleDesc = ref("");
const tabsData = ref({});
const bottomTabList = ref([]);
let ii = 0;
// 中间画布自适应宽高
const getMainSize = () => {
  // console.log(666, editCz.value.clientWidth, referSizeRef.value.clientWidth);
  if (editCz.value?.clientWidth) {
    mainSize.value.w = editCz.value.clientWidth;
    //实际宽度  因为用了rem适配 所以不是1920
    mainSize.value.rw = referSizeRef.value.clientWidth;
    mainSize.value.s = mainSize.value.rw / 1920;
    mainSize.value.cs = mainSize.value.w / mainSize.value.rw;
    console.log("mainSize", mainSize.value);
    ii = 0;
  } else {
    setTimeout(() => {
      if (ii++ > 50) {
        return;
      }
      getMainSize();
    }, 20);
  }
};
window.addEventListener("resize", (e) => {
  // console.log(e);
  getMainSize();
});

onMounted(() => {
  console.log(666);
  getMainSize();
  // initCanvas()
});

/// 开关状态：false=关闭（右白），true=开启（左绿）
const states = reactive({
  auto: false,
  manual: false,
  remote: false
})

// 切换方法（点击时翻转状态）
const toggle = (mode) => {
  states[mode] = !states[mode]
  // 扩展业务逻辑：如接口请求、状态同步等
  console.log(`${mode} 状态：`, states[mode])
}
const startCamera = [1, 20, 500];
const endCamera = [-20, 10, 20];
const gltfGlobal = ref();
const restMark = ref(false); //重置标识
const zntf = ref(false);
const dialogShow = ref(false);
// let playCanvas = ref(null);
let modelInit = ref();
modelInit.value = useThree({ startCamera, endCamera });
var player = new Player();
const playCanvas = ref(null); // ✅ 绑定 Canvas 的 ref
let hlsInstance = null;       // 保存 HLS 实例
let videoInstance = null;     // 保存 Video 实例
// 数据弹窗
const isDataShow = ref({});
//  标注弹窗变量
const { getLabelDialogEL, isShow } = changeLabelPosition(isDataShow);
// 详细信息请看 three/useThree/index.js 文件
const {
  mountRender,
  ani,
  stopRender,
  scene,
  camera,
  cameraControls,
  initModel,
  changeCurrentMesh,
  changeCurrentMeshByName,
  findMesh,
  restrictCameraVerticalDirection,
  matcapTexture,
  dbModelClick,
  executeMultipleAnimation,
  setMeshScale,
  removeMeshByName,
  executeAnimation,
  restCamera,
  addAmbientLight,
} = modelInit.value;
// 相机重置函数

// 添加灯光 默认场景已经添加部分灯光
// scene.add(addAmbientLight(0xffffff, 0.5))
// 限制相机垂直方向移动
// restrictCameraVerticalDirection(cameraControls, Math.PI / 6, Math.PI / 5)
// 加载模型
// initModel('./model/tfj.glb', [0, 0, 0], (gltf) => {
initModel("./model/localFan.glb", [0, 0, 0], (gltf) => {
  if (gltf) {
    gltfGlobal.value = gltf;
    // console.log(gltf, '局部通风机1111');
    // 移除物体模型模块
    removeMeshByName(gltf.scene, "JBTFXT_HD1");
    removeMeshByName(gltf.scene, "JBTFXT_HD2");
    // 缩放模型大小
    setMeshScale(gltf.scene, 26);
    // // 需要进行贴图的物体名称 后续唐森会将物体名称进行修改 方便操作
    const matName = [
      "JBTFXT_HD",
      "JBTFXT_HD3",
      "JBTFJ_GD",
      "JBTFJ_GD1",
      "JBTFJ_FJ_1",
      "JBTFJ_FJ_",
    ];
    // // // 贴图函数 根据模型上物体的名称选择需要进行贴图的物体
    matcapTexture(gltf.scene, matName, "./textures/287.png");

    /**
     *  初始化动画函数 详细介绍请看 three/utils/animat.js executeMultipleAnimation的第二个参数支持传多个动画 参数必须是数组 动画数组中传入多少个动画会执行多少个动画
     *  updateMultipleAnimation更新方法 需要传入下次动画与上次动画之间的时间间隔 传0不执行
     *  对于动画和模型的对应关系 可以问唐森
     *  executeAnimation用来执行单个动画 用法与 executeMultipleAnimation类似 前者只能传入一个动画对象 非数组形式
     */

    // 风机01风扇动画
    const {
      startMultipleAnimation,
      updateMultipleAnimation,
      stopMultipleAnimation,
    } = executeMultipleAnimation(gltf.scene, [
      gltf.animations[0],
      gltf.animations[1],
    ]);
    // 风机02风扇动画
    const {
      startMultipleAnimation: startAni2,
      updateMultipleAnimation: updateAni2,
      stopMultipleAnimation: stopAni2,
    } = executeMultipleAnimation(gltf.scene, [
      gltf.animations[2],
      gltf.animations[3],
    ]);

    // 初始化风机选中  高亮
    // const mesh1 = findMesh(gltf.scene, 'JBTFXT_HD')
    // const mesh2 = findMesh(gltf.scene, 'JBTFXT_HD3')

    //  changeCurrentMesh(mesh1, { isFlyMesh: true })
    // startMultipleAnimation()
    // // 模型交互事件 双击模型 参数1:需要监测那些物体 就将哪些物体传进来 可以直接传gltf.scene 则所有物体都可被点击
    // dbModelClick([mesh1, mesh2], camera, (mesh) => {
    //     const { name } = mesh.object;
    //     fanActive.value = name == 'JBTFXT_Wall' ? 0 : 1;
    //     executeFanSelect()
    // })
    dbModelClick(gltf.scene.children, camera, (v) => {
      console.log(v, "v");
    });

    // 监听复位方法

    watch(restMark, (val) => {
      if (val) {
        executeFanSelect();
      }
      setTimeout(() => {
        restMark.value = false;
      }, 200);
    });
    // 切换风机 启动动画
    // function executeFanSelect() {
    //     if (fanActive.value == 0) {
    //         startMultipleAnimation()
    //         stopAni2()
    //         changeCurrentMeshByName('JBTFXT_Door_01', gltf.scene)
    //     } else {
    //         stopMultipleAnimation()
    //         startAni2()
    //         changeCurrentMeshByName('JBTFXT_HD2', gltf.scene)
    //     }
    // }

    // 渲染函数
    ani(function (...args) {
      const [time, camera, delta] = args;
      // updateAnimation(delta)
      // 更新动画
      updateMultipleAnimation(0.15);
      updateAni2(0.15);
    });
  }
}).then((gltf) => {
  zntf.value = true;
});

// 鼠标滚轮事件
window.addEventListener("wheel", () => {
  if (!isShow.value) return;
  isShow.value = false;
});
let currentData = ref();
// 查看指定物体信息

function checkMeshData(item, type = 0) {
  const { name } = item;
  currentData.value = item;
  isShow.value = false;
  // cameraControls.moveTo(...endCamera);
  cameraControls.moveTo(...endCamera);
  setTimeout(() => {
    const nameList = ["输出电压", "输出电源"];
    if (nameList.indexOf(name) !== -1) {
      let name = "ZJKD390398_";
      changeGroup(name, { isFlyMesh: false });
      const mesh = findMesh(gltfGlobal.value.scene, "ZJKD390398_");
      const [x, y, z] = mesh.position;
      moveToCamera(cameraControls, 5, [x * 26, y * 26, z * 26]);
    } else {
      let name = "JBTFJ_DJ_03";
      changeGroup(name);
    }
    function changeGroup(
      name,
      options = { addB: 5, addT: 5, addL: 5, addR: 5, isFlyMesh: true }
    ) {
      const { addB, addT, addR, addL } = options;
      changeCurrentMeshByName(name, gltfGlobal.value.scene, {
        isLabel: true,
        isFlyMesh: true,
        addB,
        addT,
        addL,
        addR,
        callback: (val) => {
          isDataShow.value = val;
        },
      });
    }
  }, 0);
}

// 路由离开页面时触发
onBeforeRouteLeave((to, from, next) => {
  stopRender();
  modelInit.value = null;
  window.removeEventListener("wheel", () => {});
  next();
});
// 模型挂载
onMounted(() => {
  mountRender(sceneDiv1.value);

  init();
});


const allData = reactive([]);
const styles = ref({});
// 接口数据对接
let localFanCodeData = ref({});
// 局部通风机
const ourData = reactive([]);
let locaData = reactive(null);
let configAllData = reactive(null);
const devices = ref([]);
// 视频监控数据

const videoMonitorList = ref([]);
const cameras = reactive([]);

// bottomTab 组件值改变事件, 根据这个值取请求接口完成业务逻辑
// 现有逻辑，暂时用不上
const handleTab = ({ item: data }) => {

   centerTitleDesc.value = data.name;
  const index = (locaData?.Result || []).findIndex(
    (el) => el.LocalFaninfo.ID === data.id
   
  );


  formatMainData(index);
};
const init = async () => {
  await ApiLocalFanList().then((res) => {
    // console.log(res,'####ces')
    res.Result.forEach((item) => {
      // console.log(item,'####3333')
      Object.keys(item.Pointrtd).forEach((el) => {
        const ele = item.Pointrtd[el];
        const id = ele.ID;
        ele.ID = ele.KeyID;
        ele.KeyID = id;
      });
    });

    locaData = res;
   
  });
  await ApiConfigurationList().then((res) => (configAllData = res));
  formatMainData(0, true);
};

const formatMainData = (index, isInit = false) => {
  const [No1, No2] = [[], []];
  for (const key in locaData.Result[index].Pointrtd) {
    if (
      Object.prototype.hasOwnProperty.call(
        locaData.Result[index].Pointrtd, key)) {
      const element = locaData.Result[index].Pointrtd[key];

      if (key.endsWith("_1")) {
        No1.push(element);
      } else if (key.endsWith("_2")) {
        No2.push(element);
      }
    }
  }

  devices.value = [No1, No2];

  // console.log("seeeeettt", [No1, No2]);
  store.setDevicesMap([No1, No2]); // 存储设备给组件使用
  firstDevices.value = No1;
  secondDevices.value = No2;
  if (locaData.Status === 0) {
    // 赋值
    localFanCodeData.value = devices.value;
 
    const { LocalFaninfo, Pointrtd } = locaData.Result[index];
    // 视频监控数据
    videoMonitorList.value = locaData.Result[index].LocalFaninfo.Cameras;
    locaData.Result[index].LocalFaninfo.Cameras.forEach((item) => {
      cameras.push(item);
    });

    if (isInit) {
      bottomTabList.value.length = 0;
      (locaData?.Result || []).forEach((item) => {
        bottomTabList.value.push({
          id: item.LocalFaninfo.ID,
          name: item.LocalFaninfo.DevName,
          DevCode: item.LocalFaninfo.DevCode,
          Camera1: item.LocalFaninfo.Cameras,
        });
      });
    }
  }
 
   centerTitleDesc.value = bottomTabList.value[0].name;
  formatConfigData();
};
//页面配置
const formatConfigData = () => {
  ourData.length = 0;
  const list = configAllData.Result;
  console.log(list,'%%%%%%')
  
  list.forEach(item => {
    if (item.Type === 2) {
      // 局部通风机
      let option={}
      item.Config.forEach(detail => {
       
        option = {
            list: [[], []],
        }; 
        if (detail?.BoardName === "故障诊断") { 
          option.tab = 0;
          option.tIndex = 0; 
        }
        (detail?.Option?.Fans||[]).forEach((fan,_i) => {
      
            const devices = _i == 0 ? firstDevices.value : secondDevices.value;
            devices.forEach(conf => {
              if (fan.Keys.includes(conf.ID)) {
                const {ID:name,Label:label,Value:value,Unit:Unit } = conf;
                option.list[_i].push({
                  name,
                  label,
                  value,
                  Unit
                })
              }
            })
          })
        ourData.push({
          ...detail,
          option,
        })
      })
    }
  })
}


// 左右切换查看更多
const handleVideoMonitorListLeftRight = (type) => {
  // console.log(`${type}滚动`);
  if (videoMonitorList.value.length < 3) {
    //
  }
};
// 控制面板
function openStop() {
  controlDialog.value = true;
  console.log("打开页面局扇");
}

const firstDevices = ref("");
const secondDevices = ref("");
      let hls;
// 确保 devicesMap.value 是一个数组
if (Array.isArray(devicesMap.value)) {
  firstDevices.value = devicesMap.value[1];
  secondDevices.value = devicesMap.value[1];
} else {
  console.error("devicesMap.value is not an array or is null/undefined");
}



// 弹窗初始化监控视频播放 ws://*************:9001/videostream/d54b1a5c06d4447195bd1401395853c0
const handleVideoMonitorDetail =  async (item) => {
  dialogShow.value = true;
 setTimeout(() => {
    player.play({
      baseUrl: window.origin,
      canvas: document.getElementById("playCanvass"),
      //canvas: playCanvas.value, //渲染画布
      videoWidth: 670, //渲染视频宽度
      videoHeight: 350, //渲染视频高度
      codecType: item.DecodeType, //解码格式：0-h264,1-h265
      videoUrl: item.Description, //流媒体地址
      bufferLength: 0, //缓存大小
    });
  }, 50);
 
//    await nextTick(); // ✅ 等待弹窗渲染完成，确保 Canvas 存在
//    try {
//     // 1. 调用转码接口（获取转码任务 ID 或会话信息）
//     const transcodeRes = await ApiVideo(item.RTSP); 
//     console.log('转码接口响应:', transcodeRes);

//     // 2. 调用播放接口（用转码结果获取实际播放地址）
//      const m3u8Content = await ApiPlayVideo(); 
//     //  if (!playRes) {
//     //   throw new Error('播放接口返回无效：缺少 playlistUrl');
//     // }
    
//     // const m3u8Content = playRes; // 播放接口返回的 m3u8 地址
//     console.log('播放地址:', m3u8Content);

//     // 2. ✅ 替换 TS 路径为接口 URL
//    if (!window.g || !window.g.baseUrl) {
//       throw new Error('全局配置 window.g.baseUrl 未定义，请检查环境！');
//     }
//     const baseUrl = window.g.baseUrl;

//     // 2. 拼接 TS 接口基础 URL（确保格式正确）
//     const TS_API_BASE = `${baseUrl.replace(/\/$/, '')}/api/v1/video/`; 
//     console.log('动态 TS_API_BASE:', TS_API_BASE);
//      // 3. 替换 TS 路径（正则必须匹配所有 TS 片段）
// const modifiedM3u8 = m3u8Content.replace(
//   /segment_\d+\.ts/g,  // 匹配 "segment_490.ts" 等格式
//   (match) => `${TS_API_BASE}${match}` // 替换为完整 URL
// );

//      // 4. 生成 Blob URL
//     const blob = new Blob([modifiedM3u8], { type: 'application/vnd.apple.mpegurl' });
//     const m3u8Url = URL.createObjectURL(blob);

//     // 验证 Blob 内容
//     fetch(m3u8Url).then(res => res.text()).then(text => {
//       console.log('Blob 中的 m3u8 内容:', text);
//     });

//     // 5. 初始化 Canvas 和 Video
//     const canvas = playCanvas.value;
//     if (!canvas) throw new Error('Canvas 未找到');
//     const ctx = canvas.getContext('2d'); // 定义 ctx

//     const video = document.createElement('video');
//     video.style.display = 'none';
//     document.body.appendChild(video);
//     videoInstance = video; // 赋值 videoInstance

//     // 6. 播放 HLS 流
//     if (Hls.isSupported()) {
//       hlsInstance = new Hls();
//       hlsInstance.loadSource(m3u8Url);
//       hlsInstance.attachMedia(video);

//       // 错误监听
//       hlsInstance.on(Hls.Events.ERROR, (_, data) => {
//         console.error('HLS 错误:', data);
//         if (data.fatal) {
//           URL.revokeObjectURL(m3u8Url);
//           dialogShow.value = false;
//         }
//       });

//       // 解析完成后播放
//       hlsInstance.on(Hls.Events.MANIFEST_PARSED, () => {
//         video.play().catch(err => console.warn('自动播放被拦截:', err));
        
//         video.addEventListener('loadedmetadata', () => {
//           // 设置 Canvas 尺寸为视频尺寸
//           canvas.width = video.videoWidth;
//           canvas.height = video.videoHeight;
          
//           // 绘制视频帧
//           const drawFrame = () => {
//             if (video.paused || video.ended) return;
//             ctx.drawImage(video, 0, 0, canvas.width, canvas.height); // 已定义 ctx
//             requestAnimationFrame(drawFrame);
//           };
//           drawFrame();
//         });
//       });
//     }


//   } catch (err) {
//     console.error('播放失败:', err);
//     dialogShow.value = false;
//     // 清理资源
//     if (hlsInstance) hlsInstance.destroy();
//     if (videoInstance) videoInstance.remove();
//   }
};


//关闭
const dialogShowClose =async (item) => {
if (hlsInstance) {
    hlsInstance.destroy();
    hlsInstance = null;
  }
  if (videoInstance) {
    videoInstance.pause();
    videoInstance.remove(); // 从 DOM 移除
    videoInstance = null;
  }
  await fetch('/api/stream/stop'); // 调用服务端停止接口
  dialogShow.value = false;
};
const intervalId = ref(null);

//清除定时器，防止内存溢出
onBeforeUnmount(() => {
  clearInterval(intervalId.value);
});

const sceneDiv1 = ref(null);
const fanActive = ref(0);

//环境监测
let tabEvnIndex = ref(0);
const sendEvion = (e, index) => {
  tabEvnIndex.value = Number(e);
  if (e == 0) {
    document.querySelector(".tem-twows" + index).style.transform =
      " translateX(500px) scale(1)";
    document.querySelector(".tem-onews" + index).style.transform =
      " translateX(0px) scale(1)";
  } else {
    document.querySelector(".tem-onews" + index).style.transform =
      " translateX(-500px) scale(1)";
    document.querySelector(".tem-twows" + index).style.transform =
      " translateX(0px) scale(1)";
  }
};
// 动力监测
let tabPowerIndex = ref(0);
const sendPower = (e, index) => {
  // console.log(e, '局部通风机send');
  tabPowerIndex.value = Number(e);

  if (e == 0) {
    document.querySelector(".second-part" + index).style.transform =
      " translateX(500px) scale(1)";
    document.querySelector(".first-part" + index).style.transform =
      " translateX(0px) scale(1)";
  } else {
    document.querySelector(".first-part" + index).style.transform =
      " translateX(-500px) scale(1)";
    document.querySelector(".second-part" + index).style.transform =
      " translateX(0px) scale(1)";
  }
};

// 故障监测
const box5Tab = ref(0);
function changeBox5Tab(item, num) {
  item.option.tab = num;
}

let tabDaIndex = ref(0);

const sendFaultlc = (e, index) => {
  tabDaIndex.value = Number(e);

  if (e == 0) {
    document.querySelector(".two-fault1" + index).style.transform =
      " translateX(500px) scale(1)";
    document.querySelector(".one-fault1" + index).style.transform =
      " translateX(0px) scale(1)";
  } else {
    document.querySelector(".one-fault1" + index).style.transform =
      " translateX(-500px) scale(1)";
    document.querySelector(".two-fault1" + index).style.transform =
      " translateX(0px) scale(1)";
  }
};


const forList = ref({
  data: [
    {
      name: "局扇",
    },
    {
      name: "局扇",
    },
    {
      name: "局扇",
    },
  ],
});

const forData = forList.value.data.map((item) => ({
  ...item,
  icon: new URL(item.icon, import.meta.url).href,
}));
forList.value.data = forData;

// 监听
watchEffect(() => {});
</script>
<style lang="scss" scoped>
@import url(../mainFan/assets/index.scss);
/* 开关容器 */
.control-switch-group {
  margin-bottom: 10px; /* 与下方按钮拉开间距 */
  // margin-top: 15px;
}

.switch-group {
  display: flex;
  gap: 20px;
  align-items: center;
}

/* 单个开关容器 */
.switch-item {
  display: flex;
  align-items: center;
  cursor: pointer;
}

/* 开关外框（矩形） */
.switch-container {
  width: 60px;   /* 总宽度 */
  height: 30px;  /* 总高度 */
  border: 2px solid #ccc; /* 灰色边框 */
  //border-radius: 15px;    /* 圆角（视觉更柔和） */
  position: relative;
  margin-right: 8px;
  overflow: hidden;       /* 隐藏滑块溢出部分 */
}

/* 滑块（矩形） */
.switch-slider {
  width: 50%;    /* 滑块宽度=容器的1/2 */
  height: 100%;  /* 滑块高度=容器高度 */
  background: #fff;       /* 关闭时：白色 */
  position: absolute;
  right: 0;      /* 关闭时：居右 */
  transition: 
    transform 0.3s ease, 
    background 0.3s ease; /* 滑动+颜色过渡 */
}

/* 开启状态：滑块左移 + 背景变绿 */
.switch-slider.on {
  transform: translateX(-100%); /* 左滑（容器50%宽度 → 左移100%自身宽度） */
  background: #3FF1A4;         /* 绿色 */
}

/* 文字样式 */
.switch-item span {
  color: #fff;
  font-size: 14px;
}
.environmental {
  position: relative;
  color: #fff;
}

.envir_one {
  background-image: url(./assets/img/hjjc_background.png);
  background-size: 285px 271px;
  width: 285px;
  height: 271px;
  // position: relative;

  img {
    position: absolute;
    width: 139px;
    height: 139px;
    top: 66px;
    left: 71px;
  }
}

.envir_one_ul {
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #ffffff;

  .envir_one_co {
    position: absolute;
    background-image: url(./assets/img/envir_yellow_back.png);
    background-size: 115px 40px;
    width: 115px;
    height: 40px;
    top: -22px;
    left: 100px;

    p {
      text-align: center;
      padding-top: 10px;
    }
  }

  .envir_one_ws {
    @extend .envir_one_co;
    top: 67px;
    left: -34px;

    p {
      text-align: center;
      padding-top: 10px;
    }
  }

  .envir_one_gd {
    @extend .envir_one_co;
    top: 115px;
    left: 238px;

    p {
      text-align: center;
      padding-top: 10px;
    }
  }

  .envir_one_yt {
    @extend .envir_one_co;
    left: -42px;
    top: 189px;

    p {
      text-align: center;
      padding-top: 10px;
    }
  }

  .envir_one_hui {
    @extend .envir_one_co;
    left: 97px;
    top: 256px;

    p {
      text-align: center;
      padding-top: 10px;
    }
  }
}

.control_mb_div {
  color: #fff;
  display: flex;
  flex-direction: column;
}

.control_zt {
  background-image: url(../assets/img/fengj_icon_title.png);
  width: 170px;
  height: 32px;
  background-size: 170px 32px;
  margin-left: 150px;

  p {
    text-align: center;
    padding-top: 10px;
  }
}

.control_back {
  background-image: url(./assets/img/group_spreed.png);
  width: 116px;
  height: 39px;
  background-size: 116px 39px;
}

.forItemBox {
  display: flex;
  justify-content: space-around;
}

.box5-tab1 {
  display: flex;
  flex-direction: column;
  height: 200px;
  overflow-y: auto;

  // 滚动条的颜色的大小
  &::-webkit-scrollbar {
    width: 2px;
  }

  &::-webkit-scrollbar-track {
    background: #0f3450;
  }

  &::-webkit-scrollbar-thumb {
    background: #0f3450;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }

  .item-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #e3d8b7;
    padding: 10px 0;
    margin-top: 10px;
  }

  .center_guz {
    display: flex;
    flex-direction: row;
  }
}

.power-wrapper {
  transition: transform 0.6s ease-in-out;
}

.fault1-wrapper {
  transition: transform 0.6s ease-in-out;
}

.tep-wrapper {
  transition: transform 0.6s ease-in-out;
}

::v-deep .el-switch__label--right {
  color: #fff;
  opacity: 0.8;
}

.toSee {
  width: 1920px;
  height: 0px;
  z-index: 99999;
  pointer-events: none;
}

.mainForBox {
  width: 1920px;
  height: 1080px;
  position: relative;
  transform-origin: top left;
}

::v-deep .el-dialog {
  // width: 850px;
  // height: 500px;
  background: rgb(0, 0, 0) !important;
  box-shadow: rgb(255, 179, 15) 0px 0px 20px inset !important;
  border: 1px solid rgba(255, 179, 15, 0.3) !important;
  border-radius: 10px !important;
}

::v-deep .el-button + .el-button {
  background-color: rgba(255, 179, 15, 0.3) !important;
  border: 1px solid rgba(232, 157, 6, 0.8) !important;
}
</style>