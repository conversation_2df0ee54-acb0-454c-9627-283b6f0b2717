<template>
  <div ref="sceneDiv" class="canvasBox"></div>

  <div class="section_1 page" v-show="zntf" ref="editCz">
    <!-- 动力监测 -->
    <div class="toSee" ref="referSizeRef"></div>
   
      <div class="fixed" :style="{
      transform: `scale(${mainSize.cs})`,
    }" v-for="(item, index) in ourDatas" :key="index">
        <DataBox
          class="top-116px left-24px box1"
          title="动力监测"
          :tabNameList="['风机1', '风机2']"
          @sendFanActive="sendFanActiveChange($event, index)"
          v-if="item.BoardName == '动力监测'"
          :style="{
            width: item.W + sizeUnit,
            height: item.H + sizeUnit,
            left: item.X + sizeUnit,
            top: item.Y + sizeUnit,
          }"
        >
          <template #mainFan>
            <div
              class="power-parent"
              style="position: relative; overflow: hidden"
            >
              <!-- one -->
              <div
                class="power-wrapper el-carousel__item is-active is-animating"
                :class="['first-part' + index]"
                style="height: 400px"
              >
                <div class="flex justify-between">
                  <div class="relative ml-28px">
                    <img
                      src="./assets/img/dljc.png"
                      class="w-163px h-131.6px"
                    />
                    <img
                      src="./assets/img/fan_dljj.png"
                      class="z-2 absolute w-44px h-40px top-25px left-58px turn"
                    />
                  </div>
                  <div class="mr-24px text-center">
                    <div class=" fan_state_error mt-40px">
                      <span>1#风机停止运行</span>
                    </div>
                    <!-- <div class="fan_time mt-10">
                    <span>{{ box1Data.time }}</span>
                  </div> -->
                  </div>
                </div>
                <div class="flex grid grid-cols-2 w-488px flex-wrap mt-15px">
                  <!-- 风机1 -->
                  <div
                    v-for="(v, i) in item.option?.list[0] || []"
                    :key="v.id"
                    class="flex items-center"
                    :class="i > 1 ? 'mt-23px' : ''"
                  >
                    <div class="relative">
                      <img
                        :src="bule_decorate"
                        class="diffuse_ani breathe_ani w-100px h-56px"
                      />
                      <img
                        :src="air_quantity"
                        class="acaleph_ani z-2 absolute w-30px h-30px top-[-5px] left-36px"
                      />
                    </div>
                    <div class="mt-[-18px]">
                      <div class="box1_value">
                        <span>{{ v.label }}</span>
                      </div>
                      <div class="box1_name">
                        <span>{{ v.value }}{{ v.Unit }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- second -->
              <div
                class="power-wrapper is-animating"
                :class="['second-part' + index]"
                style="transform: translateX(500px); height: 400px"
              >
                <div class="flex justify-between">
                  <div class="relative ml-28px">
                    <img
                      src="./assets/img/dljc.png"
                      class="w-163px h-131.6px"
                    />
                    <img
                      src="./assets/img/fan_dljj.png"
                      class="z-2 absolute w-44px h-40px top-25px left-58px turn"
                    />
                  </div>
                  <div class="mr-24px text-center">
                    <div class="fan_state_right mt-50px">
                      <span>2#风机正在运行</span>
                    </div>
                    <!-- <div class="fan_time mt-10">
                      <span>{{ box1Data.time }}</span>
                    </div> -->
                  </div>
                </div>
                <!-- 风机2 -->
                <div class="flex grid grid-cols-2 w-488px flex-wrap mt-15px">
                  <div
                    v-for="(v, i) in item.option?.list[1]"
                    :key="v.id"
                    class="flex items-center"
                    :class="i > 1 ? 'mt-23px' : ''"
                  >
                    <div class="relative">
                      <img
                        :src="bule_decorate"
                        class="diffuse_ani breathe_ani w-100px h-56px"
                      />
                      <img
                        :src="air_quantity"
                        class="acaleph_ani z-2 absolute w-30px h-30px top-[-5px] left-36px"
                      />
                    </div>
                    <div class="mt-[-18px]">
                      <div class="box1_value">
                        <span>{{ v.label }}</span>
                      </div>
                      <div class="box1_name">
                        <span>{{ v.value }}{{ v.Unit }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </DataBox>
        <!-- 温度监测 -->

        <DataBox
          class="box2"
          title="温度监测"
          :tabNameList="['风机1', '风机2']"
          @sendFanActive="sendFanTemp($event, index)"
          v-if="item.BoardName == '温度监测'"
          :style="{
            width: item.W + sizeUnit,
            height: item.H + sizeUnit,
            left: item.X + sizeUnit,
            top: item.Y + sizeUnit,
          }"
        >
          <template #mainFan>
            <div
              class="tem_parent"
              style="position: relative; overflow: hidden; height: 400px"
            >
              <!-- 风机one -->
              <div
                class="tep-wrapper el-carousel__item is-active is-animating tem-first"
                :class="['tem-first' + index]"
                style="height: 400px"
              >
                <div class="grid grid-cols-2 w-488px">
                  <div
                    v-for="(v, i) in item.option?.list[0] || []"
                    :key="v.id"
                    class="w-100% h-100% flex justify-center items-center"
                    :class="i > 1 ? 'mt-28px' : 'mt-15px'"
                  >
                    <div class="relative">
                      <img
                        src="./assets/img/temp_tianx.png"
                        class="breathe_ani w-38px h-38px"
                      />
                      <!-- <img
                        src="./assets/img/temp_tianx.png"
                        class="breathe_ani z-2 absolute w-25px h-26px top-7px left-13px"
                      /> -->
                    </div>
                    <div class="relative mt-[-24px] ml-[-2px]">
                      <div
                        class="box2_value cursor-pointer"
                        @click="checkMeshData(v, 1)"
                      >
                        <span>{{ v.label }}</span
                        ><span>{{ v.value }}°C </span>
                      </div>
                      <div>
                        <!-- <img
                          src="./assets/img/temp_decorate.png"
                          class="w-170.7px h-7px"
                        /> -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 风机two -->
              <div
                class="tep-wrapper is-animating"
                :class="['tem-two' + index]"
                style="transform: translateX(500px); height: 400px"
              >
                <div class="grid grid-cols-2 w-488px">
                  <div
                    v-for="(v, i) in item.option?.list[1]"
                    :key="v.id"
                    class="w-100% h-100% flex justify-center items-center"
                    :class="i > 1 ? 'mt-28px' : 'mt-15px'"
                  >
                    <div class="relative">
                      <img
                        src="./assets/img/temp_tianx.png"
                        class="breathe_ani w-38px h-38px"
                      />
                      <!-- <img
                        src="./assets/img/temp.png"
                        class="breathe_ani z-2 absolute w-13px h-20px top-9px left-12px"
                      /> -->
                    </div>
                    <div class="relative mt-[-24px] ml-[-2px]">
                      <div
                        class="box2_value cursor-pointer"
                        @click="checkMeshData(v, 1)"
                      >
                        <span>{{ v.label }}</span
                        ><span>{{ v.value }}° </span>
                      </div>
                      <div>
                        <!-- <img
                          src="./assets/img/temp_decorate.png"
                          class="w-170.7px h-7px"
                        /> -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </DataBox>
     
        <!-- 控制面板 -->
        <DataBox
          class="top-116px right-25px box3"
          title="控制面板"
          :tabNameList="['风机1', '风机2']"
          v-if="item.BoardName == '控制面板'"
          :style="{
            width: item.W + sizeUnit,
            height: item.H + sizeUnit,
            left: item.X + sizeUnit,
            top: item.Y + sizeUnit,
          }"
        >
          <template #mainFan>
            <div
              class="control_parent"
              style="position: relative; overflow: hidden; height: 200px"
            >
              <div
                class="control_wrapper one_control el-carousel__item is-active is-animating"
                style="height: 200px"
              >

     
                <div class="flex justify-around my-15px mx-10px " @click="openStop()">
                  <div class="box3_btn_bg1" >
                    <div class="pt-21px pl-31px">
                      <img
                        class="w-35px h-35px"
                        src="./assets/img/start_stop.png"
                      />
                    </div>
                    <div><span >一键启停</span></div>
                  </div>
                  <div class="box3_btn_bg2" @click="openStop()">
                    <div class="pt-21px pl-31px">
                      <img
                        class="w-35px h-35px"
                        src="./assets/img/reverse.png"
                      />
                    </div>
                    <div><span>一键反向</span></div>
                  </div>
                  <div class="box3_btn_bg3">
                    <div class="pt-21px pl-31px">
                      <img
                        class="w-35px h-35px"
                        src="./assets/img/downfall.png"
                      />
                    </div>
                    <div><span>一键倒台</span></div>
                  </div>
                  <div class="flex flex-col justify-around">
                    <div class="box3_btn2_bg1 flex justify-around items-center " @click="openStop">
                      <div>
                        <img
                          class="w-20px h-20px"
                          src="./assets/img/yc_control.png"
                        />
                      </div>
                      <div><span>远程控制</span></div>
                    </div>
                    <div class="box3_btn2_bg2 flex justify-around items-center " @click="openStop">
                      <div>
                        <img
                          class="w-20px h-20px"
                          src="./assets/img/blade_angle.png"
                        />
                      </div>
                      <div><span>叶片角度</span></div>
                      
                    </div>
                  </div>
                </div>

                          <!-- 新增：自动/手动/远程控制开关 -->
      <div class="flex ml-350px control-switch-group">
         <div class="switch-group" @click="openStop">
    <!-- 自动控制 -->
    <div class="switch-item" @click="toggle('auto')">
      <div class="switch-container">
        <div 
          class="switch-slider" 
          :class="{ on: states.auto }"
        ></div>
      </div>
      <span>智能调速</span>
    </div>

    <!-- 手动控制 -->
    <!-- <div class="switch-item" @click="toggle('manual')">
      <div class="switch-container">
        <div 
          class="switch-slider" 
          :class="{ on: states.manual }"
        ></div>
      </div>
      <span>手动控制</span>
    </div> -->

    <!-- 远程控制 -->
    <!-- <div class="switch-item" @click="toggle('remote')">
      <div class="switch-container">
        <div 
          class="switch-slider" 
          :class="{ on: states.remote }"
        ></div>
      </div>
      <span>远程控制</span>
    </div> -->
  </div>
      </div>
              </div>
            </div>
          </template>
        </DataBox>
        <!-- 视频监控 -->
        <DataBox
          class="box4"
          title="视频监控"
          :tabNameList="[]"
          v-if="item.BoardName == '视频监控'"
          :style="{
            width: item.W + sizeUnit,
            height: item.H + sizeUnit,
            left: item.X + sizeUnit,
            top: item.Y + sizeUnit,
          }"
        >
          <template #mainFan>
            <div class="mt-25px flex items-center">
              <div
                @click="handleVideoMonitorListLeftRight('left')"
                class="cursor-pointer"
              >
                <img
                  class="w-33px h-58px ml-18px"
                  src="./assets/img/switch_arrow.png"
                />
              </div>
              <div class="">
                <div
                  class="flex justify-around w-386px transition-all delay-600"
                >
                  <div
                    class="box4_camera"
                    @click="handleVideoMonitorDetail(item)"
                    v-for="(item, i) in videoMonitorList"
                    :key="i"
                  >
                    <img class="w-77px h-88px" src="./assets/img/camera.png" />

                    <div class="mt-9px">
                      <span>{{ item.Name }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div
                @click="handleVideoMonitorListLeftRight('right')"
                class="cursor-pointer"
              >
                <img
                  class="w-33px h-58px mr-18px rotate-180"
                  src="./assets/img/switch_arrow.png"
                />
              </div>
            </div>
          </template>
        </DataBox>
        <!-- 故障诊断 -->
        <DataBox
          class="top-616px right-25px box5"
          title="故障诊断"
          :tabNameList="['风机1', '风机2']"
          @sendFanActive="sendFault($event, index)"
          v-if="item.BoardName == '故障诊断'"
          :style="{
            width: item.W + sizeUnit,
            height: item.H + sizeUnit,
            left: item.X + sizeUnit,
            top: item.Y + sizeUnit,
          }"
        >
          <template #mainFan>
            <div
              class="fault_parent"
              style="overflow: hidden; position: relative; height: 360px"
            >
              <!-- one_part -->
              <div
                class="fault_wrapper el-carousel__item is-active is-animating oha"
                :class="['one-fault' + index]"
                style="height: 360px"
              >
                <div class="px-22px">
                  <div>
                    <div class="w-100% flex justify-around mt-5px">
                      <div class="box5_tab" @click="changeBox5Tab(item, 0)">
                        <span>实时数据</span>
                        <div
                          :class="
                            item.option?.tab == 0 ? 'box5_tab_active' : ''
                          "
                        ></div>
                      </div>
                      <div class="box5_tab" @click="changeBox5Tab(item, 1)">
                        <span>历史报警数据</span>
                        <div
                          :class="
                            item.option?.tab == 1 ? 'box5_tab_active' : ''
                          "
                        ></div>
                      </div>
                    </div>
                    <div
                      class="w-100% h-1px mt-23px border border-solid border-[#52595F]"
                    ></div>
                  </div>
                  <div
                    class="flex justify-between w-100% flex-wrap"
                    v-if="item.option?.tab === 0"
                  >
                    <div
                      v-for="(v, i) in item.option?.list[0] || []"
                      :key="v.id"
                      :class="i > 2 ? 'mt-20px' : 'mt-20px'"
                      @click="checkMeshData(v, 2)"
                      class="cursor-pointer"
                    >
                      <div class="box5_data_name">
                        <span>{{ v.label }}</span>
                      </div>
                      <div class="box5_data_value_bg">
                        <span class="box5_data_value">{{ v.value }}</span>
                      </div>
                    </div>
                  </div>
                  <div
                    class="flex w-100% box5-tab1"
                    v-if="item.option?.tab === 1"
                  >
                    <div
                      v-for="(v, i) in item.option?.list[0]"
                      :key="i"
                      class="item-box"
                    >
                      <div class="center_guz">
                        <div class="name">1#液压泵</div>
                        <div class="val ml-10px">
                          <span>故障</span>
                        </div>
                      </div>
                      <div class="time">2022-12-16 03:19:46</div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- two_part -->
              <div
                class="fault_wrapper is-animating oha"
                :class="['two-fault' + index]"
                style="height: 360px; transform: translateX(500px)"
              >
                <div class="px-22px">
                  <div>
                    <div class="w-100% flex justify-around mt-5px">
                      <div class="box5_tab" @click="changeBox5Tab(item, 0)">
                        <span>实时数据</span>
                        <div
                          :class="
                            item.option?.tab == 0 ? 'box5_tab_active' : ''
                          "
                        ></div>
                      </div>
                      <div class="box5_tab" @click="changeBox5Tab(item, 1)">
                        <span>历史报警数据</span>
                        <div
                          :class="
                            item.option?.tab == 1 ? 'box5_tab_active' : ''
                          "
                        ></div>
                      </div>
                    </div>
                    <div
                      class="w-100% h-1px mt-23px border border-solid border-[#0BA4EE]"
                    ></div>
                  </div>
                  <div
                    class="flex justify-around w-100% flex-wrap"
                    v-if="item.option?.tab === 0"
                  >
                    <div
                      v-for="(v, i) in item.option?.list[1]"
                      :key="v.id"
                      :class="i > 2 ? 'mt-20px' : 'mt-20px'"
                      @click="checkMeshData(v, 2)"
                      class="cursor-pointer"
                    >
                      <div class="box5_data_name">
                        <span>{{ v.label }}</span>
                      </div>
                      <div class="box5_data_value_bg">
                        <span class="box5_data_value">{{ v.value }}</span>
                      </div>
                    </div>
                  </div>
                  <div
                    class="flex w-100% box5-tab1"
                    v-if="item.option?.tab === 1"
                  >
                    <div
                      v-for="(v, i) in item.option?.list[1]"
                      :key="i"
                      class="item-box"
                    >
                      <div class="center_guz">
                        <span>1#液压泵</span>
                        <span>故障</span>
                      </div>

                      <div class="time">2024-12-16 03:19:46</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </DataBox>
        <!-- 环境监测-1-局扇 -->
        <DataBox
          class="top-593px left-24px box2"
          title="环境监测"
          :tabNameList="['主扇', '备扇']"
          @sendFanActive="sendEvion($event, index)"
          v-if="item.BoardName == '环境监测-1'"
          :style="{
            width: item.W + sizeUnit,
            height: item.H + sizeUnit,
            left: item.X + sizeUnit,
            top: item.Y + sizeUnit,
          }"
        >
          <template #mainFan>
            <!-- 环境onepart -->
            <div
              class="tep-parent"
              style="position: relative; overflow: hidden"
            >
              <div
                class="tep-wrapper el-carousel__item is-active is-animating tem-onews"
                style="height: 400px"
              >
                <div class="environmental ml-74px mt-25px">
                  <div class="envir_one diffuse_ani">
                    <img src="../localFan/assets/img/icon_hjjc.png" />
                  </div>
                  <div class="envir_one_ul">
                    <div class="envir_one_co float_ani">
                      <p>迎头co</p>
                    </div>
                    <div class="envir_one_ws float_ani">
                      <p>迎头瓦斯</p>
                    </div>
                    <div class="envir_one_gd float_ani">
                      <p>供电点瓦斯</p>
                    </div>
                    <div class="envir_one_yt float_ani">
                      <p>迎头温度</p>
                    </div>
                    <div class="envir_one_hui float_ani">
                      <p>回风巷风速</p>
                    </div>
                  </div>
                </div>
              </div>

              <div
                class="tep-wrapper is-animatin tem-twows"
                style="transform: translateX(500px); height: 400px"
              >
                <div class="environmental ml-74px mt-25px">
                  <div class="envir_one diffuse_ani">
                    <img src="../localFan/assets/img/icon_hjjc.png" />
                  </div>
                  <div class="envir_one_ul">
                    <div class="envir_one_co float_ani">
                      <p>迎头co</p>
                    </div>
                    <div class="envir_one_ws float_ani">
                      <p>迎头瓦斯</p>
                    </div>
                    <div class="envir_one_gd float_ani">
                      <p>供电点瓦斯</p>
                    </div>
                    <div class="envir_one_yt float_ani">
                      <p>迎头温度</p>
                    </div>
                    <div class="envir_one_hui float_ani">
                      <p>回风巷风速</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </DataBox>
      </div>
      <!-- 基本信息 -->
    <!-- </div> -->
    <CenterTitle :title="centerTitleDesc"></CenterTitle>
    <bottomTab @change="handleTab" :bottomTabList="bottomTabList" :allData="[]"></bottomTab>
    <!--历史数据弹窗 -->
    <div v-show="isShow">
      <HistoryDialog :data="currentData" @labelDialogEL="getLabelDialogEL" />
    </div>

    <!-- 弹窗 -->
    <el-dialog
      destroy-on-close
      draggable
      v-model="dialogShow"
      @close="dialogShowClose"
      :title="dialogTitle"
      style="width: 850rem; height: 500rem"
    >
      <!-- 视频播放 -->
      <canvas
        id="playCanvass"
        style="
          background-color: black;
          width: 670rem;
          height: 350rem;
          margin-left: 60rem;
          margin-top: 20rem;
        "
      ></canvas>
    </el-dialog>

    <!-- 控制面板弹窗 -->
     <!-- 控制表单 -->
    <el-dialog title="验证面板" draggable :lock-scroll="false" modal-class="abc"  style="height: 300rem;" :modal="false" width="400px" v-model="controlDialog" @close="onClose">
      <el-form class="mt-40px" :model="form" ref="form" :rules="rules" label-width="100px" :inline="false" size="normal">
        <el-form-item label="登录密码：" style="color: white;" :rules="[
          {
            required: true,
            message: '请输入密码',
            trigger: 'blur',
          }
        ]">
          <el-input type="password" v-model="password" placeholder="请输入登录密码" clearable></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer mt-50px">
          <el-button size="medium" @click="handleCancel">取 消</el-button>
          <el-button size="medium" type="primary" @click="handleOk">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import {
  onMounted,
  ref,
  watch,
  reactive,
  watchEffect,
  onBeforeUnmount,
} from "vue";
import { onBeforeRouteLeave } from "vue-router";
import DataBox from "@/components/common/DataBox.vue";
import HistoryDialog from "@/components/common/HistoryDialog.vue";
import CenterTitle from "@/components/common/CenterTitle.vue";
import { ApiWindDoorList } from "@/https/encapsulation/WindDoor";
import bottomTab from "../../../components/common/bottomTab.vue";

import air_quantity from "./assets/img/tianx_icon.png";
import negative_pressure from "./assets/img/negative_pressure.png";
import voltage from "./assets/img/voltage.png";
import x_fan from "./assets/img/x_fan.png";
import angular_surveying from "./assets/img/angular_surveying.png";
import transient_current from "./assets/img/transient_current.png";
import green_decorate from "./assets/img/green_decorate.png";
import yellow_decorate from "./assets/img/yellow_decorate.png";

import bule_decorate from "./assets/img/tianx-bg.png";

import { useThree } from "@/three/useThree";
import { changeLabelPosition } from "@/hooks/useLabelData";

import DongLiJianCe from "../../businessCenter/components/DongLiJIanCe.vue";
import GuZhangZhenDuan from "../../businessCenter/components/GuZhangZhenDuan.vue";
import WenDuJianCe from "../../businessCenter/components/WenDuJianCe.vue";
import KongZhiMianBan from "../../businessCenter/components/KongZhiMianBan.vue";
import ShiPinJianKong from "../../businessCenter/components/ShiPinJianKong.vue";
// 接口
import {
  ApiMainFanList,
  ApiLocalFanHisalarm,
} from "@/https/encapsulation/MainFan";

//页面配置页的接口
import { ApiConfigurationList } from "@/https/encapsulation/Configuration";

import gsap from "gsap";

// 显示页面进行二次计算
function recalculateRem(remValue, baseWidth = 1920) {
  const currentWidth = window.innerWidth; // 当前屏幕宽度
  // const newBase = currentWidth / (baseWidth / 10); // 当前屏幕的 rem 基准值
  // return remValue * (newBase / (baseWidth / 10));
  return remValue * (currentWidth / baseWidth);
}
// function recalculateRem(remValue, baseWidth = 1400) {
//   const currentWidth = window.screen.width * window.devicePixelRatio; // 当前屏幕宽度
//   console.log('currentWidth',currentWidth);

//   // const newBase = currentWidth / (baseWidth / 10); // 当前屏幕的 rem 基准值
//   // return remValue * (newBase / (baseWidth / 10));
//   return remValue * (currentWidth / baseWidth);
//   // return remValue
// }


/// 开关状态：false=关闭（右白），true=开启（左绿）
const states = reactive({
  auto: false,
  manual: false,
  remote: false
})

// 切换方法（点击时翻转状态）
const toggle = (mode) => {
  states[mode] = !states[mode]
  // 扩展业务逻辑：如接口请求、状态同步等
  console.log(`${mode} 状态：`, states[mode])
}
const mainSize = ref({
  w: 0,
  rw: 0,
  s: 1,
  cs: 1,
});
const editCz = ref(null);
const referSizeRef = ref(null);
let ii = 0;
// 中间画布自适应宽高
const getMainSize = () => {
  // console.log(666, editCz.value.clientWidth, referSizeRef.value.clientWidth);
  if (editCz.value?.clientWidth) {
    mainSize.value.w = editCz.value.clientWidth;
    //实际宽度  因为用了rem适配 所以不是1920
    mainSize.value.rw = referSizeRef.value.clientWidth;
    mainSize.value.s = mainSize.value.rw / 1920;
    mainSize.value.cs = mainSize.value.w / mainSize.value.rw;
    // console.log("mainSize", mainSize.value);
    ii = 0;
  } else {
    setTimeout(() => {
      if (ii++ > 50) {
        return;
      }
      getMainSize();
    }, 20);
  }
};
window.addEventListener("resize", (e) => {
  // console.log(e);
  getMainSize();
});
onMounted(() => {
  getMainSize();
  // initCanvas()
});
// 动画
onMounted(() => {
  gsap.fromTo(
    ".main_fan",
    { transform: "rotate(0)" },
    { transform: "rotate(1turn)", duration: 2, repeat: -1 }
  );
});

const sizeUnit = ref("rem");
const dialogShow = ref(false);
let playCanvas = ref(null);
var player = new Player();
const  controlDialog = ref(false)
const centerTitleDesc = ref("");
const tabsData = ref({});
// 底部tab数据
const bottomTabList = ref([])
// bottomTab 组件值改变事件, 根据这个值取请求接口完成业务逻辑
// 现有逻辑，暂时用不上


// 模型开始
const startCamera = [1, 100, 8];
const endCamera = [3, 12, 10];
const gltfGlobal = ref();
const restMark = ref(false); // 重置标识
const zntf = ref(false);
// 数据弹窗
const isDataShow = ref({});

// 清除弹窗数据

// 详细信息请看 three/useThree/index.js 文件
let modelInit = ref();
modelInit.value = useThree({ startCamera, endCamera, isControl: true });
const {
  mountRender,
  ani,
  stopRender,
  scene,
  camera,
  cameraControls,
  initModel,
  changeCurrentMesh,
  changeCurrentMeshByName,
  findMesh,
  restrictCameraVerticalDirection,
  matcapTexture,
  dbModelClick,
  executeMultipleAnimation,
  setMeshScale,
  executeAnimation,
  cameraAni,
  addAmbientLight,
} = modelInit.value;
// 限制相机垂直方向移动

restrictCameraVerticalDirection(cameraControls, Math.PI / 6, Math.PI / 5);

//  标注弹窗变量
const { getLabelDialogEL, isShow } = changeLabelPosition(isDataShow);
// 加载模型
initModel("./model/tfj.glb", [0, 0, 0], (gltf) => {
  if (gltf) {
    gltfGlobal.value = gltf;
    setMeshScale(gltf.scene, 55);
    // 需要进行贴图的物体名称
    const matName = ["TFJ_ZJS_02", "TFJ_ZJS_03", "TFJ_ZJS_01"];
    // 贴图函数 根据模型上物体的名称选择需要进行贴图的物体
    matcapTexture(gltf.scene, matName, "./textures/222.png");
    // isControl: true,
    // 风机01风扇动画
    const {
      startMultipleAnimation,
      updateMultipleAnimation,
      stopMultipleAnimation,
    } = executeMultipleAnimation(gltf.scene, [
      gltf.animations[0],
      gltf.animations[1],
    ]);
    // 风机02风扇动画
    const {
      startMultipleAnimation: startAni2,
      updateMultipleAnimation: updateAni2,
      stopMultipleAnimation: stopAni2,
    } = executeMultipleAnimation(gltf.scene, [
      gltf.animations[2],
      gltf.animations[3],
    ]);

    // 初始化风机选中
    const mesh1 = findMesh(gltf.scene, "TFJ_ZJS_02");
    const mesh2 = findMesh(gltf.scene, "TFJ_ZJS_03");
    changeCurrentMesh(mesh1, { isFlyMesh: true });
    startMultipleAnimation();
    // 模型交互事件 双击模型 参数1:需要监测那些物体 就将哪些物体传进来 可以直接传gltf.scene 则所有物体都可被点击
    dbModelClick([mesh1, mesh2], camera, (mesh) => {
      const { name } = mesh.object;
      fanActive.value = name == "TFJ_ZJS_02" ? 0 : 1;
      isShow.value = false;
      executeFanSelect();
    });

    // 监听复位方法
    watch(restMark, (val) => {
      if (val) {
        executeFanSelect();
      }
      setTimeout(() => {
        restMark.value = false;
      }, 200);
    });
    function executeFanSelect() {
      if (fanActive.value == 0) {
        startMultipleAnimation();
        stopAni2();
        changeCurrentMeshByName("TFJ_ZJS_02", gltf.scene);
      } else {
        stopMultipleAnimation();
        startAni2();
        changeCurrentMeshByName("TFJ_ZJS_03", gltf.scene);
      }
    }

    // 渲染函数
    ani(function (...args) {
      const [time, camera, delta] = args;
      // updateAnimation(delta)
      // 更新动画
      updateMultipleAnimation(0.15);
      updateAni2(0.15);
    });
  }
}).then((gltf) => {
  zntf.value = true;
});
// 鼠标滚轮事件
window.addEventListener("wheel", () => {
  if (!isShow.value) return;
  isShow.value = false;
});

let currentData = ref();
// 查看指定物体信息
function checkMeshData(item, type = 0) {
  const { name } = item;
  currentData.value = item;
  isShow.value = false;
  cameraControls.moveTo(...endCamera);

  setTimeout(() => {
    if (type == 0) {
      const nameList = ["进线电压", "风机振动", "进线电流"];
      if (nameList.indexOf(name) !== -1) {
        let name = fanActive.value == 0 ? "DJ_02" : "DJ_03";
        changeGroup(name);
      } else if (name == "叶片角度") {
        let name = fanActive.value == 0 ? "FS_01" : "FS_03";
        changeGroup(name, { addB: 5, addT: 5, addL: 8, addR: 5 });
      }
      return;
    } else if (type == 1) {
      let name = fanActive.value == 0 ? "DJ_01" : "DJ_04";
      changeGroup(name);
      return;
    } else {
      const nameList = ["上风门状态", "下风门状态", "冷却泵状态"];
      if (nameList.indexOf(name) !== -1) {
        let name = fanActive.value == 0 ? "DJ_01" : "DJ_04";
        changeGroup(name);
      } else {
        let name = fanActive.value == 0 ? "DJ_02" : "DJ_03";
        changeGroup(name);
      }
    }

    function changeGroup(
      name,
      options = { addB: 5, addT: 5, addL: 5, addR: 5 }
    ) {
      const { addB, addT, addR, addL } = options;
      changeCurrentMeshByName(name, gltfGlobal.value.scene, {
        isLabel: true,
        isFlyMesh: true,
        addB,
        addT,
        addL,
        addR,
        callback: (val) => {
          isDataShow.value = val;
        },
      });
    }
  });
}

// 路由离开页面时触发
onBeforeRouteLeave((to, from, next) => {
  scene.remove(gltfGlobal.value);
  stopRender();
  console.log(to, "离开时触发");
  modelInit.value = null;
  window.removeEventListener("wheel", () => {});
  next();
});

// 接口数据对接
let MainFanCodeData = ref({});
// 视频监控数据
const CamerasList = ref([]);
const videoMonitorList = ref([]);

// 左右切换查看更多
const handleVideoMonitorListLeftRight = (type) => {
  console.log(`${type}滚动`);
  if (videoMonitorList.value.length < 3) {
    //
  }
};

// 控制面板
function openStop() {
   controlDialog.value = true;
   console.log('打开页面')
}

// tabId：底下tab的id， 以后接口要参数的话，用的到，后面根据业务使用
const cameras = reactive([]);

const dialogShowClose = () => {
  player.stop();
};

// 查看视频监控详情,弹窗
const handleVideoMonitorDetail = (item) => {
  dialogShow.value = true;

  setTimeout(() => {
    player.play({
      baseUrl: window.origin,
      canvas: document.getElementById("playCanvass"),
      //canvas: playCanvas.value, //渲染画布
      videoWidth: 670, //渲染视频宽度
      videoHeight: 350, //渲染视频高度
      codecType: item.DecodeType, //解码格式：0-h264,1-h265
      videoUrl: item.Description, //流媒体地址
      bufferLength: 0, //缓存大小
    });
  }, 50);
};

//定时刷新系统通知，每个1分钟
const intervalId = ref(null);

// 模型挂载
onMounted(() => {
  mountRender(sceneDiv.value);

 
  init();
});

const ourDatas = reactive([]);
const styles = ref({});
const devices = ref([]);
const allData = reactive([]);
let mainData = reactive(null);
let configAllData = reactive(null);

const handleTab = ({item:data}) => {
 
  const index = (mainData?.Result || []).findIndex(el => el.Fannerinfo.ID === data.id)
  centerTitleDesc.value = data.name;
  formatMainData(index);
};

const init = async()=>{
  await ApiMainFanList().then(res=>{
    console.log(res,'444444')
    res.Result.forEach(item=>{
      console.log(item,'4444441111')
      Object.keys(item.Pointrtd).forEach(el => {
        const ele = item.Pointrtd[el];
        const id = ele.ID;
        ele.ID = ele.KeyID;
        ele.KeyID = id;
      })
    })

    mainData=res
    // console.log(mainData,'zs111111')
  });
  await ApiConfigurationList().then(res=>configAllData=res);
  formatMainData(0,true);
}

const formatMainData = (index, isInit = false) => {
  const [No1, No2] = [[], []];
  for (const key in mainData.Result[index].Pointrtd) {

      if (Object.prototype.hasOwnProperty.call(mainData.Result[index].Pointrtd, key)) {
        const element = mainData.Result[index].Pointrtd[key];
console.log(element,'sssss')
        if (key.endsWith("_1")) {
          No1.push(element);
        } else if (key.endsWith("_2")) {
          No2.push(element);
        }
      }
  }
    
    devices.value = [No1, No2];

    // console.log("seeeeettt", [No1, No2]);
    store.setDevicesMap([No1, No2]); // 存储设备给组件使用
    firstDevices.value = No1;
    secondDevices.value = No2;
     if (mainData.Status === 0) {
      // 赋值
      MainFanCodeData.value = devices.value;

      const { Fannerinfo, Pointrtd } = mainData.Result[index];
      // 视频监控数据
      videoMonitorList.value = mainData.Result[index].Fannerinfo.Cameras;
      mainData.Result[index].Fannerinfo.Cameras.forEach((item) => {
        cameras.push(item);
      });

      if(isInit){
        bottomTabList.value.length = 0;
     (mainData?.Result||[]).forEach(item=>{
      bottomTabList.value.push({
        id: item.Fannerinfo.ID,
        name: item.Fannerinfo.DevName,
        DevCode: item.Fannerinfo.DevCode,
        Camera1: item.Fannerinfo.Cameras,
      })
    })
      }
  }
  centerTitleDesc.value = bottomTabList.value[0].name;
    formatConfigData()
}
const formatConfigData = () => {
  ourDatas.length = 0;
  const list = configAllData.Result;
  
  list.forEach(item => {
    if (item.Type === 1) {
      // 主通风机
      let option={}
      item.Config.forEach(detail => {
        option = {
            list: [[], []],
        }; 
        if (detail?.BoardName === "故障诊断") { 
          option.tab = 0;
          option.tIndex = 0; 
        }
        (detail?.Option?.Fans||[]).forEach((fan,_i) => {
          
            const devices = _i == 0 ? firstDevices.value : secondDevices.value;
            devices.forEach(conf => {
              if (fan.Keys.includes(conf.ID)) {
                const {ID:name,Label:label,Value:value,Unit:Unit } = conf;

                option.list[_i].push({
                  name,
                  label,
                  value,
                  Unit
                })
              }
            })
          })
        ourDatas.push({
          ...detail,
          option,
        })
      })
    }
  })
}
// 获取主通风机接口数据
// const getApMainFanCode = () => {
//   ApiMainFanList().then((res) => {
//     //把数据里的1号风机 2号风机push进去
//     // console.log(res,'主通风机数据===')

//     const [No1, No2] = [[], []];
//     for (const key in res.Result[0].Pointrtd) {
//       let id = res.Result[0].Pointrtd[key].ID;
//       res.Result[0].Pointrtd[key]["ID"] = res.Result[0].Pointrtd[key].KeyID;
//       res.Result[0].Pointrtd[key]["KeyID"] = id;

//       if (Object.prototype.hasOwnProperty.call(res.Result[0].Pointrtd, key)) {
//         const element = res.Result[0].Pointrtd[key];

//         if (key.endsWith("_1")) {
//           No1.push(element);
//         } else if (key.endsWith("_2")) {
//           No2.push(element);
//         }
//       }
//     }
//     devices.value = [No1, No2];
//     // console.log('devices=====',devices)

//     // console.log("seeeeettt", [No1, No2]);
//     store.setDevicesMap([No1, No2]); // 存储设备给组件使用
//     firstDevices.value = No1;
//     secondDevices.value = No2;
//     getConfigList();
//     //判断返回数据
//     if (res.Status === 0) {
      
      
//     }
//   });
// };


//故障的字典
import { configStore } from "@/store/config";
import { storeToRefs } from "pinia";
const store = configStore();
const { devicesMap = [] } = storeToRefs(store);


const firstDevices = ref("");
const secondDevices = ref("");
// 确保 devicesMap.value 是一个数组
if (Array.isArray(devicesMap.value)) {
  
  firstDevices.value = devicesMap.value[1];
  secondDevices.value = devicesMap.value[1];

} else {
 
}



//获取页面配置接口数据
// const getConfigList = () => {
//   ourDatas.length=0;
//   ApiConfigurationList().then((res) => {
//     console.log(res, "主通风机===");

//     for (let k in res.Result) {
//       if (res.Result[k].Type == 1) {
//         // configs = res.Result[k].Config;
//         for (let c in res.Result[k].Config) {
//           if (res.Result[k].Config[c]?.BoardName) {
//             // console.log("keys  ss res.Result[k] c", res.Result[k].Config[c]);
//             let option = {};
//             if (res.Result[k].Config[c]?.BoardName == ["故障诊断"]) {
//               //配置故障诊断
//               option = {
//                 tab: 0,
//                 tIndex: 0,
//                 list: [[], []],
//               };
//               console.log(
//                 "keys res.Result[k].Config[c].Option.Fans",
//                 res.Result[k].Config[c].Option.Fans
//               );
//               //故障诊断
//               for (let f in res.Result[k].Config[c].Option.Fans) {
//                 let keys = res.Result[k].Config[c].Option.Fans[f].Keys;
//                 for (let kk in keys) {
//                   let usef = f == 0 ? firstDevices.value : secondDevices.value;
//                   for (let ff in usef) {
//                     if (usef[ff].ID != keys[kk]) continue;
//                     keys[kk] = {
//                       name: keys[kk],
//                       label: usef[ff].Label,
//                       value: usef[ff].Value,
//                     };
//                     break;
//                   }
//                 }
//                 // console.log("keys1111111111", keys[kk]);
//                 option.list[f] = [...keys];
//                 // option.list.push({
//                 //   id: f,
//                 //   label: res.Result[k].Config[c].Option.Fans[f].Title,
//                 //   value: res.Result[k].Config[c].Option.Fans[f].Value,
//                 //   key: res.Result[k].Config[c].Option.Fans[f].Keys,
//                 // });
//               }
//               console.log("keys box5Data", option);
//             } else if (res.Result[k].Config[c]?.BoardName == ["动力监测"]) {
//               //配置动力监测

//               option = {
//                 list: [[], []],
//               };
//               for (let f in res.Result[k].Config[c].Option.Fans) {
//                 let keys = res.Result[k].Config[c].Option.Fans[f].Keys;
//                 for (let kk in keys) {
//                   let usef = f == 0 ? firstDevices.value : secondDevices.value;
//                   for (let ff in usef) {
//                     if (usef[ff].ID != keys[kk]) continue;
//                     keys[kk] = {
//                       name: keys[kk],
//                       label: usef[ff].Label,
//                       value: usef[ff].Value,
//                     };
//                     break;
//                   }
//                 }
//                 // console.log("keys1111111111", keys[kk]);
//                 option.list[f] = [...keys];
//               }
//             } else if (res.Result[k].Config[c]?.BoardName == ["温度监测"]) {
//               option = {
//                 list: [[], []],
//               };
//               for (let f in res.Result[k].Config[c].Option.Fans) {
//                 let keys = res.Result[k].Config[c].Option.Fans[f].Keys;
//                 for (let kk in keys) {
//                   let usef = f == 0 ? firstDevices.value : secondDevices.value;
//                   for (let ff in usef) {
//                     if (usef[ff].ID != keys[kk]) continue;
//                     keys[kk] = {
//                       name: keys[kk],
//                       label: usef[ff].Label,
//                       value: usef[ff].Value,
//                     };
//                     break;
//                   }
//                 }
//                 // console.log("keys1111111111", keys[kk]);
//                 option.list[f] = [...keys];
//               }
//             } else if (res.Result[k].Config[c]?.BoardName == ["环境监测-1"]) {
//               option = {
//                 list: [[], []],
//               };
//               for (let f in res.Result[k].Config[c].Option.Fans) {
//                 let keys = res.Result[k].Config[c].Option.Fans[f].Keys;
//                 for (let kk in keys) {
//                   let usef = f == 0 ? firstDevices.value : secondDevices.value;
//                   for (let ff in usef) {
//                     if (usef[ff].ID != keys[kk]) continue;
//                     keys[kk] = {
//                       name: keys[kk],
//                       label: usef[ff].Label,
//                       value: usef[ff].Value,
//                     };
//                     break;
//                   }
//                 }
//                 // console.log("keys1111111111", keys[kk]);
//                 option.list[f] = [...keys];
//               }
//             } else if (
//               res.Result[k].Config[c]?.BoardName == ["环境监测-风门"]
//             ) {
//               option = {
//                 list: [[], []],
//               };
//               for (let f in res.Result[k].Config[c].Option.Fans) {
//                 let keys = res.Result[k].Config[c].Option.Fans[f].Keys;
//                 for (let kk in keys) {
//                   let usef = f == 0 ? firstDevices.value : secondDevices.value;
//                   for (let ff in usef) {
//                     if (usef[ff].ID != keys[kk]) continue;
//                     keys[kk] = {
//                       name: keys[kk],
//                       label: usef[ff].Label,
//                       value: usef[ff].Value,
//                     };
//                     break;
//                   }
//                 }
//                 // console.log("keys1111111111", keys[kk]);
//                 option.list[f] = [...keys];
//               }
//             } else if (res.Result[k].Config[c]?.BoardName == ["基本信息"]) {
//               option = {
//                 list: [[], []],
//               };
//               for (let f in res.Result[k].Config[c].Option.Fans) {
//                 let keys = res.Result[k].Config[c].Option.Fans[f].Keys;
//                 for (let kk in keys) {
//                   let usef = f == 0 ? firstDevices.value : secondDevices.value;
//                   for (let ff in usef) {
//                     if (usef[ff].ID != keys[kk]) continue;
//                     keys[kk] = {
//                       name: keys[kk],
//                       label: usef[ff].Label,
//                       value: usef[ff].Value,
//                     };
//                     break;
//                   }
//                 }
//                 // console.log("keys1111111111", keys[kk]);
//                 option.list[f] = [...keys];
//               }
//             }

//             ourDatas.push({
//               ...res.Result[k].Config[c],
//               option: option,
//             });
         
            
//             if (res.Result[k].Config[c]?.BoardName == ["故障诊断"]) {
//               //测试多个重复组件
//               // let data = {
//               //   ...res.Result[k].Config[c],
//               //   X: 600,
//               //   Y: 300,
//               //   option: option,
//               // };
             
//             }
           
//           }
//         }
//       }
      
//     }
    
//   });
// };

//清除定时器，防止内存溢出
onBeforeUnmount(() => {
  clearInterval(intervalId.value);
});

const sceneDiv = ref(null);
const fanActive = ref(0);

// 动力监测
let tabNameIndex = ref(0);
const sendFanActiveChange = (e, index) => {
  tabNameIndex.value = Number(e);
  if (e == 0) {
    document.querySelector(".second-part" + index).style.transform =
      " translateX(500px) scale(1)";
    document.querySelector(".first-part" + index).style.transform =
      " translateX(0px) scale(1)";
  } else {
    document.querySelector(".first-part" + index).style.transform =
      " translateX(-500px) scale(1)";
    document.querySelector(".second-part" + index).style.transform =
      " translateX(0px) scale(1)";
  }
};
const box1Data = ref({
  state: -1,
  time: "23天5时28分46秒",
  data: [
    {
      id: "1",
      name: "正转电流",
      value: "",
      icon: air_quantity,
      iconBg: bule_decorate,
      key: ["FJ1_DL1_1", "FJ1_DL1_2"],
    },
    {
      id: "2",
      name: "入口负压",
      value: "",
      icon: negative_pressure,
      iconBg: bule_decorate,
      key: ["pressure_enterancy_1", "pressure_enterancy_2"],
    },
    {
      id: "3",
      name: "负压",
      value: "4",
      icon: voltage,
      iconBg: yellow_decorate,
      key: ["pressure_blade_1", "pressure_blade_1"],
    },
    {
      id: "4",
      name: "垂直振动",
      value: "",
      icon: x_fan,
      iconBg: yellow_decorate,
      key: ["vibration_horizontal_1", "vibration_horizontal_2"],
    },
    {
      id: "5",
      name: "叶片角度",
      value: "",
      icon: angular_surveying,
      iconBg: green_decorate,
      key: ["position_1", "position_2"],
    },
    {
      id: "6",
      name: "风机风量",
      value: "",
      icon: transient_current,
      iconBg: green_decorate,
      key: ["wind_flow_1", "wind_flow_2"],
    },
  ],
});

let tabBoxIndex = ref(0);
const sendFanTemp = (e, index) => {
  // console.log(e, "温度tab");
  tabBoxIndex.value = Number(e);
  if (e == 0) {
    document.querySelector(".tem-two" + index).style.transform =
      " translateX(500px) scale(1)";
    document.querySelector(".tem-first" + index).style.transform =
      " translateX(0px) scale(1)";
  } else {
    document.querySelector(".tem-first" + index).style.transform =
      " translateX(-500px) scale(1)";
    document.querySelector(".tem-two" + index).style.transform =
      " translateX(0px) scale(1)";
  }
};
// 温度监测
const box2Data = ref({
  data: [
    {
      id: "1",
      name: "入口温度",
      value: "33.3",
      key: ["InletTemp1", "InletTemp2"],
    },
    {
      id: "2",
      name: "U相温度",
      value: "32",
      key: ["UTemp1", "UTemp2"],
    },
    {
      id: "3",
      name: "V相温度",
      value: "34",
      key: ["InletTemp1", "InletTemp2"],
    },
    {
      id: "4",
      name: "W相温度",
      value: "36",
      key: ["VTemp1", "VTemp2"],
    },
    {
      id: "5",
      name: "角接触轴温度",
      value: "33.3",
      key: ["ContactAxleTemp1", "ContactAxleTemp2"],
    },
    {
      id: "6",
      name: "深沟球轴温度",
      value: "32.5",
      key: ["DeepBallAxleTemp1", "DeepBallAxleTemp2"],
    },
    {
      id: "7",
      name: "滚球轴温度",
      value: "33.3",
      key: ["RollBallAxleTemp1", "RollBallAxleTemp2"],
    },
    {
      id: "8",
      name: "驱动轴温度",
      value: "32.3",
      key: ["DriveAxleTemp1", "DriveAxleTemp2"],
    },
    {
      id: "9",
      name: "非驱动轴温度",
      value: "34.5",
      key: ["NonDriveAxleTemp1", "NonDriveAxleTemp2"],
    },
  ],
});

// 视频监控
const box4Data = ref({
  id: 1,
  name: "桥中区主通风机房操作室",
});
const box4DataPage = ref(0);
function changeBox4DataPage(num) {
  box4DataPage.value += num;
  if (box4DataPage.value < 0) {
    box4DataPage.value = 0;
  }
}
// 故障监测
const box5Tab = ref(0);
function changeBox5Tab(item, num) {
  item.option.tab = num;

  // box5Tab.value = num;
}
let tabDaIndex = ref(0);
const sendFault = (e, index) => {
  tabDaIndex.value = Number(e);

  if (e == 0) {
    document.querySelector(".two-fault" + index).style.transform =
      " translateX(500px) scale(1)";
    document.querySelector(".one-fault" + index).style.transform =
      " translateX(0px) scale(1)";
  } else {
    document.querySelector(".one-fault" + index).style.transform =
      " translateX(-500px) scale(1)";
    document.querySelector(".two-fault" + index).style.transform =
      " translateX(0px) scale(1)";
  }
};
const box5Data = ref({
  data: [
    // {
    //   id: "1",
    //   name: "液压站油温",
    //   value: "38.5",
    //   key: ["HStationTemp1", "HStationTemp2"],
    // },
    // {
    //   id: "2",
    //   name: "液压站油压",
    //   value: "39.5",
    //   key: ["HStationVolume1", "HStationVolume2"],
    // },
    // {
    //   id: "3",
    //   name: "液压站液位",
    //   value: "38.5",
    //   key: ["HStationLevel1", "HStationLevel2"],
    // },
    // {
    //   id: "4",
    //   name: "润滑油站油温",
    //   value: "39.5",
    //   key: ["LStationTemp1", "LStationTemp2"],
    // },
    // {
    //   id: "5",
    //   name: "润滑油站油压",
    //   value: "38.5",
    //   key: ["StationVolume1", "StationVolume2"],
    // },
    // {
    //   id: "6",
    //   name: "润滑油站液位",
    //   value: "正常",
    //   key: ["LStationLevel1", "LStationLevel2"],
    // },
    // {
    //   id: "7",
    //   name: "上风门状态",
    //   value: "正常",
    //   key: ["UpDoorStatus1", "UpDoorStatus2"],
    // },
    // {
    //   id: "8",
    //   name: "下风门状态",
    //   value: "正常",
    //   key: ["LowDoorStatus1", "LowDoorStatus2"],
    // },
    // {
    //   id: "9",
    //   name: "冷却泵状态",
    //   value: "正常",
    //   key: ["CoolingPumpStatus1", "CoolingPumpStatus2"],
    // },
  ],
});

watchEffect(() => {
 
});

import dljcTemp from "../../businessCenter/assets/img/dljc_temp.png";
const components = ref([
  // {
  //   label: "动力监测",
  //   img: "/src/views/businessCenter/assets/img/gzzd_temp.png",
  //   width: 300,
  //   height: 320,
  //   type: "DongLiJianCe",
  //   x: 0,
  //   y: 0,
  //   show: true,
  //   com1: [],
  //   com2: [],
  // },
  // {
  //   label: "动力监测",
  //   img: "/src/views/businessCenter/assets/img/gzzd_temp.png",
  //   width: 300,
  //   height: 320,
  //   type: "DongLiJianCe",
  //   x: 0,
  //   y: 400,
  //   show: true,
  //   com1: [],
  //   com2: [],
  // },
]); //这里设置初始值，可以修改刚开始显示的组件

onMounted(() => {
  components.value =
    (localStorage.getItem("components") &&
      JSON.parse(localStorage.getItem("components"))) ||
    [];
  console.log(components.value);
});

const hanldleMouseDown = (e, index) => {
  selectLayer(index);
  dragging.value = true;
  currentIndex.value = index;
  startX.value = e.clientX;
  startY.value = e.clientY;
  comStartX.value = components.value[index].x;
  comStartY.value = components.value[index].y;
};
// const DongLiJIanCe = DongLiJIanCe
// const GuZhangZhenDuan = GuZhangZhenDuan
// const WenDuJianCe = WenDuJianCe
// const KongZhiMianBan = KongZhiMianBan
// const ShiPinJianKong = ShiPinJianKong
const getComName = (str) => {
  switch (str) {
    case "DongLiJIanCe":
      return DongLiJianCe;
      break;
    case "GuZhangZhenDuan":
      return GuZhangZhenDuan;
      break;
    case "WenDuJianCe":
      return WenDuJianCe;
      break;
    case "KongZhiMianBan":
      return KongZhiMianBan;
      break;
  }
};
</script>
<style lang="scss" scoped>
@import url(./assets/index.scss);
.log {
  width: 100vw;
  height: 100vh;
}

.box {
  opacity: 0.8;
}

body,
div,
html {
  font-size: 14px;
}
/* 开关容器 */
.control-switch-group {
  margin-bottom: 10px; /* 与下方按钮拉开间距 */
  // margin-top: 15px;
}

.switch-group {
  display: flex;
  gap: 20px;
  align-items: center;
}

/* 单个开关容器 */
.switch-item {
  display: flex;
  align-items: center;
  cursor: pointer;
}

/* 开关外框（矩形） */
.switch-container {
  width: 60px;   /* 总宽度 */
  height: 30px;  /* 总高度 */
  border: 2px solid #ccc; /* 灰色边框 */
  //border-radius: 15px;    /* 圆角（视觉更柔和） */
  position: relative;
  margin-right: 8px;
  overflow: hidden;       /* 隐藏滑块溢出部分 */
}

/* 滑块（矩形） */
.switch-slider {
  width: 50%;    /* 滑块宽度=容器的1/2 */
  height: 100%;  /* 滑块高度=容器高度 */
  background: #fff;       /* 关闭时：白色 */
  position: absolute;
  right: 0;      /* 关闭时：居右 */
  transition: 
    transform 0.3s ease, 
    background 0.3s ease; /* 滑动+颜色过渡 */
}

/* 开启状态：滑块左移 + 背景变绿 */
.switch-slider.on {
  transform: translateX(-100%); /* 左滑（容器50%宽度 → 左移100%自身宽度） */
  background: #3FF1A4;         /* 绿色 */
}

/* 文字样式 */
.switch-item span {
  color: #fff;
  font-size: 14px;
}
.box5-tab1 {
  display: flex;
  flex-direction: column;
  height: 300px;
  overflow-y: auto;

  // 滚动条的颜色的大小
  &::-webkit-scrollbar {
    width: 2px;
  }

  &::-webkit-scrollbar-track {
    background-color: #0f3450;
  }

  &::-webkit-scrollbar-thumb {
    background: #0f3450;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }

  .item-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #e3d8b7;
    padding: 10px 0;
    margin-top: 10px;
  }

  .center_guz {
    display: flex;
    flex-direction: row;
  }
}

.power-wrapper {
  transition: transform 0.6s ease-in-out;
}

.tep-wrapper {
  transition: transform 0.6s ease-in-out;
}

.fault_wrapper {
  transition: transform 0.6s ease-in-out;
}

.com-box {
  // position: fixed;
  margin-top: 100px;
  width: calc(100vw - 60px);
  height: calc(100vh - 140px);
  z-index: 9999;
}
.edit_cz {
  width: 100%;
  height: 100%;
  border: 1px solid #79bbff;
  margin-left: 10px;
  position: relative;
  overflow: hidden;
}

.environmental {
  position: relative;
  color: #fff;
}

.envir_one {
  background-image: url(../localFan/assets/img/hjjc_background.png);
  background-size: 321px 321px;
  width: 321px;
  height: 321px;
  // position: relative;

  img {
    position: absolute;
    width: 145px;
    height: 145px;
    top: 88px;
    left: 88px;
  }
}

.envir_one_ul {
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #ffffff;

  .envir_one_co {
    position: absolute;
    background-image: url(../localFan/assets/img/envir_yellow_back.png);
    background-size: 115px 40px;
    width: 115px;
    height: 40px;
    top: -5px;
    left: 145px;

    p {
      text-align: center;
      padding-top: 10px;
    }
  }

  .envir_one_ws {
    @extend .envir_one_co;
    top: 67px;
    left: -15px;

    p {
      text-align: center;
      padding-top: 10px;
    }
  }

  .envir_one_gd {
    @extend .envir_one_co;
    top: 115px;
    left: 238px;

    p {
      text-align: center;
      padding-top: 10px;
    }
  }

  .envir_one_yt {
    @extend .envir_one_co;
    left: -42px;
    top: 189px;

    p {
      text-align: center;
      padding-top: 10px;
    }
  }

  .envir_one_hui {
    @extend .envir_one_co;
    left: 123px;
    top: 244px;

    p {
      text-align: center;
      padding-top: 10px;
    }
  }
}
.component {
  cursor: default;
}
.selected_com {
  background-color: #3c846c3a;
  border: 1px solid #3c846c;
}
.delete-text {
  position: absolute;
  left: 40px;
  bottom: 100px;
}
.oha {
  overflow: hidden auto;
}
.toSee {
  width: 1920px;
  height: 0px;
  z-index: 99999;
  pointer-events: none;
}
.mainForBox {
  width: 1920px;
  height: 1080px;
  position: relative;
  transform-origin: top left;
}

::v-deep .el-dialog {
  // width: 850px;
  // height: 500px;
  background: rgb(0, 0, 0) !important;
  box-shadow: rgb(255, 179, 15) 0px 0px 20px inset !important;
  border: 1px solid rgba(255, 179, 15, 0.3) !important;
  border-radius: 10px !important;
}
::v-deep .el-button + .el-button {
  background-color: rgba(255, 179, 15, 0.3) !important;
  border: 1px solid rgba(232, 157, 6, 0.8) !important;
}
</style>
