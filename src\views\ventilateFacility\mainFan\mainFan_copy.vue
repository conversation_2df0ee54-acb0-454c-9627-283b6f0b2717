<template>

<div ref="sceneDiv" class="canvasBox">
    </div>
    <div class="section_1 page"  v-show="zntf">
        <!-- 动力监测 -->
        <DataBox class="top-116px left-24px box1" title="动力监测" :tabNameList="['风机1', '风机2']">
            <template #mainFan>
                <div class="flex justify-between ">
                    <div class="relative  ml-28px">
                        <img src="./assets/img/fan_bg.png" class="w-163px h-131.6px ">
                        <img src="./assets/img/fan.png" class="z-2 absolute w-44px h-40px top-25px left-58px turn">
                    </div>
                    <div class="mr-24px text-center">
                        <div class="fan_state_error mt-20px"><span>1#风机停止运行</span></div>
                        <div class="fan_time mt-10">
                            <span>{{ box1Data.time }}</span>
                        </div>
                    </div>
                </div>
                <div class="flex justify-around w-100% flex-wrap ">
                    <div v-for="( v, i ) in  box1Data.data " :key="v.id" class=" flex items-center "
                        :class="i > 1 ? 'mt-23px' : ''">
                        <div class="relative ">
                            <img :src="v.iconBg" class="w-100px h-56px ">
                            <img :src="v.icon" class="z-2 absolute w-30px h-30px top-10px left-36px">
                        </div>
                        <div>
                            <div class="box1_value"><span>{{ v.value ?? '32°' }}</span></div>
                            <div class="box1_name"><span>{{ v.name }}</span></div>
                        </div>
                    </div>
                </div>
            </template>
        </DataBox>
        <!-- 温度监测 -->
        <DataBox class="top-593px left-24px box2" title="温度监测" :tabNameList="['风机1', '风机2']">
            <template #mainFan>
                <div class="grid grid-cols-2 w-488px">
                    <div v-for="( v, i ) in  box2Data" :key="v" class="w-100% h-100% flex justify-center items-center"
                        :class="i > 1 ? 'mt-28px' : 'mt-15px'">
                        <div class="relative ">
                            <img src="./assets/img/temp_bg.png" class="w-38px h-38px ">
                            <img src="./assets/img/temp.png" class="z-2 absolute w-13px h-20px top-9px left-12px">
                        </div>
                        <div class="relative mt-[-5px] ml-[-2px]">
                            <div class="box2_value"><span>{{ v.value }}</span></div>
                            <div><img src="./assets/img/temp_decorate.png" class="w-170.7px h-7px"></div>
                        </div>
                    </div>

                </div>
            </template>
        </DataBox>

        <CenterTitle title="中央主通风机：1#风机"></CenterTitle>
        <!-- 控制面板 -->
        <DataBox class="top-116px right-25px box3" title="控制面板" :tabNameList="['风机1', '风机2']">
            <template #mainFan>
                <div class="flex justify-around my-15px  mx-10px">
                    <div class="box3_btn_bg1 ">
                        <div class="pt-21px pl-31px"><img class="w-35px h-35px " src="./assets/img/start_stop.png"></div>
                        <div><span>一键启停</span></div>
                    </div>
                    <div class="box3_btn_bg2">
                        <div class="pt-21px pl-31px"><img class="w-35px h-35px " src="./assets/img/reverse.png"></div>
                        <div><span>一键反向</span></div>
                    </div>
                    <div class="box3_btn_bg3">
                        <div class="pt-21px pl-31px"><img class="w-35px h-35px " src="./assets/img/downfall.png"></div>
                        <div><span>一键倒台</span></div>
                    </div>
                    <div class="flex flex-col justify-around">
                        <div class="box3_btn2_bg1 flex justify-around items-center">
                            <div><img class="w-20px h-20px " src="./assets/img/yc_control.png"></div>
                            <div><span>远程控制</span></div>
                        </div>
                        <div class="box3_btn2_bg2 flex justify-around items-center">
                            <div><img class="w-20px h-20px " src="./assets/img/blade_angle.png"></div>
                            <div><span>叶片角度</span></div>
                        </div>
                    </div>
                </div>

            </template>
        </DataBox>
        <!-- 视频监控 -->
        <DataBox class="top-366px right-25px box4" title="视频监控" :tabNameList="['全部摄像头']">
            <template #mainFan>
                <div class="mt-2px flex items-center">
                    <div @click="changeBox4DataPage(-1)" class="cursor-pointer"><img class="w-33px h-58px ml-18px"
                            src="./assets/img/switch_arrow.png"></div>
                    <div v-for="(v, i) in 4" :key="v" class="">
                        <div class="flex justify-around w-386px transition-all delay-600" v-show="box4DataPage == i">
                            <div class="box4_camera">
                                <div><img class="w-77px h-88px " src="./assets/img/camera.png"></div>
                                <div><span>桥中区主通风机房操作室{{ i }}</span></div>
                            </div>
                            <div class="box4_camera">
                                <div><img class="w-77px h-88px " src="./assets/img/camera.png"></div>
                                <div><span>瑶海区主通风机房操作室</span></div>
                            </div>
                            <div class="box4_camera">
                                <div><img class="w-77px h-88px " src="./assets/img/camera.png"></div>
                                <div><span>蜀山区主通风机房操作室</span></div>
                            </div>
                        </div>
                    </div>
                    <div @click="changeBox4DataPage(+1)" class="cursor-pointer"><img
                            class="w-33px h-58px mr-18px rotate-180" src="./assets/img/switch_arrow.png">
                    </div>
                </div>
            </template>
        </DataBox>
        <!-- 故障诊断 -->
        <DataBox class="top-616px right-25px box5" title="故障诊断" :tabNameList="['风机1', '风机2']">
            <template #mainFan>
                <div class="px-22px">
                    <div>
                        <div class="w-100% flex justify-around mt-5px">
                            <div class="box5_tab" @click="changeBox5Tab(0)">
                                <span>实时数据</span>
                                <div :class="box5Tab == 0 ? 'box5_tab_active' : ''"></div>
                            </div>
                            <div class="box5_tab" @click="changeBox5Tab(1)">
                                <span>历史报警数据</span>
                                <div :class="box5Tab == 1 ? 'box5_tab_active' : ''"></div>
                            </div>
                        </div>
                        <div class="w-100% h-1px mt-23px border border-solid border-[#0BA4EE]"></div>
                    </div>
                    <div class="flex justify-around w-100% flex-wrap">
                        <div v-for="(v, i) in box5Data" :key="v" :class="i > 2 ? 'mt-20px' : 'mt-20px'">
                            <div class="box5_data_name"><span>{{ v.name }}</span></div>
                            <div class="box5_data_value_bg">
                                <span class="box5_data_value">{{ v.value }}</span>
                            </div>
                        </div>
                    </div>
                </div>


            </template>
        </DataBox>

        <bottomTab></bottomTab>

    </div>
</template>
<script setup>
import { onMounted, ref, watch,reactive } from 'vue';
import DataBox from '@/components/common/DataBox.vue';

import CenterTitle from '@/components/common/CenterTitle.vue';

import bottomTab from '../../../components/common/bottomTab.vue'

import air_quantity from './assets/img/air_quantity.png'
import negative_pressure from './assets/img/negative_pressure.png'
import voltage from './assets/img/voltage.png'
import x_fan from './assets/img/x_fan.png'
import angular_surveying from './assets/img/angular_surveying.png'
import transient_current from './assets/img/transient_current.png'
import green_decorate from './assets/img/green_decorate.png'
import yellow_decorate from './assets/img/yellow_decorate.png'
import bule_decorate from './assets/img/bule_decorate.png'
import { useThree } from "@/three/useThree";
// 接口
import {ApMainFanCode,} from '@/https/encapsulation/MainFan'

// 模型开始
const startCamera = [1, 100, 8]
const endCamera = [3, 12, 10]
const gltfGlobal = ref()
const restMark = ref(false) // 重置标识
const zntf = ref(false)
// 数据弹窗
const isDataShow = ref({})
// 详细信息请看 three/useThree/index.js 文件
const { mountRender, ani, scene, camera, cameraControls, initModel, changeCurrentMesh, changeCurrentMeshByName, findMesh, restrictCameraVerticalDirection,
    matcapTexture, dbModelClick, executeMultipleAnimation, setMeshScale,
    executeAnimation, restCamera, addAmbientLight } = useThree({ startCamera, endCamera })
// 相机重置函数
// function handleRestCamera() {
//     restCamera(() => {
//         isShow.value = false
//         restMark.value = true
//     })
// }
// 添加灯光 默认场景已经添加部分灯光 
// scene.add(addAmbientLight(0xffffff, 0.5))
// 限制相机垂直方向移动
restrictCameraVerticalDirection(cameraControls, Math.PI / 6, Math.PI / 5)
// 加载模型
// initModel('./model/tfj.glb', [0, 0, 0], (gltf) => {
initModel('./model/tfj.glb', [0, 0, 0], (gltf) => {
    if (gltf) {
        gltfGlobal.value = gltf;
        console.log(gltf, 'val');
        setMeshScale(gltf.scene, 55)
        // // 需要进行贴图的物体名称 后续唐森会将物体名称进行修改 方便操作
        const matName = ['TFJ_ZJS_02', 'TFJ_ZJS_03','TFJ_ZJS_01']
        // // 贴图函数 根据模型上物体的名称选择需要进行贴图的物体
        matcapTexture(gltf.scene, matName, './textures/222.png')

        /**
         *  初始化动画函数 详细介绍请看 three/utils/animat.js executeMultipleAnimation的第二个参数支持传多个动画 参数必须是数组 动画数组中传入多少个动画会执行多少个动画
         *  updateMultipleAnimation更新方法 需要传入下次动画与上次动画之间的时间间隔 传0不执行
         *  对于动画和模型的对应关系 可以问唐森
         *  executeAnimation用来执行单个动画 用法与 executeMultipleAnimation类似 前者只能传入一个动画对象 非数组形式
         */

        // 风机01风扇动画
        const { startMultipleAnimation, updateMultipleAnimation, stopMultipleAnimation } = executeMultipleAnimation(gltf.scene, [gltf.animations[0], gltf.animations[1]])
        // 风机02风扇动画
        const { startMultipleAnimation: startAni2, updateMultipleAnimation: updateAni2, stopMultipleAnimation: stopAni2 } = executeMultipleAnimation(gltf.scene, [gltf.animations[2], gltf.animations[3]])

        // 初始化风机选中 
        const mesh1 = findMesh(gltf.scene, 'TFJ_ZJS_02')
        const mesh2 = findMesh(gltf.scene, 'TFJ_ZJS_03')
        changeCurrentMesh(mesh1, { isFlyMesh: true })
        startMultipleAnimation()
        // 模型交互事件 双击模型 参数1:需要监测那些物体 就将哪些物体传进来 可以直接传gltf.scene 则所有物体都可被点击
        dbModelClick([mesh1, mesh2], camera, (mesh) => {
            const { name } = mesh.object;
            fanActive.value = name == 'TFJ_ZJS_02' ? 0 : 1;
            executeFanSelect()
        })

        // 监听复位方法
      // 监听复位方法
      watch(restMark, (val) => {
            if (val) {
                executeFanSelect()
            }
            setTimeout(() => {
                restMark.value = false
            }, 200)
        })
        function executeFanSelect() {
            if (fanActive.value == 0) {
                startMultipleAnimation()
                stopAni2()
                changeCurrentMeshByName('TFJ_ZJS_02', gltf.scene)
            } else {
                stopMultipleAnimation()
                startAni2()
                changeCurrentMeshByName('TFJ_ZJS_03', gltf.scene)
            }
        }

        // 渲染函数
        ani(function (...args) {
            const [time, camera, delta] = args
            // updateAnimation(delta)
            // 更新动画
            updateMultipleAnimation(0.15)
            updateAni2(0.15)
        })
    }

}).then((gltf) => {
    zntf.value = true

})
// 查看指定物体信息
function checkMeshData(name) {
    isShow.value = false
    // 测试内容
    if (name == '上风门状态') {
        // 通过物体名称获取物体 并添加效果 或者显示标签 需要让那个物体高亮 就传入其对应的名称 目前物体名称没有做修改 使用其默认的名称
        changeCurrentMeshByName('DJ_01', gltfGlobal.value.scene, {
            isLabel: true,
            // 当需要显示标签时 callback 的第一个参数可以获取到 标签的坐标位置
            callback: (val) => {
                isDataShow.value = val
                console.log(isDataShow.value);
            }
        })
    } else {
        changeCurrentMeshByName('DJ_02', gltfGlobal.value.scene, {
            isLabel: true,
            callback: (val) => {
                isDataShow.value = val
                console.log(isDataShow);
            }
        })
    }
}
// 模型结束

// 接口数据对接
const AirVolume1 = ref();
const AirVolume2 = ref();
const BladeAngle1 = ref();
const BladeAngle2 = ref();
const ContactAxleTemp1 = ref();
const ContactAxleTemp2 = ref();
const CoolingPumpStatus1 = ref();
const CoolingPumpStatus2 = ref();
const DeepBallAxleTemp1 = ref();
const DeepBallAxleTemp2 = ref();
const DriveAxleTemp1 = ref();
const DriveAxleTemp2 = ref();
const FanVibrate1 = ref();
const FanVibrate2 = ref();
const HStationLevel1 = ref();
const HStationLevel2 = ref();
const HStationTemp1 = ref();
const HStationTemp2 = ref();
const HStationVolume1 = ref();
const HStationVolume2 = ref();
const InletTemp1 = ref();
const InletTemp2 = ref();
const InletVacuum1 = ref();
const InletVacuum2 = ref();
const InlineCurrent1 = ref();
const InlineCurrent2 = ref();
const InlineVoltage1 = ref();
const InlineVoltage2 = ref();
const LStationLevel1 = ref();
const LStationLevel2 = ref();
const LStationTemp1 = ref();
const LStationTemp2 = ref();
const LowDoorStatus1 = ref();
const LowDoorStatus2 = ref();
const NonDriveAxleTemp1 = ref();
const NonDriveAxleTemp2 = ref();
const Open1 = ref();
const Open2 = ref();
const RollBallAxleTemp1 = ref();
const RollBallAxleTemp2 = ref();
const StationVolume1 = ref();
const StationVolume2 = ref();
const UTemp1 = ref();
const UTemp2 = ref();
const UpDoorStatus1 = ref();
const UpDoorStatus2 = ref();
const VTemp1 = ref();
const VTemp2 = ref();
const WTemp1 = ref();
const WTemp2 = ref();

ApMainFanCode().then((res) => {
    console.log(res,'主要通风机')
            // 判断返回数据 
           
            if(res.Status === 0){
                // 赋值
                AirVolume1.value = res.Result[0].AirVolume1
                AirVolume2.value = res.Result[0].AirVolume2
                BladeAngle1.value = res.Result[0].BladeAngle1
                BladeAngle2.value = res.Result[0].BladeAngle2
                ContactAxleTemp1.value = res.Result[0].ContactAxleTemp1
                ContactAxleTemp2.value = res.Result[0].ContactAxleTemp2
                CoolingPumpStatus1.value = res.Result[0].CoolingPumpStatus1
                CoolingPumpStatus2.value = res.Result[0].CoolingPumpStatus2
                DeepBallAxleTemp1.value = res.Result[0].DeepBallAxleTemp1
                DeepBallAxleTemp2.value = res.Result[0].DeepBallAxleTemp2
                DriveAxleTemp1.value = res.Result[0].DriveAxleTemp1
                DriveAxleTemp2.value = res.Result[0].DriveAxleTemp2
                FanVibrate1.value = res.Result[0].FanVibrate1
                FanVibrate2.value = res.Result[0].FanVibrate2
                HStationLevel1.value = res.Result[0].HStationLevel1
                HStationLevel2.value = res.Result[0].HStationLevel2
                HStationTemp1.value = res.Result[0].HStationTemp1
                HStationTemp2.value = res.Result[0].HStationTemp2
                HStationVolume1.value = res.Result[0].HStationVolume1
                HStationVolume2.value = res.Result[0].HStationVolume2
                InletTemp1.value = res.Result[0].InletTemp1
                InletTemp2.value = res.Result[0].InletTemp2
                InletVacuum1.value = res.Result[0].InletVacuum1
                InletVacuum2.value = res.Result[0].InletVacuum2
                InlineCurrent1.value = res.Result[0].InlineCurrent1
                InlineCurrent2.value = res.Result[0].InlineCurrent2
                InlineVoltage1.value = res.Result[0].InlineVoltage1
                InlineVoltage2.value = res.Result[0].InlineVoltage22
                LStationLevel1.value = res.Result[0].LStationLevel1
                LStationLevel2.value = res.Result[0].LStationLevel2
                LStationTemp1.value = res.Result[0].LStationTemp1
                LStationTemp2.value = res.Result[0].LStationTemp2
                LowDoorStatus1.value = res.Result[0].LowDoorStatus1
                LowDoorStatus2.value = res.Result[0].LowDoorStatus2
                NonDriveAxleTemp1.value = res.Result[0].NonDriveAxleTemp1
                NonDriveAxleTemp2.value = res.Result[0].NonDriveAxleTemp2
                Open1.value = res.Result[0].Open1
                Open2.value = res.Result[0].Open2
                RollBallAxleTemp1.value = res.Result[0].RollBallAxleTemp1
                RollBallAxleTemp2.value = res.Result[0].RollBallAxleTemp2
                StationVolume1.value = res.Result[0].StationVolume1
                StationVolume2.value = res.Result[0].StationVolume2
                UTemp1.value = res.Result[0].UTemp1
                UTemp2.value = res.Result[0].UTemp2
                UpDoorStatus1.value = res.Result[0].UpDoorStatus1
                UpDoorStatus2.value = res.Result[0].UpDoorStatus2
                VTemp1.value = res.Result[0].VTemp1
                VTemp2.value = res.Result[0].VTemp2
                WTemp1.value = res.Result[0].WTemp1
                WTemp2.value = res.Result[0].WTemp2









                 
                console.log(AirVolume1.value,'AirVolume1')
                

            }
           
        });
// 模型挂载
onMounted(() => {
    mountRender(sceneDiv.value);
    ApMainFanCode()
});


const sceneDiv = ref(null)
const fanActive = ref(0)
// const fanName = ref('中央区主通风机')

// // 底部标签list
// const bottomList = ref(['中央区主通风机', '南区主通风机', '东区主通风机'])
// // 获取中央菜单选中内容
// const page1 = ref(false)
// const page2 = ref(false)


// const tabActive = ref(0)
// watch(tabActive, (i) => {
//     console.log(i, 'i');
//     if (i == 3) {
//         page1.value = true;
//         page2.value = false;
//         tabActive.value = 0
//         bottomList.value.pop()
//         return
//     }
//     tabActive.value = i
//     fanName.value = bottomList.value[i]
// })
// 获取底部选中信息
// const bottomAcitveItem = ref(bottomList.value[0])
// function getTabItem(val) {
//     bottomAcitveItem.value = val
// }
// onMounted(() => {
//     setTimeout(() => {
//         page1.value = true
//     })
// })

// 动力监测
const box1Data = ref(
    {
        state: -1,
        time: '23天5时28分46秒',
        data: [
            {
                id: '1',
                name: '风机风量', 
                value: AirVolume1,
                icon: air_quantity,
                iconBg: bule_decorate
            },
            {
                id: '2',
                name: '入口负压',
                value: InletVacuum1,
                icon: negative_pressure,
                iconBg: bule_decorate
            },
            {
                id: '3',
                name: '进线电压',
                value: InlineVoltage1,
                icon: voltage,
                iconBg: yellow_decorate
            },
            {
                id: '4',
                name: '风机振动',
                value: FanVibrate1,
                icon: x_fan,
                iconBg: yellow_decorate
            },
            {
                id: '5',
                name: '叶片角度',
                value: BladeAngle1,
                icon: angular_surveying,
                iconBg: green_decorate
            },
            {
                id: '6',
                name: '进线电流',
                value: InlineCurrent1,
                icon: transient_current,
                iconBg: green_decorate
            },
        ]
    }
)

// 温度监测
const box2Data = ref(
    [
        {
            value: '入口温度(32°)'
        },
        {
            value: 'U相温度(32°)'
        },
        {
            value: 'V相温度(32°)'
        },
        {
            value: 'W相温度(32°)'
        },
        {
            value: '角接触轴温度(32°)'
        },
        {
            value: '深沟球轴温度(32°)'
        },
        {
            value: '滚球轴温度(32°)'
        },
        {
            value: '驱动轴温度(32°)'
        },
        {
            value: '非驱动轴温度(32°)'
        },
    ]
)

// 视频监控
const box4Data = ref(
    {
        id: 1,
        name: '桥中区主通风机房操作室',
    }
)
const box4DataPage = ref(0)
function changeBox4DataPage(num) {
    box4DataPage.value += num
    if (box4DataPage.value < 0) {
        box4DataPage.value = 0
    }
}
// 故障监测
const box5Tab = ref(0)
function changeBox5Tab(num) {
    box5Tab.value = num
}
const box5Data = ref(
    [
        {
            name: '液压站油温',
            value: '38.52'
        },
        {
            name: '液压站油压',
            value: '38.52'
        },
        {
            name: '液压站液位',
            value: '38.52'
        },
        {
            name: '润滑油站油温',
            value: '38.52'
        },
        {
            name: '润滑油站油压',
            value: '38.52'
        },
        {
            name: '润滑油站液位',
            value: '38.52'
        },
        {
            name: '上风门状态',
            value: '38.52'
        },
        {
            name: '下风门状态',
            value: '38.52'
        },
        {
            name: '冷却泵状态',
            value: '38.52'
        }
    ]
)
</script>
<style lang="scss" scoped>
@use './assets/index.scss';
.box {
    opacity: 0.8;
}

body,
div,
html {
    font-size: 14px;
}
</style>