<template>
    <!-- 悬浮框 -->
    <div v-show="page1">
        <transition v-show="page1" leave-active-class="animate__animated animate__fadeInRight "
            enter-active-class="animate__animated animate__fadeInLeft">
            <div class="flex flex-col flex-justify-around fixed top-0 h-full">
                <BoxContent height="463px" width="488px" :titleList="titleList" :fanActive="fanActive" title="动力监测"
                    @fan-active="changeFanActive">
                    <template #content1>
                        <div :style="{ height: needHeight(0.40) }" class="flex flex-col flex-justify-around ">
                            <div class="h-40% text-center flex justify-center items-center">
                                <div>
                                    <div class="w-40px m-auto iconfont-turn-info "><span
                                            class="iconfont icon-fengshan text-[40px] "></span>
                                    </div>
                                    <div class="mt-0.2rem"><span class="text-danger text-22px">1#风机停止运行</span>
                                    </div>
                                    <div class="mt-0.2rem"><span class="text-info text-25px">{{ powerData.time }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="h-50% flex justify-around flex-wrap ">
                                <div class=" h-30%  w-47.5%  text-center flex items-center justify-around faultDiagnosis-item theme "
                                    v-for="(v, i) in powerData.data" :key="v" @click="setMeshOutline('动力监测', '1#风机', v)">
                                    <div class="w-22px iconfont-turn text-center"><span
                                            class="iconfont icon-fengshan text-22px"></span>
                                    </div>
                                    <div class="w-80%"><span>{{ v }}</span></div>
                                </div>
                            </div>
                        </div>
                    </template>
                    <template #content2>
                        <div :style="{ height: needHeight(0.40) }" class="flex flex-col flex-justify-around ">
                            <div class="h-40% text-center flex justify-center items-center">
                                <div>
                                    <div class="w-40px m-auto iconfont-turn-info "><span
                                            class="iconfont icon-fengshan text-[40px] "></span>
                                    </div>
                                    <div class="mt-0.2rem"><span class="text-danger text-22px">2#风机停止运行</span>
                                    </div>
                                    <div class="mt-0.2rem"><span class="text-info text-25px">{{ powerData2.time }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="h-50% flex justify-around flex-wrap ">
                                <div class=" h-30% w-47.5% text-center flex items-center justify-around faultDiagnosis-item theme "
                                    v-for="(v, i) in powerData2.data" :key="v" @click="setMeshOutline('动力监测', '2#风机', v)">
                                    <div class="w-22px iconfont-turn text-center"><span
                                            class="iconfont icon-fengshan text-22px"></span>
                                    </div>
                                    <div class="w-80%"><span>{{ v }}</span></div>
                                </div>
                            </div>
                        </div>
                    </template>
                </BoxContent>
                <BoxContent height="49%" width="350px" :titleList="titleList" :fanActive="fanActive" title="温度监测"
                    @fan-active="changeFanActive">
                    <template #content1>
                        <div :style="{ height: needHeight(0.40) }" class=" grid grid-rows-5 grid-flow-col gap-5px">
                            <div class="h-100% w-100% text-center flex items-center justify-around faultDiagnosis-item theme "
                                v-for="(v, i) in tempData" :key="v" @click="setMeshOutline('温度监测', '1#风机', v)">
                                <div class="w-22px iconfont-turn text-center"><span
                                        class="iconfont icon-fengshan text-22px"></span>
                                </div>
                                <div class="w-80%"><span>{{ v }}</span></div>
                            </div>
                        </div>
                    </template>
                    <template #content2>
                        <div :style="{ height: needHeight(0.40) }" class=" grid grid-rows-5 grid-flow-col gap-5px">
                            <div class="h-100% w-100% text-center flex items-center justify-around faultDiagnosis-item theme "
                                v-for="(v, i) in tempData" :key="v" @click="setMeshOutline('温度监测', '2#风机', v)">
                                <div class="w-22px iconfont-turn text-center"><span
                                        class="iconfont icon-fengshan text-22px"></span>
                                </div>
                                <div class="w-80%"><span>{{ v }}</span></div>
                            </div>
                        </div>
                    </template>
                </BoxContent>
            </div>
        </transition>
        <transition leave-active-class="animate__animated animate__fadeOutLeft"
            enter-active-class="animate__animated animate__fadeInRight">
            <div v-show="page1" class="flex flex-col flex-justify-around fixed right-0 top-0 h-full">
                <BoxContent height="25%" width="350px" :titleList="titleList" :fanActive="fanActive" title="控制面板"
                    @fan-active="changeFanActive">
                    <template #content1>
                        <div :style="{ 'height': needHeight(0.15) }" class="grid grid-rows-2 grid-cols-3 gap-5px">
                            <div v-for="(v, i) in controlList" :key="v"
                                class="m-0.02rem list-hover flex justify-center items-center" effect="dark"
                                @click="handleVerify(v.name)">
                                <i class="iconfont icon-fengshan" :style="{ 'color': `var(--${v.style})` }"></i>
                                <span class="align-middle mx-3px" :style="{ 'color': `var(--${v.style})` }">
                                    {{ v.name }}
                                </span>
                            </div>
                        </div>
                    </template>
                    <template #content2>
                        控制面板
                    </template>
                </BoxContent>
                <BoxContent height="25%" width="350px" :titleList="['全部摄像头']" :fanActive="fanActive" title="视频监控">
                    <template #content1>
                        视频监控
                    </template>
                </BoxContent>
                <BoxContent height="50%" width="350px" :fanActive="fanActive" :tabList="tabList" title="故障诊断"
                    :titleList="titleList">
                    <template #content1="{ data: { fanActive1 } }">
                        <div class="grid grid-cols-2 gap-5px">
                            <div v-for="(v, i) in dataList" :key="i" class="faultDiagnosis-item success"
                                @click="clickMesh(v)">
                                <div class="bias"></div>
                                <div class="val"><i class="iconfont icon-fengshan"></i><span>{{ v.num }}</span>
                                </div>
                                <div class="name">

                                    <div class="round-success"></div><span>{{ v.name }} </span>
                                </div>
                                <div class="itemfoot">
                                    <div class="bias"></div>
                                </div>
                            </div>
                        </div>
                    </template>
                    <template #content2>
                        故障诊断2
                    </template>
                    <template #content3="{ data: { fanActive1 } }">
                        <div class="grid grid-cols-2 gap-5px">
                            <div v-for="(v, i) in dataList" :key="i" class="faultDiagnosis-item success"
                                @click="clickMesh(v)">
                                <div class="bias"></div>
                                <div class="val"><i class="iconfont icon-fengshan"></i><span>{{ v.num }}</span>
                                </div>
                                <div class="name">

                                    <div class="round-success"></div><span>{{ v.name }} </span>
                                </div>
                                <div class="itemfoot">
                                    <div class="bias"></div>
                                </div>
                            </div>
                        </div>
                    </template>
                    <template #content4>
                        故障诊断4
                    </template>
                </BoxContent>
            </div>
        </transition>
    </div>

    <!-- 设备特性 -->
    <div v-show="page2">
        <transition leave-active-class="animate__animated animate__fadeInRight "
            enter-active-class="animate__animated animate__fadeInLeft">
            <div v-show="page2" class="h-full fixed top-0 flex flex-col flex-justify-around">
                <BoxContent height="100%" width="350px" :style="1" :titleList="titleList" :fanActive="fanActive"
                    title="性能测试" @fan-active="changeFanActive">
                    <template #content1>
                        <div class="faultDiagnosis-item success h-80vh">
                            性能测试
                        </div>
                    </template>
                    <template #content2>
                        性能测试
                    </template>
                </BoxContent>
            </div>
        </transition>
        <transition leave-active-class="animate__animated animate__fadeOutLeft"
            enter-active-class="animate__animated animate__fadeInRight">
            <div v-show="page2" class="h-full fixed top-0 right-0 flex flex-col flex-justify-around">
                <BoxContent height="100%" width="350px" :titleList="titleList" :fanActive="fanActive" title="技术特征"
                    @fan-active="changeFanActive">
                    <template #content1>
                        技术特征
                    </template>
                    <template #content2>
                        技术特征
                    </template>
                </BoxContent>
            </div>
        </transition>

    </div>

    <!-- 验证框 -->
    <verify-dialog :title="panTitle" :isShow="panShow" @setIsShow="(val) => { panShow = val }"></verify-dialog>
</template>
<script setup>
import { ref, defineProps, onMounted, watch, defineEmits } from 'vue'
const props = defineProps({
    fanActive1: {
        type: Number,
        default: 0
    },
    page1_1: {
        type: Boolean,
        default: false
    },
    page2_1: {
        type: Boolean,
        default: false
    },
    bottomAcitveItem: { //底部菜单栏选中信息
        type: String,
        default: ''
    }
})
const fanActive = ref(0)
const page1 = ref(false)
const page2 = ref(false)
watch(() => props.fanActive1, (val) => {
    console.log(val, 'page');
    fanActive.value = val
})
watch(() => props.page1_1, (val) => {
    page1.value = val
})
watch(() => props.page2_1, (val) => {
    page2.value = val
})
watch(() => props.bottomAcitveItem, (val) => {

})

function changeFanActive() {

}
onMounted(() => {
    console.log(props.bottomAcitveItem, 'val');
})
const dataList = ref([
    {
        num: 38.52,
        name: '液压站油温',
    },
    {
        num: 38.52,
        name: '上风门状态',
    }
])
const emit = defineEmits(['meshOntlineLight', 'setMeshOutline'])
// 点击物体高亮
function clickMesh(v) {
    console.log(v, 'v');
    emit('meshOntlineLight', v.name)
}

// 标签
const titleList = ref(['1#风机', '2#风机'])
// tab栏标签
const tabList = ref(['实时数据', '历史报警数据'])

// 计算容器所需高度
const pageHeight = ref(window.innerHeight)
const needHeight = (val = 1, num = 0) => {
    return pageHeight.value * val + num + 'px'
}
onMounted(() => {
    window.addEventListener('resize', () => {
        pageHeight.value = window.innerHeight
    })
    console.log(pageHeight.value);
})

// 动力监测
const powerData = ref({
    state: 'danger',
    time: '119天10时25分16秒',
    data: ['风机风量(--)', '入口负压(--)', '进线电压(--)', '风机振动(--)', '叶片角度(--)', '进线电流(--)']
})
const powerData2 = ref({
    state: 'danger',
    time: '119天10时25分16秒',
    data: ['风机风量(--)', '入口负压(--)', '进线电压(--)', '风机振动(--)', '叶片角度(--)', '进线电流(--)']
})
function setMeshOutline(modelName, draughtFan, item) {
    emit('setMeshOutline', modelName, draughtFan, item)
}

// 温度监测
const tempData = ref(
    ['入口温度(--)', 'U相温度(--)', 'V相温度(--)', 'W相温度(--)', '角接触轴温度(--)', '深沟球轴承温度(--)', '滚球轴温度(--)',
        '驱动轴温度(--)', '非驱动轴温度(--)']
)

// 控制面板
const controlList = [
    { name: '一键启停', style: 'success' },
    { name: '一键反向', style: 'warning' },
    { name: '一键倒台', style: 'danger' },
    { name: '远程控制', style: 'info' },
    { name: '叶片角度', style: 'danger' }
]
const panTitle = ref('')
const panShow = ref(false)
const handleVerify = (title) => {
    panTitle.value = title;
    panShow.value = true
}

</script>
<style lang="scss" scoped>
body,
div,
html {
    font-size: 14px;
}
</style>