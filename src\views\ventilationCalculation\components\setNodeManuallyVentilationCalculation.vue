<template>
    <div class="w-100% h-100% relative ">
        <div class="w-100% h-100% " ref="sceneDiv" id="sceneDiv2"></div>
        <div class="btn_menu absolute top-33% left-10px">
            <el-button dark color="#3271ae" size="small" class="text-center" @click="formShow = !formShow">
                <img class="btn_img" src="../assets/img/edit.png" />&nbsp;{{ formShow ? '隐藏表单' : '显示表单' }}</el-button>
        </div>
        <div class="absolute top-33% right-10px">
            <div class="w-50px h-50px cursor-pointer" @click="restCamera"><img class=" w-full h-full"
                    src="../../thereVentilation/assets/img/scene_fw.png"></div>

        </div>

        <div v-show="formShow" @click="stopEvent" @mousemove="stopEvent"
            class="w-50vw h-100% fixed top-50px right-0 z-99 pt-60px ">
            <FourAngel style="width:900rem;height:930rem;padding:20rem; background-color: #0B2236;"
                class="mx-auto text-[#fff]" p-5px>

                <div mb-20px mt-10px class="sbbd_header h-20px flex justify-between items-center">
                    <div class="text-20px font-bold text-[#fff] ">二维编辑页面</div>
                    <div class="mt-[-25px]">
                        <el-icon @click="() => { operateID = 1; formShow = false }" size="20rem"
                            class=" cursor-pointer">
                            <Close />
                        </el-icon>
                    </div>
                </div>
                <!-- 点位搜索 -->
                <div>
                    <div border="2px solid #20c997" relative mt-10px rounded-20px p-20px py-10px pt-20px>
                        <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                            <div
                                class="w-120px h-25px text-center line-height-25px text-#20c997 font-bold bg-[#082237]">
                                点位搜索
                            </div>
                        </div>
                        <div class="flex mt-5px">
                            <el-select style="width: 250rem;" v-model="searchPoint" filterable placeholder="搜索历史点">
                                <el-option v-for="item in needNodeList" :key="item.NodeID"
                                    :label="`巷道ID:${item.NodeID}${item.NodeName && item.NodeName != 'null' ? `--名称:${item.NodeName}` : ''}`"
                                    :value="item.NodeID" />
                            </el-select>
                            <!-- 筛选框 -->
                            <el-space ml-10px>
                                <label>筛选:</label>
                                <el-radio-group v-model="radioCurrent">
                                    <el-radio value="1" size="large" border>孤岛点</el-radio>
                                    <el-radio value="2" size="large" border>联络点</el-radio>
                                    <el-radio value="3" size="large" border>全部</el-radio>
                                </el-radio-group>
                            </el-space>
                        </div>

                    </div>

                </div>
                <div class=" mb-10px mt-20px">
                    <el-space>
                        <el-tag class="cursor-pointer" effect="dark" size="large" type="primary"
                            @click="operateID = 1">新增节点</el-tag>
                        <el-tag class="cursor-pointer" effect="dark" size="large" type="warning"
                            @click="operateID = 2">更新节点</el-tag>
                        <el-tag class="cursor-pointer" effect="dark" size="large" type="danger"
                            @click="operateID = 3">删除节点</el-tag>
                        <el-tag class="cursor-pointer" effect="dark" size="large" type="primary"
                            @click="operateID = 4">设置节点关系</el-tag>
                        <el-tag class="cursor-pointer" effect="dark" size="large" type="warning"
                            @click="operateID = 5">更新节点关系</el-tag>
                        <el-tag class="cursor-pointer" effect="dark" size="large" type="danger"
                            @click="operateID = 6">删除节点关系</el-tag>
                        <el-tag class="cursor-pointer" effect="dark" size="large" type="primary"
                            @click="operateID = 7">节点导入</el-tag>
                        <el-tag class="cursor-pointer" effect="dark" size="large" type="info"
                            @click="operateID = 8">采区配置</el-tag>
                    </el-space>
                </div>


                <!-- 新增节点 -->
                <div v-show="operateID == 1" class="mt-20px">
                    <el-form label-width="auto">
                        <div border="2px solid #2DA8EB" relative rounded-20px p-20px pt-30px>
                            <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                                <div
                                    class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                                    新增节点
                                </div>
                            </div>
                            <el-scrollbar height="310rem" class="flex justify-center">
                                <el-form-item v-for="(item, index) in pointList" :key="index">
                                    <el-row :gutter="20" justify="left" align="center">
                                        <el-col :span="1">
                                        </el-col>
                                        <label class="text-[#fff] text-18px">名称:</label>
                                        <el-col :span="5">
                                            <el-input v-model.trim="item.name" />
                                        </el-col>
                                        <label class="text-[#fff] text-18px">x:</label>
                                        <el-col :span="4">
                                            <el-input v-model.trim="item.x" />
                                        </el-col>
                                        <label class="text-[#fff] text-18px">y:</label>
                                        <el-col :span="4">
                                            <el-input v-model.trim="item.y" />
                                        </el-col>
                                        <label class="text-[#fff] text-18px">z:</label>
                                        <el-col :span="4">
                                            <el-input v-model.trim="item.z" />
                                        </el-col>
                                        <el-col :span="1" v-show="index == pointList.length - 1">
                                            <el-icon size="20rem" color="#40c057" class="mt-8px" @click="addPoint">
                                                <CirclePlus />
                                            </el-icon>
                                        </el-col>
                                        <el-col :span="1" v-show="pointList.length != 1">
                                            <el-icon size="20rem" color="#ea2465" class="mt-8px"
                                                @click="removePoint(index)">
                                                <Remove />
                                            </el-icon>
                                        </el-col>
                                    </el-row>
                                </el-form-item>
                            </el-scrollbar>
                            <div class="w-92% mt-30px  flex justify-end">
                                <el-button type="primary" @click="onSubmit">保存</el-button>
                                <el-button @click="addCancel">取消</el-button>
                            </div>
                        </div>
                    </el-form>

                    <!-- 新增节点列表 -->
                    <div border="2px solid #2DA8EB" relative rounded-20px mt-15px p-20px pt-30px>
                        <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                            <div
                                class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                                新增节点列表
                            </div>
                        </div>
                        <el-scrollbar height="200rem">
                            <div class="ml-40px my-5px" v-for="(item, index) in newPointList" :key="item.NodeID">
                                <span>节点名称：</span><span>{{ item.originPoint.NodeID }}</span>&nbsp;
                                <el-button type=" danger" size="default"
                                    @click="goNodeLocation(item.originPoint.NodeID)">定位</el-button>
                            </div>
                        </el-scrollbar>

                    </div>

                </div>
                <!-- 更新节点 -->
                <div v-show="operateID == 2">
                    <div border="2px solid #2DA8EB" h-400px relative rounded-20px mt-25px p-20px pt-30px>
                        <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                            <div
                                class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                                更新节点
                            </div>
                        </div>
                        <el-form label-width="auto">
                            <el-form-item label="节点ID：">
                                <el-input disabled v-model="updateNodeInfo.NodeID" size="normal"></el-input>
                            </el-form-item>
                            <el-form-item label="节点名称：">
                                <el-input v-model="updateNodeInfo.NodeName" placeholder="请输入修改名称"
                                    size="normal"></el-input>
                            </el-form-item>
                            <el-form-item label="节点坐标：">
                                <el-space>
                                    <el-input v-model="updateNodeInfo.x" size="normal"></el-input>
                                    <el-input v-model="updateNodeInfo.y" size="normal"></el-input>
                                    <el-input v-model="updateNodeInfo.z" size="normal"></el-input>
                                </el-space>
                            </el-form-item>
                        </el-form>
                        <div class="w-92% mt-30px absolute right-8% bottom-20px flex justify-end">
                            <el-button type="primary" @click="updateSave">保存</el-button>
                            <el-button type="primary" @click="updateCancel">取消</el-button>
                        </div>
                    </div>
                </div>
                <!-- 删除节点 -->
                <div v-show="operateID == 3">
                    <div border="2px solid #2DA8EB" h-400px relative rounded-20px mt-25px p-20px pt-30px>
                        <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                            <div
                                class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                                删除节点
                            </div>
                        </div>
                        <el-form :model="removeNodeInfo" label-width="auto">
                            <el-form-item label="节点ID：">
                                <el-input :value="removeNodeInfo.NodeID" disabled />
                            </el-form-item>
                            <el-form-item label="节点名称：">
                                <el-input :value="removeNodeInfo.NodeName" disabled />
                            </el-form-item>
                            <el-form-item label="节点状态：">
                                <el-input
                                    :value="removeNodeInfo.Vertex != null ? (removeNodeInfo.Vertex == 0 ? '起始点' : '中间点') : ''"
                                    disabled />
                            </el-form-item>
                        </el-form>
                        <div class="w-92% mt-30px absolute right-8% bottom-20px flex justify-end">
                            <el-button type="primary" @click="removeSave">保存</el-button>
                            <el-button type="primary" @click="removeCancel">取消</el-button>
                        </div>
                    </div>
                </div>
                <!-- 设置节点关系 -->
                <div v-show="operateID == 4">
                    <el-form :model="form" label-width="auto" style="margin-left: 10rem;">
                        <div border="2px solid #2DA8EB" h-700rem relative rounded-20px mt-25px p-20px pt-30px>
                            <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                                <div
                                    class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                                    设置节点关系
                                </div>
                            </div>
                            <el-form-item class="mb-5px">
                                <div class="h-150rem w-100% rounded-15px p-20px bg-[#1864ab] text-[#ffff]">
                                    <h3>使用说明</h3>
                                    <div>
                                        <span>1、点击选中右侧节点,可以使用上述点位搜索功能进行查找，会直接定位到目标</span> <br />
                                        <span>2、点位右侧的三个图标可以定位、新增和删除节点</span> <br />
                                        <span class=" font-500 text-[#ffa39e]">3、新增巷道必须要选中两个节点才能进行添加</span>
                                    </div>
                                </div>
                            </el-form-item>

                            <el-form-item label="巷道名称" prop="TunName">
                                <el-input v-model="tunOther.TunName" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="用风类型" prop="Ventflow">
                                <el-select v-model="tunOther.Ventflow">
                                    <el-option :value="1" label="进风"></el-option>
                                    <el-option :value="0" label="用风"></el-option>
                                    <el-option :value="-1" label="回风"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="宽度" prop="Width">
                                <el-input v-model="tunOther.Width" clearable></el-input>
                            </el-form-item>
                            <el-scrollbar height="280rem">

                                <el-form-item v-for="( item, index) in nodeList" :label="'坐标' + (index + 1)"
                                    :key="index">
                                    <el-row :gutter="20" justify="left" align="center">
                                        <el-col :span="1">
                                        </el-col>
                                        <label class=" text-18px">ID:</label>
                                        <el-col :span="4">
                                            <el-input disabled v-model.trim="item.originPoint.NodeID" />
                                        </el-col>
                                        <label class=" text-18px">x:</label>
                                        <el-col :span="4">
                                            <el-input disabled v-model.trim="item.originPoint.x" />
                                        </el-col>
                                        <label class=" text-18px">y:</label>
                                        <el-col :span="4">
                                            <el-input disabled v-model.trim="item.originPoint.y" />
                                        </el-col>
                                        <label class=" text-18px">z:</label>
                                        <el-col :span="4">
                                            <el-input disabled v-model.trim="item.originPoint.z" />
                                        </el-col>
                                        <el-col :span="1">
                                            <el-icon size="20rem" color="#4263eb" class="mt-8px cursor-pointer"
                                                @click="goNodeLocation(item.originPoint.NodeID)">
                                                <Location />
                                            </el-icon>
                                        </el-col>
                                        <el-col :span="1">
                                            <el-icon size="20rem" color="#66bb6a" class="mt-8px cursor-pointer"
                                                @click="addMiddlePoint(index < nodeList.length - 1 ? index : null)">
                                                <CirclePlus />
                                            </el-icon>
                                        </el-col>
                                        <el-col :span="1">
                                            <el-icon size="20rem" color="#ea2465" class="mt-8px"
                                                @click="removeMiddlePoint(index)">
                                                <Remove />
                                            </el-icon>
                                        </el-col>

                                    </el-row>
                                </el-form-item>
                            </el-scrollbar>

                            <div class="w-92% mt-30px absolute right-8% bottom-20px flex justify-end">
                                <el-button type="primary" @click="onSubmitSet">保存</el-button>
                                <el-button type="primary" @click="updateTunCancel">取消</el-button>
                            </div>
                        </div>
                    </el-form>
                </div>
                <!-- 更新节点关系 -->
                <div v-show="operateID == 5">
                    <el-form :model="form" label-width="auto" style="margin-left: 10rem;">
                        <div border="2px solid #2DA8EB" h-700rem relative rounded-20px mt-25px p-20px pt-30px>
                            <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                                <div
                                    class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                                    更新节点关系
                                </div>
                            </div>
                            <el-form-item class="mb-5px">
                                <div class="h-150rem w-100% rounded-15px p-20px bg-[#1864ab] text-[#ffff]">
                                    <h3>使用说明</h3>
                                    <div>
                                        <span>1、点击右侧线段进行选中,在未进行第二步时可以自由切换巷道线段</span> <br />
                                        <span class="text-[#ffa39e]">2、需要点击编辑或者新增按钮才能进行节点点击,之后巷道点击将被锁定</span>
                                        <br />
                                        <span class="text-[#ffa39e]">3、想要切换其他巷道线段可以点击取消当前操作</span> <br />
                                    </div>
                                </div>
                            </el-form-item>

                            <el-form-item label="巷道名称" prop="TunName">
                                <el-input v-model="updateTunOther.TunName" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="用风类型" prop="Ventflow">
                                <el-select v-model="updateTunOther.Ventflow">
                                    <el-option :value="1" label="进风"></el-option>
                                    <el-option :value="0" label="用风"></el-option>
                                    <el-option :value="-1" label="回风"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="宽度" prop="Width">
                                <el-input v-model="updateTunOther.Width" clearable></el-input>
                            </el-form-item>
                            <el-scrollbar height="280rem">

                                <el-form-item v-for="( item, index) in updateNodeList" :label="'坐标' + (index + 1)"
                                    :key="index">
                                    <el-row :gutter="10" justify="left" align="center">
                                        <el-col :span="0.2">
                                        </el-col>
                                        <label class=" text-18px">ID:</label>
                                        <el-col :span="3">
                                            <el-input disabled v-model.trim="item.originPoint.NodeID" />
                                        </el-col>
                                        <label class=" text-18px">x:</label>
                                        <el-col :span="4">
                                            <el-input disabled v-model.trim="item.originPoint.x" />
                                        </el-col>
                                        <label class=" text-18px">y:</label>
                                        <el-col :span="4">
                                            <el-input disabled v-model.trim="item.originPoint.y" />
                                        </el-col>
                                        <label class=" text-18px">z:</label>
                                        <el-col :span="3">
                                            <el-input disabled v-model.trim="item.originPoint.z" />
                                        </el-col>

                                        <el-col :span="1">
                                            <el-icon size="20rem" color="#4263eb" class="mt-8px cursor-pointer"
                                                @click="goNodeLocation(item.originPoint.NodeID)">
                                                <Location />
                                            </el-icon>
                                        </el-col>
                                        <el-col :span="1">
                                            <el-icon size="20rem" color="#66bb6a" class="mt-8px cursor-pointer"
                                                @click="updateAddPoint(index)">
                                                <CirclePlus />
                                            </el-icon>
                                        </el-col>
                                        <el-col :span="1">
                                            <el-icon size="20rem" color="#ea2465" class="mt-8px"
                                                @click="updateRemovePoint(index)">
                                                <Remove />
                                            </el-icon>
                                        </el-col>
                                        <el-col :span="1">
                                            <el-icon size="20rem" color="#ECBF7D" class="mt-8px"
                                                @click="updateEditPoint(index)">
                                                <Edit />
                                            </el-icon>
                                        </el-col>
                                        <div :span="1" ml-2 v-show="index !== 0 && index !== updateNodeList.length - 1">
                                            <el-switch v-model="item.state" class="ml-2" inline-prompt size="small"
                                                style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
                                                active-text="连接" inactive-text="打断" />
                                        </div>
                                    </el-row>
                                </el-form-item>
                            </el-scrollbar>

                            <div class="w-92% mt-30px absolute right-8% bottom-20px flex justify-end">
                                <el-button type="primary" @click="updateOnSubmit">保存</el-button>
                                <el-button type="primary" @click="updateTunCancel">取消</el-button>
                            </div>
                        </div>
                    </el-form>
                </div>
                <!-- 删除节点关系 -->
                <div v-show="operateID == 6">
                    <el-form :model="form" label-width="auto" style="margin-left: 10rem;">
                        <div border="2px solid #2DA8EB" h-700rem relative rounded-20px mt-25px p-20px pt-30px>
                            <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                                <div
                                    class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                                    删除节点关系
                                </div>
                            </div>
                            <el-form-item class="mb-5px">
                                <div class="mb-5px h-150rem w-100% rounded-15px p-20px bg-[#1864ab] text-[#ffff]">
                                    <h3>使用说明</h3>
                                    <div>
                                        <span>1、点击选中右侧巷道进行选中</span> <br />
                                        <span class=" font-500 text-[#ffa39e]">3、点击鼠标右键进行撤销操作</span> <br />
                                    </div>
                                </div>

                            </el-form-item>
                            <div class="w-92% mt-30px absolute right-8% bottom-20px flex justify-end">
                                <el-button type="primary" @click="removeTunSave">保存</el-button>
                                <el-button type="primary" @click="removeTunCancel">取消</el-button>
                            </div>
                        </div>
                    </el-form>
                </div>
                <!-- 节点导入 -->
                <div v-show="operateID == 7" calss="nodeImport">
                    <el-form :model="form" label-width="auto" style="margin-left: 10rem;">
                        <div border="2px solid #2DA8EB" h-700rem relative rounded-20px mt-25px p-20px pt-30px>
                            <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                                <div
                                    class="w-120px h-25px text-center line-height-25px text-#228be6 font-bold bg-[#082237]">
                                    节点导入
                                </div>
                            </div>
                            <UploadExcel @result="getImportData"></UploadExcel>
                            <div flex flex-col my-15px>
                                <el-space mb-15px>

                                    <div flex items-center>
                                        <el-icon color="#66bb6a" size="20rem">
                                            <Select />
                                        </el-icon>
                                        <label class="color-#66bb6a text-15px font-bold">: 不存在重复点</label>
                                    </div>
                                    <div flex items-center class="cursor-pointer"> <el-icon color="#ea2465"
                                            size="20rem">
                                            <CloseBold />
                                        </el-icon>
                                        <label class="color-#ea2465 text-15px font-bold">: 存在重复点</label>
                                    </div>
                                    <div flex items-center class="cursor-pointer color-#7FF6FE text-15px font-bold">
                                        <span>所有节点状态均为&nbsp;</span>
                                        <el-icon color="#66bb6a" size="20rem">
                                            <Select />
                                        </el-icon>
                                        <span>&nbsp;才能进行保存</span>
                                    </div>

                                </el-space>
                                <el-space mb-15px alignment="center" class="color-#ea2465 text-15px font-bold">
                                    <span>点击</span>
                                    <el-icon color="#ea2465" size="20rem">
                                        <CloseBold />
                                    </el-icon>
                                    <span>会展示重复点位信息</span>
                                    &nbsp; &nbsp; &nbsp;
                                    <span class="color-#ECBF7D">点击</span>
                                    <el-icon size="20rem" color="#ECBF7D">
                                        <Search />
                                    </el-icon>
                                    <span class="color-#ECBF7D">会展示交点点位信息</span>
                                </el-space>
                                <el-space alignment="center" class="color-#ECBF7D text-15px font-bold">
                                    <!-- 设置查询范围 -->
                                    <span>设置交点查询范围：</span>
                                    <el-input-number v-model="range" :max="20" :min="2" controls-position="right" />
                                </el-space>
                            </div>



                            <el-table max-height="300rem" :data="tableData" highlight-current-row style="width: 100%;">
                                <el-table-column v-show="tableData.length > 0" label="点位状态" type="index" min-width="50">
                                    <template #default="scope">
                                        <el-icon v-show="scope.row.state" color="#66bb6a" size="20rem">
                                            <Check />
                                        </el-icon>
                                        <el-popover placement="right" width="400rem" trigger="click">
                                            <template #reference>
                                                <el-icon class="cursor-pointer" @click="getCheckDouble(scope.row)"
                                                    v-show="!scope.row.state" color="#ea2465" size="20rem">
                                                    <Close />
                                                </el-icon>
                                            </template>
                                            <el-table :data="duplicatePointList" style="width: 400rem;">
                                                <el-table-column min-width="70" prop="NodeID" label="重复点ID" />
                                                <el-table-column min-width="50" label="定位">
                                                    <template #default="scope">
                                                        <el-icon size="20rem" color="#ECBF7D"
                                                            class="mt-8px cursor-pointer"
                                                            @click="goNodeLocation(scope.row.NodeID)">
                                                            <Location />
                                                        </el-icon>
                                                    </template>
                                                </el-table-column>
                                                <el-table-column min-width="50" label="使用该节点">
                                                    <template #default="scope2">
                                                        <el-switch
                                                            @change="changeSwitch(scope.$index, scope2.row, 'checkDouble')"
                                                            v-model="scope2.row.isUse" class="mb-2" />
                                                    </template>
                                                </el-table-column>
                                            </el-table>
                                        </el-popover>

                                    </template>
                                </el-table-column>

                                <el-table-column v-show="tableData.length > 0" v-for="(item, index) of tableHeader"
                                    :key="item" :prop="tableHeader[index]" :label="tableHeader[index]" min-width="90" />
                                <el-table-column v-show="tableData.length > 0" label="交点查询" type="index" min-width="50">
                                    <template #default="scope">
                                        <div class="el-popover-box">
                                            <el-popover placement="left" width="400rem" trigger="click">
                                                <template #reference>
                                                    <el-icon class="cursor-pointer"
                                                        @click="findIntersectionPoint(scope.row)" size="20rem"
                                                        color="#ECBF7D">
                                                        <Search />
                                                    </el-icon>
                                                </template>
                                                <el-table :data="intersectionPointList" style="width: 400rem;">
                                                    <el-table-column min-width="70" prop="NodeID" label="交点ID" />
                                                    <el-table-column min-width="50" label="定位">
                                                        <template #default="scope">
                                                            <el-icon size="20rem" color="#ECBF7D"
                                                                class="mt-8px cursor-pointer"
                                                                @click="goNodeLocation(scope.row.NodeID)">
                                                                <Location />
                                                            </el-icon>
                                                        </template>
                                                    </el-table-column>
                                                    <el-table-column min-width="50" label="使用该节点">
                                                        <template #default="scope2">
                                                            <el-switch @change="changeSwitch(scope.$index, scope2.row)"
                                                                v-model="scope2.row.isUse" class="mb-2" />
                                                        </template>
                                                    </el-table-column>
                                                </el-table>
                                            </el-popover>
                                        </div>

                                    </template>
                                </el-table-column>
                            </el-table>


                            <div class="w-92% mt-30px absolute right-8% bottom-20px flex justify-end">
                                <el-button type="primary" @click="ImportOnSubmit">保存</el-button>
                                <el-button type="primary" @click="ImportCancel">取消</el-button>
                            </div>
                        </div>

                    </el-form>



                </div>
                <!-- 采区配置 -->
                <div v-show="operateID == 8" class="mt-20px">
                    <el-form label-width="auto">
                        <div border="2px solid #ff8c00" relative rounded-20px p-20px pt-30px>
                            <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                                <div
                                    class="w-120px h-25px text-center line-height-25px text-#ff8c00 font-bold bg-[#082237]">
                                    新增采区
                                </div>
                            </div>
                            <el-scrollbar height="310rem" class="flex justify-center">
                                <el-form-item v-for="(item, index) in miningAreaForm" :key="index">
                                    <el-row :gutter="20" justify="left" align="center">
                                        <el-col :span="1">
                                        </el-col>
                                        <label class="text-[#fff] text-18px">名称:</label>
                                        <el-col :span="5">
                                            <el-input v-model.trim="item.name" />
                                        </el-col>
                                        <label class="text-[#fff] text-18px">x:</label>
                                        <el-col :span="4">
                                            <el-input v-model.trim="item.x" />
                                        </el-col>
                                        <label class="text-[#fff] text-18px">y:</label>
                                        <el-col :span="4">
                                            <el-input v-model.trim="item.y" />
                                        </el-col>
                                        <label class="text-[#fff] text-18px">z:</label>
                                        <el-col :span="4">
                                            <el-input v-model.trim="item.z" />
                                        </el-col>
                                        <el-col :span="1" v-show="index == pointList.length - 1">
                                            <el-icon size="20rem" color="#40c057" class="mt-8px" @click="addMiningArea">
                                                <CirclePlus />
                                            </el-icon>
                                        </el-col>
                                        <el-col :span="1" v-show="pointList.length != 1">
                                            <el-icon size="20rem" color="#ea2465" class="mt-8px"
                                                @click="removeMiningArea(index)">
                                                <Remove />
                                            </el-icon>
                                        </el-col>
                                    </el-row>
                                </el-form-item>
                            </el-scrollbar>
                            <div class="w-92% mt-30px  flex justify-end">
                                <el-button type="primary" @click="miningAreaSubmit">保存</el-button>
                                <el-button @click="addMiningAreaCancel">取消</el-button>
                            </div>
                        </div>
                    </el-form>

                    <!-- 新增采区列表 -->
                    <div border="2px solid #ff8c00" relative rounded-20px mt-15px p-20px pt-30px>
                        <div absolute class="w-full flex justify-center top--12.5px z-9 translate-x-[-30px]">
                            <div
                                class="w-120px h-25px text-center line-height-25px text-#ff8c00 font-bold bg-[#082237]">
                                新增采区列表
                            </div>
                        </div>
                        <el-scrollbar height="200rem">
                            <div class="ml-40px my-5px" v-for="(item, index) in miningAreaList" :key="item.id">
                                <span>采区名称：</span><span>{{ item.originPoint.NodeName }}</span>&nbsp;
                                <el-button type=" danger" size="default"
                                    @click="goNodeLocation(item.originPoint.NodeID)">定位</el-button>
                            </div>
                        </el-scrollbar>

                    </div>

                </div>

            </FourAngel>

        </div>

    </div>
</template>
<script setup>
import { watch, reactive, ref, toRaw, onUnmounted, onMounted } from 'vue';
import { useThree } from '@/three/useThree';
import { moveToCamera, restrictCameraVerticalDirection } from '@/three/cameraControls.js'
import { CirclePlus, Remove, Location, Edit, Search, Check, Close, CloseBold, Select } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { deepClone, getType } from '@/utils/utils';
import { dbModelClick, modelClick } from '@/three/utils/event';
import { positionTransform } from "@/views/thereVentilation/js/tunUtils.js";
import { addSchemeNode, delSchemeNode, updateSchemeNode, getSchemeNodeList, addSchemeTun, delSchemeTun, updateSchemeTun, getSchemeTunList, getSchemeTunGis, getSchemeNodeCheckDouble, getSchemeNodeDistance } from '@/https/encapsulation/ventilationCalculation';
import { computeTunLenght } from "@/views/thereVentilation/js/tunUtils.js";
import { addEvent, removeEvent, stopEvent } from "@/utils/window";
import UploadExcel from '@/components/common/uploadExcel/index.vue'
import { createLine2Mat, createLine2 } from '@/three/mesh/Line2Mesh';
import { canvasTexture, canvasTexture2 } from '@/three/material/canvasTexture';
import { onBeforeRouteLeave, useRouter } from 'vue-router';
import FourAngel from '@/components/common/fourAngel/index.vue';
import mitt from '@/utils/eventHub';
import { objectPropertyToArray, throttleFn } from '@/utils/utils';
const emit = defineEmits(['newData', 'stopRender']);
const sceneDiv = ref(null);
const operateID = ref(1); // 控制操作方式

const props = defineProps({
    schemeId: String,
})

let schemeId = ref(null)
watch(() => props.schemeId, (id) => {
    if (schemeId.value) {
        closeImportNode();
    }
    flag.value = false;
    schemeId.value = id;
    openImportNode();
})

const apiTunList = ref([]);
const apiNodeList = ref([]);
let tunStr = ref(``);
const needNodeList = ref([]) //需要展示的点位
const searchPoint = ref(null)
const formShow = ref(false)

watch(searchPoint, (val) => {
    if (val) {
        goNodeLocation(val);
    }
})
onBeforeRouteLeave(() => {
    closeImportNode(true);
    stopRender();
})

// 选择框列表
const radioCurrent = ref(-1);
watch(radioCurrent, (val) => {
    changeNeedNodeList(val);
})

function changeNeedNodeList(state) {
    if (state) {
        if (state == 1) {
            needNodeList.value = apiNodeList.value.filter(v => tunStr.value.findIndex(k => k.includes(v.NodeID)) == -1);
        }
        else if (state == 2) {
            needNodeList.value = apiNodeList.value.filter(v => v.Vertex == 0);
        }
        else {
            needNodeList.value = apiNodeList.value
        }
    }

}

// 获取所有巷道信息 
async function getTunList(schemeId) {
    const { data: { Result } } = await getSchemeTunList(schemeId);
    apiTunList.value = Result ?? [];
    tunStr.value = ``;

    tunStr.value = Result.map(({ SnodeID, EnodeID, MnodeID }) => {
        return MnodeID ? [SnodeID, ...MnodeID.split('-').map(v => Number(v)), EnodeID] : [SnodeID, EnodeID]
    })
    return Result;
}
// 获取所有节点信息 
async function getNodeList(schemeId) {
    const { data: { Result } } = await getSchemeNodeList(schemeId);

    apiNodeList.value = Result ?? [];
    return Result;
}

let lineMeshArr = [];
let circleMeshArr = [];
let newPointList = ref([]);
let miningAreaList = ref([])

let global = ref() // 全局管家
var maxNodeID = ref(0);
let middleNode = ref([0, 0, 0]);
let maxPosition = [0, 0, 0]
let minPosition = [0, 0, 0]
let transformMaxPosition = [0, 0, 0]
let transformMinPosition = [0, 0, 0]

//threejs init
const { mountRender, cameraControls, scene, camera, renderer, THREE, cameraFlyToMesh, addOutlinePass, addOutlinePass1, setMeshRotation, setMeshScale, setPosition, ani, stopRender } = useThree({ endCamera: [0, 100, 0], isCameraAni: false, isControl: true, isAxes: false, isDepthBuffer: true })
camera.far = 60000;
let group = new THREE.Group();
scene.add(group);
const lineMat = createLine2Mat(0xfff000, 3)
const updateLineMat = createLine2Mat(0xfd7e14, 3)
const removeLineMat = createLine2Mat(0xff0000, 3)


global.value = { lineMat, group, stopRender, THREE, camera, scene, addOutlinePass, addOutlinePass1, cameraControls, cameraFlyToMesh, addOutlinePass1 };
// scene.background = new THREE.Color(0x04101F)
scene.background = new THREE.Color(0x04101F)
// 场景放大倍数
let n = 2;
let grid = null;

var setY = 0;
function restCamera() {
    moveToCamera(cameraControls, 0, [0, setY * n, 0], [0, 0, 0]);
}

// 控制三维页
//判断距离最近的节点 并更新联动相机状态
let cameraPosition = { x: null, y: null, z: null }

function getRecentNode(camera) {
    const { x, y, z } = camera.position;
    console.log(x, y, z, 'x, y, z');

    const { x: x1, y: y1, z: z1 } = cameraPosition;
    if (x == x1 && y == y1 && z == z1) {
        return null;
    }
    cameraPosition = { x, y, z };

    if (!circleMeshArr.length) return;
    let minDistance = setY * 2;
    let zjMesh = null;
    circleMeshArr.forEach((mesh, i) => {
        if (mesh instanceof THREE.Mesh) {
            const point = mesh.position;

            const distance = new THREE.Vector3(x, y, z).distanceTo(point);
            console.log(mesh.position, distance, 'distance');

            if (distance < minDistance) {
                minDistance = distance;
                zjMesh = mesh
            }
        }

    })
    return zjMesh ? zjMesh.mid : null;
}
function getRecentNodeResult(mid) {
    if (mid) {
        mitt.emit('get2DRecentNode', mid);
    }
}
let getRecentNodeThrottleFn = throttleFn(getRecentNode, 500, getRecentNodeResult) // 节流 1m判断一次


ani((time, camera, delta, requestAnimationId) => {
    getRecentNodeThrottleFn(camera)
});
const flag = ref(false) // 标识
onMounted(() => {
    const el = document.querySelector('#sceneDiv2');
    mountRender(el);
    // 跟随3D的相机变化
    mitt.on('get3DRecentNode', (mid) => {
        setTimeout(() => {
            goNodeLocation(mid, 1000, false)
        }, 200)
        // cameraControls.fitToBox(node, true, { paddingTop: 100, paddingRight: 100, paddingBottom: 100, paddingLeft: 100 })
    })
})

watch(operateID, val => {
    if (val && sceneDiv.value) {
        changeEvent(val);
    }
})
function changeEvent(index) {
    global.value.clickRemove && global.value.clickRemove();
    global.value.dblRemove && global.value.dblRemove();
    global.value.repealRemove && global.value.repealRemove();
    global.value.addOutlinePass([]);
    global.value.addOutlinePass1([]);
    switch (index) {
        case 1: break;
        case 2: addInteraction2(sceneDiv.value); break;
        case 3: addInteraction3(sceneDiv.value); break;
        case 4: addInteraction4(sceneDiv.value); break;
        case 5: addInteraction5(sceneDiv.value); break;
        case 6: addInteraction6(sceneDiv.value); break;
        case 7: break;
        case 8: break;
    }
}

function openImportNode() {
    let id = schemeId.value;
    global.value.addOutlinePass([]);
    global.value.addOutlinePass1([]);
    let vec3 = null;
    getNodeList(id).then((Result) => {
        Result.forEach(({ x, y, z, NodeID }) => {
            const [maxX, maxY, maxZ] = maxPosition;
            x > maxX ? maxPosition[0] = x : minPosition[0] = x;
            y > maxY ? maxPosition[1] = y : minPosition[1] = y;
            z > maxZ ? maxPosition[2] = z : minPosition[2] = z;
        })
        const NodeList = Result.map(v => v.NodeID);
        newPointList.value = newPointList.value.filter(v => NodeList.includes(v.originPoint.NodeID))

        const [x1, y1, z1] = maxPosition;
        const [x2, y2, z2] = minPosition;
        // vec3 = Result.length == 1 ? Result[0] : ({ x: (x1 + x2) / 2, y: (y1 + y2) / 2, z: (z1 + z2) / 2 });
        vec3 = Result.length == 1 ? Result[0] : (Result[Math.floor(Result.length / 2)] ?? { x: 0, y: 0, z: 0 });

        const { x, y, z } = vec3 ?? { x: 0, y: 0, z: 0 }
        middleNode.value = [x, y, z];
    }).then(() => {
        // threejs init
        setY = middleNode.value[2] + 5000

        if (!flag.value) {
            moveToCamera(cameraControls, 0, [0, setY * n, 0], [0, 0, 0]);
            flag.value = true;
        }

        grid = new THREE.GridHelper(((setY + 5000) * n * 2), 20);
        // group.position.y
        grid.renderOrder = 1;

        // setPosition(grid, ((averagePosition[0] - middleNode.value[0])) * n, -2, 0);
        scene.add(grid);
        restrictCameraVerticalDirection(cameraControls, 0, 0);
        cameraControls.minAzimuthAngle = 0;
        cameraControls.maxAzimuthAngle = 0;

        getSchemeTunGis(id).then(({ data: { Result } }) => {
            if (!Result || !Result.length) return
            Result.forEach(v => {
                // 渲染线段
                const newNodes = v.Nodes.map(k => {
                    const { x, y, z } = k;
                    const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { middlePosition: middleNode.value });
                    return { ...k, x: x1, y: y1, z: z1 }
                })
                if (v.Nodes.length > 1) {
                    const linePoints = newNodes.map(v => { return { x: v.x, y: v.y, z: v.z } })
                    let lineMesh = createLine({ THREE, linePoints, n, el: sceneDiv.value })
                    lineMesh.mid = v.TunID;
                    lineMesh.originPoints = v;
                    group.add(lineMesh);
                }
            })
        })
        let zMax = 0, zMin = 0;
        getTunList(id).then(v => {
            changeNeedNodeList(radioCurrent.value);
            if (!apiNodeList.value.length) return
            apiNodeList.value.forEach((v, i) => {
                // if (v.Vertex == 0 || !tunStr.includes(v.NodeID)) {

                const { x, y, z, NodeID } = v;
                if (NodeID > maxNodeID.value) {
                    maxNodeID.value = NodeID;
                }


                const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { middlePosition: middleNode.value })

                const [maxX, maxY, maxZ] = transformMaxPosition;
                x1 > maxX ? transformMaxPosition[0] = x1 : transformMinPosition[0] = x1;
                y1 > maxY ? transformMaxPosition[1] = y1 : transformMinPosition[1] = y1;
                if (z1 > zMax) {
                    zMax = z1;
                }
                if (z1 < zMin) {
                    zMin = z1;
                }

                //test 采区
                if (i == 500) {

                    let mesh = createMiningArea({ text: 'test001', scale: 5, position: { x: x1, y: y1, z: z1 }, originPoint: v, n })
                    miningAreaList.value.push(mesh);
                    group.add(mesh);
                }
                //
                let circle = createCicle({ text: NodeID, THREE: THREE, position: { x: x1, y: y1, z: z1 }, originPoint: v, n });
                group.add(circle);
                // }
            })
            transformMaxPosition[2] = zMax; transformMinPosition[2] = zMin;

            const zPy = (Math.abs(zMax) + Math.abs(zMin)) / 2
            group.position.z = -zPy + zMin / 2;

        })

    }).then(v => {
        changeEvent(operateID.value);
    })

}

function closeImportNode(isExit = false) {
    addCancel();
    updateCancel();
    removeCancel();
    tunCancel();
    updateTunCancel();
    removeTunCancel();
    ImportCancel();

    lineMeshArr = [];
    circleMeshArr = [];
    middleNode.value = [0, 0, 0];
    apiTunList.value = [];
    apiNodeList.value = [];
    tunStr.value = ``;

    grid && scene.remove(grid);
    global.value.clickRemove && global.value.clickRemove();
    global.value.dblRemove && global.value.dblRemove();
    global.value.repealRemove && global.value.repealRemove();
    group.remove(...group.children);

    if (isExit) {
        // global.value.stopRender();
        // global.value = null;
        newPointList.value = [];
        locationMesh && locationMesh.position.set(locationMesh.position.x, 2, locationMesh.position.y);
        locationMesh = null;
        sceneDiv.value = null;
    }
}

// 批量导点 表单

const pointList = ref([{ name: '', x: null, y: null, z: null }])
function addPoint() {
    pointList.value.push({ name: '', x: null, y: null, z: null });
}
function removePoint(index) {
    pointList.value.splice(index, 1);
}

// 新增节点

const onSubmit = () => {
    const newPoints = []
    let flag = true;
    for (var i = 0; i < pointList.value.length; i++) {
        const v = pointList.value[i]
        const { x, y, z, name } = v;
        // console.log(JSON.stringify(parseFloat(v.x)), parseFloat(v.y), parseFloat(v.z));
        if (JSON.stringify(parseFloat(x)) != 'null' && JSON.stringify(parseFloat(y)) != 'null' && JSON.stringify(parseFloat(z)) != 'null') {
            const bol1 = getType(parseFloat(x));
            const bol2 = getType(parseFloat(y));
            const bol3 = getType(parseFloat(z));
            flag = bol1 == 'number' && bol2 == 'number' && bol3 == 'number';
            if (!flag) {
                ElMessage.error('请保证您输入的是一个数字类型')
                break;
            } else {
                maxNodeID.value = ++maxNodeID.value;
                const vec3 = positionTransform(x, y, z, { middlePosition: middleNode.value })
                const point = { point: vec3, originPoint: { NodeID: maxNodeID.value, NodeName: name, x, y, z, state: 'new' } };
                newPoints.push(point);
            }
        } else {
            flag = false;
            ElMessage.error('请保证您输入的是一个数字类型')
            break;
        }
    }
    if (flag) {
        newPoints.length && Promise.allSettled(newPoints.map(async item => {
            let NodeAddObj = {
                NodeID: 0, NodeName: '', Num: 0, Vertex: 1, X: 0, Y: 0, Z: 0
            }
            const { originPoint: { NodeID, NodeName, x: X, y: Y, z: Z } } = item;
            NodeAddObj = { ...NodeAddObj, NodeName, NodeID, X, Y, Z }
            const { Status } = await addSchemeNode(NodeAddObj);
            return Status;
        })).then(result => {
            if (result.every(v => v.value == 0)) {
                ElMessage.success('保存成功')
                newPointList.value.push(...newPoints);
                closeImportNode();
                openImportNode();
                addCancel();
            }
        })
    }
}

// 取消节点保存
const addCancel = () => {
    addOutlinePass([])
    addOutlinePass1([])
    pointList.value = [{ name: '', x: null, y: null, z: null }];
}

var locationMesh = null;
// 定位到物体所在位置
function goNodeLocation(NodeID, addOffset = 100, isAddOutlinePass = true) {
    if (!NodeID) { ElMessage.warning('NodeID 不存在'); return };
    locationMesh && locationMesh.position.set(locationMesh.position.x, 2, locationMesh.position.z);
    if (group.children.length > 0) {
        let mesh = group.children.find(v => v.mid == NodeID);
        const { x, y, z } = mesh.position;
        isAddOutlinePass && global.value.addOutlinePass1([mesh]);
        mesh.position.set(x, 5, z);
        global.value.cameraFlyToMesh(global.value.cameraControls, { mesh: mesh, paddingTop: addOffset, paddingRight: addOffset, paddingBottom: addOffset, paddingLeft: addOffset });
        locationMesh = mesh;
    }
}

// 更新节点
const updateNodeInfo = ref({ NodeID: '', NodeName: '', x: null, y: null, z: null });
let changePoint2 = [] //统计被改变的点位数据
let findPoint2 = [] //统计已经添加过查找的数据


// 更新节点交互事件
function addInteraction2(el) {
    const { clickRemove } = modelClick(circleMeshArr, global.value.camera, (currentMesh) => {
        updateNodeInfo.value = { NodeName: '', x: null, y: null, z: null };
        checkCircleShelter(findPoint2, changePoint2, currentMesh.object)
        global.value.addOutlinePass1([currentMesh.object], 0xffab00);
        const { x, y, z, NodeID, NodeName } = currentMesh.object.originPoint;
        updateNodeInfo.value = { x, y, z, NodeID, NodeName };
    }, el)
    global.value.clickRemove = clickRemove;
}
async function updateSave() {
    const { x: X, y: Y, z: Z, NodeName, NodeID } = updateNodeInfo.value;
    const obj = apiNodeList.value.find(k => k.NodeID == NodeID)
    if (obj) {
        let NodeAddObj = {
            ...obj, X, Y, Z, NodeName
        }
        const { Status, Msg } = await updateSchemeNode(NodeAddObj);
        Status == 0 ? ElMessage.success('保存成功') : ElMessage.warning(Msg)
        Status == 0 && (closeImportNode(), openImportNode(), updateCancel());
    }
}
function updateCancel() {
    addOutlinePass([])
    addOutlinePass1([])
    updateNodeInfo.value = { NodeName: '', x: null, y: null, z: null };
    findPoint2 = []
    changePoint2 = [];
}

// 删除节点
const removeNodeInfo = ref({ NodeName: '', NodeID: '', Vertex: null });
let removeNodeMesh = null;
let changePoint4 = [];
let findPoint4 = [];
// 更新节点交互事件
function addInteraction3(el) {
    const { clickRemove } = modelClick(circleMeshArr, global.value.camera, (currentMesh) => {
        removeNodeMesh = null;
        removeNodeInfo.value = { NodeName: '', NodeID: '', Vertex: null }
        checkCircleShelter(findPoint4, changePoint4, currentMesh.object)

        global.value.addOutlinePass1([currentMesh.object], 0xffab00);
        const { NodeName, NodeID, Vertex } = currentMesh.object.originPoint;
        removeNodeInfo.value = { NodeName, NodeID, Vertex };
        removeNodeMesh = currentMesh.object;
    }, el)
    global.value.clickRemove = clickRemove;
}
async function removeSave() {
    const { NodeID, Vertex } = removeNodeInfo.value;
    const index = tunStr.value.findIndex(v => v.includes(NodeID));
    if (index !== -1) {
        ElMessage.warning('起始点需要在关联巷道删除完后才能删除')
        return;
    }

    if (NodeID) {
        const { Status } = await delSchemeNode(NodeID);
        Status == 0 ? ElMessage.success('保存成功') : ElMessage.error('保存失败')
        if (Status == 0) {
            // getNodeList();
            closeImportNode(); openImportNode();
            removeCancel();
        }
    }
}
function removeCancel() {
    addOutlinePass([])
    addOutlinePass1([])
    removeNodeInfo.value = { NodeName: '', NodeID: '', Vertex: null };
    removeNodeMesh = null;
    changePoint4 = [];
    findPoint4 = [];
}




// 设置节点关系
const nodeList = ref([{ originPoint: { x: null, y: null, z: null }, uuid: '' }]);
const tunOther = ref({
    TunName: '',
    Ventflow: 1,
    Width: 0
})

async function onSubmitSet() {
    const arr = [];
    let flag = true;
    for (var i = 0; i < nodeList.value.length; i++) {
        const v = nodeList.value[i]

        if (!v.point) {
            const { x, y, z } = v.originPoint;
            if (JSON.stringify(parseFloat(x)) != 'null' && JSON.stringify(parseFloat(y)) != 'null' && JSON.stringify(parseFloat(z)) != 'null') {
                const bol1 = getType(parseFloat(x));
                const bol2 = getType(parseFloat(y));
                const bol3 = getType(parseFloat(z));
                flag = bol1 == 'number' && bol2 == 'number' && bol3 == 'number';
                if (!flag) {
                    ElMessage.error('请保证您输入的是一个数字类型')
                    break;
                } else {
                    maxNodeID.value = ++maxNodeID.value;
                    const vec3 = positionTransform(x, y, z, { middlePosition: middleNode.value })
                    const point = { point: vec3, originPoint: { NodeID: maxNodeID.value, NodeName: '' + maxNodeID.value, x, y, z } };
                    arr.push(point);
                }
            } else {
                flag = false;
                ElMessage.error('请保证您输入的是一个数字类型')
                break;
            }
        } else {
            arr.push(v);
        }

    }
    if (flag) {
        const Nodes = arr.map(v => toRaw(v.originPoint));
        submitTunAdd(Nodes, tunCancel, tunOther.value, 0);
    }
}

// 巷道提交
async function submitTunAdd(arr, reset = () => { }, formContent, type = 0) {
    const Nodes = arr;
    const tunAddObj = {
        Place: 0, TunState: 1, SnodeID: 0, MnodeID: '', EnodeID: 0, Lx: 0, ShoringType: 0, ShapeType: 0, UseType: 0, Length: 0, Width: 0, Height: 0, S: 0, A: 0, R: 0, NetLine: true, Ventflow: 1,
        ...formContent,
    }
    console.log(tunAddObj, 'tunAddObj');
    tunAddObj.SnodeID = Nodes[0].NodeID; tunAddObj.EnodeID = Nodes[Nodes.length - 1].NodeID;
    tunAddObj.Length = computeTunLenght(Nodes);

    if (Nodes.length > 2) {
        tunAddObj.MnodeID = Nodes.slice(1, Nodes.length - 1).map(v => v.NodeID).join('-');
    } else {
        tunAddObj.MnodeID = '';
    }


    if (arr[0].NodeID && arr[0].Vertex == 1) {
        const { x, y, z, Number, NodeName } = arr[0];
        let NodeAddObj = {
            ...arr[0], NodeName: NodeName ?? '', Num: Number, Vertex: 0, X: x, Y: y, Z: z
        }
        const { Status } = await updateSchemeNode(NodeAddObj);
    }
    if (arr.at(-1).NodeID && arr.at(-1).Vertex == 1) {
        const { x, y, z, Number, NodeName } = arr.at(-1);
        let NodeAddObj = { ...arr.at(-1), NodeName: NodeName ?? '', Num: Number, Vertex: 0, X: x, Y: y, Z: z }
        const { Status } = await updateSchemeNode(NodeAddObj);
    }
    if (type == 0) {
        const { Status } = await addSchemeTun(tunAddObj);
        Status == 0 ? (closeImportNode(), openImportNode(), ElMessage.success('保存成功'), reset()) : ElMessage.error('保存失败')
        return Status
    } else {
        const { Status } = await updateSchemeTun(tunAddObj);
        Status == 0 ? (closeImportNode(), openImportNode(), ElMessage.success('保存成功'), reset()) : ElMessage.error('保存失败')
        return Status
    }

}


function tunCancel() {
    addOutlinePass([])
    addOutlinePass1([])
    nodeList.value = [{ originPoint: { x: null, y: null, z: null }, uuid: '' }];
    tunOther.value = {
        TunName: '',
        Ventflow: 1,
        Width: 0
    }
}
// addMiddlePoint
function addMiddlePoint(index) {

    const newPoint = { originPoint: { x: null, y: null, z: null }, uuid: '' }
    if (index != null) {
        nodeList.value.splice(index + 1, 0, newPoint);
    } else {
        nodeList.value.push(newPoint);
    }
}
// 移除
function removeMiddlePoint(index) {
    if (index == 0) {
        if (nodeList.value[0].originPoint.x == null) {
            ElMessage.warning('该项已被清空,请勿重复操作')
            return;
        }
        nodeList.value.splice(0, 1, { originPoint: { x: null, y: null, z: null }, uuid: '' });
    } else {
        nodeList.value.splice(index, 1);
    }
}


//
var changePoint = [] //统计被改变的点位数据
var findPoint = [] //统计已经添加过查找的数据


// 设置节点关系交互事件
function isSimilar(num1, num2, threshold) {
    return Math.abs(num1 - num2) <= threshold;
}
function addInteraction4(el) {
    const { clickRemove } = modelClick(circleMeshArr, global.value.camera, (currentMesh) => {
        if (currentMesh) {
            const { uuid } = currentMesh.object;
            const middleFlag = nodeList.value.find(v => v.uuid == uuid); // 判断有没有重复添加点位
            middleFlag && ElMessage.error('该点已被添加')
            if (middleFlag) return;
            checkCircleShelter(findPoint, changePoint, currentMesh.object);

            for (var i = 0, len = nodeList.value.length; i < len; i++) {
                const item = nodeList.value[i];
                if (!item.originPoint.x) {
                    nodeList.value.splice(i, 1, currentMesh.object);
                    break;
                } else {
                    (i == len - 1) && nodeList.value.splice(i, 1, currentMesh.object); // 仅允许替换中间点的最后一个点
                }
            }
            global.value.addOutlinePass(nodeList.value.filter(v => v.uuid), 0x0ca678);
        }
    }, el);

    // 双击保存当前节点
    addEvent(window, 'dblclick', setNodeDblclick)
    global.value.clickRemove = clickRemove;
    global.value.dblRemove = dblRemove;
    function removeEvent() {
        clickRemove();
        dblRemove();
    }
    return removeEvent;
}
function setNodeDblclick() {
    if (!nodeList.value.at(-1).originPoint.x) return;
    addMiddlePoint();
}
function dblRemove() {
    removeEvent(window, 'dblclick', setNodeDblclick)
}
// 更新节点关系
var updateNodeList = ref([{ originPoint: { x: null, y: null, z: null }, uuid: '', state: true }]);
const updateTunOther = ref({
    TunName: '',
    Ventflow: 1,
    Width: 0
})
var findPoint3 = [], changePoint3 = [];
let updateTunMesh = null
let changeIndex = null
let isFlag = true;
// 取消操作
function updateTunCancel() {
    addOutlinePass([])
    addOutlinePass1([])
    updateNodeList.value = [{ originPoint: { x: null, y: null, z: null }, uuid: '' }];
    updateTunOther.value = {
        TunName: '',
        Ventflow: 1,
        Width: 0
    }
    findPoint3 = [];
    changePoint3 = [];
    updateTunMesh && (updateTunMesh.material = lineMat);
    updateTunMesh = null;
    changeIndex = null;
    isFlag = true;
}
//
async function updateOnSubmit() {
    const arr = [];
    let flag = true;
    for (var i = 0; i < updateNodeList.value.length; i++) {
        const v = updateNodeList.value[i]

        if (!v.point) {
            const { x, y, z } = v.originPoint;
            if (JSON.stringify(parseFloat(x)) != 'null' && JSON.stringify(parseFloat(y)) != 'null' && JSON.stringify(parseFloat(z)) != 'null') {
                const bol1 = getType(parseFloat(x));
                const bol2 = getType(parseFloat(y));
                const bol3 = getType(parseFloat(z));
                flag = bol1 == 'number' && bol2 == 'number' && bol3 == 'number';
                if (!flag) {
                    ElMessage.error('请保证您输入的是一个数字类型')
                    break;
                }
            } else {
                flag = false;
                ElMessage.error('请保证您输入的是一个数字类型')
                break;
            }
        }
    }
    if (flag) {
        const Nodes = updateNodeList.value.map(v => toRaw(v.originPoint));
        if (updateNodeList.value.findIndex(v => !v.state) != -1) {
            const { Status } = await delSchemeTun(updateTunOther.value.TunID);
            Status == 0 ? ElMessage.success('删除原巷道成功') : ElMessage.warning('删除原巷道成功失败')
            const sliceIndex = []
            const sliceList = []
            updateNodeList.value.forEach((v, i) => {
                if (!v.state) {
                    sliceIndex.push(i)
                }
            })
            for (let index = 0; index < sliceIndex.length; index++) {
                const sliceI = sliceIndex[index];
                let arr = []
                if (index == 0) {
                    arr = Nodes.slice(0, sliceI + 1)
                }
                else {
                    const sliceFirst = sliceIndex[index - 1];
                    arr = Nodes.slice(sliceFirst, sliceI + 1)
                }
                sliceList.push(arr);
            }
            // 添加最后一段
            sliceList.push(Nodes.slice(sliceIndex.at(-1), Nodes.length));

            sliceList.length && Promise.allSettled(sliceList.map(async (item, i) => {
                const Status = submitTunAdd(item, () => { }, {}, 0);
                return Status
            })).then((res) => {
                if (res.every(v => v.value == 0)) {
                    updateCancel();
                    ElMessage.success('巷道打断成功')
                }
            })
            console.log(sliceList);
        } else {
            const Nodes = updateNodeList.value.map(v => toRaw(v.originPoint));
            submitTunAdd(Nodes, updateTunCancel, updateTunOther.value, 1);
        }
    }
}

function addInteraction5(el) {
    const { clickRemove } = modelClick(lineMeshArr, camera, (currentMesh) => {
        if (changeIndex == null && isFlag) {
            updateNodeList.value = [{ originPoint: { x: null, y: null, z: null }, uuid: '', state: true }];
            updateTunMesh && (updateTunMesh.material = lineMat, updateTunMesh = null)
            const { originPoints } = currentMesh.object;
            currentMesh.object.material = updateLineMat;
            updateTunOther.value = originPoints;


            updateNodeList.value = originPoints.Nodes.map(v => { return { originPoint: deepClone(v), uuid: '', state: true } });
            updateTunMesh = currentMesh.object;
        }
    }, el)
    const { clickRemove: clickRemove2 } = modelClick(circleMeshArr, global.value.camera, (currentMesh) => {
        if (currentMesh) {
            if (changeIndex == null) { ElMessage.warning({ message: '您需要点击左侧新增按钮或者编辑按钮才能进行操作', duration: 3000 }); return }
            const { uuid, originPoint } = currentMesh.object;

            const middleFlag = updateNodeList.value.find(v => v.uuid == uuid); // 判断有没有重复添加点位
            middleFlag && ElMessage.error('该点已被添加')
            if (middleFlag) return;
            checkCircleShelter(findPoint3, changePoint3, currentMesh.object);

            updateNodeList.value.splice(changeIndex, 1, { uuid, originPoint, state: true });

            global.value.addOutlinePass([currentMesh.object], 0x0ca678);

        }
    }, el);
    global.value.clickRemove = removeEvent;
    function removeEvent() {
        clickRemove();
        clickRemove2();
    }
}
// 添加
function updateAddPoint(index) {
    if (!updateAstrict()) return;
    const newPoint = { originPoint: { x: null, y: null, z: null }, uuid: '', state: true }
    if (index != null) {
        updateNodeList.value.splice(index + 1, 0, newPoint);
    } else {
        updateNodeList.value.push(newPoint);
    }
    changeIndex = index + 1;
}
// 移除
function updateRemovePoint(index) {
    if (updateNodeList.value.length == 1) {
        updateNodeList.value.splice(0, 1, { originPoint: { x: null, y: null, z: null }, uuid: '', state: true });
        ElMessage.warning('最后一项会进行保留')
        return;
    }
    updateNodeList.value.splice(index, 1);
}

// 编辑
function updateEditPoint(index) {
    if (!updateAstrict()) return;
    updateNodeList.value.splice(index, 1, { originPoint: { x: null, y: null, z: null }, uuid: '', state: true });
    changeIndex = index;
}
// 限制
function updateAstrict() {
    let flag = true;
    for (var i = 0, len = updateNodeList.value.length; i < len; i++) {
        const item = updateNodeList.value[i];
        if (!item.originPoint.x) {
            flag = false
            break;
        }
    }
    flag == false && ElMessage.warning('存在未填充的点');
    isFlag = flag;
    return flag;
}

// 删除节点关系
let tunID = null
let tunMesh = null
function addInteraction6(el) {
    const { clickRemove } = modelClick(lineMeshArr, camera, (currentMesh) => {
        tunMesh && (tunMesh.material = lineMat);
        const { mid } = currentMesh.object;
        tunID = mid;
        tunMesh = currentMesh.object;
        currentMesh.object.material = removeLineMat;
    }, el)

    global.value.clickRemove = clickRemove;
    global.value.repealRemove = removeTunCancel;
}
async function removeTunSave() {
    if (tunID) {
        const { Status } = await delSchemeTun(tunID);
        if (Status == 0) {
            ElMessage.success('删除成功')
            closeImportNode();
            openImportNode();
            tunID = null
        }
    }
}



//
function removeTunCancel() {
    tunID = null;
    tunMesh && (tunMesh.material = lineMat);
    tunMesh = null;
}

// 导入节点
const tableData = ref([])
const tableOriginData = ref([])
const tableHeader = ref(['X', 'Y', 'Z']);




//
function getImportData(val) {
    tableData.value = val.map(v => { return { ...v, x: v.X, y: v.Y, z: v.Z, state: 1, Vertex: 1 } });
    tableOriginData.value = val.map(v => { return { ...v, x: v.X, y: v.Y, z: v.z, state: 1, Vertex: 1 } });
    checkDouble(val).then(v => {
        if (!tableData.value.every(v => v.state == 1)) ElMessage.error('存在重复点，请及时处理')
    })
}

// 监测重复点的操作
function checkDouble(val) {
    const obj = { NodeID: -1, NodeName: '', Num: 0, Vertex: 1, x: 0, y: 0, z: 0 }
    const params = val.map(({ X, Y, Z }) => { return { ...obj, x: X, y: Y, z: Z } })
    return params.length && getSchemeNodeCheckDouble(params).then(({ Result }) => {
        if (!Result || !Result.length) return [];
        Result.forEach((v, i) => {
            const { x, y, z, NodeID, Vertex } = v;
            const index = tableData.value.findIndex(({ X, Y, Z }) => X == x && Y == y && Z == z);
            const lastIndex = tableData.value.findLastIndex(({ X, Y, Z }) => X == x && Y == y && Z == z);
            console.log(index, 'index');


            if (index != -1) {
                // tableData.value.splice(index, 1, { ...v, X: x, Y: y, Z: z, state: 1 });
                // index != lastIndex && lastIndex != -1 && tableData.value.splice(lastIndex, 1, { ...v, X: x, Y: y, Z: z, state: 1 });
                tableData.value.splice(index, 1, { ...tableData.value[index], state: 0 })
                index != lastIndex && lastIndex != -1 && tableData.value.splice(lastIndex, 1, { ...tableData.value[lastIndex], state: 0 })
                tableOriginData.value.splice(index, 1, { ...tableOriginData.value[index], state: 0 })
                index != lastIndex && lastIndex != -1 && tableOriginData.value.splice(lastIndex, 1, { ...tableOriginData.value[lastIndex], state: 0 })
            }
        })

        return Result;
    })
}

function ImportOnSubmit() {
    if (!tableData.value.every(v => v.state == 1)) return ElMessage.error('存在重复点，请及时处理')
    tableData.value.length && Promise.allSettled(tableData.value.map(async (item, i) => {
        if (item.NodeID) return { Status: 0, Result: item.NodeID };
        maxNodeID.value = ++maxNodeID.value;
        let NodeAddObj = {
            NodeID: maxNodeID.value, NodeName: '', Num: 0, Vertex: 1, X: 0, Y: 0, Z: 0,
            ...item
        }
        if (i == 0 || i == tableData.value.length - 1) NodeAddObj.Vertex = 0;
        const { Status, Result } = await addSchemeNode(NodeAddObj);
        return { Status, Result };
    })).then((res) => {
        const StatusList = res.map(v => v.value.Status);
        const NodeIDList = res.map(v => v.value.Result);
        if (StatusList.every(v => v == 0)) {
            tableData.value = tableData.value.map((v, i) => { return { ...v, NodeID: NodeIDList[i] } })
            submitTunAdd(tableData.value, ImportCancel);
        } else {
            ElMessage.warning('保存失败')
        }
    })
}

// 设置搜索范围 默认为5
const range = ref(5)
const intersectionPointList = ref([])
const intersectionIsUse = ref() // id array
// 重复点数组信息
const duplicatePointList = ref([])
const duplicateIsUse = ref()
function ImportCancel() {
    addOutlinePass([])
    addOutlinePass1([])
    tableData.value = [];
    tableData.value = []
    tableOriginData.value = []
    range.value = 5
    intersectionPointList.value = []
    intersectionIsUse.value = null // id array
    // 重复点数组信息
    duplicatePointList.value = []
    duplicateIsUse.value = null
}
function findIntersectionPoint(row) {
    let obj = {
        NodeID: 0, NodeName: '', Number: 0, Vertex: 1, StaticForce: 0, Temperature: 0, Humidity: 0, VaporForce: 0, Density: 0,
        SchemeID: 0, range: range.value, x: row.X, y: row.Y, z: row.Z
    }
    getSchemeNodeDistance(obj).then((v) => {
        intersectionPointList.value = v.Result.map(v => {
            let isUse = false;
            if (intersectionIsUse.value == v.NodeID) {
                isUse = true
            }
            return { ...v, isUse }
        });
    })
}
function changeSwitch(index, row, type = "intersection") {
    if (type == 'intersection') {
        if (intersectionIsUse.value) {
            const index = intersectionPointList.value.findIndex(v => v.NodeID == intersectionIsUse.value);
            index != -1 && intersectionPointList.value.splice(index, 1, { ...intersectionPointList.value[index], isUse: false })
            intersectionIsUse.value = null;
        }
    }
    else {
        if (duplicateIsUse.value) {
            const index = duplicatePointList.value.findIndex(v => v.NodeID == duplicateIsUse.value);
            index != -1 && duplicatePointList.value.splice(index, 1, { ...duplicatePointList.value[index], isUse: false })
            duplicateIsUse.value = null;
        }
    }
    if (row.isUse) {
        tableData.value.splice(index, 1, { ...row, X: row.x, Y: row.y, Z: row.z, state: 1 });
        if (type == 'intersection') {
            intersectionIsUse.value = row.NodeID
        } else {
            duplicateIsUse.value = row.NodeID
        }
    } else {
        tableData.value.splice(index, 1, tableOriginData.value[index]);
    }
}

function getCheckDouble(row) {
    checkDouble([row]).then(Result => {
        duplicatePointList.value = Result.map(v => {
            let isUse = false;
            if (duplicateIsUse.value == v.NodeID) {
                isUse = true
            }
            return { ...v, isUse }
        });
    })
}

// 采区配置
const miningAreaForm = ref([{ name: '', x: null, y: null, z: null }])
function addMiningArea() {
    miningAreaForm.value.push({ name: '', x: null, y: null, z: null });
}
function removeMiningArea(index) {
    miningAreaForm.value.splice(index, 1);
}


const miningAreaSubmit = () => {
    const newPoints = []
    let flag = true;
    for (var i = 0; i < miningAreaForm.value.length; i++) {
        const v = miningAreaForm.value[i]
        const { x, y, z, name } = v;
        if (JSON.stringify(parseFloat(x)) != 'null' && JSON.stringify(parseFloat(y)) != 'null' && JSON.stringify(parseFloat(z)) != 'null') {
            const bol1 = getType(parseFloat(x));
            const bol2 = getType(parseFloat(y));
            const bol3 = getType(parseFloat(z));
            flag = bol1 == 'number' && bol2 == 'number' && bol3 == 'number';
            if (!flag) {
                ElMessage.error('请保证您输入的是一个数字类型')
                break;
            } else {
                maxNodeID.value = ++maxNodeID.value;
                const vec3 = positionTransform(x, y, z, { middlePosition: middleNode.value })
                const point = { point: vec3, originPoint: { NodeID: maxNodeID.value, NodeName: name, x, y, z, state: 'new' } };
                newPoints.push(point);
            }
        } else {
            flag = false;
            ElMessage.error('请保证您输入的是一个数字类型')
            break;
        }
    }
    if (flag) {
        miningAreaList.value.push(...newPoints);
        newPoints.forEach(({ point, originPoint }) => {
            let mesh = createMiningArea({ text: originPoint.NodeName, scale: 5, position: point, originPoint, n })
            group.add(mesh);
        })

        // newPoints.length && Promise.allSettled(newPoints.map(async item => {
        //     let NodeAddObj = {
        //         NodeID: 0, NodeName: '', Num: 0, Vertex: 1, X: 0, Y: 0, Z: 0
        //     }
        //     const { originPoint: { NodeID, NodeName, x: X, y: Y, z: Z } } = item;
        //     NodeAddObj = { ...NodeAddObj, NodeName, NodeID, X, Y, Z }
        //     const { Status } = await addSchemeNode(NodeAddObj);
        //     return Status;
        // })).then(result => {
        //     if (result.every(v => v.value == 0)) {
        //         ElMessage.success('保存成功')
        //         newPointList.value.push(...newPoints);
        //         closeImportNode();
        //         openImportNode();
        //         addCancel();
        //     }
        // })
    }
}

// 取消节点保存
const addMiningAreaCancel = () => {
    addOutlinePass([])
    addOutlinePass1([])
    miningAreaForm.value = [{ name: '', x: null, y: null, z: null }];
}





// utils
// 节点避让
function checkCircleShelter(pointArr, changePointArr, mesh) {
    let threshold = 5;
    const { point, originPoint, uuid, position: { x, y, z } } = mesh;
    locationMesh && locationMesh.position.set(locationMesh.position.x, 2, locationMesh.position.z);
    mesh.position.set(x, 5, z);

    if (findPoint.findIndex(k => k.uuid == uuid) == -1 && changePoint.findIndex(k => k.uuid == uuid) == -1
        && changePoint2.findIndex(k => k.uuid == uuid) == -1 && (findPoint2.findIndex(k => k.uuid == uuid) == -1)
        && findPoint3.findIndex(k => k.uuid == uuid) == -1 && changePoint3.findIndex(k => k.uuid == uuid) == -1
        && findPoint4.findIndex(k => k.uuid == uuid) == -1 && changePoint4.findIndex(k => k.uuid == uuid) == -1) {
        const findArr = circleMeshArr.filter(v => isSimilar(v.originPoint.x, originPoint.x, threshold) && isSimilar(v.originPoint.y, originPoint.y, threshold));
        if (findArr.length > 1) {
            findArr.forEach((v, i) => {
                if (v.uuid == uuid) {
                    pointArr.push(v)
                    v.material.color = new global.value.THREE.Color(0xffc078);
                }
                else {
                    if (changePointArr.length == 0 || changePointArr.findIndex(k => k.uuid == v.uuid) == -1) {
                        const { x, y, z } = v.position;
                        v.position.set(x + (avoidRange * 2) * (i - 1), y, z + avoidRange * 2)
                        v.material.color = new global.value.THREE.Color(0x63e6be);
                        changePointArr.push(v);
                    }
                }
            })
        }
    }
    locationMesh = mesh;
}

// 创建 巷道线
function createLine({ THREE, linePoints = [], color = 0xffff00, n = 1, type = 0, el }) {
    const arr = type == 0 ? linePoints.map((item) => {
        const { x, y, z } = item;
        // return x1 < z1 ? new THREE.Vector3(x * n, 0, z * n) : new THREE.Vector3(z * n, 0, x * n);
        return new THREE.Vector3(x * n, 0, z * n)
    }) : linePoints;


    const arr1 = linePoints.map((item) => {
        const { x, y, z } = item;
        // return x1 < z1 ? new THREE.Vector3(x * n, 0, z * n) : new THREE.Vector3(z * n, 0, x * n);
        return [x * n, 0, z * n]
    }).flat();
    const lineMesh = createLine2(arr1, lineMat)
    lineMesh.points = linePoints;
    lineMesh.renderOrder = 2;
    lineMeshArr.push(lineMesh);

    return lineMesh;
}

const avoidRange = 30;
const geometry = new THREE.CircleGeometry(avoidRange, avoidRange / 2);
// 创建巷道节点
function createCicle({ bgColor, color, text, THREE, position = { x: 0, y: 0, z: 0 }, n = 1, originPoint }) {
    const circleMat = new THREE.MeshBasicMaterial({ color: 0xffffff });
    const circle = new THREE.Mesh(geometry, circleMat);
    const { x, y, z } = position;
    // x1 < z1 ? circle.position.set(x * n, 2, z * n) : circle.position.set(z * n, 2, x * n);
    circle.position.set(x * n, 2, z * n)
    circle.rotation.x = -Math.PI / 2;
    circle.point = position instanceof THREE.Vector3 ? position : new THREE.Vector3(x, y, z);
    circle.originPoint = originPoint;
    circle.mid = originPoint.NodeID;
    circleMeshArr.push(circle);

    var { texture } = canvasTexture(THREE, text,
        {
            fontsize: 110,
            borderColor: { r: 255, g: 0, b: 0, a: 1.0 },/* 边框黑色 */
            color: color ?? { r: 255, g: 255, b: 255, a: 1.0 },
            backgroundColor: bgColor ?? { r: 0, g: 151, b: 164, a: 1.0 }/* 背景颜色 */
        });
    circle.material.map = texture;
    circle.renderOrder = 3;
    // circle.material.transparent = true;
    // circle.material.opacity = 0.9;
    return circle;
}
// 创建采区
const miningAreaGeo = new THREE.PlaneGeometry(15, 12, 8, 8);
function createMiningArea({ text, position = { x: 0, y: 0, z: 0 }, scale = 1, n = 1, originPoint }) {
    const mat = new THREE.MeshBasicMaterial({ map: canvasTexture2(text ?? '采区工作面').texture });
    const mesh = new THREE.Mesh(miningAreaGeo, mat)
    const { x, y, z } = position;
    mesh.position.set(x * n, 2, z * n)
    mesh.rotation.x = -Math.PI / 2;
    setMeshScale(mesh, scale);
    mesh.point = position instanceof THREE.Vector3 ? position : new THREE.Vector3(x, y, z);
    mesh.originPoint = originPoint;
    mesh.mid = originPoint.NodeID;
    return mesh;
}



</script>

<style lang="scss">
@use '../assets/index.scss';
</style>

<style lang="scss" scoped>
.btn_menu {
    :deep(.el-button--large) {
        width: 135rem;
        height: 50rem;
        background-image: url('../assets/img/btn_bg.png');
        /* background-size: cover; */
        background-size: 100%;
    }

    :deep(.el-button--small) {
        width: 100rem;
        height: 30rem;
        background-image: url('../assets/img/btn_bg.png');
        background-size: 100%;
    }

    .btn_img {
        width: 16px;
        height: 16px;
    }
}

:deep(.el-button--small) {
    width: 100rem;
    height: 30rem;
    background-image: none;
    background-size: 100%;
}

:deep(.el-form-item__label) {
    color: #fff;
}

:deep(.el-dialog__header) {
    margin-left: 50px !important;
    position: relative;
}

:deep(.el-dialog__title) {
    position: absolute;
    top: 45px
}

:deep(.el-text) {
    color: #fff;
    font-size: 25px;
    margin: auto;
}

:deep(.el-input.is-disabled .el-input__inner) {
    -webkit-text-fill-color: #2962ff;
    cursor: not-allowed;
}

:deep(.el-radio.is-bordered.el-radio--large .el-radio__label) {
    color: #eee;
}

// 悬浮框
:deep(.el-alert--success.is-dark) {
    background-color: #1864ab;
}
</style>