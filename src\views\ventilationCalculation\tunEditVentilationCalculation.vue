<template>
    <div class="relative w-full h-full">
        <!-- <FourAngel w-full h-full flex justify-center items-center> -->
        <div class="w-100% h-100%" ref="sceneDiv"></div>
        <!-- </FourAngel> -->
        <!-- 操作菜单 -->
        <div class="btn_menu absolute top-50px left-10px flex flex-col h-300px justify-around items-start">
            <el-button dark color="#3271ae"
                :disabled="!currentOperateId || currentOperateId == 6 || currentOperateId == 7" size="small"
                class="ml-13px text-center" @click="repeal"> <img class="btn_img"
                    :src="getImg('repeal')" />&nbsp;撤销操作</el-button>
            <!-- <el-button dark color="#3271ae" size="small" class="ml-12rem text-center" @click="operateTun(1)"> <img
                    class="btn_img" :src="getImg('draw')" />&nbsp;巷道绘制</el-button> -->

            <el-button dark color="#3271ae" size="small" class="text-center" @click="operateTun(2)">
                <img class="btn_img" :src="getImg('edit')" />&nbsp;巷道编辑</el-button>
            <el-button dark color="#3271ae" size="small" class="text-center" @click="operateTun(3)"> <img
                    class="btn_img" :src="getImg('remove')" />&nbsp;巷道删除</el-button>
            <!-- <el-button dark color="#3271ae" size="small" class="text-center" @click="operateTun(4)"> <img
                        class="btn_img" :src="getImg('merge')" />&nbsp;合并节点</el-button> -->
            <el-button dark color="#3271ae" size="small" class="text-center" @click="operateTun(5)"> <img
                    class="btn_img" :src="getImg('interrupt')" />&nbsp;巷道打断</el-button>
            <el-button dark color="#3271ae" size="small" class="text-center" @click="(event) => operateTun(6, event)">
                <img class="btn_img" :src="getImg('tunEdit')" />&nbsp;巷道属性</el-button>
            <!-- <el-button dark color="#3271ae" size="small" class="text-center"
                @click="() => { isDialogShow = true; operateTun(0) }"> <img class="btn_img"
                    :src="getImg('pldd')" />&nbsp;设置节点</el-button> -->
            <!-- <el-button dark color="#5e35b1" size="small" class="text-center" @click="operateTun(4)"> <img
                        class="btn_img" :src="getImg('mnjs')" />&nbsp;模拟解算</el-button> -->
        </div>
        <!--  -->
        <!-- <div class="czsm_div" absolute bottom-717px right-68px v-show="currentOperateId"> -->

        <div v-show="tControlPosition" border="1px solid white"
            class=" rounded-10px absolute right-20px bottom-20px w-220px h-150px opacity-75 text-#f8f9fa font-bold bg-[#082237] flex flex-col justify-around items-center">
            <div class="w-180px text-left"><span class="font-bold font-30px">X:</span> {{ tControlPosition &&
                tControlOriginPosition.x.toFixed(5) }}</div>
            <div class="w-180px text-left"><span class="font-bold font-30px">Y:</span> {{ tControlPosition &&
                tControlOriginPosition.y.toFixed(5) }}</div>
            <div class="w-180px text-left"><span class="font-bold font-30px">Z:</span> {{ tControlPosition &&
                tControlOriginPosition.z.toFixed(5) }}</div>
        </div>
        <!-- 巷道属性修改 -->
        <el-dialog title="巷道属性修改" v-model="tunEditShow" width="40%" style="height: 550rem;position: relative;">
            <el-form class="mt-20px mr-10px" :model="tunEditInfo" ref="form" :rules="rules" label-width="80px"
                :inline="false" size="normal">
                <el-form-item label="巷道名称" prop="TunName">
                    <el-input v-model="tunEditInfo.TunName" clearable></el-input>
                </el-form-item>
                <el-form-item label="用风类型" prop="Ventflow">
                    <el-select v-model="tunEditInfo.Ventflow">
                        <el-option :value="1" label="进风"></el-option>
                        <el-option :value="0" label="用风"></el-option>
                        <el-option :value="-1" label="回风"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="宽度" prop="Width">
                    <el-input v-model="tunEditInfo.Width" clearable></el-input>
                </el-form-item>
            </el-form>

            <template #footer>
                <span class="absolute bottom-40px right-30px ">
                    <el-button @click="tunEditShow = false">取消</el-button>
                    <el-button type="primary" @click="saveTunEdit">保存</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 使用说明 -->
        <div v-show="infoShow"
            class="absolute left-0 top-350px w-400rem h-150rem w-100%  p-10px bg-[#1864abE6] text-[#ffff] flex flex-col justify-around">
            <div text-18px>操作说明</div>
            <div>1、巷道编辑需要点击巷道节点进行操作</div>
            <div>2、巷道编辑下面的三个操作都是点击巷道进行操作</div>
            <div class=" font-500 text-[#ffa39e]">3、双击会保存此次编辑操作,点击右侧保存才会存储</div>
        </div>
        <div class="btn_menu absolute right-2px top-100px">
            <el-button v-show="currentOperateId" class=" text-center" dark color="#4a4b9d" size="small"
                @click="operateTun(0)">退出当前模式</el-button>
            <el-button @click="infoShow = !infoShow" size="small" dark color="#4a4b9d">{{ infoShow ? '隐藏说明' : '显示说明'
                }}</el-button>
            <div class="right_btn" @click="handleSave"> <img class="btn_img" :src="getImg('save')" />&nbsp;保存</div>
            <div class="right_btn" @click="exitScene"> <img class="btn_img" :src="getImg('back')" />&nbsp;退出</div>
            <Toolbar :resetEffect="resetEffect" style="position:relative;scale: 0.8;right: -30px;bottom:0px"></Toolbar>
        </div>
        <!--  -->

    </div>


</template>

<script setup>
import { ref, onMounted, watch, onUnmounted, watchEffect, onBeforeUnmount, toRaw } from 'vue'
import { useThree } from '@/three/useThree';
import { onBeforeRouteLeave, useRouter } from 'vue-router';
import Toolbar from '../thereVentilation/components/toolbar/toolbar.vue';
import _ from 'lodash'
import mitt from '@/utils/eventHub';
import FourAngel from '@/components/common/fourAngel/index.vue';
// 重置效果
function resetEffect() {
    addOutlinePass([])
    restCamera()
}
const props = defineProps({
    schemeId: String,
})


const router = useRouter();
// 获取图片的方法
function getImg(name, type = 'png') {
    return new URL(`./assets/img/${name}.${type}`, import.meta.url)
}
const sceneDiv = ref(null)


const infoShow = ref(false);

onMounted(() => {
    mountRender(sceneDiv.value)
    mitt.on('get2DRecentNode', (mid) => {
        const mesh = [...pointGroup.children, ...middlePointList].find(v => v.mid == mid);
        cameraControls.fitToBox(mesh, true, { paddingTop: 600, paddingRight: 0, paddingBottom: 1000, paddingLeft: 0 })
    })
});
onBeforeRouteLeave(() => {
    initData && initData.dataReset();
    stopRender();
})




const isShow = ref(false)
watch(tControlPosition, (val) => {
    if (val) {
        isShow.value = true;
    } else {
        isShow.value = false;
    }
})

// 导入辅助坐标轴
import { positionTransform, createSpriteLabel } from "@/views/thereVentilation/js/tunUtils.js";
import { createTun, tun, createPoint, createLine } from '@/views/thereVentilation/js/tun.js';
import { TransformControls } from 'three/examples/jsm/controls/TransformControls.js';
import { deepCloneMaterial, setMeshScale } from "@/three/utils/meshUtils";
// 获取接口数据
import { meshPickup } from '@/three/raycaster'
import { ElMessage, ElMessageBox } from "element-plus";
import { addEvent, removeEvent } from '@/utils/window';
import { NodeConfig } from './js/config';
import { calculateDistance } from "@/views/thereVentilation/js/tunUtils.js";
import { addSchemeNode, delSchemeNode, updateSchemeNode, addSchemeTun, delSchemeTun, updateSchemeTun } from '@/https/encapsulation/ventilationCalculation';

import { moveToCamera } from '@/three/cameraControls.js'
import { tunInit, getTunList, tunLabelShowByCamera, getRecentNodeThrottleFn } from '@/views/thereVentilation/js/tunInitVentilationCalculation';
import {
    tunOriginList, pointMeshArr, tunPointsAll, pointShowAll, allGroup, meshGroup, pointGroup, middlePointList, spriteLabelGroup, maxPosition,
    minPosition, averagePosition, transformMaxPosition, transformMinPosition, setY, n, tunStr, apiTunList, apiNodeList, middleNode, maxNodeID
} from '../thereVentilation/js/tunInitVentilationCalculation';// 变量
const endCamera = [-6792, 500, -3620]
const { scene, mountRender, gsap, renderer, initModelArr, meshRemove, clearMesh, setPosition, restCamera, setMeshRotation, stopRender, cameraControls, ani, camera, THREE, addOutlinePass, outlinePass } = useThree({ endCamera, isControl: true, isComposer: true, isCameraAni: false, isAxes: false, isDepthBuffer: true });;
cameraControls.dollySpeed = 0.22;
cameraControls.truckSpeed = 1.1;
camera.far = 60000;

// 初始化配置
scene.background = new THREE.Color(0x04101F);
// scene.background = null;

let currentRemoveEvent = null;

var initData = null;
let schemeId = ref(null)
watch(() => props.schemeId, (id) => {
    if (initData) {
        schemeId.value = schemeId;
        updateScene(false);// 清除上一个场景
    }
    initData = tunInit(scene, cameraControls, id);
    initData.dataInit();
})







// 批量导点功能传输出来刷新页面
function updateScene(flag = true, isResetCamera = true) {
    if (isResetCamera) {
        restCamera();
        moveToCamera(cameraControls, 0, [-setY / 2, setY, -setY / 2], [0, 0, 0]);
    }
    initData && initData.dataReset(() => {
        currentRemoveEvent = null;
        resetVariable();
    });
    flag && initData && setTimeout(() => {
        initData.dataInit();
    }, 500)
}

// 控制说明面板是否显示
var tControlPosition = ref(null);
var tControlOriginPosition = ref({ x: 0, y: 0, z: 0 });
// 当前操作id
const currentOperateId = ref(null);

function operateTun(num, event) {
    addOutlinePass([]);
    currentOperateId.value = num;
    // 清除上次触发事件
    if (currentRemoveEvent && currentRemoveEvent instanceof Function) {
        currentRemoveEvent();
        tControlRemoveEvent();
    }
    let msg = ''
    switch (num) {
        case 1:
            msg = '当前处于绘制模式'
            const { clearEvent: clearEvent1 } = draw();
            tControlAddEvent();
            currentRemoveEvent = clearEvent1;
            break;
        case 2:
            msg = '当前处于编辑模式'
            tControlAddEvent();
            const { clearEvent: clearEvent2 } = edit();
            currentRemoveEvent = clearEvent2;
            break;
        case 3:
            msg = '当前处于删除模式'
            const { clearEvent: clearEvent3 } = remove();
            currentRemoveEvent = clearEvent3;
            tControlPosition.value = null;
            break;
        case 4:
            msg = '当前处于节点合并模式'
            const { clearEvent: clearEvent4 } = merge();
            currentRemoveEvent = clearEvent4;
            tControlPosition.value = null;
            break;
        case 5:
            msg = '当前处于打断巷道模式'
            const { clearEvent: clearEvent5 } = interrupt();
            currentRemoveEvent = clearEvent5;
            tControlPosition.value = null;
            break;
        case 6:
            msg = '当前处于巷道配置模式'
            const { clearEvent: clearEvent6 } = tunEdit();
            currentRemoveEvent = clearEvent6;
            tControlPosition.value = null;
            event.stopPropagation();
            break;

    }
    if (msg) {
        ElMessage.success(msg);
    }

}

// 重置之前存储的变量信息
function resetVariable() {
    drawReset();
    editReset();
    removeReset();
    mergeReset();
    interruptReset();
}


/** 绘制巷道*/
// 所需变量
const newTunData = [];
const drawChangeMesh = {
    tunTempList: [], //临时存储
    pointTempList: [],
    tunList: [],
    pointList: [],
}
let tubeMesh = null;    // 生成的临时巷道
let spherePoint = null; // 被拖动的辅助节点
let drawPositionList = []; // 临时存储编辑坐标的数组
let intersectObj = { // 存储相交节点坐标
    state: 0,
    vec3: null
}; // 判定是否相交
let startSelectPoint = null; //初始选中物体
let drawNodeList = [[]];
let drawLabel = 0;
function draw() {
    // 判定节点相交的距离
    const distanceNode = NodeConfig.radius * 2 + 2;
    // 添加事件
    addEvent(window, 'click', click);
    addEvent(window, 'mousemove', mouseMove);
    addEvent(window, 'dblclick', dblClick);
    // 选中节点
    function click(event) {
        const { currentMesh } = meshPickup(event, pointGroup.children, camera, sceneDiv.value)
        if (currentMesh && !tubeMesh) {
            startSelectPoint = currentMesh.object;

            drawPositionList[0] = currentMesh.object.point;
            const { x, y, z } = currentMesh.object.point;
            const mat = deepCloneMaterial(currentMesh.object.material)
            mat.color = new THREE.Color(0x00ff00);
            //console.log(x, y, z);
            if (!spherePoint) {
                spherePoint = new THREE.Mesh(new THREE.SphereGeometry(NodeConfig.radius + 2, 32, 32), mat);
                spherePoint.position.set(x, y, z);
                spherePoint.updateMatrixWorld();
                scene.add(spherePoint);
                scene.add(tControl);
                tControl.attach(spherePoint);
            }
        }
    }

    // 拖动节点移动
    function mouseMove(event) {
        if (drawPositionList[0] instanceof THREE.Vector3 && drawPositionList[1] instanceof THREE.Vector3) {
            const arr = pointGroup.children.filter(v => JSON.stringify(v.point) != JSON.stringify(startSelectPoint.point))
            // 监听节点相交
            const { currentMesh } = meshPickup(event, [...arr], camera, sceneDiv.value);
            if (currentMesh) {
                // //console.log(currentMesh, 'currentMesh', allMesh, 'allMeshallMesh');
                // 计算节点是否需要进行矫正
                if (currentMesh.object && currentMesh.object.point) {
                    const point1 = drawPositionList[1];
                    const point2 = currentMesh.object.point;
                    // 计算两个坐标点之间的距离

                    let distance = 0;
                    if (point1 && point2) {
                        distance = point1.distanceTo(point2);
                        intersectObj.state = distance <= distanceNode ? 1 : 0;
                    }
                    intersectObj.vec3 = currentMesh.object.point;
                    // //console.log(intersectObj, 'intersectObj');
                    // //console.log(distance, 'distance');
                }
            }

            if (tubeMesh) {
                tubeMesh.geometry = tun(drawPositionList, { isEditMat: true, ventflow: 1, tunName: 'newTun', radius: 8 }).tubeMesh.geometry;
            } else {
                tubeMesh = tun(drawPositionList, { isEditMat: true, ventflow: 1, tunName: 'newTun', radius: 8 }).tubeMesh;
            }
            meshGroup.add(tubeMesh);
        }
    }

    // 双击保留当前节点
    function dblClick(event) {

        if (!spherePoint) return;
        const { currentMesh: currentSphere } = meshPickup(event, [spherePoint], camera, sceneDiv.value);
        if (currentSphere && tubeMesh) {
            const { x, y, z } = drawPositionList[1];
            if (intersectObj.state == 1) {
                // 是否合并节点
                ElMessageBox.confirm(
                    '是否合并节点?',
                    '合并巷道',
                    {
                        confirmButtonText: '确认',
                        cancelButtonText: '取消',
                        type: 'warning',
                        callback: (action) => {
                            if (action === 'confirm') {
                                if (intersectObj.vec3) {
                                    drawPositionList[1] = intersectObj.vec3;
                                }
                                // 矫正巷道
                                tubeMesh.geometry = tun(drawPositionList, {
                                    isEditMat: true, ventflow: 1, tunName: 'newTun', radius: 8
                                }).tubeMesh.geometry;
                                ElMessage({
                                    type: 'success',
                                    message: '合并成功',
                                })
                            } else {
                                intersectObj.state = 0;
                                ElMessage({
                                    type: 'info',
                                    message: '取消操作',
                                })

                            }
                            defaultHandle();

                        },
                    }
                )
            } else {
                defaultHandle();
            }
            // 通用处理 默认执行
            function defaultHandle() {
                if (intersectObj.state != 1) {
                    const { sphere } = createPoint({ x, y, z })
                    sphere.state = 'new'
                    pointGroup.add(sphere);
                    drawChangeMesh.pointTempList.push(sphere);
                }

                intersectObj = {
                    state: 0,
                    vec3: null
                };
                const arr = drawPositionList.map(v => { return { x: v.x, y: v.y, z: v.z } })
                let index = 0;
                if (newTunData.length > 0) {

                    // 上一条巷道的末尾点 等于下一条巷道的起点 那么他们被视为同一条巷道
                    if (JSON.stringify(startSelectPoint.point) == JSON.stringify(newTunData.at(-1)[1])) {
                        index = drawLabel;
                    }
                    else {
                        let flag = true
                        for (var i = 0; i < drawNodeList.length; i++) {
                            let v = drawNodeList[i]
                            if (v.length > 0) {
                                if (JSON.stringify(startSelectPoint.point) == JSON.stringify(v.at(-1)[1])) {
                                    index = i;
                                    flag = false
                                    break;
                                }
                            }
                        }
                        if (flag) {
                            drawLabel = ++drawLabel;
                            if (drawNodeList[drawLabel] == undefined) {
                                drawNodeList[drawLabel] = [];
                            }
                            index = drawLabel
                        }
                    }

                } else {
                    index = 0;
                }
                drawPositionList[2] = index;
                drawNodeList[index].push([...arr, index]);

                // 判定是否存在两个巷道的其实
                // console.log(drawNodeList, 'drawNodeList');
                newTunData.push(drawPositionList);
                drawChangeMesh.tunTempList.push(tubeMesh);


                drawReset();
            }
        }
    }

    // 移除事件 退出当前模式时
    function clearEvent() {
        removeEvent(window, 'click', click);
        removeEvent(window, 'mousemove', mouseMove);
        removeEvent(window, 'dblclick', dblClick);
        // 处理数据 生成新巷道 
        drawChangeMesh.tunTempList.forEach(v => {
            meshGroup.remove(v);
        })
        drawNodeList.forEach((v, j) => {
            let arr = v.map(v => [v[0], v[1]])
            let arr1 = _.flattenDeep(arr); // 拉平
            let points = _.uniqWith(arr1, _.isEqual); //去重
            const Nodes = []
            // createTunOrigin()
            points.forEach(({ x, y, z }, i) => {
                const point = pointShowAll.find(v => v.point.x == x && v.point.y == y && v.point.z == z)
                let originPoint = point ? point.originPoint : createOriginPoint(x, y, z);
                Nodes.push(originPoint);
                const newPoint = drawChangeMesh.pointTempList.find(v => v.point.x == x && v.point.y == y && v.point.z == z);
                pointGroup.remove(newPoint);
                if (!point) {
                    const { sphere } = createPoint({ x, y, z, originPoint });
                    if (i == 0 || i == points.length - 1) {
                        pointShowAll.push(sphere);
                        pointMeshArr.push(sphere);
                        pointGroup.add(sphere);
                        sphere.name = 'new0';
                    } else {
                        sphere.name = 'new1';
                    }
                    drawChangeMesh.pointList.push(sphere);
                }
            })
            const tunOrigin = createTunOrigin(Nodes);
            tunOriginList.push(tunOrigin);
            const mesh = createTun({ tubePoints: points, originPoints: tunOrigin }).meshArr[0];
            meshGroup.add(mesh); tunPointsAll.push(points); drawChangeMesh.tunList.push(mesh);

        })
        //console.log(drawChangeMesh.tunList, drawChangeMesh.pointList);
        drawChangeMesh.tunTempList = []; drawChangeMesh.pointTempList = [];
        drawNodeList = [[]];
        drawReset();
    }
    return { clearEvent }
}
// 撤销
function drawRepeal() {
    if (tubeMesh) {
        meshGroup.remove(tubeMesh);
        drawReset()
    } else {
        if (drawChangeMesh.tunTempList.length < 1) return;
        let drawPoint = newTunData.pop();
        const index = drawNodeList[drawPoint[2]].find(v => JSON.stringify(v) == JSON.stringify(drawPoint))
        drawNodeList[drawPoint[2]].splice(index, 1);
        meshGroup.remove(drawChangeMesh.tunTempList.pop());
        pointGroup.remove(drawChangeMesh.pointTempList.pop());
    }
}
// 重置内容
function drawReset() {
    tubeMesh = null;
    drawPositionList = [];
    spherePoint && scene.remove(spherePoint);
    tControl && tControl.detach();
    tControl && scene.remove(tControl);
    spherePoint = null;
    tControlPosition.value = null;

}

// 将点位转为正式点位
function createOriginPoint(x, y, z) {
    const obj = positionTransform(x, y, z, { n, type: 1, middlePosition: middleNode.value })
    const originPoint = { NodeID: '', NodeName: '', ...obj }
    return originPoint;
}
// 创建Nodes 它将被携带到巷道之中
function createTunOrigin(originPoints, options = { Ventflow: 1, TunName: '-' }) {
    const { Ventflow, TunName } = options;
    return { TunName, TunID: '', Ventflow, S: '-', Q: '-', V: '-', H: '-', Nodes: originPoints }
}
//////////////////////////////////////////////////
/** 编辑巷道*/

// 变量
let originalTunPositionList = [];//存储巷道原始点位信息 方便恢复
let originalPositionList = [];//存储节点原始点位信息 方便恢复
let editChangeMesh = {             // 被修改的物体列表
    tunList: [],
    pointList: []
}
let intersectTunNum = null; // 节点相交数
let intersectTunList = []; // 相交物体数组
let currentPoint = null; // 选中的节点
let editPosition = null; // 记录节点所在坐标

function edit() {
    ElMessage.warning('点击节点移动巷道位置，禁止修改交叉点')
    // 添加事件
    addEvent(window, 'click', click);
    addEvent(window, 'mousemove', mouseMove);
    addEvent(window, 'dblclick', dblClick);
    // 选中节点
    function click(event) {
        const { currentMesh } = meshPickup(event, [...pointMeshArr], camera, sceneDiv.value)
        console.log(currentMesh.object, 'currentMesh');
        if (!currentPoint && currentMesh && currentMesh.object) {
            console.log(currentMesh.object, 'currentMesh');

            // 判定相交
            let num = 0;
            const point = currentMesh.object.point;
            currentPoint = currentMesh.object;
            const allArr = [];
            meshGroup.children.forEach(v => {
                const arr = v.points;
                if (JSON.stringify(arr).includes(JSON.stringify(point))) {
                    num += 1;
                    // 设置需要被更新的编号
                    v.needUpdateNum = JSON.stringify(v.points[0]) == JSON.stringify(point) ? 0 : v.points.length - 1;

                    intersectTunList.push(v);
                    let arr1 = [];
                    v.points.forEach(v => {
                        const { x, y, z } = v;
                        arr1.push(new THREE.Vector3(x, y, z));
                    })
                    allArr.push(arr1);
                }
            })
            intersectTunNum = num;
            if (intersectTunNum > 2) {
                ElMessage.warning('禁止修改交叉点');
                intersectTunNum = null;
                currentPoint = null;
                return
            }
            originalTunPositionList.push(allArr);
            const { x, y, z } = point;
            originalPositionList.push(new THREE.Vector3(x, y, z));
            editPosition = point;
            scene.add(tControl);
            tControl.attach(currentMesh.object);
        }
    }
    // 移动节点
    function mouseMove(event) {
        if (currentPoint && intersectTunNum <= 2 && intersectTunNum > 0 && editPosition instanceof THREE.Vector3 && intersectTunList.length == intersectTunNum) {

            intersectTunList.forEach(v => {
                let arr = v.points;
                arr[v.needUpdateNum] = editPosition;
                const { x, y, z } = editPosition;
                // 坐标转换
                const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { n, type: 1, middlePosition: middleNode.value })
                const updateOrigin = { ...v.originPoints.Nodes[v.needUpdateNum], x: x1, y: y1, z: z1 };
                v.points = arr;
                v.originPoints.Nodes[v.needUpdateNum] = updateOrigin;
                currentPoint.point = editPosition;
                currentPoint.originPoint = updateOrigin;
                v.geometry = tun(arr, { isEditMat: true }).tubeMesh.geometry;
            })
        }
    }

    // 保存当前编辑
    function dblClick() {
        if (currentPoint) {
            editChangeMesh.tunList.push(intersectTunList);
            editChangeMesh.pointList.push(currentPoint);
            editReset();
            //console.log(editChangeMesh.tunList, 'editChangeMesh.tunList');

        }
    }


    // 重置参数
    function editReset() {
        intersectTunNum = null;
        intersectTunList = [];
        currentPoint = null;
        editPosition = null;
        scene.remove(tControl);
        tControlPosition.value = null;
    }

    // 移除事件
    function clearEvent() {
        removeEvent(window, 'click', click);
        removeEvent(window, 'mousemove', mouseMove);
        removeEvent(window, 'dblclick', dblClick);
        editReset();
    }
    return { clearEvent }
}
// 右键取消编辑当前节点
function editRepeal(event) {
    // 取消当前操作
    if (currentPoint && originalTunPositionList.length >= 1) {
        ctrlZ(intersectTunList, currentPoint);
        editReset()
    }
    // 取消已经保存的节点操作
    if (originalTunPositionList.length >= 1 && originalPositionList.length >= 1) {
        let arr1 = editChangeMesh.tunList.pop();
        let arr2 = editChangeMesh.pointList.pop();
        ctrlZ(arr1, arr2);
    }
    function ctrlZ(tunArr, pointMesh) {
        const arr = originalTunPositionList.at(-1)
        tunArr.forEach((v, i) => {
            v.points = arr.at(i);
            v.geometry = tun(v.points, { isEditMat: true, tunName: 'editTun', radius: 8 }).tubeMesh.geometry;
        })
        const { x, y, z } = originalPositionList.at(-1);
        pointMesh.position.set(x, y, z);
        pointMesh.point = new THREE.Vector3(x, y, z);
        originalTunPositionList.pop();
        originalPositionList.pop();
    }
}

// 重置参数
function editReset() {
    intersectTunNum = null;
    intersectTunList = [];
    currentPoint = null;
    editPosition = null;
    tControl && scene.remove(tControl);
    tControlPosition.value = null;
}

///////////////////////////////////////////////////
/** 删除*/

// 变量
const removeChangeMesh = {
    tunList: [],
    pointList: []
}
let currentDelete = null;
function remove() {
    ElMessage.success('请点击选中巷道,之后双击会删除巷道')
    addEvent(window, 'click', click);
    addEvent(window, 'dblclick', dblClick);
    function click(event) {
        const { currentMesh } = meshPickup(event, meshGroup.children, camera, sceneDiv.value);
        if (currentMesh) {
            //console.log(currentMesh.object);
            currentDelete = currentMesh.object;
            addOutlinePass([currentMesh.object]);
        }
    }
    function dblClick(event) {
        if (currentDelete) {
            const i = meshGroup.children.findIndex(v => v.mid == currentDelete.mid);
            if (i != -1) {
                removeChangeMesh.tunList.push(meshGroup.children[i]);
                meshGroup.remove(currentDelete);
            }
            removeReset();
            // 判断是否有节点所连接的巷道已经清除完
            const tubePointArr = meshGroup.children.map(v => v.points);
            const removeArr = []
            pointGroup.children.forEach((item, i) => {
                const point = JSON.stringify(item.point);
                let num = 0;
                for (var j = 0; j < tubePointArr.length; j++) {
                    const tunList = tubePointArr[j]
                    if (JSON.stringify(tunList).includes(point)) {
                        let index = JSON.stringify(tunList[0]) == point && 0;
                        index = index != 0 && JSON.stringify(tunList[tunList.length - 1]) == point && tunList.length - 1;
                        if (index == 0 || index == tunList.length - 1) {
                            num += 1;
                            break;
                        }
                    }
                }
                if (num == 0) {
                    removeArr.push(item);
                }
            })
            removeArr.forEach(v => {
                removeChangeMesh.pointList.push(v);
                pointGroup.remove(v);
            })

        }
    }



    // 移除事件
    function clearEvent() {
        removeEvent(window, 'click', click);
        removeEvent(window, 'dblclick', dblClick);
    }
    return { clearEvent }
}
// 右键取消编辑当前节点
function removeRepeal(event) {
    // 取消当前操作
    if (currentDelete) {
        removeReset();
    } else {
        // 删除撤回
        if (removeChangeMesh.tunList.length > 0) {
            const mesh = removeChangeMesh.tunList.pop();
            const point = removeChangeMesh.pointList.pop();
            pointMeshArr.push(point);
            meshGroup.add(mesh);
            pointGroup.add(point);
        }
    }
}
function removeReset() {
    currentDelete = null;
    addOutlinePass([]);
}

///////////////////////////////////////////////////
/** 巷道合并*/

// 变量
const mergeChangeMesh = {
    tunList: [], // 被操作的巷道
    pointList: [], // 被操作移除的点位
    positionList: []
}
let mergeCurrentMesh = [null, null];
let relevancyTunList = [];
let positionArr = [];
let isExecute = true;
let isSave = false;
function merge() {
    addEvent(window, 'click', click);
    addEvent(window, 'dblclick', dblClick);
    function click(event) {
        const { currentMesh } = meshPickup(event, pointGroup.children, camera, sceneDiv.value);
        let isFlag = true;
        if (currentMesh && !isSave) {
            isExecute = true;
            currentMesh.object.material = changeMat('#ff0000');
            mergeCurrentMesh.length && (mergeCurrentMesh.forEach((v, i) => {
                if (v && currentMesh.object.uuid == v.uuid) {
                    isFlag = false;
                    mergeReset();
                }
                if (!isFlag && v) {
                    v.material = changeMat('#3742fa');
                }
            }))
            if (isFlag) {
                if (!mergeCurrentMesh[0]) {
                    mergeCurrentMesh[0] = currentMesh.object;
                    ElMessage.success('1节点已被选中')
                } else if (!mergeCurrentMesh[1]) {
                    mergeCurrentMesh[1] = currentMesh.object;
                    ElMessage.success('2节点已被选中')
                } else {
                    mergeCurrentMesh[1].material = changeMat('#3742fa');
                    mergeCurrentMesh[1] = currentMesh.object;
                    ElMessage.success('2节点已切换选中')
                }
            }
            // 判断合并节点是否在同一节点上
            if (mergeCurrentMesh[0] && mergeCurrentMesh[1]) {
                mergeCurrentMesh.forEach((v, i) => {
                    positionArr.splice(i, 1, v.point);
                })
                const indexList1 = [], indexList2 = [];
                tunPointsAll.forEach((v, i) => {
                    if (JSON.stringify(v).includes(JSON.stringify(positionArr[0]))) {
                        indexList1.push(i);
                    }
                    if (JSON.stringify(v).includes(JSON.stringify(positionArr[1]))) {
                        indexList2.push(i);
                    }
                })
                if (hasCommonItems(indexList1, indexList2)) {
                    isExecute = false;
                    ElMessage.error('不允许合并同一条巷道上的节点');
                } else {
                    // 查找与节点1相关的所有巷道模型
                    meshGroup.children.forEach(v => {
                        const arr = v.points;
                        if (JSON.stringify(arr).includes(JSON.stringify(positionArr[0]))) {
                            relevancyTunList.push(v);
                        }
                    })
                }
            }

        }
    }

    function dblClick() {
        if (isExecute && mergeCurrentMesh[0] && mergeCurrentMesh[1] && relevancyTunList.length) {
            isSave = true;
            changeTun(relevancyTunList, positionArr[0], positionArr[1], mergeCurrentMesh[1]);

            pointGroup.remove(mergeCurrentMesh[0]);

            let j = pointMeshArr.findIndex(v => v.uuid === mergeCurrentMesh[0].uuid);
            pointMeshArr.splice(j, 1);
            mergeCurrentMesh.forEach(v => {
                v.material = changeMat('#3742fa');
            })
            mergeChangeMesh.tunList.push(relevancyTunList);
            mergeChangeMesh.pointList.push(mergeCurrentMesh[0]);
            mergeChangeMesh.positionList.push(positionArr);
            mergeReset();
        }
    }





    // 判断两个长度相同的数组中 是否存在相同的项
    function hasCommonItems(arr1, arr2) {
        for (let i = 0; i < arr1.length; i++) {
            if (arr2.includes(arr1[i])) {
                return true;
            }
        }
        return false;
    }


    // 移除事件
    function clearEvent() {
        removeEvent(window, 'click', click);
        removeEvent(window, 'dblclick', dblClick);
        mergeReset();
    }
    return { clearEvent }
}
// 合并巷道 将被重新赋值
function changeTun(array = [], vec3Old, vec3New, newPointMesh) {
    array.length && array.forEach(v => {
        // 将与1点相关巷道数据 节点替换成 2点节点
        let index = v.points.findIndex(v => {
            const { x, y, z } = v;
            const { x: x1, y: y1, z: z1 } = vec3Old;
            return x == x1 && y == y1 && z == z1
        });
        const { mid, originPoint } = newPointMesh;
        v.SnodeID = index == 0 ? mid : null;
        v.EnodeID = index == v.points.length - 1 ? mid : null;
        v.points.splice(index, 1, vec3New);
        v.originPoints.Nodes.splice(index, 1, originPoint);
        v.geometry = tun(v.points, { tunName: 'mergeTun' }).tubeMesh.geometry;
    })
    //console.log(array, 'mergeNum');
}
function changeMat(color = "#3742fa") {
    const { mat } = createPoint({ isEditMat: true, color, radius: 8 })
    return mat
}
// 撤销操作
function mergeRepeal() {
    if (mergeChangeMesh.tunList.length) {
        const point = mergeChangeMesh.pointList.pop();
        const tunArr = mergeChangeMesh.tunList.pop();
        const pointArr = mergeChangeMesh.positionList.pop();
        changeTun(tunArr, pointArr[1], pointArr[0], point);
        pointGroup.add(point);
        pointMeshArr.push(point);
    }
}

function mergeReset() {
    mergeCurrentMesh.length && mergeCurrentMesh.forEach(v => {
        v && (v.material = changeMat('#3742fa'))
    })
    mergeCurrentMesh = [null, null];
    relevancyTunList = [];
    positionArr = [];
    isExecute = true;
    isSave = false;
}


/** 打断巷道*/
let interruptMovePosition = null;//存储悬浮点位坐标
let interruptCurrentTun = null;//存储悬浮点位坐标
let interruptMiddleLine = null;
const interruptChangeMesh = {
    addTunList: [],
    removeTunList: [],
    pointList: []
}
function interrupt() {
    addEvent(window, 'click', click);
    addEvent(window, 'mousemove', mouseMove);
    addEvent(window, 'dblclick', dblClick);
    function click(event) {
        const { currentMesh } = meshPickup(event, meshGroup.children, camera, sceneDiv.value);
        if (currentMesh) {
            // //console.log(currentMesh, 'currentMesh');
            // interruptMovePosition = currentMesh.point;
            if (interruptCurrentTun && interruptCurrentTun.mid != currentMesh.mid) {
                // 恢复上一个
                interruptCurrentTun.visible = true;
            }
            interruptCurrentTun = currentMesh.object;
            interruptCurrentTun.visible = false;
            if (!interruptMiddleLine) {
                interruptMiddleLine = createLine({ linePoints: interruptCurrentTun.points })
                scene.add(interruptMiddleLine)
            } else {
                interruptMiddleLine.geometry = createLine({ linePoints: interruptCurrentTun.points }).geometry;
            }
        }
    }
    function mouseMove(event) {
        if (interruptMiddleLine) {
            const { currentMesh: mesh } = meshPickup(event, [interruptMiddleLine], camera, sceneDiv.value);
            if (mesh) {
                interruptMovePosition = mesh.point;
                interruptMiddleLine.material.color = new THREE.Color(0xff0000);
            } else {
                interruptMiddleLine.material.color = new THREE.Color(0xffff00);
            }
        }
        const { currentMesh } = meshPickup(event, meshGroup.children, camera);
        if (currentMesh) {
            addOutlinePass([currentMesh.object], '#5f3dc4');
        } else {
            addOutlinePass([]);
        }
    }
    function dblClick() {
        if (interruptCurrentTun && interruptMovePosition) {
            const points = interruptCurrentTun.points;
            const { Ventflow, Nodes } = interruptCurrentTun.originPoints;
            let minDistance = 0;
            let num = 0;
            // 查找距离该点最近的巷道点位
            points.forEach((v, i) => {
                const distance = calculateDistance(interruptMovePosition, v);
                i == 0 && (minDistance = distance);
                if (distance < minDistance) {
                    minDistance = distance;
                    num = i;
                }
            })
            // 计算拾取点位 跟该点位哪个距离零点较近
            const distance1 = calculateDistance(points[num], points[0]);
            const distance2 = calculateDistance(interruptMovePosition, points[0]);
            let points1, points2, originsPoint1, originsPoint2;
            const { x, y, z } = interruptMovePosition;
            const originPoint = createOriginPoint(x, y, z);
            if (distance2 > distance1) {
                // 拆分节点 绘制两条巷道
                points1 = [...points.slice(0, num + 1), interruptMovePosition];
                points2 = [interruptMovePosition, ...points.slice(num + 1, points.length)]
                originsPoint1 = [...Nodes.slice(0, num + 1), originPoint];
                originsPoint2 = [originPoint, ...Nodes.slice(num + 1, Nodes.length)]
            } else {
                points1 = [...points.slice(0, num), interruptMovePosition];
                points2 = [interruptMovePosition, ...points.slice(num, points.length)]
                originsPoint1 = [...Nodes.slice(0, num), originPoint];
                originsPoint2 = [originPoint, ...Nodes.slice(num, Nodes.length)]
            }


            const originTun1 = createTunOrigin(originsPoint1, { Ventflow });
            const originTun2 = createTunOrigin(originsPoint2, { Ventflow });
            const mesh = createTun({ tubePoints: points1, originPoints: originTun1 }).meshArr[0];
            const mesh2 = createTun({ tubePoints: points2, originPoints: originTun2 }).meshArr[0];
            const { sphere } = createPoint({ x, y, z, originPoint });
            sphere.name = 'new0' // 标识为起始点位
            pointGroup.add(sphere); pointMeshArr.push(sphere);
            meshGroup.add(mesh); tunPointsAll.push(points1);
            meshGroup.add(mesh2); tunPointsAll.push(points2);
            meshGroup.remove(interruptCurrentTun);
            interruptChangeMesh.addTunList.push([mesh, mesh2]);
            interruptChangeMesh.removeTunList.push(interruptCurrentTun);
            interruptChangeMesh.pointList.push(sphere);
            //console.log(interruptChangeMesh.addTunList, 'interruptChangeMesh.addTunList');
            interruptReset();
        }
    }



    // 移除事件
    function clearEvent() {
        removeEvent(window, 'click', click);
        removeEvent(window, 'mousemove', mouseMove);
        removeEvent(window, 'dblclick', dblClick);
    }
    return { clearEvent }
}
// 撤销
function interruptRepeal() {
    if (interruptCurrentTun) {
        interruptReset();
    } else {
        if (interruptChangeMesh.addTunList.length > 0) {
            meshGroup.add(interruptChangeMesh.removeTunList.pop());
            interruptChangeMesh.addTunList.pop().forEach((v, i) => {
                meshGroup.remove(v);
                let index = tunPointsAll.findIndex(x => JSON.stringify(x) == JSON.stringify(v.points));
                tunPointsAll.splice(index, 1);
            })
            pointGroup.remove(interruptChangeMesh.pointList.pop());
        }

    }
}
// 移除操作
function interruptReset() {
    interruptCurrentTun && (interruptCurrentTun.visible = true);
    interruptMovePosition = null;
    interruptCurrentTun = null;
    interruptMiddleLine && scene.remove(interruptMiddleLine);
    interruptMiddleLine = null;
    addOutlinePass([]);
}
/** 撤销操作 */
function repeal() {
    if (currentOperateId.value) {
        switch (currentOperateId.value) {
            case 1: drawRepeal(); break;
            case 2: editRepeal(); break;
            case 3: removeRepeal(); break;
            case 4: mergeRepeal(); break;
            case 5: interruptRepeal(); break;
        }
    }
}
/** 巷道属性修改 */
const tunEditShow = ref(false);
const tunEditInfo = ref({});
let tunEditChangeMesh = null;
function tunEdit() {
    if (currentOperateId.value == 6) {
        addOutlinePass([]);
        tunEditAddEvent();
    }
    watch(tunEditShow, val => {
        clearEvent();
        if (!val && currentOperateId.value == 6) {
            addOutlinePass([]);
            setTimeout(() => {
                tunEditAddEvent();
            }, 10);
        }

    })

    function tunEditclick(event) {
        const { currentMesh } = meshPickup(event, meshGroup.children, camera, sceneDiv.value);
        if (currentMesh) {
            tunEditInfo.value = currentMesh.object.originPoints;
            addOutlinePass([currentMesh.object], 0xf08c00);
            tunEditShow.value = true;
            tunEditChangeMesh = currentMesh.object;
        }
    }
    function clearEvent() {
        removeEvent(window, 'click', tunEditclick);
    }
    function tunEditAddEvent() {
        addEvent(window, 'click', tunEditclick);
    }
    return { clearEvent }
}
async function saveTunEdit() {
    // ElMessage.success('修改已保存');
    const id = tunEditChangeMesh.mid;
    if (!id) {
        ElMessage.error('该巷道尚未上传,无法修改其属性');
    } else {
        const obj = apiTunList.value.find(v => v.TunID == id);
        const { Ventflow, TunName } = tunEditInfo.value;
        if (!obj) return;
        const MnodeID = obj.MnodeID ?? ''
        const tunObj = { ...obj, MnodeID, ...tunEditInfo.value }
        const { Status } = await updateSchemeTun(tunObj);
        if (Status == 0) {
            ElMessage.success('巷道修改成功');
            tunEditChangeMesh.material = tun(tunEditChangeMesh.points, { isEditMat: false, ventflow: Ventflow }).mat;
            // 替换label
            const { middlePoint: { x, y, z }, mid } = tunEditChangeMesh;
            const sprite = createSpriteLabel(x, y, z, TunName, mid);
            spriteLabelGroup.remove(spriteLabelGroup.children.find(v => v.mid == mid));
            spriteLabelGroup.add(sprite);
        } else {
            ElMessage.error('巷道修改失败');
        }
    }
    tunEditShow.value = false;
}





function handleSave() {
    saveScene().then(v => {
        setTimeout((() => {
            updateScene(true);
        }), 500)
    })
}

// 场景保存
async function saveScene() {
    operateTun(0);
    // 新增板块 
    // 查看编辑物体是否有编辑到新物体
    const addTunList = [...interruptChangeMesh.addTunList.flat(), ...drawChangeMesh.tunList].filter(v => v != undefined); // 打断巷道需要重新生成两条新巷道
    const removeTunList = [...interruptChangeMesh.removeTunList, ...removeChangeMesh.tunList].filter(v => v != undefined);//打断巷道时 删除原本的巷道
    const addPointList = [...interruptChangeMesh.pointList, ...drawChangeMesh.pointList].filter(v => v != undefined); // 在打断处生成新的连接 节点
    const removePointList = [...mergeChangeMesh.pointList].filter(v => v != undefined); // 合并时也需要删除节点
    const newPointList = [];

    if (addPointList.length) {
        createPoint().then(v => {
            createTun();
        })
    } else {
        createTun();
    }

    async function createTun() {
        addTunList.length && await Promise.allSettled(addTunList.map(async v => {
            if (removeTunList.length && removeTunList.find(k => k.uuid == v.uuid)) return;
            const editTun = editChangeMesh.tunList.find(k => k.uuid == v.uuid);
            let item = editTun ?? v;
            const tunAddObj = {
                TunName: '', Place: 0, TunState: 1, SnodeID: 0, MnodeID: '', EnodeID: 0, Ventflow: 1, Lx: 0, ShoringType: 0, ShapeType: 0, UseType: 0, Length: 0, Width: 0, Height: 0, S: 0, A: 0, R: 0, NetLine: true
            }
            const { originPoints } = item;
            const { Nodes, Length } = originPoints;

            tunAddObj.SnodeID = findId(Nodes[0]);
            tunAddObj.EnodeID = findId(Nodes[Nodes.length - 1]);
            tunAddObj.Length = Length;

            function findId(info) {
                const { NodeID, x: x1, y: y1, z: z1 } = info;
                if (NodeID) {
                    return NodeID
                } else {
                    const findNode = newPointList.find(({ x, y, z }) => { return x == x1 && y == y1 && z == z1 });
                    return findNode.NodeID;
                }
            }

            if (Nodes.length > 2) {
                tunAddObj.MnodeID = Nodes.slice(1, Nodes.length - 1).map(v => findId(v)).join('-');
            } else {
                tunAddObj.MnodeID = '';
            }
            const { Status } = await addSchemeTun(tunAddObj);
            return Status;
        })).then((result) => {
            if (result.every(v => v.value == 0)) {
                drawChangeMesh.tunList = [] // 
                interruptChangeMesh.addTunList = [] //打断新增的
            }
        })
    }
    function createPoint() {
        return addPointList.length && Promise.allSettled(addPointList.map(async v => {
            if (removePointList.length && removePointList.find(k => k.uuid == v.uuid)) return;
            // let item = pointGroup.children.find(k => k.uuid == v.uuid);
            const editPoint = editChangeMesh.pointList.find(k => k.uuid == v.uuid);
            let item = editPoint ?? v;
            //console.log(item, 'item');
            if ((removeChangeMesh.pointList.length && JSON.stringify(removeChangeMesh.pointList).includes(JSON.stringify(item))) || !item) return;
            let NodeAddObj = {
                NodeID: 0, NodeName: '', Num: 0, Vertex: 1, X: 0, Y: 0, Z: 0
            }
            const { originPoint: { NodeID, x: X, y: Y, z: Z }, name } = item;
            NodeAddObj.Vertex = name == 'new0' ? 0 : 1;
            NodeAddObj = { ...NodeAddObj, NodeID, X, Y, Z }
            const { Status, Result } = await addSchemeNode(NodeAddObj);
            newPointList.push({ NodeID: Result, x: X, y: Y, z: Z })
            return Status;
        })).then(result => {
            if (result.every(v => v.value == 0)) {
                drawChangeMesh.pointList = []
                interruptChangeMesh.pointList = []
            }
        })
    }

    // 编辑
    editChangeMesh.pointList.length && Promise.allSettled(editChangeMesh.pointList.filter(v => v != undefined).map(async v => {
        if (v.name.includes('new') || !v) return;
        const { originPoint: { x: X, y: Y, z: Z } } = v;
        const obj = apiNodeList.value.find(k => k.NodeID == v.mid)
        let NodeAddObj = {
            ...obj,
            X, Y, Z
        }
        const { Status } = await updateSchemeNode(NodeAddObj);;
        return Status;
    })).then(result => {
        if (result.every(v => v.value == 0)) {
            editChangeMesh.pointList = []
            editChangeMesh.tunList = []
        }
    })
    // 合并物体
    const mergeTunList = mergeChangeMesh.tunList.flat().filter(v => v != undefined);
    mergeTunList.length && Promise.allSettled(mergeTunList.map(async v => {
        if (v.name.includes('new') || !v) return;
        const obj = apiTunList.value.find(k => k.TunID == v.mid)
        if (obj) {
            let tunAddObj = {
                ...obj,
            }
            tunAddObj.SnodeID = v.SnodeID ?? tunAddObj.NodeID; tunAddObj.EnodeID = v.EnodeID ?? tunAddObj.NodeID;
            const { Status } = await updateSchemeTun(tunAddObj);
            return Status;
        } else {
            return 1;
        }

    })).then(result => {
        if (result.every(v => v.value == 0)) {
            mergeChangeMesh.tunList = []
        }
    })
    // // 删除板块
    removeTunList.length && Promise.allSettled(removeTunList.map(async v => {
        if (v.mid == '' || !v) return;
        const { Status } = await delSchemeTun(v.mid);
        const nodes = v.originPoints.Nodes;

        getTunList().then(() => {
            nodes.length && nodes.forEach(async (v, i) => {
                if (!v) return;
                const index = tunStr.value.findIndex(k => k.includes(v.NodeID));
                if (index === -1) {
                    const { Status } = await delSchemeNode(v.NodeID);;
                    return Status;
                }
            })
        })

    })).then(result => {
        if (result.every(v => v.value == 0)) {
            interruptChangeMesh.removeTunList = []
            removeChangeMesh.tunList = []
            removeChangeMesh.pointList = []
        }
    })
    removePointList.length && Promise.allSettled(removePointList.map(async v => {
        if (v.name.includes('new') || !v) return;
        const { Status } = await delSchemeNode(v.mid);;
        return Status;
    })).then(result => {
        if (result.every(v => v.value == 0)) {
            removeChangeMesh.pointList = []
            mergeChangeMesh.pointList = []
        }
    })
    return Promise.resolve();
}
function exitScene() {
    router.replace('./threeVentilate')
}

// 变换控制器
let tControl = new TransformControls(camera, renderer.domElement);
// removeEvent(tControl, 'change', tControlChange)
function tControlChange() {
    const bool = typeof tControl.object != 'undefined' && typeof tControl.object.position != 'undefined' && typeof tControl.object.position.x != 'undefined' && typeof tControl.object.position.y != 'undefined' && typeof tControl.object.position.z != 'undefined'
    // 首尾节点不相等
    if (bool) {
        const { x, y, z } = tControl.object && tControl.object.position;
        if (currentOperateId.value == 1) {
            if (drawPositionList[0] instanceof THREE.Vector3 && JSON.stringify(drawPositionList[0]) !== JSON.stringify(tControl.object.position)) {
                drawPositionList[1] = new THREE.Vector3(x, y, z);
                tControlPosition.value = { x, y, z };
                const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { n, type: 1, middlePosition: middleNode.value })
                tControlOriginPosition.value = { x: x1, y: y1, z: z1 }
            }
        } else if (currentOperateId.value == 2) {
            if (currentPoint) {
                editPosition = new THREE.Vector3(x, y, z);
                tControlPosition.value = { x, y, z };
                const { x: x1, y: y1, z: z1 } = positionTransform(x, y, z, { n, type: 1, middlePosition: middleNode.value })
                tControlOriginPosition.value = { x: x1, y: y1, z: z1 }
            }
        }
    }
}

//  操作变换控制器时暂时禁用其他控制器
function tControlDraggingChanged(event) {
    if (currentOperateId.value == 1) {
        if (drawPositionList[0] instanceof THREE.Vector3) {
            cameraControls.enabled = !event.value;
        }
    } else {
        cameraControls.enabled = !event.value;
    }
}
function tControlAddEvent() {
    addEvent(tControl, 'change', tControlChange);
    addEvent(tControl, 'dragging-changed', tControlDraggingChanged);
}
function tControlRemoveEvent() {
    removeEvent(tControl, 'change', tControlChange);
    removeEvent(tControl, 'dragging-changed', tControlDraggingChanged);
}



ani((...args) => {
    const [time, camera, delta] = args
    tunLabelShowByCamera && tunLabelShowByCamera(camera);
    // getRecentNodeThrottleFn && getRecentNodeThrottleFn(camera);
})












</script>

<style lang="scss" scoped>
.czsm_div {
    width: 271rem;
    height: 171rem;
    background-image: url('./assets/img/czsm.png');
    background-size: 100%;
}

.right_btn {
    width: 90rem;
    height: 30rem;
    background-image: url('./assets/img/back_save_bg.png');
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    margin-top: 10px;
    font-size: 15px;
    cursor: pointer;
}

.btn_img {
    width: 16px;
    height: 16px;
}

.btn_menu {
    :deep(.el-button--large) {
        width: 135rem;
        height: 50rem;
        background-image: url('./assets/img/btn_bg.png');
        /* background-size: cover; */
        background-size: 100%;
    }

    :deep(.el-button--small) {
        width: 100rem;
        height: 30rem;
        background-image: url('./assets/img/btn_bg.png');
        background-size: 100%;
    }
}


:deep(.el-form-item__label) {
    color: #fff;
}

:deep(.el-form-el-dialog__header) {
    margin: 10px 0 0 10px;
    color: #fff;
}


body {
    overflow: hidden;
}

:v-deep(.el-button .el-icon) {
    height: 0.5em;
    width: 0.5em;
}

.norem-box {
    /* padding: 20px;
    background-color: #000; */
    color: white;
    margin: 20px;
    display: flex;


    .norem-btn {
        /* postcss-pxtorem-disable */
        display: flex;
        justify-content: center;
        align-items: center;
        width: 55px;
        height: 25px;
        font-size: 12px;
        border-radius: 0;
        cursor: pointer;
        background-color: rgba(0, 255, 255, 0.6);
        margin: 0.5px;
        color: white;

        &:hover {
            background-color: rgba(0, 255, 255, 0.8);
        }
    }

    .norem-btn1 {
        width: auto;

        &:hover {
            background-color: rgba(0, 255, 255, 0.6);
        }

        .norem-btn1-1 {
            background-color: rgba(255, 255, 255, 0.8);
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: #fff;
            display: flex;
            justify-items: center;
            align-items: center;

            &:hover {
                background-color: rgba(255, 255, 255, 1.0);
            }
        }
    }
}

.scene {
    width: 100vw;
    height: 100vh;
    position: absolute;
    left: 0;
    top: 0;
}
</style>