<template>
    <div class="h-1080px p-2px pt-100px bg-#041120 grid grid-rows-2 grid-cols-3 gap-10px">
        <FourAngel p-2px col-span-2>
            <TunEditVentilationCalculation :schemeId="schemeId"></TunEditVentilationCalculation>
        </FourAngel>
        <FourAngel p-2px col-span-1>
            <VentilationNetworkVentilationCalculation></VentilationNetworkVentilationCalculation>
        </FourAngel>
        <FourAngel p-2px col-span-2>
            <SetNodeManuallyVentilationCalculation :schemeId="schemeId"></SetNodeManuallyVentilationCalculation>
        </FourAngel>
        <FourAngel p-2px col-span-1>
            <div class="bg-[#04101F] w-full h-full text-[#fff] p-20px">
                <div mb-20px>
                    <label class="text-16px">方案列表: </label>
                    <el-select mx-10px v-model="schemeId" clearable collapse-tags placeholder="选择你要加载的方案"
                        popper-class="custom-header" :max-collapse-tags="1" style="width: 200rem;">
                        <el-option v-for="item in schemeList" :key="item.value" :label="item.SchemeName"
                            :value="item.ID" />
                    </el-select>


                    <el-popover :visible="visible1" placement="top" width="200rem">
                        <div>
                            <div class=" my-8px text-[#fff]"><label>方案名称：</label> <el-input v-model="form1.SchemeName"
                                    size="normal" clearable></el-input>
                            </div>
                            <div>
                                <el-button size="small" type="info" @click="cancel(1)">
                                    取消
                                </el-button>
                                <el-button size="small" type="primary" @click="save(1)">
                                    保存
                                </el-button>
                            </div>
                        </div>
                        <template #reference>
                            <el-button type="primary" size="small" @click="closeDialog(1)">新增方案</el-button>
                        </template>
                    </el-popover>
                    <el-popover :visible="visible2" placement="top" width="200rem">
                        <div class=" my-8px text-[#fff]"><label>方案名称：</label> <el-input v-model="form.SchemeName"
                                size="normal" clearable></el-input>
                        </div>
                        <el-button size="small" type="info" @click="cancel(2)">
                            取消
                        </el-button>
                        <el-button size="small" type="primary" @click="save(2)">
                            保存
                        </el-button>

                        <template #reference>
                            <el-button v-show="form.ID" type="warning" size="small"
                                @click="closeDialog(2)">编辑方案</el-button>
                        </template>
                    </el-popover>
                    <el-popover :visible="visible3" placement="top" width="200rem">
                        <div>
                            <div class=" my-8px text-[#fff]"><label>方案名称：</label> <el-input v-model="form.SchemeName"
                                    size="normal" clearable></el-input>
                            </div>
                            <el-button size="small" type="info" @click="cancel(3)">
                                取消
                            </el-button>
                            <el-button size="small" type="primary" @click="save(3)">
                                保存
                            </el-button>
                        </div>
                        <template #reference>
                            <el-button v-show="form.ID" type='danger' size="small"
                                @click="closeDialog(3)">删除方案</el-button>
                        </template>
                    </el-popover>
                </div>
                <div class="text-20px font-bold text-[#fff] ">通风解算结果 </div>
                <el-divider style="border-color: #228be6;" />
                <el-space :size="20" direction="vertical">
                    暂无数据
                </el-space>
            </div>
        </FourAngel>
    </div>
    <!-- <div class="h-990px  mt-90px bg-#262626E6">
        <el-row class="h-50%" :gutter="5">
            <el-col :span="12" class="">

                <TunEditVentilationCalculation :schemeId="schemeId">
                </TunEditVentilationCalculation>
            </el-col>
            <el-col :span="12">
                <VentilationNetworkVentilationCalculation></VentilationNetworkVentilationCalculation>
            </el-col>
        </el-row>
        <el-row class="h-50% mt-8px" :gutter="5">
            <el-col :span="12" class="bg-#062136">
                <SetNodeManuallyVentilationCalculation :schemeId="schemeId"></SetNodeManuallyVentilationCalculation>
            </el-col>
            <el-col :span="12">
                <div class="bg-[#082237] w-full h-full text-[#fff] p-20px">
                    <div mb-20px>
                        <label class="text-16px">方案列表: </label>
                        <el-select mx-10px v-model="schemeId" clearable collapse-tags placeholder="选择你要加载的方案"
                            popper-class="custom-header" :max-collapse-tags="1" style="width: 200rem;">
                            <el-option v-for="item in schemeList" :key="item.value" :label="item.SchemeName"
                                :value="item.ID" />
                        </el-select>


                        <el-popover :visible="visible1" placement="top" width="200rem">
                            <div>
                                <div class=" my-8px text-[#fff]"><label>方案名称：</label> <el-input
                                        v-model="form1.SchemeName" size="normal" clearable></el-input>
                                </div>
                                <div>
                                    <el-button size="small" type="info" @click="cancel(1)">
                                        取消
                                    </el-button>
                                    <el-button size="small" type="primary" @click="save(1)">
                                        保存
                                    </el-button>
                                </div>
                            </div>
                            <template #reference>
                                <el-button type="primary" size="small" @click="closeDialog(1)">新增方案</el-button>
                            </template>
                        </el-popover>
                        <el-popover :visible="visible2" placement="top" width="200rem">
                            <div class=" my-8px text-[#fff]"><label>方案名称：</label> <el-input v-model="form.SchemeName"
                                    size="normal" clearable></el-input>
                            </div>
                            <el-button size="small" type="info" @click="cancel(2)">
                                取消
                            </el-button>
                            <el-button size="small" type="primary" @click="save(2)">
                                保存
                            </el-button>

                            <template #reference>
                                <el-button v-show="form.ID" type="warning" size="small"
                                    @click="closeDialog(2)">编辑方案</el-button>
                            </template>
                        </el-popover>
                        <el-popover :visible="visible3" placement="top" width="200rem">
                            <div>
                                <div class=" my-8px text-[#fff]"><label>方案名称：</label> <el-input
                                        v-model="form.SchemeName" size="normal" clearable></el-input>
                                </div>
                                <el-button size="small" type="info" @click="cancel(3)">
                                    取消
                                </el-button>
                                <el-button size="small" type="primary" @click="save(3)">
                                    保存
                                </el-button>
                            </div>
                            <template #reference>
                                <el-button v-show="form.ID" type='danger' size="small"
                                    @click="closeDialog(3)">删除方案</el-button>
                            </template>
                        </el-popover>
                    </div>
                    <div class="text-20px font-bold text-[#fff] ">通风解算结果 </div>
                    <el-divider style="border-color: #228be6;" />
                    <el-space :size="20" direction="vertical">
                        暂无数据
                    </el-space>
                </div>
            </el-col>
        </el-row>
    </div> -->

</template>
<script setup>
import SetNodeManuallyVentilationCalculation from './components/setNodeManuallyVentilationCalculation.vue';
import TunEditVentilationCalculation from './tunEditVentilationCalculation.vue';
import VentilationNetworkVentilationCalculation from '../ventilationNetwork/ventilationNetworkVentilationCalculation.vue';
import FourAngel from '@/components/common/fourAngel/index.vue';
import { addScheme, delScheme, getSchemeList, updateScheme } from '@/https/encapsulation/ventilationCalculation';
import { ref, watch, toRaw } from 'vue'
import { ElMessage } from 'element-plus';
import { deepClone2 } from '@/utils/utils';
import { useVentilationCalculation } from '@/store/ventilationCalculation';
import { useTunEdit } from '@/store/tunEdit';
import mitt from '@/utils/eventHub';
const store = useVentilationCalculation();
// const store = useTunEdit();
// 添加图标
// 引入svg
const checkIcon = new URL('./assets/svg/select.svg', import.meta.url).href;
const roamIcon = new URL('./assets/svg/drag.svg', import.meta.url).href;

// 操作模式
const interactionMode = ref(1);

function changeOperationMode(index) {
    interactionMode.value = index;
    store.$patch(() => {
        store.interactionMode = index;
    })
}

const svgCurrentIndex = ref(1);
const svgList = [
    { icon: checkIcon, content: '选择模式' },
    { icon: roamIcon, content: '漫游模式' },
]
const svgCurrentIndex1 = ref(-1);

const toolList = ref(
    [
        {
            icon: 'fullScreen.svg',
            content: '全屏展示'
        },
        {
            icon: 'lookDown.svg',
            content: '俯视'
        },
        {
            icon: 'look.svg',
            content: '正视'
        },
        {
            icon: 'sideLook.svg',
            content: '侧视'
        },
        {
            icon: 'reset.svg',
            content: '重置相机'
        },
        
    ]
)
toolList.value = toolList.value.map(item => ({
    ...item,
    icon: new URL(`./assets/svg/${item.icon}`, import.meta.url),
}));
let isFullScreen = ref(false)
const handleClick = (i) => {
    svgCurrentIndex1.value = i;
    switch (i) {
        case 0:
            isFullScreen.value ? exitFullscreen() : enterFullscreen();
            break;
        case 1: mitt.emit('moveCamera', 'up'); break;
        case 2: mitt.emit('moveCamera', 'front'); break;
        case 3: mitt.emit('moveCamera', 'side'); break;
        case 4: mitt.emit('resetCamera', true); break;
        // case 3: router.push('/tunEdit'); break;
        case 5: break;
        case 6: break;
    }
    // 进入全屏模式
    function enterFullscreen() {
        var element = document.documentElement;
        if (element.requestFullscreen) {
            element.requestFullscreen();
        } else if (element.mozRequestFullScreen) { // Firefox
            element.mozRequestFullScreen();
        } else if (element.webkitRequestFullscreen) { // Chrome, Safari and Opera
            element.webkitRequestFullscreen();
        } else if (element.msRequestFullscreen) { // IE/Edge
            element.msRequestFullscreen();
        }
        isFullScreen.value = true
    }

    // 退出全屏模式
    function exitFullscreen() {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.mozCancelFullScreen) { // Firefox
            document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) { // Chrome, Safari and Opera
            document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) { // IE/Edge
            document.msExitFullscreen();
        }
        isFullScreen.value = false
    }

}

// 切换方案
const schemeList = ref([])
const schemeId = ref()
async function getScheme() {
    const { data: { Result } } = await getSchemeList()
    schemeList.value = Result;
    if (Result.length) {
        schemeId.value = Result[0].ID
        store.$patch({
            schemeId: Result[0].ID
        })
    }


}
getScheme();

const visible1 = ref(false)
const visible2 = ref(false)
const visible3 = ref(false)
function closeDialog(type) {
    visible1.value = false
    visible2.value = false
    visible3.value = false
    type == 1 ? visible1.value = true : (type == 2 ? visible2.value = true : visible3.value = true)

}
const form1 = ref({ SchemeName: '' })
const form = ref({ ID: '', SchemeName: '' })

function cancel(type) {
    visible1.value = false
    visible2.value = false
    visible3.value = false
}

async function save(type) {
    switch (type) {
        // let data ={}
        case 1:
            const { data: { Status: Status1 } } = await addScheme(form1.value)
            handle(Status1)
            form1.value = { SchemeName: '' }
            break;
        case 2:
            const { data: { Status: Status2 } } = await updateScheme(form.value)
            form.value = { ID: '', SchemeName: '' }
            handle(Status2)
            break;
        case 3:
            const { data: { Status: Status3 } } = await delScheme(form.value.ID)
            form.value = { ID: '', SchemeName: '' }
            handle(Status3)
            break;
    }
    function handle(Status, Msg) {
        if (Status == 0) {
            ElMessage.success('保存成功')
            cancel();
            getScheme();
        } else {
            ElMessage.warning(Msg)
        }
    }
}

watch(schemeId, (val) => {
    if (val) {
        store.$patch({
            schemeId: val
        })
        form.value = deepClone2(toRaw(schemeList.value.find(v => v.ID == val)));
    } else {
        form.value = { ID: '', SchemeName: '' }
    }
})
</script>
<style lang="scss" scoped>
/* :deep(.el-button--small) {
    width: 100rem;
    height: 30rem;
    background-image: none;
    background-size: 100%;
}

:deep(.el-form-item__label) {
    color: #fff;
}

:deep(.el-dialog__header) {
    margin-left: 50px !important;
    position: relative;
}

:deep(.el-dialog__title) {
    position: absolute;
    top: 45px
}

:deep(.el-text) {
    color: #fff;
    font-size: 25px;
    margin: auto;
}

:deep(.el-input.is-disabled .el-input__inner) {
    -webkit-text-fill-color: #2962ff;
    cursor: not-allowed;
}

:deep(.el-radio.is-bordered.el-radio--large .el-radio__label) {
    color: #eee;
}

// 悬浮框
:deep(.el-alert--success.is-dark) {
    background-color: #1864ab;
} */
</style>