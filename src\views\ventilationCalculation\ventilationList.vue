<template>
  <div class="box statementBox">
    <!-- 头部分装 -->
    <encapsulationTable>
      <template #title> 用风地点</template>

      <!-- 左边 -->
      <template #top-left>
        <p class="bullue1" @click="add(formRef)">
          <img class="img" src="../businessCenter/img/plusNew.png" alt="" />新增
        </p>
      </template>
 <!-- 左边 -->
    
      <template #top-center>
        <p class="bullue" @click="checEdit">
          <img class="img" src="../businessCenter/img/editNew.png" alt="" />编辑
        </p>
      
      </template>
      <template #top-right>
        <p class="red" @click="checkbox">
          <img class="img" src="../businessCenter/img/del.png" alt="" />删除
        </p>
      </template>
      
      <template #table>
        <el-table
          ref="multipleTableRef"
          :data="tableData"
          highlight-current-row
          :cell-style="{ textAlign: 'center' }"
          :header-cell-style="{ 'text-align': 'center' }"
          @selection-change="handleSelectionChange"
          style="overflow: auto; height: 90%;width: 100%;"
        >
        <el-table-column type="selection" width="55" />
          <el-table-column type="index" width="60" label="序号" />
          <el-table-column prop="Name" label="名称" width="140"/>
           <el-table-column prop="AirVolume" label="实际风量(m3/min)"  />
         <el-table-column prop="Requiredairflow" label="需风量(m3/min)"  />
         <el-table-column label="类型" align="center" >
            <template #default="scope">
              <span v-if="scope.row.Type == '0'">其他</span>
              <span v-if="scope.row.Type == '1'">采煤工作面</span>
              <span v-if="scope.row.Type == '2'">掘进工作面</span>
              <span v-if="scope.row.Type == '3'">机电硐室</span>
              <span v-if="scope.row.Type == '4'">爆破材料库</span>
            </template>
          </el-table-column>
        
          <!-- <el-table-column prop="Description" label="" /> -->
          <el-table-column prop="EnterTemperature" label="进风温度"  />
          <el-table-column  prop="Gas"  label="瓦斯涌出量(m3/t)"  />
          <!-- <el-table-column prop="LastModifyTime" label="" /> -->
         
          <el-table-column prop="OutTemperature" label="回风温度"  />
          <el-table-column prop="PersonNum" label="人员数量"  />
          
           <el-table-column prop="Length" label="长度(m)" />
          
          <el-table-column prop="TotolPower" label="总功率(kW)" />
          <el-table-column prop="Transect" label="断面大小" />
          <el-table-column prop="WindSpeed" label="风速(m/s)"  />
           <el-table-column prop="LocalFanVolume" label="局通吸风量" /> 
            <el-table-column prop="Area" label="材料库面积(m2)"/>
        

          <el-table-column label="操作" >
            <template #default="scope">
              <div style="display: flex" >
                <!-- <div class="bullue1 pl-2px"  @click="updateC(scope.row)">修改</div> -->
               
                <div class="bullue1 pl-2px"   @click="compu(scope.row)">计算</div>
                 <!-- <div class="userRde pl-2px"  @click="del(scope.row)">删除</div> -->  

              </div>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pages>
        <el-pagination
          :page-sizes="[5, 10, 15, 20, 25]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </template>
    </encapsulationTable>

    <!-- 弹框 -->
    <el-dialog v-model="dialogShow" :title="dialogTitle" style="height: 500px;">
      <div
        v-if="dialogTitle == '新增' || dialogTitle == '编辑'"
        style="width: 80%;margin-top: 30px;"
      >
        <el-form
          :label-position="right"
          :model="windList"
          ref="formRef"
          style="margin-left: -55px"
        >
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="名称" prop="Name">
                <el-input
                  v-model="windList.Name"
                  placeholder="请输入名称"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="实际风量(m3/min)" prop="AirVolume">
                <el-input
                  v-model="windList.AirVolume"
                  placeholder="请输入实际风量"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="材料库面积(m2)" prop="Area">
                <el-input
                  v-model="windList.Area"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="类型" prop="Type">
                <el-select
                  style="width: 100%"
                  v-model="windList.Type"
                  placeholder="请选择"
                  size="large"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="进风温度" prop="EnterTemperature">
                <el-input
                  v-model="windList.EnterTemperature"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="瓦斯涌出量(m3/t)" prop="Gas">
                <el-input
                  v-model="windList.Gas"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="局通吸风量" prop="LocalFanVolume">
                <el-input
                  v-model="windList.LocalFanVolume"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="回风温度" prop="OutTemperature">
                <el-input
                  v-model="windList.OutTemperature"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="人员数量" prop="PersonNum">
                <el-input
                  v-model="windList.PersonNum"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="需风量(m3/min)" prop="Requiredairflow">
                <el-input
                  v-model="windList.Requiredairflow"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
           <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="总功率(kW)" prop="TotolPower">
                <el-input
                  v-model="windList.TotolPower"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="断面大小" prop="Transect">
                <el-input
                  v-model="windList.Transect"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="风速(m/s)" prop="WindSpeed">
                <el-input
                  v-model="windList.WindSpeed"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </el-col>
             <el-col :span="12">
              <el-form-item label="长度" prop="Length">
                <el-input
                  v-model="windList.Length"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </el-col>
           
          </el-row>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer pt-40px pr-20px" >
          <el-button @click="dialogShow = false">取消</el-button>
          <el-button
            v-if="dialogTitle == '新增'"
            type="primary"
            @click="addList"
          >
            确认
          </el-button>
          <el-button
            v-if="dialogTitle == '编辑'"
            type="primary"
            @click="editEdit"
          >
            提交
          </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 弹窗结束 -->

    <!-- 计算需风量弹窗 -->
    <el-dialog v-model="dialogShowCom" :title="dialogTitle1">
      <div
        v-if="dialogTitle1 == '计算'"
        style="max-width: 80%"
      >
        <div class="table-container">
    <table class="custom-table">
      <!-- 第一行：两列 -->
      <tr>
        <td class="cell">
          <div class="sjair">实际风量(m3/min)：<p>{{ AirVolume }}</p></div></td>
        <td class="cell"><div class="sjair">需风量(m3/min):<p>{{ Requiredairflow }}</p></div></td>
      </tr>
      
      <!-- 第二行：一列（跨两列） -->
      <tr>
        <td class="cell" colspan="2"><div class="sjair">建议:<p>{{ Plan }}</p></div></td>
      </tr>
    </table>
  </div>
          </div>
          </el-dialog>
          <!-- 弹窗结束 -->
  </div>
</template>
<script setup>
import encapsulationTable from "../businessCenter/components/table.vue";
import { ApiWindlocaList,ApiWindlocaAdd,ApiWindlocaUpdate,ApiWindlocaDel,ApiVentflow } from "../../https/encapsulation/threeDimensional";
import { ElMessage, ElMessageBox } from "element-plus";
// 引入样式
import "../businessCenter/css/index.scss";
import { onMounted, ref, reactive } from "vue";
// import "../../css/el-pagination.scss" //xmm

// 弹框名字
const dialogTitle = ref("");
const dialogTitle1 = ref("");
const dialogShow = ref(false);
const dialogShowCom = ref(false);

onMounted(() => {
  alarmList();
});

const value1 = ref("");
const windList = reactive({
  ID: "",
  Name: "",
  AirVolume: "",
  Area: "",
  EnterTemperature: "",
  Gas: "",
  LocalFanVolume: "",
  OutTemperature: "",
  PersonNum: "",
  Requiredairflow: "",
  TotolPower: "",
  Transect: "",
  WindSpeed: "",
  Type: "",
  Length: "",
  
});
// 获取选中的
const SelectList = ref([]);
//定义需风计算
const Requiredairflow = ref("");
const Plan = ref("");
const AirVolume = ref("");

const options = [
  { value: "0", label: "其他" },
  { value: "1", label: "采煤工作面" },
  { value: "2", label: "掘进工作面" }, 
  { value: "3", label: "机电硐室" }, 
  { value: "4", label: "爆破材料库" }, 

]
const tableData = ref([]);
const pageIndex = ref(1);
const pageSize = ref(5);
const total = ref();
// 获取列表
const alarmList = () => {
  ApiWindlocaList().then((res) => {
    tableData.value = res.Result;
    // total.value = res.PageInfo.Total;
   
  });
};

// 选择框修改
const checEdit = async () => {
  if (SelectList.value.length == 0) {
    ElMessage({
      message: "请至少选中一行",
      grouping: true,
      type: "warning",
      messageBox: true,
    });
  } else if (SelectList.value.length > 1) {
    ElMessage({
      message: "最多选择一个设备",
      grouping: true,
      type: "warning",
      messageBox: true,
    });
  } else {
    SelectList.value.forEach((item) => {
      updateC(item);
    });
  }
};

// 获取选中的
const handleSelectionChange = (val) => {
  SelectList.value = val;
  console.log(SelectList.value, "选中");
};

const add = (formEl) => {
  dialogTitle.value = "新增";

  windList.ID = "";
  windList.Name = "";
  windList.AirVolume = "";
  windList.Area = "";
  windList.EnterTemperature = "";
  windList.Gas = "";
  windList.LocalFanVolume = "";
  windList.OutTemperature = "";
   windList.PersonNum = "";
  windList.Requiredairflow = "";
   windList.TotolPower = "";
  windList.Transect = "";
   windList.WindSpeed = "";
  windList.Type = "";
  windList.Length = "";

  dialogShow.value = true;
};
// 新增
const addList = async () => {
  console.log(windList, "添加");
  ApiWindlocaAdd(windList).then((res) => {
    console.log(res, "添加");
    if (res.Status == 0) {
      ElMessage({
        type: "success",
        grouping: true,
        message: "成功",
      });
      dialogShow.value = false;
      alarmList();
    } else if (res.Status == 3) {
      ElMessage({
        message: "添加失败",
        type: "warning",
      });
      dialogShow.value = false;
    }
  });
};

const formRef = ref(null);
//修改
const updateC = async (row) => {
  console.log(11111, row);
  dialogTitle.value = "编辑";
  dialogShow.value = true;
   windList.ID = row.ID;
   windList.Name = row.Name;
   windList.AirVolume = row.AirVolume
   windList.Area = row.Area;
   windList.EnterTemperature = row.EnterTemperature;
   windList.Gas = row.Gas;
   windList.LocalFanVolume = row.LocalFanVolume;
   windList.OutTemperature = row.OutTemperature;
   windList.PersonNum = row.PersonNum;
  windList.Requiredairflow = row.Requiredairflow;
   windList.TotolPower = row.TotolPower;
  windList.Transect = row.Transect;
   windList.WindSpeed = row.WindSpeed;
  windList.Type = row.Type;
  windList.Length = row.Length;

  let type;
  if (row.Type == "0") {
    type = "其他";
  } else if (row.AlarmType == "1") {
    type = "采煤工作面";
  } else if (row.AlarmType == "2") {
    type = "掘进工作面";
  } else if (row.AlarmType == "3") {
    type = "机电硐室";
  } else {
    type = "爆破材料库";
  }

  windList.Type = type;
};

const editEdit = async () => {
  ApiWindlocaUpdate(windList).then((res) => {
    console.log(windList, "$$$$$$$$$$$");
    
    if (res.Status == 0) {
      ElMessage({
        type: "success",
        grouping: true,
        message: "成功",
      });
      dialogShow.value = false;

      alarmList();
    } else if (res.Status == 3) {
      ElMessage({
        message: "修改失败",
        type: "warning",
      });
      dialogShow.value = false;
    }
  });
};
// 多选删除
const checkbox = () => {
  if (SelectList.value.length == 0) {
    ElMessage({
      message: "请至少选中一行",
      grouping: true,
      type: "warning",
      messageBox: true,
    });
  } else {
    ElMessageBox.confirm("此操作将永久删除该文件, 是否继续?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        SelectList.value.forEach((item) => {
           ApiWindlocaDel(item.ID).then((res) => {
            if (res.Status == 0) {
              Tips();
              alarmList();
            }
          });
        });
      })
      .catch(() => {
        ElMessage({
          type: "info",
          grouping: true,
          message: "已取消",
        });
      });
  }
};
// 删除
const del = (row) => {
  console.log(row, "删除警告");
  ElMessageBox.confirm("此操作将永久删除, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      ApiWindlocaDel(row.ID).then((res) => {
        if (res.Status == 0) {
          alarmList();
          Tips();
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "已取消",
      });
    });
};

// 成功提示
const Tips = () => {
  ElMessage({
    type: "success",
    message: "删除成功",
  });
};

//计算需风量
const compu = (row) => {
  console.log(row.ID, "计算11111"); 
  dialogTitle1.value = "计算";

  dialogShowCom.value = true;
  ApiVentflow(row.ID).then((res) => {
    AirVolume.value = res.Result.AirVolume;
    Requiredairflow.value = res.Result.Requiredairflow;
    Plan.value = res.Result.Plan;
    console.log(res, "计算");
  });
}
// 分页
const handleSizeChange = (val) => {
  pageSize.value = val;
  alarmList();
};
const handleCurrentChange = (val) => {
  pageIndex.value = val;
  alarmList();
};
</script>
<style lang="scss" scoped>
.box {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  color: #fff;
}

.underline {
  cursor: pointer;
}

.underline:hover {
  font-size: 15px;
  color: #315efb;
}

.el-upload__tip {
  color: #fff;
}



.editB {
  width: 100px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #40f4f1;
  color: #fff;
  cursor: pointer;
  border-radius: 16px;
  margin: auto;
}


.userRde {
  width: 80px;
  height: 32px;
  margin-left: 3px;
  display: flex;
  justify-content: center;
  align-items: center;
}

::v-deep .el-form-item__label {
  color: #fff !important;
  width: 130px !important;
}

::v-deep .el-form {
  margin-top: 15px;
}

// 修改输入框
::v-deep .el-input__wrapper {
  background-color: transparent !important;
}

::v-deep .el-input__inner {
  color: #fff !important;
}

::v-deep .el-textarea__inner {
  background-color: transparent !important;
  color: #fff;
}

// 修改扇形
::v-deep .el-tree-node__content:active {
  background-color: transparent !important;
}

::v-deep .el-tree {
  background-color: transparent !important;
  color: #fff;

  :hover {
    background: rgba(10, 162, 238, 0.25);
  }
}

::v-deep .el-form-item__label {
  width: 120px;
}

::v-deep .el-tabs__item.is-active {
  color: #409eff;
}

::v-deep .el-tabs__item {
  color: #fff;
}

.scroll-container {
  height: 350px;
  /* 设置容器高度 */
  overflow: auto;
  /* 显示滚动条 */

  /* 自定义滚动条样式 */
  scrollbar-color: #999 transparent;
  /* 滚动条颜色 */
  scrollbar-width: thin;
  /* 滚动条宽度 */
}

.scroll-container::-webkit-scrollbar {
  width: 6px;
  /* 设置滚动条宽度 */
  height: 6px;
  /* 设置滚动条高度 */
}

.scroll-container::-webkit-scrollbar-thumb {
  background-color: #4d89c9;
  /* 设置滚动条 thumb 颜色 */
}

::v-deep .el-select--large .el-select__wrapper {
  min-height: 32rem;
}

// xmm修改 2025 02 12  修改表格样式
::v-deep .el-table th {
  color: #ffb30f;
  /* 修改表头字体颜色 */
  font-weight: bold;
  /* 可选：加粗字体 */
}

/* 自定义高亮样式 */
::v-deep .el-table__body tr.current-row > td {
  background-color: #5e430a;
  /* 高亮背景色 */
}

::v-deep .el-dialog {
  max-width: 850px;
  min-width: 300px;

  max-height: 450px;
  min-height: 320px;
  background: rgb(0, 0, 0) !important;
  box-shadow: rgb(255, 179, 15) 0px 0px 20px inset !important;
  border: 1px solid rgba(255, 179, 15, 0.3) !important;
  border-radius: 10px !important;
}

::v-deep .el-button + .el-button {
  background-color: rgba(255, 179, 15, 0.3) !important;
  border: 1px solid rgba(232, 157, 6, 0.8) !important;
}

// xmm修改 2025 02 24  修改表格样式
::v-deep .el-table td.el-table__cell div {
  --un-text-opacity: 1;
  color: rgba(227, 216, 183, var(--un-text-opacity));
}

// 弹窗
::v-deep .el-dialog__body {
  width: 100%;
  height: 70%;
  display: flex;
  justify-content: center;
  align-items: center;
}

::v-deep .el-input__wrapper {
  --el-input-focus-border-color: rgba(232, 157, 6, 0.8) !important;
}

::v-deep .el-select__wrapper.is-focused {
  box-shadow: 0 0 0 1px rgba(232, 157, 6, 0.8) !important;
}

.table-container {
  
   margin: 0;
  padding: 0;
  background-color: #000;
}

.custom-table {
  width: 100%;
  height: 100%;
  border-collapse: collapse; /* 合并边框，避免间隙 */
  max-width: 600px;
  margin: 0 auto;
  margin-top: 10px;
}

.cell {
  border: 1px solid #e0e0e0; /* 浅灰色边框 */
  padding: 30px;
  text-align: center;
  min-height: 40px;
  letter-spacing: 1;
}

.sjair {
  display: flex;
flex-direction: row;  

}

</style>