import * as THREE from "three";
import { initMatcapThree } from '@/three/effect/matcap';
import gsap from 'gsap';
import { canvasTexture2 } from '@/three/material/canvasTexture';
import { createCss3DText, createCss3DSprite, createCss3DObj } from '@/three/css3DRender';
var material = new THREE.LineBasicMaterial();
const ConeGeoMat = initMatcapThree({ path: './textures/444.png', color: 0xffa726 })
export function createLine({ THREE, linePoints = [], color = 0xffff00, n = 1, type = 0 }) {
    // const [x1, y1, z1] = props.needData.maxPosition;
    const arr = linePoints.map((item) => {
        const [x, z] = item;
        return new THREE.Vector3(x, 2, z);
    })
    const { x, y, z } = arr[0];
    const { x: x1, y: y1, z: z1 } = arr[1];

    const ConeGeo = new THREE.ConeGeometry(3, 8, 32);
    const Cone = new THREE.Mesh(ConeGeo, ConeGeoMat);

    Cone.position.set(x, y, z);

    const conePosition = new THREE.Vector3(x, y, z); // 圆锥体的当前位置
    const targetPosition = new THREE.Vector3(x1, y1, z1); // 目标点的位置

    // 计算指向目标点的方向向量
    const direction = new THREE.Vector3();
    direction.subVectors(targetPosition, conePosition).normalize();

    // 计算从默认方向向量 (0, 1, 0) 到目标方向向量的四元数
    const quaternion = new THREE.Quaternion();
    quaternion.setFromUnitVectors(new THREE.Vector3(0, 1, 0), direction);

    // 应用四元数旋转到圆锥体的几何体
    ConeGeo.applyQuaternion(quaternion);

    // 标记顶点和法线已更新
    ConeGeo.verticesNeedUpdate = true;
    ConeGeo.normalsNeedUpdate = true;
    // tetrahedron.add(plane);


    gsap.to(Cone.position, {
        x: x1,
        y: y1,
        z: z1,
        duration: 10,
        repeat: -1,
        ease: "none",
    });

    material.color = new THREE.Color(color)
    const geometry = new THREE.BufferGeometry().setFromPoints(arr);
    const lineMesh = new THREE.Line(geometry, material);
    lineMesh.points = linePoints;
    lineMesh.add(Cone);
    lineMesh.renderOrder = 1;

    return lineMesh;
}






// 创建几何体
const circleGeo = new THREE.CircleGeometry(5, 100)
export function createCicle({ text, THREE, position = { x: 0, y: 0, z: 0 }, bgColor }) {
    const { x, y, z } = position;

    // 创建材质
    const map = canvasTexture2(text, { bgColor: bgColor ?? "rgba(230, 81, 0,0.7)", color: '#ffffff' }).texture;
    const mat = new THREE.MeshBasicMaterial({ map });
    // 创建网格
    const circle = new THREE.Mesh(circleGeo, mat);
    circle.position.set(x, 2, z);
    circle.rotation.x = -Math.PI / 2;
    circle.point = position instanceof THREE.Vector3 ? position : new THREE.Vector3(x, y, z);
    return circle;
}

export function createRect({ text, THREE, position = { x: 0, y: 0, z: 0 }, }) {
    const { x, y, z } = position;
    const rect = createCss3DObj({
        dom: `
        <div style="
            writing-mode: vertical-rl; /* 垂直排列，从右到左 */
            text-orientation: mixed;   /* 保持字符方向 */
            white-space: nowrap;       /* 不换行 */
            height: 75px;              /* 设置高度 */
            padding: 2px;
            background: #ff9800;
            text-align: center;
            color: #fff;
            font-size: 5px;
            line-height: 8px;
            display: flex;
            align-items: center;
        ">
            ${text}
        </div>
        `});
    rect.position.set(x, y, z);
    rect.rotation.x = -Math.PI / 2;
    rect.point = position instanceof THREE.Vector3 ? position : new THREE.Vector3(x, y, z);
    return rect;
}