<template>
    <div v-show="isAni">
        <div class="canvasBox" ref="sceneDiv"></div>
        <div @click="stopEvent" @mousemove="stopEvent"
            class=" fixed bottom-20px right-20px w-25% h-260px bg-[#ff980099] p-20px" rounded-20px
            border="2px solid #2DA8EB" v-show="nodeEditShow">
            <div my-10px class="sbbd_header h-20px flex justify-between items-center">
                <div class="text-20px font-bold text-[#fff] ">通风节点编辑 </div>
                <div class="mt-[-25px]">
                    <el-icon @click="() => { reset(); nodeEditShow = false }" size="20rem" class=" cursor-pointer">
                        <Close />
                    </el-icon>
                </div>
            </div>
            <el-form class="mt-20px mr-10px" ref="form" label-width="80px" :inline="false" size="normal">
                <el-form-item label="节点名称：" label-width="100px">
                    <el-input v-model="nodeForm.NodeName"></el-input>
                </el-form-item>
                <el-form-item label="节点位置：" label-width="100px">
                    <el-space>
                        <span class="text-20px text-[#eee]">
                            x:
                        </span>
                        <el-input v-model="nodeForm.x"></el-input>
                        <span class="text-20px text-[#eee]">
                            y:
                        </span>
                        <el-input v-model="nodeForm.y"></el-input>
                    </el-space>
                </el-form-item>
            </el-form>
            <div>
                <span class="absolute bottom-40px right-30px ">
                    <el-button @click="reset">取消</el-button>
                    <el-button type="primary" @click="save">保存</el-button>
                </span>
            </div>
        </div>
    </div>
    <div class="canvasBox" ref="sceneDiv"></div>
    <div v-show="!isAni" class="canvasBox mt-20px  flex justify-center items-center font-bold text-20px text-#888888">
        暂无数据</div>

</template>
<script setup>
import { watch, reactive, ref, toRaw, onMounted } from 'vue';
import { useThree } from '@/three/useThree';
import { restrictCameraVerticalDirection } from '@/three/cameraControls.js'
import { createCss3DSprite, createCss3DObj } from '@/three/css3DRender';
import { ApiVentilatenetNodes, ApiVentilatenetBranch } from '@/https/encapsulation/VentilateNet';
import { onBeforeRouteLeave } from 'vue-router';
import { initMatcapThree } from '@/three/effect/matcap';
import { modelClick } from '@/three/utils/event';
import { addEvent, removeEvent, stopEvent } from '@/utils/window';
import { meshPickup } from '@/three/raycaster';
import { canvasTexture2 } from '@/three/material/canvasTexture';
import { updateVentilatenetNodes } from '@/https/api';
import { Close } from '@element-plus/icons-vue';
import { ElMessage } from "element-plus";
import { getType } from '@/utils/utils';
import { ApiTunList } from '@/https/encapsulation/threeDimensional';
import { createLine, createCicle, createRect } from './js/createMesh';

const endCamera = [0, 500, 0]
const { mountRender, scene, camera, renderer, css3Renderer, addOutlinePass, setMeshRotation, THREE, gsap, setMeshScale, setPosition, cameraControls, ani, stopRender } = useThree({ endCamera, isCameraAni: false, isControl: true, isAxes: false, isDepthBuffer: true })
cameraControls.minDistance = 100;
cameraControls.maxDistance = 800;
restrictCameraVerticalDirection(cameraControls, 0, 0);
cameraControls.minAzimuthAngle = 0;
cameraControls.maxAzimuthAngle = 0;

const sceneDiv = ref(null)
onBeforeRouteLeave(() => {
    stopRender();
})
onMounted(() => {
    mountRender(sceneDiv.value);
})


const pointsArr = ref([]);

let circleMesh = []
let lineMeshList = []
const isAni = ref(true);
const group = new THREE.Group();
const pointGroup = new THREE.Group();

// setMeshScale(group, 0)
const xChange = 300;
const zChange = 150;
setPosition(group, -xChange, 0, -zChange)
scene.add(group);

const css3Obj = createCss3DSprite('', '', 10, `
            <div class="flex justify-center items-center p-5px text-[12px]  text-[#fff]">
                    <p>暂无数据</p>
                </div>
        `)
css3Obj.name = 'empty'
onMounted(() => {
    sceneInit();
})
const apiTunList = ref()
async function getTunList() {
    const { Result } = await ApiTunList();
    apiTunList.value = Result ?? [];
}
getTunList();
function sceneReset() {
    clearEvent && clearEvent();
    currentLineMesh = null;
    group.remove(...group.children);
    circleMesh = []
}
function sceneInit() {
    // 两个点的坐标
    ApiVentilatenetNodes().then(res => {
        let { Result } = res;
        if (!Result.length) {
            isAni.value = false;
            return;
        }
        Result.length && (pointsArr.value = Result)

        pointsArr.value.forEach(v => {
            const { NodeID, X: x, Y: z, NodeName: text } = v;
            if (text == '1') return;
            const circle = createCicle({ THREE, text, position: { x, y: 0.5, z } })
            circle.renderOrder = 2;
            circle.originPoint = v;
            circle.mid = NodeID;
            circleMesh.push(circle)
        })
    }).then(res => {
        // 连线
        ApiVentilatenetBranch().then(res => {
            const { Result } = res;
            Result.forEach(v => {
                const { TunType, ENodeID, SNodeID, Remark } = v;
                const pointNodeName = pointsArr.value.map(v => v.NodeName)
                const pointPosition = pointsArr.value.map(v => [v.X, v.Y])
                const startNode = pointNodeName.findIndex(v => v.includes(SNodeID))
                const endNode = pointNodeName.findIndex(v => v.includes(ENodeID))
                const startPoint = pointPosition.at(startNode);
                const endPoint = pointPosition.at(endNode);
                if (SNodeID == 1 || ENodeID == 1) return;

                if (TunType == 43) {
                    if (!Remark) return;

                    const arr = Remark.split('；').map(v => v.split('，').map(v1 => Number(v1))).map(v => [v[0], v[1]])

                    const arr2 = [...arr, arr[0]];

                    const bCenter = [(arr[0][0] + arr[1][0]) / 2, (arr[0][1] + arr[1][1]) / 2]
                    const tCenter = [(arr[2][0] + arr[3][0]) / 2, (arr[2][1] + arr[3][1]) / 2]
                    const [x, y, z] = [(bCenter[0] + tCenter[0]) / 2, 0, (bCenter[1] + tCenter[1]) / 2]
                    const name = apiTunList.value.find(k => k.TunID == v.TunID)?.TunName;
                    const rect = createRect({ THREE, position: { x, y, z }, text: name ?? '-' })
                    const lineMesh2 = createLine({ THREE: THREE, linePoints: [startPoint, tCenter], color: 0xff9800 })
                    const lineMesh3 = createLine({ THREE: THREE, linePoints: [bCenter, endPoint], color: 0xff9800 })
                    group.add(rect);
                    group.add(lineMesh2);
                    group.add(lineMesh3);
                    lineMeshList.push(lineMesh2);
                    lineMeshList.push(lineMesh3);
                } else {
                    const lineMesh = createLine({ THREE: THREE, linePoints: [startPoint, endPoint], color: 0xff9800 },)
                    group.add(lineMesh);
                    lineMeshList.push(lineMesh);
                }
            })
            circleMesh.forEach(v => {
                group.add(v)
            })
        }).then(res => {
            addPointEvent();
        })
    })
}

let currentLineMesh = null;
let currentPoint = null;
let nodeForm = ref({ NodeName: '', x: null, y: null });
let clearEvent = null;
const nodeEditShow = ref(true);
let xgLineList = []; //与移动点相关的
let isDraw = true;
// 事件
// 创建射线
const raycaster = new THREE.Raycaster();
// 定义射线相交平面
const planeNormal = new THREE.Vector3(0, 1, 0); // 平面的法线，这里是朝上的
const planeConstant = 0; // 平面到原点的距离
const targetVec3 = new THREE.Vector3(0, 1, 0);
function addPointEvent() {

    function oncClick(event) {
        const { currentMesh } = meshPickup(event, circleMesh, camera);
        if (!currentPoint && currentMesh) {
            if (currentLineMesh && currentLineMesh.mid != currentMesh.object.mid) {
                reset();
            }
            nodeEditShow.value = true;
            currentPoint = currentMesh.object;
            nodeForm.value = currentMesh.object.originPoint;
            const { X, Y } = currentMesh.object.originPoint;
            xgLineList = [];
            lineMeshList.forEach(v => {
                const points = v.points;
                let index = points.findIndex((v) => {
                    const [x, y] = v;
                    return x === X && y === Y
                })
                if (index !== -1) {
                    v.needUpdateIndex = index;
                    xgLineList.push(v);
                    v.children[0].visible = false;
                }
            })

            addOutlinePass([currentMesh.object])
            isDraw = true;
            currentLineMesh = currentMesh.object;
        }
    }

    function onMouseMove(event) {
        if (currentPoint) {

            // 将鼠标位置转换为归一化设备坐标
            const mouse = new THREE.Vector2();
            mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
            mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
            // 设置射线的原点和方向
            raycaster.setFromCamera(mouse, camera);
            // 计算射线与平面的相交
            const intersection = raycaster.ray.intersectPlane(new THREE.Plane(planeNormal, planeConstant), targetVec3);
            if (intersection) {
                // 相交时的坐标
                const { x, y, z } = intersection;
                currentPoint.position.set(x + xChange, 2, z + zChange);
                nodeForm.value = { ...nodeForm.value, x: (x + xChange).toFixed(2), y: (z + zChange).toFixed(2) }; // 数据库 z为y 调换一下

                xgLineList.forEach(v => {
                    if (typeof v.needUpdateIndex != 'undefined' && getType(v.needUpdateIndex) == 'number') {
                        const { points } = v;
                        if (v.needUpdateIndex == 0) {
                            v.updatePoints = [[x + xChange, z + zChange], points[1]]
                        } else {
                            v.updatePoints = [points[0], [x + xChange, z + zChange]]
                        }
                        v.geometry = createLine({ THREE: THREE, linePoints: v.updatePoints, color: 0xff9800, isAddCone: false }).geometry
                    }
                })
            }
        }
    }
    // // 提交表单
    function dblClick() {
        if (currentPoint) {
            addOutlinePass([]);
            currentPoint = null
        }
    }

    function nodeReset() {
        if (currentPoint) {
            reset()
        }
    }

    function clearEventFn() {
        removeEvent(window, 'mousemove', onMouseMove)
        removeEvent(window, 'click', oncClick)
        // removeEvent(window, 'contextmenu', nodeReset)
        removeEvent(window, 'dblclick', dblClick)
    }
    addEvent(window, 'mousemove', onMouseMove)
    addEvent(window, 'click', oncClick)
    // addEvent(window, 'contextmenu', nodeReset)
    addEvent(window, 'dblclick', dblClick)
    clearEvent = clearEventFn;
}

function reset() {
    addOutlinePass([]);
    currentLineMesh && currentLineMesh.position.set(currentLineMesh.originPoint.X, 2, currentLineMesh.originPoint.Y);// 恢复原位
    xgLineList.length > 0 && xgLineList.forEach(v => {
        v.geometry = createLine({ THREE: THREE, linePoints: v.points, color: 0xff9800, isAddCone: false }).geometry;
        v.children[0].visible = true;
    })
    currentPoint = null;
    nodeForm.value = { NodeName: '', x: null, y: null };
}

async function save() {
    if (typeof nodeForm.value.NodeID != 'undefined') {
        const { x, y } = nodeForm.value;
        let data = { SchemeID: '', ...nodeForm.value, x, y };
        const { data: { Status, Msg } } = await updateVentilatenetNodes(data);
        handle(Status, Msg);
        function handle(Status, Msg) {
            if (Status == 0) {
                ElMessage.success('保存成功')
                reset();
                sceneReset();
                setTimeout(() => {
                    sceneInit();
                }, 100)
            } else {
                ElMessage.warning(Msg)
            }
        }
    }
}



ani((time, camera) => {
});
</script>
<style lang="scss">
#map {
    width: 100vw;
    height: 89vh;
    background-color: rgba(5, 25, 40, .5);
}


.leaflet_cicle_box {
    width: 30px;
    height: 30px;
    background-color: #1890ff;
    /* border: 4rem solid #40a9ff; */
    border-radius: 50%;
    text-align: center;
    line-height: 30px;
    font-size: 12px;


    span {
        color: white;

    }

}

.leaflet_rectangle_box {

    font-size: 15px;
    text-align: center;

    .leaflet_rectangle_text {
        color: white;
    }
}

.norem_svg_text {
    font-size: 10em;
}
</style>
<style lang="scss">
.sbbd_header {
    .el-icon {
        top: 15px;

        svg {
            color: #fff;

            &:hover {
                color: #ea2465;
            }
        }

    }
}


.sbbd {
    padding: 20px;
    width: 500px;
    background: transparent;
    background-image: url('./assets/img/side.png');
    background-repeat: no-repeat;
    height: 800px;
    background-size: 100%;
    top: 150px;
    overflow: hidden;

    .el-drawer__body {
        overflow: hidden;
    }

}
</style>
<style lang="scss" scoped>
::v-deep .el-button+.el-button {
    background-color: rgba(255, 179, 15, 0.3) !important;
    border: 1px solid rgba(232, 157, 6, 0.8) !important;
}

.btn_img {
    width: 19px;
    height: 19px;
}

.btn_menu {
    :deep(.el-button--large) {
        width: 135rem;
        height: 50rem;
        background-image: url('../tunEdit/assets/img/btn_bg.png');
        background-size: 100%;
    }

    :deep(.el-button--small) {
        width: 100rem;
        height: 30rem;
        background-image: url('../tunEdit/assets/img/btn_bg.png');
        background-size: 100%;
    }
}


:deep(.el-form-item__label) {
    color: #fff;
}

:deep(.el-form-el-dialog__header) {
    margin: 10px 0 0 10px;
    color: #fff;
}

:v-deep(.el-button .el-icon) {
    height: 0.5em;
    width: 0.5em;
}

:deep(.el-input.is-disabled .el-input__inner) {
    -webkit-text-fill-color: #2962ff;
    cursor: not-allowed;
}
</style>