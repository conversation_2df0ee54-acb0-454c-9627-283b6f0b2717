<template>
    <div v-show="isAni" class="h-100% w-100%  bg-[#262626]" ref="sceneDiv"></div>
    <div v-show="!isAni"
        class="w-100%h-100% mt-20px  flex justify-center items-center font-bold text-20px text-#888888">
        暂无数据</div>
</template>
<script setup>
import { watch, reactive, ref, toRaw, onMounted } from 'vue';
import { useThree } from '@/three/useThree';
import { restrictCameraVerticalDirection } from '@/three/cameraControls.js'
import { createCss3DText, createCss3DSprite, createCss3DObj } from '@/three/css3DRender';
import { ApiVentilatenetNodes, ApiVentilatenetBranch } from '@/https/encapsulation/VentilateNet';
import { onBeforeRouteLeave } from 'vue-router';
import { saveCameraPosition } from '../thereVentilation/js/tunInit';
import { ApiTunList } from '@/https/encapsulation/threeDimensional';
import { createLine, createCicle, createRect } from './js/createMesh';
const endCamera = [0, 500, 0]
const { mountRender, scene, camera, renderer, css3Renderer, addOutlinePass, setMeshRotation, THREE, gsap, setMeshScale, setPosition, cameraControls, ani, stopRender } = useThree({ endCamera, isCameraAni: false, isControl: true, isAxes: false, isDepthBuffer: true })
scene.background = new THREE.Color(0x262626)
restrictCameraVerticalDirection(cameraControls, 0, 0);
cameraControls.minDistance = 100;
cameraControls.maxDistance = 800;
cameraControls.minAzimuthAngle = 0;
cameraControls.maxAzimuthAngle = 0;

const { setCameraFly, lastCameraPosition } = saveCameraPosition(cameraControls, endCamera)
const sceneDiv = ref(null)
onBeforeRouteLeave(() => {
    stopRender();
})
onMounted(() => {
    mountRender(sceneDiv.value);
})
const apiTunList = ref()
async function getTunList() {
    const { Result } = await ApiTunList();
    apiTunList.value = Result ?? [];
}
getTunList();

const pointsArr = ref([]);

let circleMesh = []
const group = new THREE.Group();
const pointGroup = new THREE.Group();
const isAni = ref(true);
const xChange = 300;
const zChange = 150;
setPosition(group, -xChange, 0, -zChange)
scene.add(group);

const css3Obj = createCss3DSprite('', '', 10, `
            <div class="flex justify-center items-center p-5px text-[12px]  text-[#fff]">
                    <p>暂无数据</p>
                </div>
        `)
css3Obj.name = 'empty'
onMounted(() => {
    sceneInit();
})


function sceneInit() {
    // 两个点的坐标
    ApiVentilatenetNodes().then(res => {
        let { Result } = res;
        if (!Result.length) {
            isAni.value = false;
            return;
        }
        Result.length && (pointsArr.value = Result)

        pointsArr.value.forEach(v => {
            const { X: x, Y: z, NodeName: text } = v;
            if (text == '1') return;
            const circle = createCicle({ THREE, text, position: { x, y: 0.5, z } })
            circle.renderOrder = 2;
            circle.originPoint = v;
            circleMesh.push(circle)
        })
    }).then(res => {
        // 连线
        ApiVentilatenetBranch().then(res => {
            const { Result } = res;
            Result.forEach(v => {
                const { TunType, ENodeID, SNodeID, Remark } = v;
                if (SNodeID == 1 || ENodeID == 1) return;
                const pointNodeName = pointsArr.value.map(v => v.NodeName)
                const pointPosition = pointsArr.value.map(v => [v.X, v.Y])
                const startNode = pointNodeName.findIndex(v => v.includes(SNodeID))
                const endNode = pointNodeName.findIndex(v => v.includes(ENodeID))
                const startPoint = pointPosition.at(startNode);
                const endPoint = pointPosition.at(endNode);

                if (TunType == 43) {
                    if (!Remark) return;

                    const arr = Remark.split('；').map(v => v.split('，').map(v1 => Number(v1))).map(v => [v[0], v[1]])

                    const arr2 = [...arr, arr[0]];

                    const bCenter = [(arr[0][0] + arr[1][0]) / 2, (arr[0][1] + arr[1][1]) / 2]
                    const tCenter = [(arr[2][0] + arr[3][0]) / 2, (arr[2][1] + arr[3][1]) / 2]
                    const [x, y, z] = [(bCenter[0] + tCenter[0]) / 2, 0, (bCenter[1] + tCenter[1]) / 2]
                    const name = apiTunList.value.find(k => k.TunID == v.TunID)?.TunName;
                    const rect = createRect({ THREE, position: { x, y, z }, text: name ?? '-' })
                    const lineMesh2 = createLine({ THREE: THREE, linePoints: [startPoint, tCenter], color: 0xff9800 })
                    const lineMesh3 = createLine({ THREE: THREE, linePoints: [bCenter, endPoint], color: 0xff9800 })
                    group.add(rect);
                    group.add(lineMesh2);
                    group.add(lineMesh3);
                } else {
                    const lineMesh = createLine({ THREE: THREE, linePoints: [startPoint, endPoint], color: 0xff9800 },)
                    group.add(lineMesh);
                }
            })
            circleMesh.forEach(v => {
                group.add(v)
            })
        }).then(res => {

        })
    })
}
ani((time, camera) => {
});
</script>
<style lang="scss">
#map {
    width: 100vw;
    height: 89vh;
    background-color: rgba(5, 25, 40, .5);
}


.leaflet_cicle_box {
    width: 30px;
    height: 30px;
    background-color: #1890ff;
    /* border: 4rem solid #40a9ff; */
    border-radius: 50%;
    text-align: center;
    line-height: 30px;
    font-size: 12px;


    span {
        color: white;

    }

}

.leaflet_rectangle_box {

    font-size: 15px;
    text-align: center;

    .leaflet_rectangle_text {
        color: white;
    }
}

.norem_svg_text {
    font-size: 10em;
}
</style>
<style lang="scss">
.sbbd_header {
    .el-icon {
        top: 15px;

        svg {
            color: #fff;

            &:hover {
                color: #ea2465;
            }
        }

    }
}


.sbbd {
    padding: 20px;
    width: 500px;
    background: transparent;
    background-image: url('./assets/img/side.png');
    background-repeat: no-repeat;
    height: 800px;
    background-size: 100%;
    top: 150px;
    overflow: hidden;

    .el-drawer__body {
        overflow: hidden;
    }

}
</style>
<style lang="scss" scoped>
.btn_img {
    width: 19px;
    height: 19px;
}

.btn_menu {
    :deep(.el-button--large) {
        width: 135rem;
        height: 50rem;
        background-image: url('../tunEdit/assets/img/btn_bg.png');
        background-size: 100%;
    }

    :deep(.el-button--small) {
        width: 100rem;
        height: 30rem;
        background-image: url('../tunEdit/assets/img/btn_bg.png');
        background-size: 100%;
    }
}


:deep(.el-form-item__label) {
    color: #fff;
}

:deep(.el-form-el-dialog__header) {
    margin: 10px 0 0 10px;
    color: #fff;
}

:v-deep(.el-button .el-icon) {
    height: 0.5em;
    width: 0.5em;
}

:deep(.el-input.is-disabled .el-input__inner) {
    -webkit-text-fill-color: #2962ff;
    cursor: not-allowed;
}
</style>