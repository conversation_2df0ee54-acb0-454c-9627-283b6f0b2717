<template>
    <!-- 这是新加的页面 所以组件在别的目录下 -->
    <div class="h-990px  mt-90px">
        <el-row class="h-50%">
            <el-col :span="24">
                <TunEditVentilationCalculation></TunEditVentilationCalculation>
            </el-col>
        </el-row>
        <el-row class="h-50%">
            <el-col :span="12" class="bg-#062136">
                <SetNodeManuallyVentilationCalculation></SetNodeManuallyVentilationCalculation>
            </el-col>
            <el-col :span="12">
                <div class="bg-[#082237] w-full h-full text-[#fff] p-20px">
                    <div class="text-20px font-bold text-[#fff] ">巷道基本信息 </div>
                    <el-divider style="border-color: #228be6;" />

                    <el-space :size="20" direction="vertical">
                        暂无数据
                    </el-space>

                </div>
            </el-col>
        </el-row>
    </div>

</template>
<script setup>
import SetNodeManuallyVentilationCalculation from '../tunEdit/components/2DTunEdit.vue';
import TunEditVentilationCalculation from '../tunEdit/3DTunEdit.vue';
import VentilationNetworkVentilationCalculation from '../ventilationNetwork/ventilationNetworkVentilationCalculation.vue';

</script>
<style lang="scss" scoped></style>