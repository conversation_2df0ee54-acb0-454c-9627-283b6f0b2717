// vite.config.js
import { defineConfig } from "file:///D:/project/%E6%99%BA%E8%83%BD%E9%80%9A%E9%A3%8E%E5%B9%B3%E5%8F%B0/Code/5%20Demos/node_modules/.store/vite@4.5.3/node_modules/vite/dist/node/index.js";
import { fileURLToPath, URL } from "node:url";
import vue from "file:///D:/project/%E6%99%BA%E8%83%BD%E9%80%9A%E9%A3%8E%E5%B9%B3%E5%8F%B0/Code/5%20Demos/node_modules/.store/@vitejs+plugin-vue@4.6.2/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import UnoCSS from "file:///D:/project/%E6%99%BA%E8%83%BD%E9%80%9A%E9%A3%8E%E5%B9%B3%E5%8F%B0/Code/5%20Demos/node_modules/.store/unocss@0.56.5/node_modules/unocss/dist/vite.mjs";
import { presetIcons, presetAttributify, presetUno } from "file:///D:/project/%E6%99%BA%E8%83%BD%E9%80%9A%E9%A3%8E%E5%B9%B3%E5%8F%B0/Code/5%20Demos/node_modules/.store/unocss@0.56.5/node_modules/unocss/dist/index.mjs";
import AutoImport from "file:///D:/project/%E6%99%BA%E8%83%BD%E9%80%9A%E9%A3%8E%E5%B9%B3%E5%8F%B0/Code/5%20Demos/node_modules/.store/unplugin-auto-import@0.16.7/node_modules/unplugin-auto-import/dist/vite.js";
import Components from "file:///D:/project/%E6%99%BA%E8%83%BD%E9%80%9A%E9%A3%8E%E5%B9%B3%E5%8F%B0/Code/5%20Demos/node_modules/.store/unplugin-vue-components@0.25.2/node_modules/unplugin-vue-components/dist/vite.mjs";
import autoprefixer from "file:///D:/project/%E6%99%BA%E8%83%BD%E9%80%9A%E9%A3%8E%E5%B9%B3%E5%8F%B0/Code/5%20Demos/node_modules/.store/autoprefixer@10.4.19/node_modules/autoprefixer/lib/autoprefixer.js";
import postCssPxToRem from "file:///D:/project/%E6%99%BA%E8%83%BD%E9%80%9A%E9%A3%8E%E5%B9%B3%E5%8F%B0/Code/5%20Demos/node_modules/.store/postcss-pxtorem@5.1.1/node_modules/postcss-pxtorem/index.js";
import glsl from "file:///D:/project/%E6%99%BA%E8%83%BD%E9%80%9A%E9%A3%8E%E5%B9%B3%E5%8F%B0/Code/5%20Demos/node_modules/.store/vite-plugin-glsl@1.3.0/node_modules/vite-plugin-glsl/src/index.js";
var __vite_injected_original_import_meta_url = "file:///D:/project/%E6%99%BA%E8%83%BD%E9%80%9A%E9%A3%8E%E5%B9%B3%E5%8F%B0/Code/5%20Demos/vite.config.js";
var vite_config_default = defineConfig({
  base: "./",
  server: {
    host: "0.0.0.0"
  },
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", __vite_injected_original_import_meta_url))
    }
  },
  plugins: [
    AutoImport({
      // resolvers: [ElementPlusResolver()],
    }),
    Components({
      // resolvers: [ElementPlusResolver()],
    }),
    vue(),
    glsl(),
    UnoCSS({ presets: [presetIcons(), presetAttributify(), presetUno()] })
  ],
  css: {
    postcss: {
      plugins: [
        autoprefixer({
          overrideBrowserslist: ["Android 4.1", "iOS 7.1", "Chrome > 31", "ff > 31", "ie >= 8"]
        }),
        postCssPxToRem({
          // 自适应，px>rem转换
          rootValue: 1,
          // 75表示750设计稿，37.5表示375设计稿
          propList: ["*"],
          // 需要转换的属性，这里选择全部都进行转换
          selectorBlackList: ["norem"]
          // 过滤掉norem-开头的class，不进行rem转换 可以统一处理第三方组件库
        })
      ]
    }
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
